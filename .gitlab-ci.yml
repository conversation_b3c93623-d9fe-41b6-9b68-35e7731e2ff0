image: ghcr.io/cirruslabs/flutter
stages:
  - build
  - deploy
  
android:
  stage: build
  script:
    - flutter build apk --release --build-number ${CI_JOB_ID:0:8}
    - flutter build appbundle --release --build-number ${CI_JOB_ID:0:8}
    - sudo bundle install
    - cd android && bundle exec fastlane move_files
  artifacts:
    paths:
      - build/artifacts/
  tags:
    - flutter

playstore:
  stage: deploy
  dependencies:

    - android
  script:
    - cd android && bundle exec fastlane playstore_internal_release
  tags:
    - flutter