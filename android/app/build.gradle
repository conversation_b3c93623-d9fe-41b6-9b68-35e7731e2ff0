plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"

}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "com.darent"
    compileSdk 35
    ndkVersion flutter.ndkVersion
//    ndkVersion "25.1.8937393"
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        // Enable desugaring for flutter_local_notifications
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.darent"

        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        // Updated minSdkVersion to 26 for myfatoorah_flutter compatibility
        minSdkVersion 26
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        resValue "string", "build_config_package", "com.darent"

    }
    // This duplicate compileOptions block is removed as it's already defined above with desugaring enabled

  signingConfigs {
           release {
            def keystorePropertiesFile = rootProject.file("key.properties")
            def keystoreProperties = new Properties()
            keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {


        release {

            minifyEnabled false  // Disable R8 for now
            shrinkResources false
            signingConfig signingConfigs.release
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            // signingConfig signingConfigs.debug

            // using proguard to enable local notification in release mode
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

        }
    }
}

flutter {
    source '../..'
}

dependencies {
        // implementation 'com.android.support:multidex:2.0.1'
        // Add desugaring support for flutter_local_notifications
        coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'
        // Add Firebase dependencies for MainApplication.kt
        implementation platform('com.google.firebase:firebase-bom:32.7.4')
        implementation 'com.google.firebase:firebase-messaging-ktx'
         // Required dependencies for MoEngage SDK
        implementation("androidx.core:core:1.9.0")
        implementation("androidx.appcompat:appcompat:1.4.0")
        implementation("androidx.lifecycle:lifecycle-process:2.5.1")
        // Add Google Pay dependencies for pay_android plugin
        implementation 'com.google.android.gms:play-services-wallet:19.4.0'
}
