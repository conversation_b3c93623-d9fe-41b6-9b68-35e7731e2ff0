<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.darent">
    <!-- Location permissions -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Internet and storage permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <!-- Advertising ID permission -->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />

    <!-- MoEngage required permissions -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />


    <queries>
        <package android:name="com.snapchat.android" />
    </queries>
    <queries>
        <package android:name="com.zhiliaoapp.musically" />
        <package android:name="com.ss.android.ugc.trill" />
    </queries>
    <application
        android:name=".MainApplication"
        android:allowBackup="false"
        android:icon="@mipmap/ic_launcher"
        android:label="Darent"
        tools:replace="android:allowBackup">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">

            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme"
            />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="FLUTTER_NOTIFICATION_CLICK" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <meta-data android:name="flutter_deeplinking_enabled" android:value="true" />
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:host="static.darent.com"
                    android:scheme="https" />
                <!--                <data-->
                <!--                    android:host="darent.com"-->
                <!--                    android:pathPrefix="/ar/properties"-->
                <!--                    android:scheme="https"/>-->
            </intent-filter>
        </activity>
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.flutter_inappwebview.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        <meta-data android:name="com.facebook.sdk.AdvertiserIDCollectionEnabled"
            android:value="true" />
        <meta-data android:name="com.snapchat.kit.sdk.clientId"
            android:value="80a649a3-1ec3-4091-90d7-22f8f3495ede" />
        <meta-data android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyBwsMnL9BeaD5OWxE3AyuvZlGniO8as6w8" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="channel_ID" />
        <meta-data android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />
        <meta-data android:name="com.webengage.sdk.android.environment" android:value="in" />
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <!-- MoEngage Configuration -->
        <meta-data
            android:name="com.moengage.app_id"
            android:value="O1ZAC6O2X0IHDHVKG0JR1KEC" />
        <meta-data
            android:name="com.moengage.data_center"
            android:value="DATA_CENTER_2" />
    </application>
</manifest>