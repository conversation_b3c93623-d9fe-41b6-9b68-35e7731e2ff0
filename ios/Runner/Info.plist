<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>FlutterDeepLinkingEnabled</key>
		<true />
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true />
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Darent</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>darent</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>WEGLicenseCode</key>
		<string>in~58adcbb5</string>
		<key>WEGLogLevel</key>
		<string>VERBOSE</string>
		<key>WEGEnvironment</key>
		<string>IN</string>
		<key>MoEngage</key>
		<dict>
			<key>APP_ID</key>
			<string>O1ZAC6O2X0IHDHVKG0JR1KEC</string>
			<key>DATA_CENTER</key>
			<string>DATA_CENTER_2</string>
			<key>DEBUG_LOG</key>
			<true />
		</dict>
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleTypeRole</key>
				<string>Editor</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.googleusercontent.apps.220208665865-iaov5mb9svmj1vbdhanolnk98mu1pgu4</string>
					<string>fb551083250080905</string>
					<string>com.darent</string>
					<string>aw8gmot8nb7b8s6i</string>
				</array>
			</dict>
		</array>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>FacebookAppID</key>
		<string>551083250080905</string>
		<key>FacebookClientToken</key>
		<string>********************************</string>
		<key>FacebookDisplayName</key>
		<string>Darent</string>
		<key>LSApplicationQueriesSchemes</key>
		<array>
			<string>tiktokopensdk</string>
			<string>tiktoksharesdk</string>
			<string>snssdk1180</string>
			<string>snssdk1233</string>
			<string>snapchat</string>
			<string>comgooglemaps</string>
			<string>iosamap</string>
		</array>
		<key>LSRequiresIPhoneOS</key>
		<true />
		<key>NSAppTransportSecurity</key>
		<dict>
			<key>NSAllowsArbitraryLoads</key>
			<true />
		</dict>
		<key>NSBonjourServices</key>
		<array>
			<string>_dartobservatory._tcp</string>
		</array>
		<key>NSCameraUsageDescription</key>
		<string>Darent would like to access your camera to take and upload profile picture and
			listing pictures</string>
		<key>NSPhotoLibraryUsageDescription</key>
		<string>Darent wants to access your photos for updating your property pictures</string>
		<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
		<string>Your location is required to show the properties near you.</string>
		<key>NSLocationAlwaysUsageDescription</key>
		<string>Your location is required to show the properties near you.</string>
		<key>NSLocationWhenInUseUsageDescription</key>
		<string>Your location is required to show the properties near you.</string>
		<key>NSAppleMusicUsageDescription</key>
		<string>We access media features to help you upload and view property photos seamlessly.</string>
		<key>NSMotionUsageDescription</key>
		<string>We use motion data to improve location accuracy and enhance your experience finding
			properties.</string>
		<key>NSBluetoothAlwaysUsageDescription</key>
		<string>We use Bluetooth to enhance location services and provide nearby property
			recommendations.</string>

		<key>SCSDKClientId</key>
		<string>80a649a3-1ec3-4091-90d7-22f8f3495ede</string>
		<key>TikTokAppID</key>
		<string>aw8gmot8nb7b8s6i</string>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true />
		<key>UIBackgroundModes</key>
		<array>
			<string>fetch</string>
			<string>remote-notification</string>
		</array>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIStatusBarHidden</key>
		<false />
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false />
	</dict>
</plist>