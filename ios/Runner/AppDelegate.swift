import UIKit
import Flutter
import GoogleMaps
import FirebaseCore
// import Tik<PERSON>ok<PERSON>penSDK
import WebEngage
import webengage_flutter
import MoEngageSDK
import moengage_flutter_ios

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    FirebaseApp.configure()
    GMSServices.provideAPIKey("AIzaSyBlsO3-QsNeKNFIf1fllhl9i29kJ0azdWo")
    GeneratedPluginRegistrant.register(with: self)

    // Initialize MoEngage SDK
    let sdkConfig = MoEngageSDKConfig(appId: "O1ZAC6O2X0IHDHVKG0JR1KEC", dataCenter: .data_center_02)
    // sdkConfig.dataCenter = .data_center_2

    // Configure logs for development
    #if DEBUG
        sdkConfig.consoleLogConfig = MoEngageConsoleLogConfig(isLoggingEnabled: true, loglevel: .verbose)
    #endif

    // Initialize MoEngage SDK
   MoEngageInitializer.sharedInstance.initializeDefaultInstance(sdkConfig, launchOptions: launchOptions)


    // Configure UNUserNotificationCenter delegate
    if #available(iOS 10.0, *) {
         UNUserNotificationCenter.current().delegate = self as? UNUserNotificationCenterDelegate
    }

    // Register for remote notifications
    application.registerForRemoteNotifications()

    // Initialize WebEngage
    WebEngage.sharedInstance().application(application, didFinishLaunchingWithOptions: launchOptions)

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // Handle push notification registration
  override func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    // MoEngageSDKMessaging.sharedInstance.setPushToken(deviceToken)
    super.application(application, didRegisterForRemoteNotificationsWithDeviceToken: deviceToken)
  }

  // Handle push notification reception
  override func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
    // MoEngageSDKMessaging.sharedInstance.passPushPayload(userInfo)
    super.application(application, didReceiveRemoteNotification: userInfo, fetchCompletionHandler: completionHandler)
  }
}
