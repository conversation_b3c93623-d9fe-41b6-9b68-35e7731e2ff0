name: Distribute Android To AppTester

on:
  push:
    branches:
      - test

  pull_request:
    branches:
      - test

env:
  ENVIRONMENT_NAME: Test

jobs:
  Build_distribute:
    if: github.event_name == 'push'
    name: Build APK
    runs-on: ubuntu-latest
    environment: Test
    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.5

      - name: set up JDK 17
        uses: actions/setup-java@v4.2.1
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: 'gradle'

      - name: Setup NDK
        uses: nttld/setup-ndk@v1
        id: setup-ndk-r27c
        with:
          ndk-version: r27c
          local-cache: true

      - uses: subosito/flutter-action@v2.14.0
        with:
          flutter-version: '3.29.0'

      - name: Print Selected environment
        run: |
          echo "Selected Environment: ${{ env.ENVIRONMENT_NAME }}"

      - name: Create .env file
        run: |
          echo "${{ vars.ENV_FILE }}" >> .env

      - name: Create key.properties file
        run: 
          echo "${{ secrets.ANDROID_KEY_PROPERTIES }}" >> android/key.properties

      - name: Decode Keystore
        id: decode_keystore
        uses: timheuer/base64-to-file@v1
        with:
          fileDir: './android/app/'
          fileName: 'keystore-file.jks'
          encodedString: ${{ secrets.ANDROID_KEY_STORE }}

      - name: Install dependencies
        run: flutter pub get
      
      - name: Run tests
        run: flutter test -r github

      - name: Set new version of app
        env:
          CURRENT_VERSION_NAME: ${{ vars.VERSION_NAME }}
          CURRENT_VERSION: ${{ vars.VERSION_CODE }}
        run: |
          CURRENT_VERSION=$((CURRENT_VERSION + 1))
          CODE_VERSION=$((CURRENT_VERSION))
          echo $CURRENT_VERSION
          sed -i "/version:/c version: $CURRENT_VERSION_NAME+$CODE_VERSION" pubspec.yaml
          echo "CURRENT_VERSION=$CURRENT_VERSION" >> $GITHUB_ENV

      - name: Update environment variable
        env:
          GH_PAT: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          REPO_OWNER: ${{ github.repository_owner }}
          REPO_NAME: ${{ github.event.repository.name }}
          ENVIRONMENT_NAME: ${{ env.ENVIRONMENT_NAME }}
          VARIABLE_NAME: VERSION_CODE
          VARIABLE_VALUE: ${{ env.CURRENT_VERSION }}
        run: |
          curl -X PATCH \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Authorization: token $GH_PAT" \
            https://api.github.com/repos/$REPO_OWNER/$REPO_NAME/environments/$ENVIRONMENT_NAME/variables/$VARIABLE_NAME \
            -d '{"name":"'$VARIABLE_NAME'", "value":"'$VARIABLE_VALUE'"}'

      - name: build apk
        run: flutter build apk --no-tree-shake-icons

      - name: Set release notes
        id: set_release_notes
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            MESSAGE="${{ github.event.pull_request.title }}"
          else
            MESSAGE="${{ github.event.head_commit.message }}"
          fi
          echo "RELEASE_NOTES=${{ env.ENVIRONMENT_NAME }} - $MESSAGE" >> $GITHUB_ENV

      - name: upload artifact to Firebase App Distribution
        uses: wzieba/Firebase-Distribution-Github-Action@v1.7.0
        with:
          appId: ${{ secrets.FIREBASE_APP_ID }}
          serviceCredentialsFileContent: ${{ secrets.FIREBASE_CREDENTIAL_FILE_CONTENT }}
          groups: testers
          releaseNotes: ${{ env.RELEASE_NOTES }}
          file: build/app/outputs/flutter-apk/${{ vars.ANDROID_OUTPUT_FILE_NAME }}.apk

  run_tests:
    if: github.event_name == 'pull_request'
    name: Run Tests
    runs-on: ubuntu-latest
    environment: Test
    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.5

      - name: set up JDK 17
        uses: actions/setup-java@v4.2.1
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: 'gradle'

      - uses: subosito/flutter-action@v2.14.0
        with:
          flutter-version: '3.29.0'

      - name: Run tests
        run: flutter test -r github