name: Distribute To Google Play (mac)

on:
  push:
    branches:
      - prod

env:
  ENVIRONMENT_NAME: Prod

jobs:
  Build_distribute:
    if: github.event_name == 'push'
    name: Build AAB
    runs-on: macos-latest
    environment: Prod
    timeout-minutes: 15
    steps:
      - name: Checkout
        uses: actions/checkout@v4.1.5

      - name: set up JDK 17
        uses: actions/setup-java@v4.2.1
        with:
          java-version: '17'
          distribution: 'temurin'

      - uses: subosito/flutter-action@v2.14.0
        with:
          flutter-version: '3.29.0'

      - name: Expose git commit data
        uses: rlespinasse/git-commit-data-action@v1.5.0

      - name: Print Selected environment
        run: |
          echo "Selected Environment: ${{ env.ENVIRONMENT_NAME }}"

      - name: Create .env file
        run: |
          echo "${{vars.ENV_FILE}}" >> .env

      - name: Create key.properties file
        run: |
          echo "${{secrets.ANDROID_KEY_PROPERTIES}}" >> android/key.properties

      - name: Decode Keystore
        id: decode_keystore
        uses: timheuer/base64-to-file@v1
        with:
          fileDir: './android/app/'
          fileName: 'keystore-file.jks'
          encodedString: ${{ secrets.ANDROID_KEY_STORE }}

      - name: Install dependencies
        run: flutter pub get

      - name: Set new version of app
        env:
          CURRENT_VERSION_NAME: ${{ vars.VERSION_NAME }}
          CURRENT_VERSION: ${{ vars.VERSION_CODE }}
        run: |
          CURRENT_VERSION=$((CURRENT_VERSION + 1))
          CODE_VERSION=$((CURRENT_VERSION))
          echo $CURRENT_VERSION
          sed -i "/version:/c version: $CURRENT_VERSION_NAME+$CODE_VERSION" pubspec.yaml
          echo "CURRENT_VERSION=$CURRENT_VERSION" >> $GITHUB_ENV

      - name: Update environment variable
        env:
          GH_PAT: ${{ secrets.PERSONAL_ACCESS_TOKEN }}
          REPO_OWNER: ${{ github.repository_owner }}
          REPO_NAME: ${{ github.event.repository.name }}
          ENVIRONMENT_NAME: ${{ env.ENVIRONMENT_NAME }}
          VARIABLE_NAME: VERSION_CODE
          VARIABLE_VALUE: ${{ env.CURRENT_VERSION }}
        run: |
          curl -X PATCH \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Authorization: token $GH_PAT" \
            https://api.github.com/repos/$REPO_OWNER/$REPO_NAME/environments/$ENVIRONMENT_NAME/variables/$VARIABLE_NAME \
            -d '{"name":"'$VARIABLE_NAME'", "value":"'$VARIABLE_VALUE'"}'

      - name: build aab
        run: flutter build appbundle --flavor=${{vars.FLAVOR_NAME}}
      - uses: r0adkll/upload-google-play@v1
        with:
          serviceAccountJsonPlainText: ${{ secrets.GP_SERVICE_ACCOUNT_JSON }}
          packageName: ${{vars.GP_BUNDLE_ID}}
          releaseFiles: build/app/outputs/bundle/${{vars.AAB_OUTPUT_PATH}}/${{vars.ANDROID_OUTPUT_FILE_NAME}}.aab
          track: alpha
          status: draft
