{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
    
        {
            "name": "darent",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "darent (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "darent (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "pay_platform_interface",
            "cwd": "build/unit_test_assets/packages/pay_platform_interface",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "pay_platform_interface (profile mode)",
            "cwd": "build/unit_test_assets/packages/pay_platform_interface",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "pay_platform_interface (release mode)",
            "cwd": "build/unit_test_assets/packages/pay_platform_interface",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}