import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:get/get.dart';

class PdfReceiptViewer extends StatelessWidget {
  final File file;
  final String screenName;
  const PdfReceiptViewer({super.key,required this.file, this.screenName = "VAT Invoice"});

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(icon: const Icon(Icons.chevron_left),onPressed: (){
          Get.back();
        }),
        title: Text(screenName),
      ),
      body: PDFView(
        filePath: file.path,
        // onRender: (pages) => c.setPageRender,
        // onViewCreated: (controller) => c.setPdfController,
        // onPageChanged: (indexPage, _) => c.setPageIndex,
      ),

    );
  }
}
