import 'package:darent/components/custom_textfield.dart';
import 'package:darent/screens/host/host_review/evaluate_guest.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../components/common_button.dart';
import '../../../components/common_checkbox.dart';
import '../../../components/custom_text.dart';
import '../../../components/views_common.dart';
import '../../../controllers/hostDashboard_controller.dart';
import '../../../utils/sizeconfig.dart';
import '../../../helperMethods/translation_helper.dart';

class RateAndReviewScreen extends StatelessWidget {
  const RateAndReviewScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HostDashboardController>(
        id: "rate",
        builder: (c) => Scaffold(
              resizeToAvoidBottomInset: true,
              appBar: AppBar(
                elevation: 0,
                leading: IconButton(
                    onPressed: Get.back,
                    icon: const Icon(Icons.chevron_left, size: 40)),
              ),
              body: SingleChildScrollView(
                padding: EdgeInsets.all(widthSpace(viewPadding)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SvgPicture.asset("assets/rate.svg", height: 75, width: 75),
                    SizedBox(height: heightSpace(4.5)),
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .cleanlinessTitle
                            .replaceAll(
                                ":customer_name", c.guestData.value!.userName!),
                        size: 3.2),
                    SizedBox(height: heightSpace(3)),
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostReview
                          .subtitle
                          .replaceAll(
                              ":customer_name", c.guestData.value!.userName!),
                      size: 2.2,
                      color: Colors.black,
                    ),
                    SizedBox(height: heightSpace(1.5)),
                    RatingBar.builder(
                      initialRating: 0,
                      minRating: 0,
                      direction: Axis.horizontal,
                      allowHalfRating: false,
                      unratedColor: Colors.grey.withOpacity(.5),
                      itemCount: 5,
                      itemSize: widthSpace(10),
                      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                      itemBuilder: (context, _) => const Icon(
                        Icons.star,
                        color: Colors.black,
                      ),
                      onRatingUpdate: (rating) {
                        c.hostRating = rating;
                        if (c.hostRating > 3) {
                          c.somethingElse = false;
                          bool containsTrue =
                              c.defaultReviews.any((e) => e['isChecked']);
                          if (containsTrue) {
                            c.defaultReviews.value = c.defaultReviews.map((e) {
                              if (e['isChecked'] == true) {
                                e['isChecked'] = false;
                              }
                              return e;
                            }).toList();
                          }
                          c.defaultReviews.refresh();
                          c.somethingElseController.clear();
                        }
                        c.update(["rate"]);
                      },
                      updateOnDrag: true,
                    ),
                    SizedBox(height: heightSpace(1.5)),
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostReview
                          .selectRating,
                      size: 2.2,
                      color: Colors.black,
                    ),
                    SizedBox(height: heightSpace(1.5)),
                    const Divider(),
                    if (c.hostRating <= 3 && c.hostRating != 0) ...[
                      SizedBox(height: heightSpace(1.5)),
                      CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .whatHappend,
                        size: 2.2,
                        color: Colors.black,
                      ),
                      SizedBox(height: heightSpace(1.5)),
                      CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .selectOption,
                        size: 1.9,
                        color: const Color(greyText),
                      ),
                      SizedBox(height: heightSpace(1.5)),
                      Wrap(
                          runSpacing: 10,
                          spacing: 10,
                          runAlignment: WrapAlignment.spaceBetween,
                          direction: Axis.horizontal,
                          alignment: WrapAlignment.spaceBetween,
                          children: c.defaultReviews
                              .map<Widget>((item) => GestureDetector(
                                    onTap: () {
                                      int index = c.defaultReviews
                                          .indexWhere((e) => e == item);
                                      if (item["title"] ==
                                          Get.find<TranslationHelper>()
                                              .translations
                                              .hostReview
                                              .cleanlinessOption5) {
                                        c.somethingElse = !c.somethingElse;
                                      }
                                      c.defaultReviews[index]["isChecked"] =
                                          !c.defaultReviews[index]["isChecked"];
                                      c.defaultReviews.refresh();
                                      c.update(["rate"]);
                                    },
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: widthSpace(5),
                                          vertical: heightSpace(1)),
                                      decoration: BoxDecoration(
                                          border: Border.all(
                                              color: item["isChecked"]
                                                  ? Colors.black
                                                  : const Color(greyBorder)),
                                          borderRadius:
                                              BorderRadius.circular(25)),
                                      child: CustomText(
                                        item["title"],
                                        size: 1.9,
                                        color: item["isChecked"]
                                            ? Colors.black
                                            : const Color(greyText),
                                      ),
                                    ),
                                  ))
                              .toList())
                    ],
                    if (c.somethingElse &&
                        c.hostRating <= 3 &&
                        c.hostRating != 0) ...[
                      SizedBox(height: heightSpace(3)),
                      CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .moreDetails,
                        size: 1.9,
                        color: const Color(greyText),
                      ),
                      SizedBox(height: heightSpace(1.5)),
                      CustomTextField(
                          maxlines: 5,
                          hint: Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .details,
                          controller: c.somethingElseController,
                          validator: (val) {
                            return val.isNotEmpty
                                ? null
                                : Get.find<TranslationHelper>()
                                    .translations
                                    .jqueryValidation
                                    .required;
                          },
                          onChanged: (value) {
                            c.update(['rate']);
                          },
                          inputType: TextInputType.emailAddress,
                          isRoundedBorder: true),
                    ],
                    if (c.hostRating == 1) ...[
                      SizedBox(height: heightSpace(3)),
                      CommonCheckBox(
                        onPressed: () {
                          if (c.blockUser) {
                            c.blockUser = !c.blockUser;
                            c.update(["rate"]);
                          } else {
                            showBlockSheet(c);
                          }
                        },
                        isSelected: c.blockUser,
                        title: Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .blockUser,
                      ),
                      SizedBox(height: heightSpace(1.5)),
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                            color: Colors.redAccent.withOpacity(.1),
                            borderRadius: BorderRadius.circular(10)),
                        child: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostReview
                              .blockInstruction,
                          size: 2.2,
                          color: Colors.red,
                          textAlign: TextAlign.start,
                        ),
                      )
                    ],
                  ],
                ),
              ),
              bottomNavigationBar: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  stepper(3, total: 8),
                  Container(
                    height: heightSpace(10),
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(
                        horizontal: widthSpace(viewPadding)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                          onPressed: () => Get.back(),
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingDescription
                              .back,
                          isBorder: true,
                        ),
                        CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingBasic
                              .next,
                          horizontalPadding: 7,
                          backgroundBg: (c.hostRating <= 3 &&
                                      c.defaultReviews
                                          .any((e) => e['isChecked']) &&
                                      c.hostRating != 0 &&
                                      !c.somethingElse) ||
                                  (c.hostRating < 3 &&
                                      c.defaultReviews
                                          .any((e) => e['isChecked']) &&
                                      c.hostRating != 0 &&
                                      c.somethingElse &&
                                      c.somethingElseController.text
                                          .isNotEmpty) ||
                                  c.hostRating > 3 && c.hostRating != 0
                              ? Colors.black.withOpacity(0.88)
                              : Colors.grey.withOpacity(0.5),
                          onPressed: () {
                            if ((c.hostRating <= 3 &&
                                    c.defaultReviews
                                        .any((e) => e['isChecked']) &&
                                    c.hostRating != 0 &&
                                    !c.somethingElse) ||
                                (c.hostRating < 3 &&
                                    c.defaultReviews
                                        .any((e) => e['isChecked']) &&
                                    c.hostRating != 0 &&
                                    c.somethingElse &&
                                    c.somethingElseController.text
                                        .isNotEmpty) ||
                                c.hostRating > 3 && c.hostRating != 0) {
                              c.evaluateRating = 0.0;
                              c.somethingElseCommunicate = false;
                              bool containsTrue = c.defaultCommunication
                                  .any((e) => e['isChecked']);
                              if (containsTrue) {
                                c.defaultCommunication.value =
                                    c.defaultCommunication.map((e) {
                                  if (e['isChecked'] == true) {
                                    e['isChecked'] = false;
                                  }
                                  return e;
                                }).toList();
                              }
                              c.defaultCommunication.refresh();
                              c.somethingElseCommController.clear();
                              c.update(['evaluate']);
                              Get.to(() => const EvaluateGuestCommunication());
                            }
                          },
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ));
  }

  showBlockSheet(HostDashboardController c) {
    ViewsCommon.showModalBottom(BottomSheet(
        builder: (BuildContext context) {
          return Container(
            padding: EdgeInsets.all(widthSpace(viewPadding)),
            height: heightSpace(30),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Align(
                    alignment: Alignment.center,
                    child: CustomText(
                        Get.find<TranslationHelper>().translations.footer.alert,
                        size: 3.2),
                  ),
                  SizedBox(height: heightSpace(1.5)),
                  Align(
                    alignment: Alignment.center,
                    child: CustomText(
                      "Are you sure you want to block ${c.guestData.value!.userName}?",
                      size: 2.2,
                      color: Colors.black,
                    ),
                  ),
                  SizedBox(height: heightSpace(1.5)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: CommonButton(
                          onPressed: () => Get.back(),
                          title: Get.find<TranslationHelper>()
                              .translations
                              .general
                              .no,
                          isBorder: true,
                        ),
                      ),
                      SizedBox(width: widthSpace(1.5)),
                      Expanded(
                        child: CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .general
                              .yes,
                          backgroundBg: Colors.black.withOpacity(0.88),
                          onPressed: () {
                            c.blockUser = !c.blockUser;
                            c.update(["rate"]);
                            Get.back();
                          },
                        ),
                      )
                    ],
                  ),
                ]),
          );
        },
        onClosing: () {}));
  }
}
