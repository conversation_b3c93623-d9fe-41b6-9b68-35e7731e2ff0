import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/screens/host/host_review/over_all_review.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../components/common_button.dart';
import '../../../controllers/hostDashboard_controller.dart';
import '../../../helperMethods/translation_helper.dart';
import '../../../utils/sizeconfig.dart';

class EvaluateGuestCommunication extends StatelessWidget {
  const EvaluateGuestCommunication({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HostDashboardController>(
        id: "evaluate",
        builder: (c) => Scaffold(
              resizeToAvoidBottomInset: true,
              appBar: AppBar(
                elevation: 0,
                leading: IconButton(
                    onPressed: Get.back,
                    icon: const Icon(Icons.chevron_left, size: 40)),
              ),
              body: SingleChildScrollView(
                padding: EdgeInsets.all(widthSpace(viewPadding)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SvgPicture.asset("assets/message.svg",
                        height: 75, width: 75),
                    SizedBox(height: heightSpace(4.5)),
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .communicationTitle
                            .replaceAll(
                                ":customer_name", c.guestData.value!.userName!),
                        size: 3.2),
                    SizedBox(height: heightSpace(3)),
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostReview
                          .subtitle
                          .replaceAll(
                              ":customer_name", c.guestData.value!.userName!),
                      size: 2.2,
                      color: Colors.black,
                    ),
                    SizedBox(height: heightSpace(1.5)),
                    RatingBar.builder(
                      initialRating: 0,
                      minRating: 0,
                      direction: Axis.horizontal,
                      allowHalfRating: false,
                      unratedColor: Colors.grey.withOpacity(.5),
                      itemCount: 5,
                      itemSize: widthSpace(10),
                      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                      itemBuilder: (context, _) => const Icon(
                        Icons.star,
                        color: Colors.black,
                      ),
                      onRatingUpdate: (rating) {
                        c.evaluateRating = rating;
                        if (c.evaluateRating > 3) {
                          c.somethingElseCommunicate = false;
                          bool containsTrue =
                              c.defaultCommunication.any((e) => e['isChecked']);
                          if (containsTrue) {
                            c.defaultCommunication.value =
                                c.defaultCommunication.map((e) {
                              if (e['isChecked'] == true) {
                                e['isChecked'] = false;
                              }
                              return e;
                            }).toList();
                          }
                          c.defaultCommunication.refresh();
                          c.somethingElseCommController.clear();
                        }
                        c.update(["evaluate"]);
                      },
                      updateOnDrag: true,
                    ),
                    SizedBox(height: heightSpace(1.5)),
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostReview
                          .selectRating,
                      size: 2.2,
                      color: Colors.black,
                    ),
                    SizedBox(height: heightSpace(1.5)),
                    const Divider(),
                    if (c.evaluateRating <= 3 && c.evaluateRating != 0) ...[
                      SizedBox(height: heightSpace(1.5)),
                      CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .whatHappend,
                        size: 2.2,
                        color: Colors.black,
                      ),
                      SizedBox(height: heightSpace(1.5)),
                      CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .selectOption,
                        size: 1.9,
                        color: const Color(greyText),
                      ),
                      SizedBox(height: heightSpace(1.5)),
                      Wrap(
                          runSpacing: 10,
                          spacing: 10,
                          runAlignment: WrapAlignment.spaceBetween,
                          direction: Axis.horizontal,
                          alignment: WrapAlignment.spaceBetween,
                          children: c.defaultCommunication
                              .map<Widget>((item) => GestureDetector(
                                    onTap: () {
                                      int index = c.defaultCommunication
                                          .indexWhere((e) => e == item);
                                      if (item["title"] ==
                                          Get.find<TranslationHelper>()
                                              .translations
                                              .hostReview
                                              .communicationOption5) {
                                        c.somethingElseCommunicate =
                                            !c.somethingElseCommunicate;
                                      }
                                      c.defaultCommunication[index]
                                              ["isChecked"] =
                                          !c.defaultCommunication[index]
                                              ["isChecked"];
                                      c.defaultCommunication.refresh();
                                      c.update(["evaluate"]);
                                    },
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: widthSpace(5),
                                          vertical: heightSpace(1)),
                                      decoration: BoxDecoration(
                                          border: Border.all(
                                              color: item["isChecked"]
                                                  ? Colors.black
                                                  : const Color(greyBorder)),
                                          borderRadius:
                                              BorderRadius.circular(25)),
                                      child: CustomText(
                                        item["title"],
                                        size: 1.9,
                                        color: item["isChecked"]
                                            ? Colors.black
                                            : const Color(greyText),
                                      ),
                                    ),
                                  ))
                              .toList())
                    ],
                    if (c.somethingElseCommunicate &&
                        c.evaluateRating <= 3 &&
                        c.evaluateRating != 0) ...[
                      SizedBox(height: heightSpace(3)),
                      CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .moreDetails,
                        size: 1.9,
                        color: const Color(greyText),
                      ),
                      SizedBox(height: heightSpace(1.5)),
                      CustomTextField(
                          maxlines: 5,
                          hint: Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .details,
                          controller: c.somethingElseCommController,
                          validator: (val) {
                            return val.isNotEmpty
                                ? null
                                : Get.find<TranslationHelper>()
                                    .translations
                                    .jqueryValidation
                                    .required;
                          },
                          onChanged: (value) {
                            c.update(['evaluate']);
                          },
                          inputType: TextInputType.emailAddress,
                          isRoundedBorder: true),
                    ],
                  ],
                ),
              ),
              bottomNavigationBar: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  stepper(4, total: 8),
                  Container(
                    height: heightSpace(10),
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(
                        horizontal: widthSpace(viewPadding)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                          onPressed: () => Get.back(),
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingDescription
                              .back,
                          isBorder: true,
                        ),
                        CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingBasic
                              .next,
                          horizontalPadding: 7,
                          backgroundBg: (c.evaluateRating <= 3 &&
                                      c.defaultCommunication
                                          .any((e) => e['isChecked']) &&
                                      c.evaluateRating != 0 &&
                                      !c.somethingElseCommunicate) ||
                                  (c.evaluateRating < 3 &&
                                      c.defaultCommunication
                                          .any((e) => e['isChecked']) &&
                                      c.evaluateRating != 0 &&
                                      c.somethingElseCommunicate &&
                                      c.somethingElseCommController.text
                                          .isNotEmpty) ||
                                  c.evaluateRating > 3 && c.evaluateRating != 0
                              ? Colors.black.withOpacity(0.88)
                              : Colors.grey.withOpacity(0.5),
                          onPressed: () {
                            if ((c.evaluateRating <= 3 &&
                                    c.defaultCommunication
                                        .any((e) => e['isChecked']) &&
                                    c.evaluateRating != 0 &&
                                    !c.somethingElseCommunicate) ||
                                (c.evaluateRating < 3 &&
                                    c.defaultCommunication
                                        .any((e) => e['isChecked']) &&
                                    c.evaluateRating != 0 &&
                                    c.somethingElseCommunicate &&
                                    c.somethingElseCommController.text
                                        .isNotEmpty) ||
                                c.evaluateRating > 3 && c.evaluateRating != 0) {
                              c.overallRatingController.clear();
                              Get.to(() => const OverAllRating());
                            }
                          },
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ));
  }
}
