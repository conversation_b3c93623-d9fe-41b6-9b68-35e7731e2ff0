import 'package:darent/screens/host/host_review/private_note.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../components/common_button.dart';
import '../../../components/custom_text.dart';
import '../../../controllers/hostDashboard_controller.dart';
import '../../../helperMethods/translation_helper.dart';
import '../../../utils/constants.dart';
import '../../../utils/sizeconfig.dart';
import '../../listing_journey/stepper.dart';

class OverAllReviewInstruction extends StatelessWidget {
  const OverAllReviewInstruction({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HostDashboardController>(
        id: "overallInstruct",
        builder: (c) => Scaffold(
              resizeToAvoidBottomInset: true,
              appBar: AppBar(
                elevation: 0,
                leading: IconButton(
                    onPressed: Get.back,
                    icon: const Icon(Icons.chevron_left, size: 40)),
              ),
              body: SingleChildScrollView(
                padding: EdgeInsets.all(widthSpace(viewPadding)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SvgPicture.asset("assets/thumb.svg", height: 75, width: 75),
                    SizedBox(height: heightSpace(4.5)),
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .overallTitle,
                        size: 3.2),
                    SizedBox(height: heightSpace(3)),
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostReview
                          .overallInstruction
                          .replaceAll(
                              ":customer_name", c.guestData.value!.userName!),
                      size: 2.2,
                      color: Colors.black,
                    ),
                    SizedBox(height: heightSpace(3)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CommonButton(
                          onPressed: () {
                            c.selection = "Yes";
                            c.update(['overallInstruct']);
                          },
                          title: Get.find<TranslationHelper>()
                              .translations
                              .general
                              .yes,
                          backgroundBg: c.selection == "Yes"
                              ? Colors.black.withOpacity(0.88)
                              : Colors.grey.withOpacity(0.5),
                        ),
                        SizedBox(
                          width: widthSpace(2),
                        ),
                        CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .general
                              .no,
                          backgroundBg: c.selection == "No"
                              ? Colors.black.withOpacity(0.88)
                              : Colors.grey.withOpacity(0.5),
                          onPressed: () {
                            c.selection = "No";
                            c.update(['overallInstruct']);
                          },
                        )
                      ],
                    ),
                  ],
                ),
              ),
              bottomNavigationBar: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  stepper(6, total: 8),
                  Container(
                    height: heightSpace(10),
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(
                        horizontal: widthSpace(viewPadding)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                          onPressed: () => Get.back(),
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingDescription
                              .back,
                          isBorder: true,
                        ),
                        CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingBasic
                              .next,
                          horizontalPadding: 7,
                          backgroundBg:
                              c.overallRatingController.text.isNotEmpty
                                  ? Colors.black.withOpacity(0.88)
                                  : Colors.grey.withOpacity(0.5),
                          onPressed: () {
                            if (c.overallRatingController.text.isNotEmpty) {
                              c.privateNoteController.clear();
                              Get.to(() => const PrivateNoteScreen());
                            }
                          },
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ));
  }
}
