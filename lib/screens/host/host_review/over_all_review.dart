import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/screens/host/host_review/over_all_instruction.dart';
import 'package:darent/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../components/common_button.dart';
import '../../../components/custom_text.dart';
import '../../../components/custom_textfield.dart';
import '../../../helperMethods/translation_helper.dart';
import '../../../utils/sizeconfig.dart';
import '../../listing_journey/stepper.dart';

class OverAllRating extends StatelessWidget {
  const OverAllRating({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HostDashboardController>(
        id: "overall",
        builder: (c) => Scaffold(
              resizeToAvoidBottomInset: true,
              appBar: AppBar(
                elevation: 0,
                leading: IconButton(
                    onPressed: Get.back,
                    icon: const Icon(Icons.chevron_left, size: 40)),
              ),
              body: SingleChildScrollView(
                padding: EdgeInsets.all(widthSpace(viewPadding)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SvgPicture.asset("assets/pencil.svg",
                        height: 75, width: 75),
                    SizedBox(height: heightSpace(4.5)),
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .overallTitle,
                        size: 3.2),
                    SizedBox(height: heightSpace(3)),
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostReview
                          .subtitle
                          .replaceAll(
                              ":customer_name", c.guestData.value!.userName!),
                      size: 2.2,
                      color: Colors.black,
                    ),
                    SizedBox(height: heightSpace(3)),
                    CustomTextField(
                        maxlines: 7,
                        maxLength: 1000,
                        hint: Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .writeReview,
                        controller: c.overallRatingController,
                        validator: (val) {
                          return val.trim().isNotEmpty
                              ? null
                              : Get.find<TranslationHelper>()
                                  .translations
                                  .jqueryValidation
                                  .required;
                        },
                        onChanged: (value) {
                          c.update(['overall']);
                        },
                        inputType: TextInputType.emailAddress,
                        isRoundedBorder: true),
                  ],
                ),
              ),
              bottomNavigationBar: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  stepper(5, total: 8),
                  Container(
                    height: heightSpace(10),
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(
                        horizontal: widthSpace(viewPadding)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                          onPressed: Get.back,
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingDescription
                              .back,
                          isBorder: true,
                        ),
                        CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingBasic
                              .next,
                          horizontalPadding: 7,
                          isDisabled:
                              c.overallRatingController.text.trim().length < 20,
                          onPressed: () {
                            Get.to(() => const OverAllReviewInstruction());
                          },
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ));
  }
}
