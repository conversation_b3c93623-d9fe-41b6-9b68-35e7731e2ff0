import 'package:darent/components/common_button.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/screens/host/host_review/rate_review_guest.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../../components/custom_text.dart';
import '../../../helperMethods/translation_helper.dart';

class ReviewInstructions extends StatelessWidget {
  const ReviewInstructions({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HostDashboardController>(
        id: "detail",
        builder: (c) => Scaffold(
              resizeToAvoidBottomInset: true,
              appBar: AppBar(
                elevation: 0,
                leading: IconButton(
                    onPressed: Get.back,
                    icon: const Icon(Icons.chevron_left, size: 40)),
              ),
              body: SingleChildScrollView(
                padding: EdgeInsets.all(widthSpace(viewPadding)),
                child: Column(
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        c.guestData.value?.profile?.contains("svg") ?? false
                            ? GlobalHelper.buildNetworkSvgWidget(
                          url: c.guestData.value?.profile ??"",
                          height: 75, width: 75,
                          defaultOption: const Icon(Icons.person,
                            size:48,
                          ),)
                            : CircleAvatar(
                                backgroundImage: GlobalHelper.buildNetworkImageProvider(url: c.guestData.value!.profile ??''),
                              )
                      ],
                    ),
                    SizedBox(height: heightSpace(4.5)),
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .writeGuestReview
                            .replaceAll(
                                ":customer_name", c.guestData.value!.userName!),
                        size: 3.2),
                    SizedBox(height: heightSpace(3)),
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostReview
                          .guestReviewInstruction
                          .replaceAll(
                              ":customer_name", c.guestData.value!.userName!),
                      size: 1.9,
                      color: const Color(greyText),
                    ),
                  ],
                ),
              ),
              bottomNavigationBar: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  stepper(2, total: 8),
                  Padding(
                    padding: EdgeInsets.all(widthSpace(viewPadding)),
                    child: SizedBox(
                      height: heightSpace(7),
                      width: double.infinity,
                      child: CommonButton(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .listing
                            .start,
                        onPressed: () {
                          c.blockUser = false;
                          c.hostRating = 0.0;
                          c.somethingElse = false;
                          bool containsTrue =
                              c.defaultReviews.any((e) => e['isChecked']);
                          if (containsTrue) {
                            c.defaultReviews.value = c.defaultReviews.map((e) {
                              if (e['isChecked'] == true) {
                                e['isChecked'] = false;
                              }
                              return e;
                            }).toList();
                          }
                          c.defaultReviews.refresh();
                          c.somethingElseController.clear();
                          c.update(['rate']);
                          Get.to(() => const RateAndReviewScreen());
                        },
                        backgroundBg: Colors.black.withOpacity(0.88),
                        buttonThemeColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ));
  }
}
