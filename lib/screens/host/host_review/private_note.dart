import 'package:darent/components/common_button.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../components/custom_text.dart';
import '../../../components/custom_textfield.dart';
import '../../../helperMethods/translation_helper.dart';

class PrivateNoteScreen extends StatelessWidget {
  const PrivateNoteScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HostDashboardController>(
        id: "private",
        builder: (c) => Scaffold(
              resizeToAvoidBottomInset: true,
              appBar: AppBar(
                elevation: 0,
                leading: IconButton(
                    onPressed: Get.back,
                    icon: const Icon(Icons.chevron_left, size: 40)),
              ),
              body: SingleChildScrollView(
                padding: EdgeInsets.all(widthSpace(viewPadding)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SvgPicture.asset("assets/note.svg", height: 75, width: 75),
                    SizedBox(height: heightSpace(4.5)),
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .privateTitle,
                        size: 3.2),
                    SizedBox(height: heightSpace(3)),
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostReview
                          .privateSubtitle,
                      size: 2.2,
                      color: Colors.black,
                    ),
                    SizedBox(height: heightSpace(3)),
                    CustomTextField(
                        maxlines: 7,
                        maxLength: 1000,
                        hint: Get.find<TranslationHelper>()
                            .translations
                            .hostReview
                            .writePrivateMessage,
                        controller: c.privateNoteController,
                        onChanged: (value) {
                          c.update(['private']);
                        },
                        inputType: TextInputType.emailAddress,
                        isRoundedBorder: true),
                  ],
                ),
              ),
              bottomNavigationBar: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  stepper(7, total: 8),
                  Container(
                    height: heightSpace(10),
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(
                        horizontal: widthSpace(viewPadding)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                          onPressed: () => Get.back(),
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingDescription
                              .back,
                          isBorder: true,
                        ),
                        CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingCalendar
                              .submit,
                          horizontalPadding: 7,
                          isLoading: c.isReviewRequest,
                          isDisabled: c.privateNoteController.text
                                  .trim()
                                  .isNotEmpty &&
                              c.privateNoteController.text.trim().length < 2,
                          backgroundBg: Colors.black.withOpacity(0.88),
                          onPressed: c.submitHostReview,
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ));
  }
}
