import 'dart:io';
import 'package:darent/components/common_button.dart';
import 'package:darent/components/common_checkbox.dart';
import 'package:darent/components/custom_image_picker_sheet.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../helperMethods/remote_config.dart';
import '../helperMethods/translation_helper.dart';
import '../helperMethods/authHelper.dart';

class AccountInfo extends StatelessWidget {
  const AccountInfo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            leading: IconButton(
                onPressed: () {
                  AuthHelper.c.clearFields();
                  Get.until((route) => route.isFirst);
                },
                icon: const Icon(Icons.chevron_left)),
            title: CustomText(
                Get.find<TranslationHelper>().translations.sidenav.editProfile),
            actions: [
              IconButton(
                  onPressed: AuthHelper.c.deleteAccount,
                  icon: const Icon(Icons.delete_outlined,
                      color: Color(warningColor)))
            ]),
        body: PopScope(
          onPopInvoked: (pop) async {
            if (pop) {
              AuthHelper.c.clearFields();
            }
          },
          child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                  horizontal: widthSpace(viewPadding),
                  vertical: heightSpace(3)),
              child: Form(
                  key: AuthHelper.c.profileLoginKey,
                  child: Obx(
                    () => Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          GestureDetector(
                            onTap: () {
                              imagePickerModes((ImageSource source) {
                                AuthHelper.c.pickProfileImage(source);
                                Get.back();
                              });
                            },
                            child: Align(
                              alignment: Alignment.center,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                          color: Colors.grey[200],
                                          border: Border.all(
                                              color: Colors.black, width: 1),
                                          shape: BoxShape.circle),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(45),
                                        child: AuthHelper
                                                .c.isProfileUploading.value
                                            ? const Padding(
                                                padding: EdgeInsets.all(30),
                                                child:
                                                    CircularProgressIndicator(
                                                        strokeWidth: 2),
                                              )
                                            : (userModel.value?.profile_image ?? "").contains(".svg")
                                                ? GlobalHelper.buildNetworkSvgWidget(
                                          url: userModel.value?.profile_image ??"",
                                          height: 48,
                                          width: 48,
                                          defaultOption: const Icon(Icons.person,
                                            size:48,
                                          ),)
                                                : AuthHelper.c.pickedImagePath
                                                            .value !=
                                                        null
                                                    ? Image.file(
                                                        File(AuthHelper
                                                            .c
                                                            .pickedImagePath
                                                            .value!),
                                                        fit: BoxFit.fill,
                                                        width: 90,
                                                        height: 90,
                                                      )
                                            : GlobalHelper.resolveImageUrl(userModel.value?.profile_image??"").isNotEmpty
                                            ? Image(
                                               image: GlobalHelper.buildNetworkImageProvider(url: userModel.value?.profile_image??"",),
                                                        width: 90,
                                                        height: 90,
                                                        fit: BoxFit.fill,
                                        )
                                            : Padding(
                                          padding:
                                          const EdgeInsets
                                              .all(30),
                                          child: Icon(
                                              Icons.person,
                                              size:
                                              widthSpace(
                                                  14)),
                                        ),
                                      )),
                                  SizedBox(height: heightSpace(0.5)),
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .header
                                          .editProfile,
                                      underline: true,
                                      size: 2.4,
                                      weight: FontWeight.w500),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(height: heightSpace(3)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .firstName,
                              color: const Color(greyText),
                              size: 2.1,
                              weight: FontWeight.w500),
                          const SizedBox(height: 5),
                          CustomTextField(
                              //isEnabled: AuthHelper.c.firstNameEnabled.value,
                              controller: AuthHelper.c.firstName,
                              textCapitalization: TextCapitalization.words,
                              validator: (String? val) => val!.isEmpty
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .jqueryValidation
                                      .required
                                  : null),
                          SizedBox(height: heightSpace(3)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .lastName,
                              color: const Color(greyText),
                              size: 2.1,
                              weight: FontWeight.w500),
                          const SizedBox(height: 5),
                          CustomTextField(
                              //isEnabled: AuthHelper.c.lastNameEnabled.value,
                              textCapitalization: TextCapitalization.words,
                              controller: AuthHelper.c.lastName,
                              validator: (String? val) => val!.isEmpty
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .jqueryValidation
                                      .required
                                  : null),
                          SizedBox(height: heightSpace(3)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .gender,
                              color: const Color(greyText),
                              size: 2.1,
                              weight: FontWeight.w500),
                          SizedBox(height: heightSpace(2)),
                          CommonCheckBox(
                              onPressed: () =>
                                  AuthHelper.c.selectGender("male"),
                              isSelected: userModel.value?.gender == "male",
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .male),
                          SizedBox(height: heightSpace(2)),
                          CommonCheckBox(
                              onPressed: () =>
                                  AuthHelper.c.selectGender("female"),
                              isSelected: userModel.value?.gender == "female",
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .female),
                          Divider(height: heightSpace(5)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .birthDate,
                              color: const Color(greyText),
                              size: 2.1,
                              weight: FontWeight.w500),
                          Container(
                              width: double.maxFinite,
                              margin: AuthHelper.c.dobError.value
                                  ? const EdgeInsets.only(bottom: 8)
                                  : null,
                              padding: EdgeInsets.only(
                                  bottom: heightSpace(2),
                                  top: heightSpace(1.7)),
                              decoration: BoxDecoration(
                                  border: Border(
                                      bottom: BorderSide(
                                          color: AuthHelper.c.dobError.value
                                              ? Colors.red[700]!
                                              : const Color.fromARGB(
                                                  255, 230, 230, 230)))),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  CustomText(
                                      AuthHelper.c.dob.value == null
                                          ? Get.find<TranslationHelper>()
                                              .translations
                                              .usersProfile
                                              .genderNotSet
                                          : formDateFormat
                                              .format(AuthHelper.c.dob.value!),
                                      size: 2.1,
                                      color: AuthHelper.c.dob.value == null
                                          ? const Color(greyText)
                                          : null,
                                      weight: FontWeight.w500),
                                  InkWell(
                                      onTap: AuthHelper.c.selectDob,
                                      child: const Icon(
                                          Icons.calendar_month_outlined))
                                ],
                              )),
                          if (AuthHelper.c.dobError.value)
                            CustomText(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .jqueryValidation
                                    .required,
                                color: Colors.red[700],
                                size: 1.55),
                          SizedBox(height: heightSpace(3)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .whereLive,
                              color: const Color(greyText),
                              size: 2.1,
                              weight: FontWeight.w500),
                          const SizedBox(height: 5),
                          CustomTextField(
                              controller: AuthHelper.c.liveC,
                              hint: Get.find<TranslationHelper>()
                                  .translations
                                  .photoDetails
                                  .location,
                              validator: (String? val) => val!.isEmpty
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .jqueryValidation
                                      .required
                                  : null),
                          SizedBox(height: heightSpace(3)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .aboutMe,
                              color: const Color(greyText),
                              size: 2.1,
                              weight: FontWeight.w500),
                          const SizedBox(height: 5),
                          CustomTextField(
                              controller: AuthHelper.c.about,
                              hint: "fillThisField".tr,
                              validator: (String? val) => val!.isEmpty
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .jqueryValidation
                                      .required
                                  : null),
                          SizedBox(height: heightSpace(3)),
                          Align(
                              alignment: Alignment.centerRight,
                              child: CommonButton(
                                  title: Get.find<TranslationHelper>()
                                      .translations
                                      .utility
                                      .saveChanges,
                                  onPressed: AuthHelper.c.updateProfileApi,
                                  isLoading: AuthHelper.c.isLoading.isTrue)),
                          // CustomText(Get.find<TranslationHelper>().translations.home.chooseLanguage,color: const Color(greyText), size: 2.1, weight: FontWeight.w500),
                          // SizedBox(height: heightSpace(2)),
                          // Obx(()=>Row(
                          //       children:  [
                          //       CommonCheckBox(
                          //             onPressed: AuthHelper.c.isLoadingGoogle.value?null:() => AuthHelper.c.selectLanguage("en"),
                          //             isSelected: Get.locale?.languageCode == "en",
                          //             title: "English"),
                          //             SizedBox(width: widthSpace(20)),
                          //             CommonCheckBox(
                          //             onPressed: AuthHelper.c.isLoadingGoogle.value?null:() => AuthHelper.c.selectLanguage("ar"),
                          //             isSelected: Get.locale?.languageCode == "ar",
                          //             title: "arabic".tr),
                          //             ],
                          //   ),
                          // ),
                          // if(false)...[
                          //   SizedBox(height: heightSpace(2)),
                          //   CustomText(Get.find<TranslationHelper>().translations.home.chooseCurrency,color: const Color(greyText), size: 2.1, weight: FontWeight.w500),
                          //   SizedBox(height: heightSpace(2)),
                          //   Obx(()=>
                          //       Row(
                          //           children:  [
                          //             CommonCheckBox(
                          //                 onPressed: () => AuthHelper.c.selectCurrency("sar"),
                          //                 isSelected: AuthHelper.c.selectedCurrency.value == "sar",
                          //                 title: "SAR"),
                          //             SizedBox(width: widthSpace(26)),
                          //             CommonCheckBox(
                          //                 onPressed: () => AuthHelper.c.selectCurrency("usd"),
                          //                 isSelected: AuthHelper.c.selectedCurrency.value == "usd",
                          //                 title: "USD")])),
                          // ]
                        ]),
                  ))),
        ));
  }
}
