import 'package:darent/components/views_common.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';

import '../../components/custom_text.dart';
import '../../utils/constants.dart';
import 'package:get/get.dart';

class LatestTranfers extends StatelessWidget {
  const LatestTranfers({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(icon:Icon(Icons.chevron_left,color: Color(lightBlack)),onPressed: Get.back),
        title: Text('Latest Transfers'),
      ),
      body: Column(children: [
        TextField(
          decoration:InputDecoration(
              hintText:'Search with the reservation number',
              contentPadding: const EdgeInsets.only(left:20, right: 20),
              prefixIcon: const Icon(Icons.search),
              filled: true,
              fillColor: Colors.grey[100],
              border: UnderlineInputBorder(borderRadius: BorderRadius.circular(8),borderSide: BorderSide.none),
              enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(8),borderSide: BorderSide.none)),
        ),
       Expanded(
         child: ListView.separated(
           padding: EdgeInsets.only(top: widthSpace(viewPadding)),
             itemBuilder: (context, index) => Container(
               decoration: BoxDecoration(
                 color: Colors.white,
                 borderRadius: BorderRadius.circular(10),
                 border: Border.all(color:Color(greyBorder)),
                 boxShadow: ViewsCommon.boxShadow
               ),child: Column(children: [
                 Row(
                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                     crossAxisAlignment: CrossAxisAlignment.start,
                     children: [
                  Column(children: [
                    CustomText('Transfer number',color: Color(greyText)),
                    SizedBox(height: heightSpace(.5)),
                    CustomText('TR-6004928579',size: 2.3,weight: FontWeight.bold)
                  ]).paddingAll(widthSpace(3)),
                  Container(
                    padding: EdgeInsets.all(widthSpace(4)),
                    width: widthSpace(32),
                    decoration: BoxDecoration(
                        color:Colors.lightGreen[100],
                        borderRadius: BorderRadius.only(bottomLeft: Radius.circular(10),topRight: Radius.circular(10)),
                    ),child: CustomText('Transfer has been done',color: Colors.green,weight: FontWeight.bold,textAlign: TextAlign.center),
                  )
                 ]),
               SizedBox(height: heightSpace(3)),
               Column(children: [
                 row('Date of implementation','March 05, 2025'),
                 SizedBox(height: heightSpace(1.5)),
                 row('Date of creation','March 03, 2025'),
                 SizedBox(height: heightSpace(1.5)),
                 row('Method of conversion','Bank transfer'),
                 SizedBox(height: heightSpace(1.5)),
                 row('IBAN number','***************'),
                 SizedBox(height: heightSpace(1.5)),
                 row('Number of reservations','One reservation',isUnderLine:true),
               ]).paddingAll(widthSpace(3)),
               Divider(),
               Row(
                   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                   children: [
                     CustomText('Amount of transfer',weight: FontWeight.w500),
                     CustomText('250.76 Riyal',weight: FontWeight.bold,size: 2.1),
                   ]).paddingAll(widthSpace(3))
             ]),
             ),
             separatorBuilder:(c,i) => SizedBox(height:heightSpace(3)), itemCount: 10),
       )
      ]).paddingAll(widthSpace(viewPadding)),
    );
  }
  row(String text1, String text2,{bool isUnderLine=false}){
    return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      CustomText(text1,weight: FontWeight.w500),
      CustomText(text2,color: isUnderLine?null:Colors.black54,weight: isUnderLine?FontWeight.bold:null),
    ]);
  }
}
