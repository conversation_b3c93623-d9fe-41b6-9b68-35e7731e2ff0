import 'package:darent/helperMethods/authHelper.dart';

import '../../components/custom_text.dart';
import '../../components/views_common.dart';
import '../../helperMethods/translation_helper.dart';
import '../../utils/constants.dart';
import '../../utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Translation extends StatelessWidget {
  const Translation({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          elevation: 0,
          leading: IconButton(
              onPressed: Get.back, icon: const Icon(Icons.chevron_left))),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .translation,
              size: 2.5,
              weight: FontWeight.w500),
          SizedBox(height: heightSpace(3)),
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .translateNote,
              color: const Color(greyText),
              size: 2),
          SizedBox(height: heightSpace(3)),
          ViewsCommon.switchButton(Get.locale?.languageCode ?? 'en',
              toogle: Get.locale?.languageCode == 'ar',
              onConfirm: () => AuthHelper.c.selectLanguage(
                  Get.locale?.languageCode == 'ar' ? 'en' : 'ar'),
              fontSize: 2.15)
        ],
      ).paddingSymmetric(
          horizontal: widthSpace(viewPadding), vertical: heightSpace(3)),
    );
  }
}
