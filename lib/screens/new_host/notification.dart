import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/hostDashboard_controller.dart';
import '../../helperMethods/translation_helper.dart';

class Notifications extends StatelessWidget {
  const Notifications({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
            onPressed: Get.back,
            icon: const Icon(
              Icons.chevron_left,
              size: 40,
            )),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(3.5)),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .notification
                      .notification,
                  size: 3.2),
              SizedBox(height: heightSpace(3.5)),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    InkWell(
                        child: Container(
                          padding: EdgeInsets.only(
                              top: heightSpace(1), bottom: heightSpace(2)),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: c.notificatons.value == "offer"
                                    ? Colors.black
                                    : Colors.grey,
                                width: c.notificatons.value == "offer" ? 3 : 2,
                              ),
                            ),
                          ),
                          child: CustomText(
                            "Offers And Updates",
                            size: 2.1,
                            color: c.notificatons.value == "offer"
                                ? Colors.black
                                : Colors.grey,
                            weight: FontWeight.w500,
                          ),
                        ),
                        onTap: () {
                          c.setNotificationTab("offer");
                        }),
                    Container(
                      width: widthSpace(8),
                      decoration: const BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Colors.grey,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                    InkWell(
                        child: Container(
                          padding: EdgeInsets.only(
                              top: heightSpace(0.3), bottom: heightSpace(2)),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: c.notificatons.value == "account"
                                    ? Colors.black
                                    : Colors.grey,
                                width:
                                    c.notificatons.value == "account" ? 3 : 2,
                              ),
                            ),
                          ),
                          child: CustomText(
                            "Account",
                            size: 2.1,
                            color: c.notificatons.value == "account"
                                ? Colors.black
                                : Colors.grey,
                            weight: FontWeight.w500,
                          ),
                        ),
                        onTap: () {
                          c.setNotificationTab("account");
                        }),
                    Container(
                      width: widthSpace(100),
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Colors.grey,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              c.notificatons.value == "offer"
                  ? Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: heightSpace(2),
                        ),
                        const CustomText(
                          "Hosting Insights And Rewards",
                          size: 2.3,
                          weight: FontWeight.w500,
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "Learn about best hosting practices, and get access to exclusive hosting perks.",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const CustomText(
                              "Recognition and achievements",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Recognition And Achievements",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Stand out by reaching hosting milestones and Superhost status",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Email",
                                                          onConfirm: () {
                                                        c.emailRecog.toggle();
                                                      },
                                                          toogle: c.emailRecog
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: () {
                                                        c.notificationRecog
                                                            .toggle();
                                                      },
                                                          toogle: c
                                                              .notificationRecog
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS", onConfirm: () {
                                                        c.smsRecog.toggle();
                                                      },
                                                          toogle:
                                                              c.smsRecog.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Phone call",
                                                          onConfirm: () {
                                                        c.callRecog.toggle();
                                                      },
                                                          toogle: c
                                                              .callRecog.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "Insights and tips",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Insights And Tips",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Reaching the goal using advice and ideas supported by data",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Email",
                                                          onConfirm: c
                                                              .emailInsig
                                                              .toggle,
                                                          toogle: c.emailInsig
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: c
                                                              .notificationInsig
                                                              .toggle,
                                                          toogle: c
                                                              .notificationInsig
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS",
                                                          onConfirm:
                                                              c.smsInsig.toggle,
                                                          toogle:
                                                              c.smsInsig.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Phone call",
                                                          onConfirm: c
                                                              .callInsig.toggle,
                                                          toogle: c
                                                              .callInsig.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "Pricing trends and suggestions",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Pricing Trends And Suggestions",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Improve your price with tips and insights backed by data",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "E-mail",
                                                          onConfirm: c
                                                              .emailPrice
                                                              .toggle,
                                                          toogle: c.emailPrice
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: c
                                                              .notificationPrice
                                                              .toggle,
                                                          toogle: c
                                                              .notificationPrice
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS",
                                                          onConfirm:
                                                              c.smsPrice.toggle,
                                                          toogle:
                                                              c.smsPrice.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Phone call",
                                                          onConfirm: c
                                                              .callPrice.toggle,
                                                          toogle: c
                                                              .callPrice.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "Hosting perks",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Hosting Perks",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "You can benefit from Darent privileges such as discounts",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "E-mail",
                                                          onConfirm: c
                                                              .emailPrice
                                                              .toggle,
                                                          toogle: c.emailPerks
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: c
                                                              .notificationPrice
                                                              .toggle,
                                                          toogle: c
                                                              .notificationPerks
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS",
                                                          onConfirm:
                                                              c.smsPrice.toggle,
                                                          toogle:
                                                              c.smsPerks.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Phone call",
                                                          onConfirm: c
                                                              .callPrice.toggle,
                                                          toogle: c
                                                              .callPerks.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Divider(),
                        SizedBox(
                          height: heightSpace(2),
                        ),
                        const CustomText(
                          "Hosting Updates",
                          size: 2.3,
                          weight: FontWeight.w500,
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "Get updates about programs, features, and regulations.",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "News and updates",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "News And Updates",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Be the first to know about new tools and changes to the app and our service",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "E-mail",
                                                          onConfirm: () {
                                                        c.emailNews.toggle();
                                                      },
                                                          toogle: c
                                                              .emailNews.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: () {
                                                        c.notificationNews
                                                            .toggle();
                                                      },
                                                          toogle: c
                                                              .notificationNews
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS", onConfirm: () {
                                                        c.smsNews.toggle();
                                                      },
                                                          toogle:
                                                              c.smsNews.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Phone call",
                                                          onConfirm: () {
                                                        c.callNews.toggle();
                                                      },
                                                          toogle:
                                                              c.callNews.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "Local laws and regulations",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Local Laws And Regulations",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Get updates on short-term rental laws in your area.",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "E-mail",
                                                          onConfirm: () {
                                                        c.emailLaws.toggle();
                                                      },
                                                          toogle: c
                                                              .emailLaws.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: () {
                                                        c.notificationLaws
                                                            .toggle();
                                                      },
                                                          toogle: c
                                                              .notificationLaws
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS", onConfirm: () {
                                                        c.smsLaws.toggle();
                                                      },
                                                          toogle:
                                                              c.smsLaws.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Phone call",
                                                          onConfirm: () {
                                                        c.callLaws.toggle();
                                                      },
                                                          toogle:
                                                              c.callLaws.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Divider(),
                        SizedBox(
                          height: heightSpace(2),
                        ),
                        const CustomText(
                          "Travel Tips And Offers",
                          size: 2.3,
                          weight: FontWeight.w500,
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "Inspire your next trip with personalized recommendations and special offers.",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "Inspiration and offers",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Inspiration And Offers",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Stay fully informed of new programs and announcements",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "E-mail",
                                                          onConfirm: () {
                                                        c.emailInsp.toggle();
                                                      },
                                                          toogle: c
                                                              .emailInsp.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: () {
                                                        c.notificationInsp
                                                            .toggle();
                                                      },
                                                          toogle: c
                                                              .notificationInsp
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS", onConfirm: () {
                                                        c.smsInsp.toggle();
                                                      },
                                                          toogle:
                                                              c.smsInsp.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "Trip planning",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Trip Planning",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Travel smart with updates on regulations",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "E-mail",
                                                          onConfirm: () {
                                                        c.emailTrip.toggle();
                                                      },
                                                          toogle: c
                                                              .emailTrip.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: () {
                                                        c.notificationTrip
                                                            .toggle();
                                                      },
                                                          toogle: c
                                                              .notificationTrip
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS", onConfirm: () {
                                                        c.smsTrip.toggle();
                                                      },
                                                          toogle:
                                                              c.smsTrip.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                      ],
                    )
                  :
                  // account
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: heightSpace(2),
                        ),
                        const CustomText(
                          "Account Activity And Policies",
                          size: 2.3,
                          weight: FontWeight.w500,
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "Confirm your booking and account activity, and lern about important Airbnb policies.",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const CustomText(
                              "Account activity",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Account Activity",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Stay fully informed of new programs and announcements",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Email",
                                                          onConfirm: () {
                                                        c.emailRecog.toggle();
                                                      },
                                                          toogle: c.emailRecog
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: () {
                                                        c.notificationRecog
                                                            .toggle();
                                                      },
                                                          toogle: c
                                                              .notificationRecog
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS", onConfirm: () {
                                                        c.smsRecog.toggle();
                                                      },
                                                          toogle:
                                                              c.smsRecog.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email and SMS",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "Listing activity",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Listing Activity",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Reaching the goal using advice and ideas supported by data",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Email",
                                                          onConfirm: c
                                                              .emailInsig
                                                              .toggle,
                                                          toogle: c.emailInsig
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: c
                                                              .notificationInsig
                                                              .toggle,
                                                          toogle: c
                                                              .notificationInsig
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS",
                                                          onConfirm:
                                                              c.smsInsig.toggle,
                                                          toogle:
                                                              c.smsInsig.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Phone call",
                                                          onConfirm: c
                                                              .callInsig.toggle,
                                                          toogle: c
                                                              .callInsig.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email and SMS",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "Guest policies",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Guest Policies",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Improve your price with tips and insights backed by data",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "E-mail",
                                                          onConfirm: c
                                                              .emailPrice
                                                              .toggle,
                                                          toogle: c.emailPrice
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: c
                                                              .notificationPrice
                                                              .toggle,
                                                          toogle: c
                                                              .notificationPrice
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS",
                                                          onConfirm:
                                                              c.smsPrice.toggle,
                                                          toogle:
                                                              c.smsPrice.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Phone call",
                                                          onConfirm: c
                                                              .callPrice.toggle,
                                                          toogle: c
                                                              .callPrice.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email and SMS",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "Host policies",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Host Policies",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "You can benefit from Darent privileges such as discounts",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "E-mail",
                                                          onConfirm: c
                                                              .emailPrice
                                                              .toggle,
                                                          toogle: c.emailPerks
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: c
                                                              .notificationPrice
                                                              .toggle,
                                                          toogle: c
                                                              .notificationPerks
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS",
                                                          onConfirm:
                                                              c.smsPrice.toggle,
                                                          toogle:
                                                              c.smsPerks.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Phone call",
                                                          onConfirm: c
                                                              .callPrice.toggle,
                                                          toogle: c
                                                              .callPerks.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email and SMS",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Divider(),
                        SizedBox(
                          height: heightSpace(2),
                        ),
                        const CustomText(
                          "Reminders",
                          size: 2.3,
                          weight: FontWeight.w500,
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "Get important reminders about your reservations, listings, and account activity.",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "Reminders",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Reminders",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Be the first to know about new tools and changes to the app and our service",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "E-mail",
                                                          onConfirm: () {
                                                        c.emailNews.toggle();
                                                      },
                                                          toogle: c
                                                              .emailNews.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: () {
                                                        c.notificationNews
                                                            .toggle();
                                                      },
                                                          toogle: c
                                                              .notificationNews
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS", onConfirm: () {
                                                        c.smsNews.toggle();
                                                      },
                                                          toogle:
                                                              c.smsNews.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Phone call",
                                                          onConfirm: () {
                                                        c.callNews.toggle();
                                                      },
                                                          toogle:
                                                              c.callNews.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email and SMS",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Divider(),
                        SizedBox(
                          height: heightSpace(2),
                        ),
                        const CustomText(
                          "Guest And Host Messages",
                          size: 2.3,
                          weight: FontWeight.w500,
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "Keep in touch with your Host or guests before and during your trip.",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                              "Messages",
                              color: Colors.black87,
                            ),
                            InkWell(
                                onTap: () {
                                  ViewsCommon.showModalBottom(
                                      DraggableScrollableSheet(
                                          maxChildSize: .5,
                                          initialChildSize: .5,
                                          expand: false,
                                          builder: (context,
                                                  scrollController) =>
                                              Obx(
                                                () => Padding(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical:
                                                          heightSpace(3.5),
                                                      horizontal:
                                                          widthSpace(5)),
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Align(
                                                          alignment: Alignment
                                                              .topRight,
                                                          child: InkWell(
                                                              onTap: () =>
                                                                  Get.back(),
                                                              child: Icon(Icons
                                                                  .clear))),
                                                      const CustomText(
                                                        "Messages",
                                                        size: 2.3,
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(2),
                                                      ),
                                                      const CustomText(
                                                        "Stay fully informed of new programs and announcements",
                                                        size: 1.8,
                                                        color: Color(greyText),
                                                      ),
                                                      SizedBox(
                                                        height: heightSpace(3),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "E-mail",
                                                          onConfirm: () {
                                                        c.emailInsp.toggle();
                                                      },
                                                          toogle: c
                                                              .emailInsp.value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "Push notification",
                                                          onConfirm: () {
                                                        c.notificationInsp
                                                            .toggle();
                                                      },
                                                          toogle: c
                                                              .notificationInsp
                                                              .value),
                                                      SizedBox(
                                                        height:
                                                            heightSpace(2.5),
                                                      ),
                                                      ViewsCommon.switchButton(
                                                          "SMS", onConfirm: () {
                                                        c.smsInsp.toggle();
                                                      },
                                                          toogle:
                                                              c.smsInsp.value),
                                                    ],
                                                  ),
                                                ),
                                              )));
                                },
                                child: CustomText(
                                  "Edit",
                                  underline: true,
                                )),
                          ],
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                        const CustomText(
                          "On: Email and SMS",
                          color: Color(greyText),
                        ),
                        SizedBox(
                          height: heightSpace(1.5),
                        ),
                      ],
                    )
            ],
            //]
          ),
        ),
      ),
    );
  }
}
