import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/helperMethods/listing_helper/listing_helper.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../components/common_button.dart';
import '../../components/host/host_textfield.dart';
import '../../components/views_common.dart';
import '../../utils/constants.dart';
import '../../utils/sizeconfig.dart';

class WeeklyMonthlyDiscount extends StatelessWidget {
  WeeklyMonthlyDiscount({super.key});
  final note =
      '''${Get.find<TranslationHelper>().translations.hostDashboard.discountNote}''';
  final HostDashboardController c = Get.find();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          leading: IconButton(
              icon: Icon(Icons.chevron_left, color: Color(lightBlack)),
              onPressed: Get.back),
          title: Text(Get.find<TranslationHelper>()
              .translations
              .hostDashboard
              .bookingSettings!)),
      body: GetBuilder<HostDashboardController>(
        id: 'detail',
        builder: (c) => SingleChildScrollView(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .weeklyMonthlyDiscount,
                size: 2.1,
                weight: FontWeight.bold),
            SizedBox(height: heightSpace(1.5)),
            CustomText(note),
            SizedBox(height: heightSpace(4)),
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .selectUnit,
                size: 2.1,
                weight: FontWeight.bold),
            Container(
              margin: EdgeInsets.only(
                  top: heightSpace(1.5), bottom: heightSpace(4)),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: ViewsCommon.boxShadow,
                borderRadius: BorderRadius.circular(12),
              ),
              child: DropdownButton<int>(
                menuMaxHeight: heightSpace(40),
                items: c.calendarProperties.map((dwelling) {
                  return DropdownMenuItem<int>(
                    value: dwelling.id, // Assuming dwelling has an id property
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                            width: widthSpace(80),
                            child: CustomText(dwelling.name,
                                weight: FontWeight.bold)),
                        if (c.selectedDwelling.value != null &&
                            c.selectedDwelling.value == dwelling.id)
                          Icon(Icons.check, color: Color(lightBlack), size: 20),
                      ],
                    ),
                    onTap: () {
                      c.getDwellingDetail(dwelling.code);
                      c.setDwellingId(dwelling.id!);
                    },
                  );
                }).toList(),
                hint: c.selectedDwelling.value != null
                    ? CustomText(
                        c.dwellings
                                .firstWhere(
                                  (element) =>
                                      element.id == c.selectedDwelling.value,
                                  orElse: () => null,
                                )
                                ?.name ??
                            Get.find<TranslationHelper>()
                                .translations
                                .hostDashboard
                                .selectProperty,
                        weight: FontWeight.bold,
                      )
                    : CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .selectProperty,
                        weight: FontWeight.bold),
                padding: EdgeInsets.symmetric(horizontal: widthSpace(4)),
                isExpanded: true,
                underline: const SizedBox(),
                icon: const Icon(Icons.keyboard_arrow_down,
                    color: Color(greyText), size: 20),
                onChanged: (val) {},
              ),
            ),
            SizedBox(height: heightSpace(2)),
            ElevatedButton.icon(
                onPressed: () {
                  if (c.dwellingDetail == null) {
                    ViewsCommon.showSnackbar(Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .selectUnitMessage!);
                    return;
                  }
                  c.clearForDiscountModal();
                  discountSettings();
                },
                style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey[100],
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10))),
                label: CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .createDiscountSetting,
                    weight: FontWeight.bold),
                icon: Icon(Icons.add_circle_outline_rounded,
                    color: Colors.black)),
            SizedBox(height: heightSpace(3.5)),
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .weeklyDiscountSetting,
                size: 2.1,
                weight: FontWeight.bold),
            SizedBox(height: heightSpace(1.5)),
            if ((c.dwellingDetail?.propertyPrice?.weeklyDiscount != null &&
                    c.dwellingDetail?.propertyPrice?.weeklyDiscount != 0) ||
                (c.dwellingDetail?.propertyPrice?.monthlyDiscount != null &&
                    c.dwellingDetail?.propertyPrice?.monthlyDiscount != 0)) ...[
              listOfDiscounts()
            ] else ...[
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .emptyDiscountSetting,
                  size: 1.9,
                  color: Color(greyText))
            ],
            SizedBox(height: heightSpace(3)),
            infoWidget()
          ]).paddingAll(widthSpace(viewPadding)),
        ),
      ),
    );
  }

  infoWidget() {
    String title =
        '''${Get.find<TranslationHelper>().translations.hostDashboard.discountNote}''';
    return Container(
        padding: EdgeInsets.all(widthSpace(4)),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Color(lightBlack).withOpacity(.2)),
        child: CustomText(title,
            color: Color(lightBlack), size: 1.9, weight: FontWeight.w500));
  }

  listOfDiscounts() {
    return Container(
        padding: EdgeInsets.all(widthSpace(3)),
        decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(12),
            boxShadow: ViewsCommon.boxShadow),
        child: Column(children: [
          Row(children: [
            Expanded(
                flex: 2,
                child: CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .accountTransaction
                        .type,
                    weight: FontWeight.bold)),
            Expanded(
                child: CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .promocode
                        .discount,
                    weight: FontWeight.bold,
                    textAlign: TextAlign.center)),
            const Expanded(child: SizedBox()),
            const Expanded(child: Icon(Icons.edit_note_rounded)),
          ]),
          SizedBox(height: heightSpace(3)),
          if (c.dwellingDetail?.propertyPrice?.weeklyDiscount != 0) ...[
            Row(children: [
              Expanded(
                  flex: 2,
                  child: CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .weekly,
                      textOverflow: TextOverflow.ellipsis)),
              Expanded(
                  child: CustomText(
                      c.dwellingDetail?.propertyPrice?.weeklyDiscount
                          .toString(),
                      textAlign: TextAlign.center)),
              Expanded(
                  child: CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .accountTransaction
                          .detail,
                      color: Color(lightBlack),
                      textAlign: TextAlign.center)),
              Expanded(
                  child: GestureDetector(
                      onTap: () {
                        c.removeDiscount(isForWeekly: true);
                      },
                      child: const Icon(Icons.delete_outline_rounded,
                          color: Color(warningColor)))),
            ]),
            SizedBox(height: heightSpace(3)),
          ],
          if (c.dwellingDetail?.propertyPrice?.monthlyDiscount != 0) ...[
            Row(children: [
              Expanded(
                  flex: 2,
                  child: CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .monthly,
                      textOverflow: TextOverflow.ellipsis)),
              Expanded(
                  child: CustomText(
                      c.dwellingDetail?.propertyPrice?.monthlyDiscount
                          .toString(),
                      textAlign: TextAlign.center)),
              Expanded(
                  child: CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .accountTransaction
                          .detail,
                      color: Color(lightBlack),
                      textAlign: TextAlign.center)),
              Expanded(
                  child: GestureDetector(
                      onTap: () {
                        c.removeDiscount();
                      },
                      child: Icon(Icons.delete_outline_rounded,
                          color: Color(warningColor)))),
            ])
          ],
        ]));
  }

  discountSettings() {
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        minChildSize: .80,
        initialChildSize: .80,
        expand: false,
        builder: (context, scrollController) {
          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AppBar(
                    automaticallyImplyLeading: false,
                    backgroundColor: Colors.white,
                    actions: [
                      IconButton(
                          onPressed: Get.back,
                          padding: EdgeInsets.zero,
                          icon: Icon(Icons.close, color: Color(lightBlack)))
                    ],
                    title: CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .createDiscountSetting,
                        color: Color(lightBlack),
                        weight: FontWeight.bold)),
                Divider(height: 0),
                Obx(
                  () => Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .weeklyDiscountInstruction,
                          size: 2.1,
                          weight: FontWeight.bold),
                      SizedBox(height: heightSpace(2)),
                      SizedBox(
                        height: heightSpace(6.5),
                        child: Row(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Expanded(
                                  child: HostTextField(
                                      textController:
                                          ListingHelper.c.weeklyDiscount,
                                      hint: '15%',
                                      onChanged: (val) {
                                        if (c.dwellingDetail == null) {
                                          return;
                                        }
                                        if (val.isEmpty) {
                                          c.weeklyDisabled.value = true;
                                          return;
                                        }
                                        if (val ==
                                            (c.dwellingDetail?.propertyPrice
                                                        ?.weeklyDiscount ??
                                                    0)
                                                .toString()) {
                                          c.weeklyDisabled.value = true;
                                          return;
                                        }

                                        c.weeklyDisabled.value = false;
                                        return;
                                      })),
                              SizedBox(width: widthSpace(3)),
                              Expanded(
                                  child: CommonButton(
                                onPressed: () async {
                                  // same method we are hitting in dwelling details
                                  await ListingHelper.submitWeeklyDiscount(
                                      getPrice: true);
                                  showPrice('Weekly discount',
                                      c.dwellingDetail?.name);
                                },
                                title: Get.find<TranslationHelper>()
                                    .translations
                                    .hostDashboard
                                    .submitAndShow,
                                isDisabled: c.weeklyDisabled.value,
                                isLoading: c.isLoading.value,
                                borderRadius: 12,
                                backgroundBg: Color(lightBlack),
                              ))
                            ]),
                      ),
                      SizedBox(height: heightSpace(1)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .weeklyDiscountRecommendation,
                          size: 1.85,
                          color: Colors.green,
                          weight: FontWeight.w500),
                      SizedBox(height: heightSpace(2)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .monthlyDiscountRecommendation,
                          size: 2.1,
                          weight: FontWeight.bold),
                      SizedBox(height: heightSpace(2)),
                      SizedBox(
                        height: heightSpace(6.5),
                        child: Row(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Expanded(
                                  child: HostTextField(
                                      textController:
                                          ListingHelper.c.monthlyDiscount,
                                      hint: '28%',
                                      onChanged: (val) {
                                        if (c.dwellingDetail == null) {
                                          return;
                                        }
                                        if (val.isEmpty) {
                                          c.monthlyDisabled.value = true;
                                          return;
                                        }
                                        if (val ==
                                            (c.dwellingDetail?.propertyPrice
                                                        ?.monthlyDiscount ??
                                                    0)
                                                .toString()) {
                                          c.monthlyDisabled.value = true;
                                          return;
                                        }

                                        c.monthlyDisabled.value = false;
                                        return;
                                      })),
                              SizedBox(width: widthSpace(3)),
                              Expanded(
                                  child: CommonButton(
                                onPressed: () {
                                  ListingHelper.submitMonthlyDiscount(
                                      getPrice: true);
                                  showPrice('Monthly discount',
                                      c.dwellingDetail?.name);
                                },
                                title: Get.find<TranslationHelper>()
                                    .translations
                                    .hostDashboard
                                    .submitAndShow,
                                borderRadius: 12,
                                isLoading: c.isLoading.value,
                                isDisabled: c.monthlyDisabled.value,
                                backgroundBg: Color(lightBlack),
                              ))
                            ]),
                      ),
                      SizedBox(height: heightSpace(1)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .monthlyDiscountRecommendation,
                          size: 1.85,
                          color: Colors.green,
                          weight: FontWeight.w500)
                    ],
                  ).paddingAll(widthSpace(viewPadding)),
                ),
                // Spacer(),
                // Container(
                //     padding: EdgeInsets.all(widthSpace(3)),
                //     decoration: BoxDecoration(
                //         border: Border(top: BorderSide(color: Color(greyBorder))),
                //         borderRadius: BorderRadius.vertical(top:Radius.circular(14))
                //     ),
                //     child: CommonButton(title: 'Send', onPressed: (){},verticalPadding: 4,borderRadius: 12))
              ],
            ),
          );
        }));
  }

  showPrice(title, propertyName) {
    ViewsCommon.showModalBottom(BottomSheet(
        onClosing: () {},
        showDragHandle: false,
        builder: (context) {
          return SizedBox(
            height: heightSpace(40),
            child: Column(
              children: [
                AppBar(
                    automaticallyImplyLeading: false,
                    backgroundColor: Colors.white,
                    actions: [
                      IconButton(
                          onPressed: Get.back,
                          padding: EdgeInsets.zero,
                          icon: Icon(Icons.close, color: Color(lightBlack)))
                    ],
                    title: CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .priceBeforeAfter,
                        color: Color(lightBlack),
                        weight: FontWeight.bold)),
                const Divider(height: 0),
                Column(
                  children: [
                    Row(children: [
                      Expanded(
                          flex: 3,
                          child: CustomText(title, weight: FontWeight.bold)),
                      Expanded(
                          child: CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .hostDashboard
                                  .before,
                              weight: FontWeight.bold,
                              textAlign: TextAlign.center)),
                      Expanded(
                          child: CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .hostDashboard
                                  .after,
                              weight: FontWeight.bold,
                              textAlign: TextAlign.center)),
                    ]),
                    SizedBox(height: heightSpace(3)),
                    Obx(
                      () => Row(children: [
                        Expanded(
                            flex: 3,
                            child: CustomText(propertyName,
                                textOverflow: TextOverflow.ellipsis)),
                        Expanded(
                            child: CustomText(
                                c.priceBeforeDiscount.value.toString(),
                                textAlign: TextAlign.center)),
                        Expanded(
                            child: CustomText(
                                c.priceAfterDiscount.value.toString(),
                                textAlign: TextAlign.center)),
                      ]),
                    )
                  ],
                ).paddingSymmetric(
                    horizontal: widthSpace(6.5), vertical: heightSpace(6)),
              ],
            ),
          );
        }));
  }
}
