

import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class InvoiceScreen extends StatelessWidget {
  const InvoiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(icon: Icon(Icons.chevron_left,color: Color(lightBlack)),onPressed: Get.back),
        title: Text('Invoices'),
      ),
      body: ListView.separated(
          padding: EdgeInsets.only(top: widthSpace(viewPadding)),
          itemBuilder: (context, index) => InkWell(
            onTap: (){
              // Get.to(AccountStatementDetailsScreen());
            },
            child: Container(
              padding: EdgeInsets.all(widthSpace(4.5)),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(13),
                border: Border.all(color:Color(greyBorder)),
                boxShadow: ViewsCommon.boxShadow,
              ),child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        SizedBox(
                            width: widthSpace(20),
                            child: const CustomText('Number',weight: FontWeight.w500)),
                        SizedBox(width: widthSpace(5),),
                        const CustomText('INV-225823213#',weight: FontWeight.w500),
                      ],
                    ),
                    Row(
                      children: [
                        SizedBox(
                            width: widthSpace(20),
                            child: const CustomText('Date',weight: FontWeight.w500)),
                        SizedBox(width: widthSpace(5),),
                        const CustomText('21 March 2024',weight: FontWeight.w500),
                      ],
                    ),
                    Row(
                      children: [
                        SizedBox(
                            width: widthSpace(20),
                            child: const CustomText('Amount',weight: FontWeight.w500)),
                        SizedBox(width: widthSpace(5),),
                        const CustomText('SAR 54.2',weight: FontWeight.w500),
                      ],
                    ),
                  ],
                ),
                Icon(Icons.chevron_right)
              ],
            ),
            ),
          ),
          separatorBuilder:(c, i) => SizedBox(height: heightSpace(3)), itemCount: 4).paddingAll(widthSpace(viewPadding)),
    );
  }
}
