import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';

class AccountStatementDetailsScreen extends StatelessWidget {
   AccountStatementDetailsScreen({super.key});

   @override
   Widget build(BuildContext context) {
     return Scaffold(
       appBar: AppBar(
         leading: IconButton(
           icon: const Icon(Icons.chevron_left, color: Colors.black),
           onPressed: () {
             Navigator.of(context).pop();
           },
         ),
         title: Text('Account statements (March 2024)'),
       ),
       body: Column(
         crossAxisAlignment: CrossAxisAlignment.stretch,
         children: [
           accountStatementPeriodCard(),
           Expanded(
             child: SingleChildScrollView(
               child: Padding(
                 padding: EdgeInsets.all(widthSpace(viewPadding)),
                 child: statementTable(),
               ),
             ),
           ),
           totalAccountStatement(),
         ],
       ),
     );
   }

   totalAccountStatement() {
     return Container(
       padding: EdgeInsets.all(widthSpace(viewPadding)),
       decoration: BoxDecoration(
         color: Colors.white,
         borderRadius: BorderRadius.only(
           topRight: Radius.circular(13),
           topLeft: Radius.circular(13),
         ),
         border: Border.all(color: Color(greyBorder)),
         boxShadow: ViewsCommon.boxShadow,
       ),
       child: Row(
         mainAxisAlignment: MainAxisAlignment.spaceBetween,
         children: [
           CustomText("Total account statement", weight: FontWeight.w500),
           CustomText(
             "SAR 3.03",
             weight: FontWeight.w900,
             size: 2.3,
             color: Color(themeColor),
           ),
         ],
       )
     );
   }

  accountStatementPeriodCard() {
    return Container(
      padding: EdgeInsets.all(widthSpace(viewPadding)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(13),
          bottomLeft: Radius.circular(13),
        ),
        border: Border.all(color: Color(greyBorder)),
        boxShadow: ViewsCommon.boxShadow,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              CustomText('March 2024', weight: FontWeight.w500),
              SizedBox(
                width: widthSpace(2),
              ),
              Icon(Icons.calendar_month, color: Color(lightBlack)),
            ],
          ),
          SizedBox(
            height: heightSpace(2),
          ),
          const Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              CustomText(
                'From 1st March to 31st March 2024',
                weight: FontWeight.w900,
                color: Color(themeColor),
                size: 2.4,
              ),
            ],
          ),
          Divider(
            height: heightSpace(5),
          ),
          balanceCard(),
        ],
      ),
    );
  }

  balanceCard() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        showBalance(text: "Opening Balance", value: "0.025", isOpening: true),
        showBalance(text: "Closing Balance", value: "2.025"),
      ],
    );
  }

  showBalance({required text, required value, bool isOpening = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CustomText(text, weight: FontWeight.w500),
        SizedBox(
          height: heightSpace(2),
        ),
        CustomText(
          "SAR $value",
          weight: FontWeight.w900,
          size: 2.3,
          color: isOpening ? Colors.black : Color(themeColor),
        ),
      ],
    );
  }

  List tableList =[
    {
      "statement" :"Add a paid amount to a reservation (Confirmed) No. 3187130 - Apartment Simple "
          "and quiet - apartment It has a simple and integrated nature",
      "amount": "295.0",
      "balance": "295.0",
    },
    {
      "statement" :"Add a paid amount to a reservation (Confirmed) No. 3187130 - Apartment Simple "
          "and quiet - apartment It has a simple and integrated nature",
      "amount": "20.0",
      "balance": "0.0",
    },
    {
      "statement" :"Add a paid amount to a reservation (Confirmed) No. 3187130 - Apartment Simple "
          "and quiet - apartment It has a simple and integrated nature",
      "amount": "295.0",
      "balance": "-4.0",
    },
  ];

  statementTable() {
    return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(13),
          border: Border.all(color: Colors.black),
        ),
        child: Column(
          children: [
            Table(
                columnWidths: <int, TableColumnWidth>{
                  1: FixedColumnWidth(widthSpace(25)),
                  2: FixedColumnWidth(widthSpace(25)),
                  3: FixedColumnWidth(widthSpace(25)),
                },
                defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                children: <TableRow>[
                  tableHeading(),
                  divider(),
                  for (var item in tableList)...[
                    tableDataList(item),
                    divider(),
                  ],
                ]),
            tableFooter(),
          ],
        ));
  }

  TableRow tableHeading() {
    return TableRow(
      children: [
        TableCell(
          child: Container(
            padding: EdgeInsets.all(8.0),
            decoration: BoxDecoration(
                border:
                    Border(right: BorderSide(width: 1.0, color: Colors.black))),
            child: CustomText(
              'Statement/History',
              weight: FontWeight.w900,
              color: Color(themeColor),
              size: 2.4,
            ),
          ),
        ),
        TableCell(
          child: IntrinsicHeight(
            child: Container(
              padding: EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                  border:
                      Border(right: BorderSide(width: 1.0, color: Colors.black))),
              child: CustomText(
                'Amount',
                weight: FontWeight.w900,
                color: Color(themeColor),
                size: 2.4,
              ),
            ),
          ),
        ),
        TableCell(
          child: Padding(
            padding: EdgeInsets.all(8.0),
            child: CustomText(
              'Balance',
              weight: FontWeight.w900,
              color: Color(themeColor),
              size: 2.4,
            ),
          ),
        ),
      ],
    );
  }

    tableFooter() {
     return Container(
       padding: EdgeInsets.all(8.0),
       child: CustomText(
         'End of account statement',
         weight: FontWeight.w900,
         color: Colors.grey,
         size: 2.4,
       ),
     );
   }

  TableRow tableDataList(var item) {
    return TableRow(
      children: [
        TableCell(
          child: Container(
            padding: EdgeInsets.all(8.0),
            decoration: BoxDecoration(
                border:
                Border(right: BorderSide(width: 1.0, color: Colors.black))),
            child: CustomText(
              item['statement'],
              weight: FontWeight.w500,
            ),
          ),
        ),
        TableCell(
          child: IntrinsicHeight(
            child: Container(
              padding: EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                  border:
                  Border(right: BorderSide(width: 1.0, color: Colors.black))),
              child: CustomText(
                item['amount'].contains("-")
                ? item['amount'] : "+${item['amount']}",
                weight: FontWeight.w500,
                color: item['amount'].contains("-")
                    ? Color(warningColor) : Color(successColor),
              ),
            ),
          ),
        ),
        TableCell(
          child: Padding(
            padding: EdgeInsets.all(8.0),
            child: CustomText(
              item['balance'],
              weight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

   TableRow divider() {
     return TableRow(
       children: [
         Container(
           height: 1.0,
           color: Colors.black,
         ),
         Container(
           height: 1.0,
           color: Colors.black,
         ),
         Container(
           height: 1.0,
           color: Colors.black,
         ),
       ],
     );
   }
}
