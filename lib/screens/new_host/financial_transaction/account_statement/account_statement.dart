

import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/screens/new_host/financial_transaction/account_statement/account_statement_details.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AccountStatement extends StatelessWidget {
  const AccountStatement({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(icon: Icon(Icons.chevron_left,color: Color(lightBlack)),onPressed: Get.back),
        title: Text('Account statements'),
      ),
      body: ListView.separated(
          padding: EdgeInsets.only(top: widthSpace(viewPadding)),
          itemBuilder: (context, index) => InkWell(
            onTap: (){
              Get.to(AccountStatementDetailsScreen());
            },
            child: Container(
              padding: EdgeInsets.all(widthSpace(4.5)),
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(13),
                border: Border.all(color:Color(greyBorder)),
                boxShadow: ViewsCommon.boxShadow,
              ),child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(
                    width: widthSpace(70),
                    child: const CustomText('March 2024',weight: FontWeight.w500)),
                Icon(Icons.chevron_right)
              ],
            ),
            ),
          ),
          separatorBuilder:(c, i) => SizedBox(height: heightSpace(3)), itemCount: 4).paddingAll(widthSpace(viewPadding)),
    );
  }
}
