import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/new_host/accounts_summary.dart';
import 'package:darent/screens/new_host/financial_transaction/account_statement/account_statement.dart';
import 'package:darent/screens/new_host/financial_transaction/duration_of_receivables.dart';
import 'package:darent/screens/new_host/financial_transaction/how_to_receive_payments.dart';
import 'package:darent/screens/new_host/financial_transaction/invoices.dart';
import 'package:darent/screens/new_host/latest_transfers.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FinancialTransaction extends StatelessWidget {
  const FinancialTransaction({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
            onPressed: Get.back,
            icon: const Icon(
              Icons.chevron_left,
              size: 40,
            )),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(3.5)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .financialTransaction,
                size: 3.2),
            SizedBox(
              height: heightSpace(3.5),
            ),
            extraComponent(
                name: "How to receive money ",
                function: () {
                  Get.to(() => HowToReceivePayments());
                }),
            SizedBox(height: heightSpace(3.5)),
            extraComponent(
                name: "Duration of transferring funds",
                function: () {
                  Get.to(() => DurationOfReceivables());
                }),
            SizedBox(height: heightSpace(3.5)),
            extraComponent(
                name: "Money transfers",
                function: () {
                  Get.to(() => LatestTranfers());
                }),
            SizedBox(height: heightSpace(3.5)),
            extraComponent(
                name: "Invoices",
                function: () {
                  Get.to(() => const InvoiceScreen());
                }),
            SizedBox(height: heightSpace(3.5)),
            extraComponent(
                name: "Account statements",
                function: () {
                  Get.to(() => const AccountStatement());
                }),
            SizedBox(height: heightSpace(3.5)),
            extraComponent(
                name: "Summary of accounts",
                function: () {
                  Get.to(() => const AccountsSummary());
                }),
          ],
        ),
      ),
    );
  }

  extraComponent({required name, Widget? screen, function}) {
    return InkWell(
      onTap: function ??
          () {
            if (screen != null) {
              Get.to(() => screen);
            }
          },
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: widthSpace(1.5)),
            Expanded(
              child: CustomText(
                name,
                color: Colors.grey[800],
                weight: FontWeight.w500,
                size: 2.1,
              ),
            ),
            SizedBox(width: widthSpace(8)),
            const Icon(
              Icons.arrow_forward_ios,
              size: 20,
            )
          ]),
    );
  }
}
