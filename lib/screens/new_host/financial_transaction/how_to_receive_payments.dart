import 'package:flutter/material.dart';

import '../../../components/custom_text.dart';
import '../../../components/views_common.dart';
import '../../../utils/constants.dart';
import 'package:get/get.dart';

import '../../../utils/sizeconfig.dart';

class HowToReceivePayments extends StatelessWidget {
  const HowToReceivePayments({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(icon: Icon(Icons.chevron_left,color: Color(lightBlack)),onPressed: Get.back),
        title: Text('How to receive the payments'),
      ),
      body: ListView.separated(
        padding: EdgeInsets.all(widthSpace(viewPadding)),
        itemBuilder:(c,i) =>Container(
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color:Color(greyBorder)),
              boxShadow: ViewsCommon.boxShadow
          ),child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
          Container(
            padding: EdgeInsets.all(widthSpace(4)),
            width: widthSpace(29),
            decoration: BoxDecoration(
              color:Colors.lightGreen[100],
              borderRadius: BorderRadius.only(bottomLeft: Radius.circular(10),topRight: Radius.circular(10)),
            ),child: CustomText('Activated',color: Colors.green,weight: FontWeight.bold,textAlign: TextAlign.center),
          ),
          SizedBox(height: heightSpace(3)),
          Column(children: [
            row('Method of payment','Bank transfer'),
            SizedBox(height: heightSpace(1.5)),
            row('Bank name','Al-Rajhi Bank'),
            SizedBox(height: heightSpace(1.5)),
            row('Account holder nmae','Hamza Ali Abdullah'),
            SizedBox(height: heightSpace(1.5)),
            row('IBAN number','***************'),
          ]).paddingSymmetric(horizontal:widthSpace(3)),
              SizedBox(height: heightSpace(1.5)),
              IconButton(onPressed: (){}, icon: Icon(Icons.delete_outline_rounded,color: Color(greyText)))
        ]),
        ),
        separatorBuilder: (c,i) => SizedBox(height: heightSpace(3)),
        itemCount: 5,
      ),
    );
  }
  row(String text1, String text2,{bool isUnderLine=false}){
    return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomText(text1,weight: FontWeight.bold),
          CustomText(text2,color: isUnderLine?null:Colors.black54,weight: isUnderLine?FontWeight.bold:null),
        ]);
  }
}
