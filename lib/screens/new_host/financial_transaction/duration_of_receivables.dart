import 'package:flutter/material.dart';

import '../../../components/custom_text.dart';
import '../../../components/views_common.dart';
import '../../../utils/constants.dart';
import 'package:get/get.dart';

import '../../../utils/sizeconfig.dart';
import 'edit_receiving_payments.dart';

class DurationOfReceivables extends StatelessWidget {
  const DurationOfReceivables({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(icon: Icon(Icons.chevron_left,color: Color(lightBlack)),onPressed: Get.back),
        title: Text('Duration of receiving funds'),
      ),
      body:Column(
        children: [
          Container(
            margin: EdgeInsets.all(widthSpace(viewPadding)),
            padding: EdgeInsets.all(widthSpace(3)),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                border: Border.all(color:Color(greyBorder)),
                boxShadow: ViewsCommon.boxShadow
            ),child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                row('The transfer period','Virtual'),
                SizedBox(height: heightSpace(1.5)),
                row('The duration','48 hours'),
                SizedBox(height: heightSpace(1.5)),
                InkWell(onTap: (){
                  Get.to(()=>EditReceivingPayments());
                },child: Icon(Icons.edit_note_rounded,color: Color(greyText)))
            ]),
          ),
        ],
      ),
    );
  }
  row(String text1, String text2,{bool isUnderLine=false}){
    return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomText(text1,weight: FontWeight.w500),
          CustomText(text2,color: isUnderLine?null:Colors.black54,weight: isUnderLine?FontWeight.bold:null),
        ]);
  }
}