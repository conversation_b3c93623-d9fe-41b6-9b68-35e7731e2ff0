import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/components/host/host_radio.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import '../../../components/custom_text.dart';
import '../../../utils/constants.dart';
import 'package:get/get.dart';

class EditReceivingPayments extends StatelessWidget {
  const EditReceivingPayments({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(icon: Icon(Icons.chevron_left,color: Color(lightBlack)),onPressed: Get.back),
        title: Text('Duration of receiving funds'),
      ),
      body: Obx(()=>ListView(
        padding: EdgeInsets.all(widthSpace(viewPadding)),
        children: [
          CustomText('Choose the period within which you prefer to transfer funds',weight: FontWeight.w500),
          SizedBox(height: heightSpace(2)),
          HostRadio(
              title: 'Direct transfer (48 hours after the guest leaves) default',
              fontColor:Color(greyText),
              fontSize: 2,
              value:'direct',
              parent: c.selectedPayDuration.value,
              onPressed: c.selectPayDuration),
          SizedBox(height: heightSpace(1)),
          HostRadio(
              title: 'Transfer when a specific amount is reached',
              fontColor:Color(greyText),
              fontSize: 2,
              value:'specific', parent: c.selectedPayDuration.value, onPressed:c.selectPayDuration),
          SizedBox(height: heightSpace(1)),
          if(c.selectedPayDuration.value=='specific')...[
            Row(
                children: [
              CustomText('Enter the amount',weight: FontWeight.bold),
              Container(
                  width: widthSpace(30),
                  margin: EdgeInsets.symmetric(horizontal: widthSpace(4)),
                  child: CustomTextField(hint: 'Amount',isRoundedBorder: true)),
              CustomText('SAR',weight: FontWeight.bold),
            ]),
            SizedBox(height: heightSpace(1)),
          ],
          HostRadio(
              title: 'Weekly transfer',
              fontColor:Color(greyText),
              fontSize: 2,
              value:'weekly', parent: c.selectedPayDuration.value, onPressed: c.selectPayDuration),
          SizedBox(height: heightSpace(1)),
          if(c.selectedPayDuration.value=='weekly')...[
            CustomText('Select the day you want the transfer to arrive',weight: FontWeight.bold),
            Wrap(
                spacing: widthSpace(2),
                runSpacing:widthSpace(2),
                children: days.map<Widget>((e) => InkWell(
                  onTap: ()=>c.transferDay.value=e,
                  borderRadius: BorderRadius.circular(13),
                  child: Container(
                    padding: EdgeInsets.all(widthSpace(2.5)),
                                decoration: BoxDecoration(
                                  color: c.transferDay.value==e?Color(lightBg):null,
                  borderRadius: BorderRadius.circular(13),
                  border: Border.all(color: Color(greyBorder)),
                                  boxShadow: c.transferDay.value==e?ViewsCommon.boxShadow:null
                                ),child:CustomText(e)),
                )).toList()),
            SizedBox(height: heightSpace(1)),
          ],
          CommonButton(title: 'Save', onPressed: (){}),
          SizedBox(height: heightSpace(2)),
          infoWidget('If the period for transferring funds is changed, this will apply Amendment after executing the next transfer')
        ]),
      ),
    );
  }
  infoWidget(String title,{double padding=4,double rightMargin=0,double radius = 13}){
    return Container(
        padding: EdgeInsets.all(widthSpace(padding)),
        margin: EdgeInsets.only(bottom: heightSpace(1),right: rightMargin),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(radius),
            color: Color(lightBlack).withOpacity(.2)
        ),child:CustomText(title,color: Color(lightBlack),size: 1.9,weight: FontWeight.w500)
    );
  }
}
