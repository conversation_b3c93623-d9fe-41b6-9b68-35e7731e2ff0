import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/edit_component2.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/helperMethods/authHelper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/listing_helper/listing_helper.dart';
import 'package:darent/screens/co-host/cohost_listing.dart';
import 'package:darent/screens/host/common_get_dwelling.dart';
import 'package:darent/screens/host/notification_setting.dart';
import 'package:darent/screens/myfatoorah_screen.dart';
import 'package:darent/screens/new_host/edit_property/edit_ical.dart';
import 'package:darent/screens/new_host/host_reservations.dart';
import 'package:darent/screens/new_host/host_v2/host_personal_profile.dart';
import 'package:darent/screens/new_host/payment_payouts.dart';
import 'package:darent/screens/new_host/personal_file.dart';
import 'package:darent/screens/new_host/prices.dart';
import 'package:darent/screens/new_host/info_box.dart';
import 'package:darent/screens/new_host/reviews/reviews.dart';
import 'package:darent/screens/promotion/promotions.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../components/common_button.dart';
import '../../helperMethods/remote_config.dart';
import '../../helperMethods/search_helper.dart';
import '../../helperMethods/translation_helper.dart';
import '../../utils/routes.dart';

class More extends StatelessWidget {
  const More({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(widthSpace(viewPadding)),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          InkWell(
            onTap: () async {
              Get.to(() => const Personal());
            },
            child: Center(
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ClipRRect(
                            borderRadius: BorderRadius.circular(45),
                            child: (userModel.value?.profile_image ?? "").contains(".svg")
                                ? GlobalHelper.buildNetworkSvgWidget(
                              url:userModel.value?.profile_image??"",
                                height: widthSpace(19),
                                width: widthSpace(19),
                              defaultOption: const Icon(Icons.person),)
                                : GlobalHelper.resolveImageUrl(userModel.value?.profile_image ??"").isNotEmpty
                                ? Image(
                                image: GlobalHelper.buildNetworkImageProvider(
                                    url: userModel.value?.profile_image ??"",),
                                    height: widthSpace(19),
                                    width: widthSpace(19),
                                    fit: BoxFit.fill,
                            )
                                : Container(
                              width: widthSpace(19),
                              height: widthSpace(19),
                              clipBehavior: Clip.antiAlias,
                              decoration: const BoxDecoration(
                                  color: Color(themeColor),
                                  shape: BoxShape.circle,
                                  image: DecorationImage(
                                      image: AssetImage(
                                          'assets/icons/d_logo.png'))),
                            )
                        ),
                        SizedBox(height: heightSpace(2.3)),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            CustomText(
                                "${userModel.value?.first_name} ${userModel.value?.last_name}",
                                size: 2.1,
                                weight: FontWeight.w500),
                            SizedBox(height: heightSpace(0.7)),
                            CustomText(
                                userModel.value?.email ??
                                    "", // ??offlineTranslations?["view_profile"]
                                size: 1.8,
                                color: Colors.grey,
                                weight: FontWeight.normal),
                            //Divider(height: heightSpace(4)),
                          ],
                        ),
                      ]),
                  CircleAvatar(
                    radius: widthSpace(4),
                    backgroundColor: const Color(0xffFEE6B8),
                    child: SvgPicture.asset(
                        "assets/icons/host_new/edit_profile.svg"),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: heightSpace(3)),
          rowIcons(c.changeIndex),
          SizedBox(height: heightSpace(3)),
          CustomText(Get.find<TranslationHelper>().translations.footer.hosting,
              size: 1.7,
              weight: FontWeight.normal,
              color: const Color(greyText)),
          SizedBox(height: heightSpace(3)),
          extraComponent("icons/host_new/reservation",
              name: Get.find<TranslationHelper>()
                  .translations
                  .reservation
                  .reservation, function: () {
            Get.to(() => HostReservations());
          }),
          SizedBox(height: heightSpace(3)),
          extraComponent(Icons.monetization_on_outlined,
              name: Get.find<TranslationHelper>()
                  .translations
                  .listingPrice
                  .price, function: () {
            Get.to(() => PriesScreen());
          }),
          SizedBox(height: heightSpace(3)),
          extraComponent("icons/host_new/payment",
              name: Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .paymentAndPayouts, function: () {
            Get.to(() => const PaymentAndPayouts());
          }),
          SizedBox(height: heightSpace(3)),
          extraComponent(Icons.real_estate_agent_outlined,
              name: Get.find<TranslationHelper>().translations.sidenav.reviews,
              function: () async {
            Get.to(() => Reviews());
          }),
          if (displayPromo) ...[
            SizedBox(height: heightSpace(3)),
            extraComponent("icons/host_new/promotion",
                name: Get.find<TranslationHelper>()
                    .translations
                    .listing
                    .promotions,
                screen: const Promotions()),
          ],
          SizedBox(height: heightSpace(3)),
          extraComponent('icons/host_new/more',
              name: Get.find<TranslationHelper>()
                  .translations
                  .coHost
                  .cohostTitle, function: () async {
            if (ListingHelper.c.selectedProperty.value != null ||
                ListingHelper.c.properties.isNotEmpty) {
              Get.to(() => CommonDwellingSelectionWidget(
                    title: Get.find<TranslationHelper>()
                        .translations
                        .coHost
                        .cohostTitle,
                    trailingWidget: EditComponent2(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .coHost
                            .cohostTitle,
                        value: Get.find<TranslationHelper>()
                            .translations
                            .coHost
                            .startingDescription,
                        clickEvent: () => Get.to(() => const CohostListing())),
                  ));
            } else {
              ViewsCommon.showSnackbar(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .commonMessage!,
                  displayTime: 750,
                  keyword: DialogKeyword.info);
            }
          }),
          SizedBox(height: heightSpace(3)),
          extraComponent('icons/host_new/calendar',
              name: Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .syncCalendar, function: () async {
            if (ListingHelper.c.selectedProperty.value != null ||
                ListingHelper.c.properties.isNotEmpty) {
              Get.to(() => CommonDwellingSelectionWidget(
                    title: Get.find<TranslationHelper>()
                            .translations
                            .hostListing
                            .calendar ??
                        "",
                    trailingWidget: EditComponent2(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .hostListing
                            .calendar!,
                        value: Get.find<TranslationHelper>()
                            .translations
                            .ical
                            .syncWithOther,
                        clickEvent: () => Get.to(() => const EditIcal())),
                  ));
            } else {
              ViewsCommon.showSnackbar(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .commonMessage!,
                  displayTime: 750,
                  keyword: DialogKeyword.info);
            }
          }),
          SizedBox(height: heightSpace(3)),
          extraComponent('note',
              name: Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .referralLink, function: () async {
            if (ListingHelper.c.selectedProperty.value != null ||
                ListingHelper.c.properties.isNotEmpty) {
              Get.to(() => CommonDwellingSelectionWidget(
                  title: Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .referralLink ??
                      "",
                  trailingWidget: Obx(() => EditComponent2(
                      title: Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .referralLink ??
                          '',
                      value: Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .generateReferral,
                      secondaryIcon: ListingHelper.c.isBtnLoading.value
                          ? const SizedBox(
                              height: 15,
                              width: 15,
                              child: CircularProgressIndicator(strokeWidth: 1))
                          : null,
                      clickEvent: ListingHelper.c.generateReferralCode))));
            } else {
              ViewsCommon.showSnackbar(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .commonMessage!,
                  displayTime: 750,
                  keyword: DialogKeyword.info);
            }
          }),
          SizedBox(height: heightSpace(3)),
          extraComponent('icons/host_new/insurance',
              name: Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .insurance, function: () async {
            Get.to(() => MyFatoorahScreen(
                url:
                    '$baseUrl/${Get.locale?.languageCode ?? 'en'}/insurance?mobile=1',
                screenName: Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .insurance ??
                    ''));
          }),
          SizedBox(height: heightSpace(3)),
          extraComponent('icons/host_new/dwellings',
              name: Get.find<TranslationHelper>()
                  .translations
                  .usersProfile
                  .activatePropertyLicense,
              function: () => showActivateLicense(c.changeIndex)),

          SizedBox(height: heightSpace(3)),
          CustomText(
              Get.find<TranslationHelper>().translations.account.accountSetting,
              size: 1.7,
              weight: FontWeight.normal,
              color: const Color(greyText)),
          SizedBox(height: heightSpace(3)),
          extraComponent("icons/host_new/more",
              name: Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .editPersonalInfo, function: () {
            Get.to(() => HostPersonalProfileScreen());
          }),
          SizedBox(height: heightSpace(3)),
          extraComponent("notification",
              name: Get.find<TranslationHelper>()
                  .translations
                  .notification
                  .notification, function: () async {
            await ListingHelper.c.getNotificationPermission();
            Get.to(() => const NotificationSetting());
          }),
          SizedBox(height: heightSpace(3)),
          InkWell(
              onTap: () {
                AuthHelper.c.selectLanguage(
                    Get.locale?.languageCode == 'ar' ? 'en' : 'ar');
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(
                    Icons.language,
                    size: widthSpace(5),
                  ),
                  SizedBox(width: widthSpace(2)),
                  Expanded(
                      child: CustomText(
                          '${Get.find<TranslationHelper>().translations.accountMobile.browseIn} ${Get.locale?.languageCode == 'ar' ? 'English' : 'عربي'}',
                          color: Colors.grey[800],
                          size: 2.0)),
                  const Icon(Icons.chevron_right,
                      size: 20, color: Color(greyText))
                ],
              )),
          // extraComponent("security",name: Get.find<TranslationHelper>().translations.hostDashboard.privacySharing, function:  (){
          //   Get.to(()=> const PrivacyAndSharing());
          // }),
          // SizedBox(height: heightSpace(3)),
          // extraComponent(Icons.security_rounded,name: 'Host protection program', function:  (){
          //   Get.to(()=> const HostProtection());
          // }),
          SizedBox(height: heightSpace(3)),

          CustomText(
              Get.find<TranslationHelper>().translations.account.helpSupport,
              size: 1.7,
              weight: FontWeight.normal,
              color: const Color(greyText)),
          SizedBox(height: heightSpace(3)),
          extraComponent("icons/host_new/report",
              name: Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .hostReports, function: () {
            Get.toNamed(Routes.customerServices);
          }),
          SizedBox(height: heightSpace(3)),
          InkWell(
              onTap: () async {
                AuthHelper.c.openWebView('faqs',
                    Get.find<TranslationHelper>().translations.footer.faq);
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(Icons.question_answer_outlined),
                  SizedBox(width: widthSpace(1.5)),
                  Expanded(
                      child: CustomText(
                          Get.find<TranslationHelper>().translations.footer.faq,
                          size: 2.1,
                          weight: FontWeight.w500,
                          color: Colors.grey[800])),
                  const Icon(Icons.chevron_right,
                      size: 20, color: Color(greyText))
                ],
              )),
          SizedBox(height: heightSpace(3)),

          CustomText(
              Get.find<TranslationHelper>().translations.account.legalDocument,
              size: 1.7,
              weight: FontWeight.normal,
              color: const Color(greyText)),
          SizedBox(height: heightSpace(3)),
          extraComponent("icons/host_new/trems",
              name: Get.find<TranslationHelper>()
                  .translations
                  .accountMobile
                  .termsAndConditions, function: () async {
            Get.to(() => MyFatoorahScreen(
                url:
                    "$baseUrl/term_condition?lang=${Get.locale?.languageCode ?? "en"}&mobile=1",
                screenName: Get.find<TranslationHelper>()
                        .translations
                        .accountMobile
                        .termsAndConditions ??
                    "Terms"));
          }),
          SizedBox(height: heightSpace(3)),
          extraComponent("list",
              name: Get.find<TranslationHelper>()
                  .translations
                  .account
                  .hostAgreement, function: () async {
            Get.to(() => MyFatoorahScreen(
                url:
                    "$baseUrl/${Get.locale?.languageCode ?? "en"}/property/hostAgreement?lang=${Get.locale?.languageCode ?? "en"}&mobile=1",
                screenName: "Host Agreement"));
          }),
          SizedBox(height: heightSpace(3)),
          extraComponent('icons/host_new/privacy',
              name: Get.find<TranslationHelper>()
                  .translations
                  .footer
                  .privacyPolicy, function: () async {
            Get.to(() => MyFatoorahScreen(
                url:
                    "$baseUrl/privacy-policy?lang=${Get.locale?.languageCode ?? "en"}&mobile=1",
                screenName: Get.find<TranslationHelper>()
                    .translations
                    .footer
                    .privacyPolicy));
          }),
          // CustomText(Get.find<TranslationHelper>().translations.hostDashboard.accountInfo, size: 2.3, weight: FontWeight.w500),
          // SizedBox(height: heightSpace(3)),
          // extraComponent("payment",name: Get.find<TranslationHelper>().translations.hostDashboard.financialTransaction, function: (){
          //   Get.to(()=>const FinancialTransaction());
          // }),
          // SizedBox(height: heightSpace(3)),
          // extraComponent("list",name: Get.find<TranslationHelper>().translations.hostDashboard.hostSuggestion, function: (){
          //   Get.to(()=>HostSuggestions());
          // }),
          SizedBox(height: heightSpace(3)),
          SizedBox(
            height: heightSpace(7),
            width: double.infinity,
            child: CommonButton(
              title: Get.find<TranslationHelper>()
                  .translations
                  .accountMobile
                  .switchToTravelling,
              onPressed: SearchHelper.c.switchMode,
              backgroundBg: Colors.white,
              buttonThemeColor: Colors.black,
            ),
          ),
          SizedBox(height: heightSpace(3)),
          SizedBox(
            height: heightSpace(7),
            width: double.infinity,
            child: CommonButton(
              title: Get.find<TranslationHelper>()
                  .translations
                  .accountMobile
                  .logOut,
              onPressed: AuthHelper.c.showLogout,
              backgroundBg: Colors.black.withOpacity(0.88),
              buttonThemeColor: Colors.white,
            ),
          ),
          SizedBox(height: heightSpace(3)),
          // CommonButton(
          //     title: 'Switch to ${lightBlack==0xff010101?'Yellow':'Black'} theme', onPressed: (){
          //   if(lightBlack==0xff010101){
          //     lightBlack = themeColor;
          //   }else{
          //     lightBlack = 0xff010101;
          //   }
          //   Get.forceAppUpdate();
          // },backgroundBg: Color(lightBlack==0xff010101?themeColor:0xff010101)),

          // SizedBox(
          //                    height: heightSpace(7),
          //                    width: double.infinity,
          //                    child: CommonButton(title: Get.find<TranslationHelper>().translations.accountMobile.switchToTravelling, onPressed: SearchHelper.c.switchMode,
          //                    backgroundBg: Colors.white,
          //                    buttonThemeColor: Colors.black,
          //
          //                    ),
          //                  ),
          // SizedBox(height: heightSpace(3)),
          // SizedBox(
          //                    height: heightSpace(7),
          //                    width: double.infinity,
          //                    child: CommonButton(title: Get.find<TranslationHelper>().translations.accountMobile.logOut, onPressed:AuthHelper.c.showLogout,
          //                    backgroundBg: Colors.black.withOpacity(0.88),
          //                    buttonThemeColor: Colors.white,
          //
          //                    ),
          //                  ),
          // SizedBox(height: heightSpace(1)),
        ]),
      ),
    );
  }

  showActivateLicense(changeIndex) {
    final trans = Get.find<TranslationHelper>().translations;
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        maxChildSize: .6,
        initialChildSize: .6,
        expand: false,
        builder: (ctx, s) =>
            Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Align(
                  alignment: Alignment.centerRight,
                  child: IconButton(
                      onPressed: Get.back,
                      icon: const CircleAvatar(
                          backgroundColor: Colors.black12,
                          foregroundColor: Colors.black,
                          child: Icon(Icons.close)))),

              CustomText(trans.usersProfile.activatePropertyLicense,
                  weight: FontWeight.w500, size: 2.2),
              SizedBox(height: heightSpace(.7)),
              CustomText(trans.usersProfile.activatePropertyLicenseNote,
                  color: Color(greyText), weight: FontWeight.w300),
              SizedBox(height: heightSpace(2.5)),
              // iconLinkContainer(
              //   SvgPicture.asset('assets/icons/host_new/certificate.svg',width: widthSpace(8)),
              //   trans.usersProfile.issuePropertyLicense,
              //   trans.usersProfile.issuePropertyLicenseNote,
              //     ()=>launchUrlString(propertyLicenseLink)
              // ),
              // SizedBox(height: heightSpace(1.5)),
              iconLinkContainer(
                  SvgPicture.asset(
                    'assets/icons/host_new/activate_check.svg',
                    width: widthSpace(8),
                    color: Colors.black,
                  ),
                  trans.usersProfile.activatePropertyLicense,
                  trans.usersProfile.activatePropertyLicenseNotee, () {
                changeIndex(3);
              }),
              SizedBox(height: heightSpace(1.5)),
              iconLinkContainer(
                  Container(
                    width: widthSpace(7),
                    height: widthSpace(7),
                    // height: widthSpace(8),
                    decoration: BoxDecoration(
                        color: Colors.black.withOpacity(.12),
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.black, width: 2)),
                    child: const Center(
                        child: CustomText('i', weight: FontWeight.w500)),
                  ),
                  trans.usersProfile.infoAboutActivateSteps,
                  trans.usersProfile.infoAboutActivateStepsNote,
                  () => AuthHelper.c.openWebView(
                      'managehost/instruction',
                      Get.find<TranslationHelper>()
                          .translations
                          .hostListing
                          .instructions!)),
            ]).paddingAll(widthSpace(viewPadding))));
  }

  iconLinkContainer(Widget icon, String title, String subTitle, function) {
    return InkWell(
      onTap: () {
        Get.back();
        function();
      },
      child: Container(
        padding: EdgeInsets.all(widthSpace(3)),
        decoration: BoxDecoration(
            border: Border.all(color: const Color(greyBorder), width: 3),
            borderRadius: BorderRadius.circular(12)),
        child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
          icon,
          SizedBox(width: widthSpace(3)),
          Expanded(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              CustomText(title, weight: FontWeight.w500),
              SizedBox(height: heightSpace(.7)),
              CustomText(subTitle,
                  color: const Color(greyText),
                  weight: FontWeight.w300,
                  size: 1.9)
            ]),
          )
        ]),
      ),
    );
  }

  rowIcons(changeIndex) {
    final trans = Get.find<TranslationHelper>().translations;
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: InfoBoxHorizontal(
              image: 'icons/host_new/sale',
              name: CustomText(
                "${(AuthHelper.c.totalSales.value ?? 0.0)} SAR",
                size: 2.05,
                weight: FontWeight.w500,
                textAlign: TextAlign.start,
                maxlines: 1,
                textOverflow: TextOverflow.clip,
              ),
              value: CustomText(
                "Total ${trans.hostDashboard.sales!}",
                textAlign: TextAlign.start,
                size: 1.8,
                color: const Color(greyText),
              ),
            )),
          ],
        ),
        SizedBox(height: heightSpace(2)),
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          SizedBox(width: widthSpace(1.5)),
          renderInfoBox(
              color:0xff005248,
              'icons/host_new/reservation',
              trans.reservation.reservation,Obx(()=>renderInfoValue(ListingHelper.c.bookingsTotal.value.allCount)),function: () {
            Get.to(() => HostReservations());
          }),
          SizedBox(width: widthSpace(1.5)),
          renderInfoBox(
              color:0xff00E4C9,
              'icons/host_new/dwellings',trans.hostDashboard.units!,Obx(()=>renderInfoValue(ListingHelper.c.allPropertiesCount.value)),function:() {
            changeIndex(3);
          }),
        ]),
      ],
    );
  }
  Widget renderInfoValue(value)=>CustomText(
    "$value",
    textAlign: TextAlign.start,
    size: 1.8,
    color: const Color(greyText),
    textOverflow: TextOverflow.ellipsis,
  );
  renderInfoBox(String iconPath,String title,Widget value,{int? color,function}){
    return Expanded(
        child: InfoBoxHorizontal(
          color: color,
          image: iconPath,
          name: CustomText(
            title,
            size: 2.05,
            weight: FontWeight.w500,
            textAlign: TextAlign.start,
            maxlines: 1,
            textOverflow: TextOverflow.ellipsis,
          ),
          value: value,
          function:function,
        ));
  }

  extraComponent(image, {required name, Widget? screen, function}) {
    return InkWell(
      onTap: function ??
          () {
            if (screen != null) {
              Get.to(() => screen);
            }
          },
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            image is String
                ? SvgPicture.asset("assets/$image.svg",
                    width: widthSpace(5), color: Colors.black)
                : Icon(image, size: widthSpace(5)),
            SizedBox(width: widthSpace(2)),
            Expanded(
              child: CustomText(name, color: Colors.grey[800], size: 2.0),
            ),
            SizedBox(width: widthSpace(8)),
            const Icon(Icons.chevron_right, size: 20, color: Color(greyText))
          ]),
    );
  }
}
