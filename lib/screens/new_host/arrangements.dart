import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/host_radio.dart';
import 'package:darent/controllers/bookings_controller.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../helperMethods/translation_helper.dart';
import '../../utils/constants.dart';

class Arrangement extends StatelessWidget {
  const Arrangement({super.key});

  @override
  Widget build(BuildContext context) {
    BookingsController c = Get.find();
    return Scaffold(
        body: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
          SizedBox(
            height: heightSpace(10),
            child: Padding(
              padding: EdgeInsets.only(top: heightSpace(5)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(width: widthSpace(5)),
                  CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .arrangement,
                      size: 2.2),
                  IconButton(onPressed: Get.back, icon: const Icon(Icons.close))
                ],
              ),
            ),
          ),
          const Divider(thickness: 1),
          Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: widthSpace(5), vertical: heightSpace(1)),
              child: Obx(() => Column(children: [
                    HostRadio(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .newToOldCheckin!,
                        fontSize: 2,
                        value: "checkin_new",
                        height: heightSpace(6),
                        parent: c.selectedSort.value,
                        onPressed: c.selectSort),
                    HostRadio(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .oldToNewCheckin!,
                        fontSize: 2,
                        value: "checkin_old",
                        height: heightSpace(6),
                        parent: c.selectedSort.value,
                        onPressed: c.selectSort),
                    HostRadio(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .newToOldBooking!,
                        fontSize: 2,
                        value: "booking_new",
                        height: heightSpace(6),
                        parent: c.selectedSort.value,
                        onPressed: c.selectSort),
                    HostRadio(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .oldToNewBooking!,
                        fontSize: 2,
                        value: "booking_old",
                        height: heightSpace(6),
                        parent: c.selectedSort.value,
                        onPressed: c.selectSort)
                  ])))
        ]),
        bottomNavigationBar: Container(
          height: heightSpace(11),
          alignment: Alignment.centerRight,
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(viewPadding), vertical: widthSpace(4)),
          decoration: const BoxDecoration(
              border: Border(top: BorderSide(color: Color(greyBorder)))),
          child: CommonButton(
              title: Get.find<TranslationHelper>().translations.filter.apply,
              backgroundBg: Colors.black,
              minimumSize: Size.fromWidth(widthSpace(30)),
              horizontalPadding: 6,
              onPressed: c.applySort),
        ));
  }
}
