import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../components/views_common.dart';
import '../../helperMethods/remote_config.dart';
import '../../helperMethods/search_helper.dart';
import '../../utils/constants.dart';
import '../../utils/routes.dart';
import '../../utils/sizeconfig.dart';

class HostNotification extends StatelessWidget {
  const HostNotification({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: CustomText(
              Get.find<TranslationHelper>().translations.notification.notification,
              size: 2.2,
              weight: FontWeight.w500),
          leading: I<PERSON><PERSON><PERSON>on(
              onPressed: Get.back,
              icon: const Icon(Icons.chevron_left),
              iconSize: 30),
          centerTitle: false,
          elevation: 0,
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            await SearchHelper.c.getNotifications(refresh: true);
          },
          child: Obx(
                () => SearchHelper.c.notifyList.isEmpty
                ? SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: SizedBox(
                height: MediaQuery.of(context).size.height,
                child: Center(
                  child: CustomText(
                    Get.find<TranslationHelper>().translations.notification.noNotification,
                  ),
                ),
              ),
            )
                : NotificationListener<ScrollNotification>(
              // Use ScrollNotification to handle both updates and end events
              onNotification: (notification) {
                if (notification is ScrollEndNotification) {
                  final metrics = notification.metrics;
                  if (metrics.atEdge && metrics.pixels > 0) {
                    // Only trigger pagination at bottom, not top
                    if (SearchHelper.c.notifyPage < SearchHelper.c.notifyLastPage) {
                      SearchHelper.c.notifyPage++;
                      SearchHelper.c.getNotifications();
                    }
                  }
                }
                return false;
              },
              child: ListView.separated(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.only(top: heightSpace(2)),
                itemCount: SearchHelper.c.notifyList.length,
                itemBuilder: (context, index) {
                  final notifyObj = SearchHelper.c.notifyList[index];
                  return InkWell(
                    onTap: () {
                      SearchHelper.c.handleMessage(notifyObj.data!);
                      if (notifyObj.data?['slug'].contains(Routes.inbox) &&
                          notifyObj.data?['chat_head_id'] != null) {
                        SearchHelper.c.loadingNotify = index;
                        SearchHelper.c.notifyList.refresh();
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.all(widthSpace(4)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CircleAvatar(
                            minRadius: widthSpace(7),
                            backgroundColor: const Color(lightBg),
                            child: notifyObj.data?['imagePath'] != null
                                ? Image(
                              image: GlobalHelper.buildNetworkImageProvider(
                                  url: notifyObj.data?['imagePath'] ?? ""),
                              height: 23,
                            )
                                : Icon(
                              Icons.person,
                              color: Colors.black.withOpacity(0.7),
                              size: 23,
                            ),
                          ),
                          SizedBox(width: widthSpace(4)),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    if (notifyObj.data?['heading'] != null)
                                      Expanded(
                                        child: CustomText(
                                          notifyObj.data!['heading'].isEmpty
                                              ? "Notification"
                                              : notifyObj.data!['heading'],
                                          size: 1.9,
                                          maxlines: 1,
                                          textOverflow: TextOverflow.ellipsis,
                                          textAlign: TextAlign.start,
                                        ),
                                      ),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: widthSpace(2),
                                        vertical: heightSpace(0.5),
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(8),
                                        boxShadow: ViewsCommon.boxShadow,
                                      ),
                                      child: CustomText(
                                        notifyObj.createdAt,
                                        size: 1.5,
                                        color: const Color(themeColor).withOpacity(.8),
                                        weight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: heightSpace(0.8)),
                                CustomText(
                                  notifyObj.message,
                                  color: const Color(greyText),
                                  size: 1.8,
                                  maxlines: 2,
                                  textOverflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
                separatorBuilder: (context, index) => Divider(
                  color: Colors.black.withOpacity(.03),
                  thickness: 3,
                ),
              ),
            ),
          ),
        )
    );
  }

  profileImage(String? image) {
    return ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: (image ?? "").contains(".svg")
            ? GlobalHelper.buildNetworkSvgWidget(
          url:image??"",
          height: widthSpace(14),
          width: widthSpace(14),
          defaultOption: Image.asset(
              "assets/default-image.png",
              width: widthSpace(14),
              height: widthSpace(14),
              fit: BoxFit.fill),)
            : Image(
            image: GlobalHelper.buildNetworkImageProvider(url: image ??""),
                width: widthSpace(14),
                height: widthSpace(14),
                fit: BoxFit.fill,
        ));
  }
}
