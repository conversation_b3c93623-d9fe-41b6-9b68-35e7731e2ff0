import 'package:darent/components/custom_text.dart';
import '../../controllers/hostDashboard_controller.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TransactionHistory extends StatelessWidget {
  const TransactionHistory({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();

    return Scaffold(
      body: Obx(
        () => Padding(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(5), vertical: heightSpace(6)),
          child: Container(
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                          onTap: () => Get.back(),
                          child: Icon(Icons.arrow_back_ios)),
                      CustomText(
                        "Transaction History",
                        size: 2.5,
                        weight: FontWeight.w500,
                      ),
                      CustomText(
                        "Help",
                        size: 2.2,
                        underline: true,
                      )
                    ],
                  ),
                  SizedBox(height: heightSpace(4)),
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Container(
                          width: widthSpace(5),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: Colors.grey,
                                width: 2,
                              ),
                            ),
                          ),
                        ),
                        InkWell(
                            child: Container(
                              padding: EdgeInsets.only(
                                  top: heightSpace(1), bottom: heightSpace(2)),
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: c.transactionHistory == "return"
                                        ? Colors.green
                                        : Colors.grey,
                                    width: c.transactionHistory == "return"
                                        ? 3
                                        : 2,
                                  ),
                                ),
                              ),
                              child: CustomText(
                                "Completed Returns",
                                size: 2,
                                color: c.transactionHistory == "return"
                                    ? Colors.green
                                    : Colors.black,
                                weight: FontWeight.w500,
                              ),
                            ),
                            onTap: () {
                              c.setTransactionTab("return");
                            }),
                        Container(
                          width: widthSpace(10),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: Colors.grey,
                                width: 2,
                              ),
                            ),
                          ),
                        ),
                        InkWell(
                            child: Container(
                              padding: EdgeInsets.only(
                                  top: heightSpace(1), bottom: heightSpace(2)),
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: c.transactionHistory.value ==
                                            "returnback"
                                        ? Colors.green
                                        : Colors.grey,
                                    width: c.transactionHistory == "returnback"
                                        ? 3
                                        : 2,
                                  ),
                                ),
                              ),
                              child: CustomText(
                                "Upcoming Returns",
                                size: 2,
                                color: c.transactionHistory == "returnback"
                                    ? Colors.green
                                    : Colors.black,
                                weight: FontWeight.w500,
                              ),
                            ),
                            onTap: () {
                              c.setTransactionTab("returnback");
                            }),
                        Container(
                          width: widthSpace(5),
                          decoration: BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: Colors.grey,
                                width: 2,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: heightSpace(3),
                  ),
                  const Align(
                      alignment: Alignment.centerLeft,
                      child: CustomText(
                        "There Are No completed Transactions",
                        size: 2.1,
                      )),
                ],
              )),
        ),
      ),
    );
  }
}
