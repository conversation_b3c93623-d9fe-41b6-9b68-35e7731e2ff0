import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../helperMethods/listing_helper/listing_helper.dart';
import '../../../helperMethods/translation_helper.dart';

class EditHowBook extends StatelessWidget {
  const EditHowBook({super.key});

  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return Obx(
      () => Scaffold(
          appBar: AppBar(
            leading: IconButton(
                icon: const Icon(Icons.chevron_left), onPressed: Get.back),
            elevation: 0,
          ),
          body: ListView(
              padding: EdgeInsets.all(widthSpace(viewPadding)),
              children: [
                CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .howGuestsBook,
                        size: 2.3,
                        weight: FontWeight.w500)
                    .paddingOnly(bottom: heightSpace(2.5)),
                ViewsCommon.switchButton(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostListing
                        .instantBook!,
                    toogle: c.instantBook.value == "instant",
                    onConfirm: () => c.instantBook.value =
                        c.instantBook.value == "instant"
                            ? "request"
                            : "instant",
                    fontSize: 2.15),
                SizedBox(height: heightSpace(1.5)),
                CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .howGuestsBookNote,
                  color: const Color(greyText),
                  size: 1.7,
                  lineSpacing: 1.75,
                ),
                // SizedBox(height: heightSpace(3)),
                // if(c.instantBook.value)...[
                //   InkWell(
                //     onTap:()=>Get.to(()=>EditOptInstantBook()),
                //     child: Column(
                //         crossAxisAlignment: CrossAxisAlignment.start,
                //         children: [
                //       CustomText("Optional instant booking settings",size: 2.15),
                //       SizedBox(height: heightSpace(1.5)),
                //       CustomText("These settings are available when Instant Book is on. Guests who do not meet these requirements can submit reservation requests.",
                //           size: 1.7,color:Color(greyText),lineSpacing: 1.75),
                //     ]),
                //   ),
                // ],
                // SizedBox(height: heightSpace(3)),
                // ViewsCommon.switchButton("Good record",toogle: c.goodRecord.value,onConfirm:()=>c.goodRecord.toggle(),fontSize: 2.1),
                // SizedBox(height: heightSpace(1.5)),
                // RichText(
                //   text: TextSpan(
                //       text: "Approve only guests who have previously stayed on Airbnb without any incidents or negative reviews. ",
                //       style: TextStyle(
                //           color: const Color(greyText),
                //           fontSize: heightSpace(1.7),height: 1.5),
                //       children: <TextSpan>[
                //         TextSpan(
                //           text: "know more",
                //           style: TextStyle(color: Colors.black,decoration: TextDecoration.underline,fontSize:heightSpace(1.8)),
                //         )
                //       ]),
                // )
              ]),
          bottomNavigationBar: Container(
              height: heightSpace(10),
              padding: EdgeInsets.all(widthSpace(3.5)),
              alignment: Alignment.bottomRight,
              decoration: const BoxDecoration(
                  border: Border(top: BorderSide(color: Color(greyBorder)))),
              child: Obx(() => CommonButton(
                  title: Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .save,
                  isLoading: c.isLoading.value,
                  onPressed: ListingHelper.submitQuestion,
                  minimumSize: Size(widthSpace(30), heightSpace(6)),
                  backgroundBg: Colors.black)))),
    );
  }
}
