import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../components/common_button.dart';
import '../../../components/custom_text.dart';
import '../../../helperMethods/listing_helper/listing_helper.dart';
import '../../../helperMethods/translation_helper.dart';
import '../../../utils/constants.dart';
import '../../../utils/sizeconfig.dart';

class EditCancellation extends StatelessWidget {
  const EditCancellation({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: CircleAvatar(
              maxRadius: 15,
              backgroundColor: Colors.grey[200],
              child: const Icon(Icons.chevron_left, color: Colors.black)),
          onPressed: Get.back,
        ),
        backgroundColor: Colors.transparent,
        centerTitle: false,
        title: CustomText(
            Get.find<TranslationHelper>()
                .translations
                .hostDashboard
                .cancellationPolicy!,
            size: 2.4,
            weight: FontWeight.bold),
      ),
      body: Obx(
        () => SingleChildScrollView(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            renderRadio(
                'Flexible',
                Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .flexible!),
            CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .fullRefund1DayPriorToArrival,
                    size: 1.9,
                    lineSpacing: 1.8,
                    color: const Color(greyText))
                .paddingOnly(top: heightSpace(1), bottom: heightSpace(2.5)),

            renderRadio(
                'Moderate',
                Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .moderate!),
            CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .fullRefund5DayPriorToArrival,
                    size: 1.9,
                    lineSpacing: 1.8,
                    color: const Color(greyText))
                .paddingOnly(top: heightSpace(1), bottom: heightSpace(2.5)),

            renderRadio('Firm',
                Get.find<TranslationHelper>().translations.hostDashboard.firm!),
            CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .fullRefundUpto30DayBeforeUpdate,
                    size: 1.9,
                    lineSpacing: 1.8,
                    color: const Color(greyText))
                .paddingOnly(top: heightSpace(1), bottom: heightSpace(2.5)),

            renderRadio(
                'Strict',
                Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .strict!),
            CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .fullRefundForCancelWithin48Hours,
                    size: 1.9,
                    lineSpacing: 1.8,
                    color: const Color(greyText))
                .paddingOnly(top: heightSpace(1), bottom: heightSpace(2.5)),

            // HostRadio(
            //     title: Get.find<TranslationHelper>().translations.hostDashboard.flexible!,
            //     subTitle:Get.find<TranslationHelper>().translations.hostDashboard.fullRefund1DayPriorToArrival,
            //     value: "Flexible",
            //     isRadioFront: true,
            //     parent: ListingHelper.c.cancellation.value, onPressed:ListingHelper.changeCancellation),
            // SizedBox(height: heightSpace(1.5)),
            // HostRadio(
            //     title: Get.find<TranslationHelper>().translations.hostDashboard.moderate!,
            //     subTitle:Get.find<TranslationHelper>().translations.hostDashboard.fullRefund5DayPriorToArrival,
            //     value: "Moderate",
            //     isRadioFront: true,
            //     parent: ListingHelper.c.cancellation.value,
            //     onPressed: ListingHelper.changeCancellation),
            // SizedBox(height: heightSpace(1.5)),
            // HostRadio(
            //     title: Get.find<TranslationHelper>().translations.hostDashboard.firm!,
            //     subTitle:Get.find<TranslationHelper>().translations.hostDashboard.fullRefundUpto30DayBeforeUpdate,
            //     value: "Firm",
            //     isRadioFront: true,
            //     parent: ListingHelper.c.cancellation.value,
            //     onPressed: ListingHelper.changeCancellation),
            // SizedBox(height: heightSpace(1.5)),
            // HostRadio(
            //     title: Get.find<TranslationHelper>().translations.hostDashboard.strict!,
            //     subTitle:Get.find<TranslationHelper>().translations.hostDashboard.fullRefundForCancelWithin48Hours,
            //     value: "Strict",
            //     isRadioFront: true,
            //     parent: ListingHelper.c.cancellation.value,
            //     onPressed: ListingHelper.changeCancellation),
          ]).paddingAll(widthSpace(viewPadding)),
        ),
      ),
      bottomNavigationBar: BottomAppBar(
          height: heightSpace(10),
          padding: EdgeInsets.all(widthSpace(3.5)),
          child: Obx(
            () => CommonButton(
                title: Get.find<TranslationHelper>()
                    .translations
                    .usersProfile
                    .save,
                isLoading: ListingHelper.c.isLoading.value,
                onPressed: ListingHelper.submitCancellation,
                backgroundBg: const Color(themeColor),
                borderRadius: 7,
                verticalPadding: 1),
          )),
    );
  }

  renderRadio(String value, String title) {
    return Row(children: [
      Radio(
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          visualDensity: const VisualDensity(
              horizontal: VisualDensity.minimumDensity,
              vertical: VisualDensity.minimumDensity),
          value: value,
          groupValue: ListingHelper.c.cancellation.value,
          onChanged: ListingHelper.changeCancellation),
      SizedBox(width: widthSpace(1)),
      CustomText(title, size: 2.3),
    ]);
  }
}
