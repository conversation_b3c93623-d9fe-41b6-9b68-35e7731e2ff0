import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/floating_text_field.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
class EditOptInstantBook extends StatelessWidget{
  const EditOptInstantBook({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          leading:IconButton(icon: const Icon(Icons.chevron_left),onPressed: Get.back),
          elevation: 0,
        ),
      body: ListView(
        padding: EdgeInsets.all(widthSpace(viewPadding)),
        children: [
          const CustomText("How can guests book",size: 2.3,weight: FontWeight.w500).paddingOnly(bottom: heightSpace(2.5)),
          CustomText("Check-in window",size: 2.1),
          SizedBox(height: heightSpace(3)),
          borderContainer(Column(children: [
            FloatingTextField(labelText: "Reach After"),
            Divider(height: heightSpace(3),color: Colors.black),
            FloatingTextField(labelText: "Pre-arrival"),
          ])),
          SizedBox(height: heightSpace(4)),
          CustomText("Pre-Departure",size: 2.1),
          SizedBox(height: heightSpace(3.5)),
          FloatingTextField(labelText: "Pre-Departure",borderType: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8)
          ),contentPadding: EdgeInsets.symmetric(vertical: 27,horizontal:widthSpace(viewPadding))),
        ],
      ),
    );
  }
  borderContainer(Widget child,{isCalendar=false}){
    return Container(
        padding: EdgeInsets.symmetric(vertical: widthSpace(4)),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all()
        ),child: child);
  }
}