import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/floating_text_field.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../helperMethods/listing_helper/listing_helper.dart';
import '../../../helperMethods/translation_helper.dart';

class EditPrice extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return PopScope(
      onPopInvoked: (pop) async {
        if (pop) {
          ListingHelper.setPrice();
        }
      },
      child: Scaffold(
          appBar: AppBar(
            leading: IconButton(
                icon: const Icon(Icons.chevron_left),
                onPressed: () {
                  ListingHelper.setPrice();
                  Get.back();
                }),
            elevation: 0,
          ),
          body: ListView(
              padding: EdgeInsets.all(widthSpace(viewPadding)),
              children: [
                CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .listingCalendar
                            .price,
                        size: 2.3,
                        weight: FontWeight.w500)
                    .paddingOnly(bottom: heightSpace(2.5)),
                for (String priceField
                    in ViewsCommon.priceControllers.keys) ...[
                  CustomText(
                          priceField == "price"
                              ? Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .pricePerNight
                              : "${Get.find<TranslationHelper>().translations.listing.priceOn} ${Get.find<TranslationHelper>().translations.general.toJson()[priceField]}",
                          size: 2.2,
                          weight: FontWeight.w500)
                      .paddingOnly(
                          top: heightSpace(2), bottom: heightSpace(.5)),
                  FloatingTextField(
                      controller: ViewsCommon.priceControllers[priceField],
                      labelText: "SAR",
                      inputType: TextInputType.number,
                      formatters: [
                        LengthLimitingTextInputFormatter(4),
                        FilteringTextInputFormatter.digitsOnly
                      ],
                      borderType: const UnderlineInputBorder()),
                ],
                // const CustomText("How often do you want to host?",size: 2.3,weight: FontWeight.w500).paddingOnly(top: heightSpace(2),bottom: heightSpace(1.5)),
                // const CustomText("This does not change availability, but prices will be adjusted to enable you to",size: 1.7,lineSpacing: 1.8,color: Color(greyText)),
                // Container(
                //   margin: EdgeInsets.symmetric(vertical: heightSpace(2)),
                //   padding: const EdgeInsets.only(bottom: 15),
                //     decoration: const BoxDecoration(
                //       border: Border(bottom: BorderSide())
                //     ),
                //     child: HostRadio<bool>(title: "Whenever possible", value: false, parent: c.partTime.value, onPressed: (val){
                //       c.partTime.value = val;
                //     },fontSize: 2.0)),
                // Container(
                //     padding: const EdgeInsets.only(bottom: 10),
                //     decoration: const BoxDecoration(
                //         border: Border(bottom: BorderSide())),
                //     child: HostRadio<bool>(title: "Part time", value: true, parent: c.partTime.value, onPressed: (val){
                //       c.partTime.value = val;
                //     },fontSize: 2.0)),
                // CustomText("Why is this important?", underline: true).paddingOnly(top: heightSpace(2))
              ]),
          bottomNavigationBar: Container(
              margin: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              height: heightSpace(10.5),
              padding: EdgeInsets.symmetric(
                  horizontal: widthSpace(5), vertical: heightSpace(1.5)),
              decoration: const BoxDecoration(
                  border: Border(top: BorderSide(color: Color(greyBorder)))),
              child: Align(
                alignment: Alignment.centerRight,
                child: Obx(
                  () => CommonButton(
                      title: Get.find<TranslationHelper>()
                          .translations
                          .usersProfile
                          .save,
                      backgroundBg: Colors.black,
                      borderRadius: 5,
                      isLoading: c.isLoading.value,
                      minimumSize: Size.fromWidth(widthSpace(25)),
                      onPressed: ListingHelper.submitPrice),
                ),
              ))),
    );
  }
}
