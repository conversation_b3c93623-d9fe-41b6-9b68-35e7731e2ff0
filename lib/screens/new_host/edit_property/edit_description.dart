import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/floating_text_field.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../helperMethods/listing_helper/listing_helper.dart';
import '../../../helperMethods/translation_helper.dart';

class EditDescription extends StatelessWidget {
  const EditDescription({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: SizedBox(
        height: heightSpace(70),
        child: BottomSheet(
            builder: (context) {
              return SingleChildScrollView(
                child: Form(
                  key: ListingHelper.c.descriptionForm,
                  child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Align(
                            alignment: Alignment.topRight,
                            child: InkWell(
                                onTap: Get.back,
                                child: const Icon(Icons.clear))),
                        SizedBox(height: heightSpace(2)),
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .listing
                                .writeDescriptionOfDwelling,
                            size: 2.3,
                            weight: FontWeight.bold),
                        SizedBox(height: heightSpace(3)),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const CustomText("عربي",
                                color: Color(greyText), size: 1.9),
                            CommonButton(
                                title: Get.find<TranslationHelper>()
                                    .translations
                                    .listing
                                    .translateToEnglish,
                                onPressed: () => ListingHelper.c.translate(
                                    "en",
                                    ListingHelper.c.description,
                                    ListingHelper.c.descriptionAr),
                                fontSize: 1.75,
                                backgroundBg: Colors.black)
                          ],
                        ),
                        FloatingTextField(
                          controller: ListingHelper.c.descriptionAr,
                          labelText: "أدخل الوصف الخاص بك هنا",
                          validator: (val) => val!.trim().isEmpty
                              ? Get.find<TranslationHelper>()
                                  .translations
                                  .jqueryValidation
                                  .required
                              : GlobalHelper.checkStringIsArabicUsingReg(val)
                                  ? null
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .jqueryValidation
                                      .textInArabic,
                          borderType: OutlineInputBorder(
                              borderSide: const BorderSide(),
                              borderRadius: BorderRadius.circular(10)),
                          lines: 5,
                          fontSize: 2.6,
                        ),
                        SizedBox(height: heightSpace(3)),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const CustomText("English",
                                color: Color(greyText), size: 1.9),
                            CommonButton(
                                title: Get.find<TranslationHelper>()
                                    .translations
                                    .listing
                                    .translateToArabic,
                                onPressed: () => ListingHelper.c.translate(
                                    "ar",
                                    ListingHelper.c.description,
                                    ListingHelper.c.descriptionAr),
                                fontSize: 1.75,
                                backgroundBg: Colors.black)
                          ],
                        ),
                        FloatingTextField(
                          controller: ListingHelper.c.description,
                          labelText: "Enter your description here",
                          validator: (val) => val!.trim().isEmpty
                              ? Get.find<TranslationHelper>()
                                  .translations
                                  .jqueryValidation
                                  .required
                              : val.trim().length < 20
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .listing
                                      .descriptionMinValidation
                                  : GlobalHelper.checkStringIsEnglishUsingReg(
                                          val)
                                      ? null
                                      : Get.find<TranslationHelper>()
                                          .translations
                                          .jqueryValidation
                                          .textInEnglish,
                          borderType: OutlineInputBorder(
                              borderSide: const BorderSide(),
                              borderRadius: BorderRadius.circular(10)),
                          lines: 5,
                          fontSize: 2.6,
                        ),
                        Container(
                          padding: const EdgeInsets.only(top: 15),
                          decoration: const BoxDecoration(
                              border: Border(
                                  top: BorderSide(color: Color(greyBorder)))),
                          child: CommonButton(
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .save,
                              minimumSize: Size.fromHeight(heightSpace(6)),
                              isLoading: ListingHelper.c.isLoading.value,
                              onPressed: ListingHelper.c.submitDescription,
                              backgroundBg: Colors.black,
                              borderRadius: 5),
                        )
                      ]).paddingAll(widthSpace(viewPadding)),
                ),
              );
            },
            onClosing: () {}),
      ),
    );
  }
}
