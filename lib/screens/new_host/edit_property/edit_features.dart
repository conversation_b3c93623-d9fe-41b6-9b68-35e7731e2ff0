import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/new_radio.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/helperMethods/listing_helper/listing_helper.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../helperMethods/search_helper.dart';

class EditFeatures extends StatelessWidget {
  const EditFeatures({super.key});
  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return Obx(
      () => Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: CircleAvatar(
                  maxRadius: 15,
                  backgroundColor: Colors.grey[200],
                  child: const Icon(Icons.chevron_left, color: Colors.black)),
              onPressed: Get.back,
            ),
            backgroundColor: Colors.transparent,
            centerTitle: false,
            title: CustomText(
                Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .amenitiesOffered ??
                    '',
                size: 2.4,
                weight: FontWeight.bold),
          ),
          body: ListView(
              padding: EdgeInsets.all(widthSpace(viewPadding)),
              children: [
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .tellGuestsAboutFeatureInYourSpace!,
                    color: const Color(greyText)),
                SizedBox(height: heightSpace(3)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .essential,
                    color: Colors.black54,
                    weight: FontWeight.w500),
                // SizedBox(height: heightSpace(1)),
                // const CustomText("These are the amenities that guests usually look for in a stay.",
                //     color: Colors.black54, size: 1.9),
                SizedBox(height: heightSpace(3)),
                for (var item
                    in SearchHelper.c.filters.value?.amenities ?? []) ...[
                  NewRadio(
                      title: Get.locale?.languageCode == "ar"
                          ? item.titleAr
                          : item.title,
                      icon: item.iconImage,
                      // trailing:renderRadio(item.isChecked),
                      // subtitle: (item?.description??"").isEmpty?null:item?.description,
                      // borderColor: const Color(greyBorder),
                      onChanged: () {
                        item.isChecked = !item.isChecked;
                        SearchHelper.c.filters.refresh();
                      },
                      marginPadding: heightSpace(1.5),
                      selected: item.isChecked),
                ],
                // SizedBox(height: heightSpace(3)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .propertySingle
                        .safetyFeature,
                    color: Colors.black54,
                    weight: FontWeight.w500),
                SizedBox(height: heightSpace(3)),
                for (var item
                    in SearchHelper.c.filters.value?.safetyAmenities ?? []) ...[
                  NewRadio(
                      title: Get.locale?.languageCode == "ar"
                          ? item.titleAr
                          : item.title,
                      icon: item.iconImage,
                      // subtitle: (item?.description??"").isEmpty?null:item?.description,
                      onChanged: () {
                        item.isChecked = !item.isChecked;
                        SearchHelper.c.filters.refresh();
                      },
                      selected: item.isChecked,
                      marginPadding: heightSpace(1.5)),
                ],
              ]),
          bottomNavigationBar: BottomAppBar(
              height: heightSpace(10),
              padding: EdgeInsets.symmetric(
                  horizontal: widthSpace(5), vertical: heightSpace(1.5)),
              child: CommonButton(
                  title: Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .save,
                  backgroundBg: const Color(themeColor),
                  borderRadius: 7,
                  isLoading: c.isLoading.value,
                  verticalPadding: 1,
                  onPressed: ListingHelper.submitAmenities))),
    );
  }

  renderRadio(bool value) {
    return Row(children: [
      Container(
          padding: EdgeInsets.all(widthSpace(1)),
          decoration: BoxDecoration(
              color: value ? Colors.white : Colors.black,
              shape: BoxShape.circle,
              border: Border.all()),
          child: Icon(Icons.close,
              color: value ? Colors.black : Colors.white,
              size: widthSpace(4.5))),
      SizedBox(width: widthSpace(3)),
      Container(
          padding: EdgeInsets.all(widthSpace(1)),
          decoration: BoxDecoration(
              color: value ? Colors.black : Colors.white,
              shape: BoxShape.circle,
              border: Border.all()),
          child: Icon(Icons.check,
              color: value ? Colors.white : Colors.black,
              size: widthSpace(4.5)))
    ]);
  }
}
