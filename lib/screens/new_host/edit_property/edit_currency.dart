import 'package:darent/components/host/host_radio.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class EditCurrency extends StatelessWidget {
  const EditCurrency({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            icon: const Icon(Icons.chevron_left), onPressed: Get.back),
        elevation: 0,
        title: Text(Get.find<TranslationHelper>()
            .translations
            .hostDashboard
            .listingCurrency!),
      ),
      body: Column(children: [
        HostRadio(title: "SAR", value: true, parent: true, onPressed: (val) {})
      ]).paddingAll(widthSpace(viewPadding)),
    );
  }
}
