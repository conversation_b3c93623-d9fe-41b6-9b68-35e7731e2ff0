import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/floating_text_field.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../helperMethods/translation_helper.dart';

class EditAddress extends StatelessWidget {
  const EditAddress({super.key});

  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return Scaffold(
      appBar: AppBar(
        elevation: 0.0,
        leading: Icon<PERSON>utton(
            icon: const Icon(Icons.chevron_left), onPressed: Get.back),
      ),
      body:
          ListView(padding: EdgeInsets.all(widthSpace(viewPadding)), children: [
        const CustomText("Address and internal name",
            size: 2.3, weight: FontWeight.w500),
        Si<PERSON><PERSON><PERSON>(height: heightSpace(4)),
        const CustomText("Residence address", size: 2.1),
        SizedBox(height: heightSpace(1.5)),
        RichText(
          text: TextSpan(
              text:
                  "Your ad should highlight what makes your home stand out From the rest. ",
              style: TextStyle(
                  color: const Color(greyText),
                  fontSize: heightSpace(1.55),
                  height: 1.5),
              children: const <TextSpan>[
                TextSpan(
                  text: "Read the list guidelines to write a list.",
                  style: TextStyle(
                      color: Colors.black,
                      decoration: TextDecoration.underline),
                )
              ]),
        ),
        SizedBox(height: heightSpace(3.5)),
        Form(
          key: c.addressKey,
          child: FloatingTextField(
              controller: c.address,
              labelText: "The address",
              validator: (v) => (v?.isEmpty ?? true)
                  ? Get.find<TranslationHelper>()
                      .translations
                      .jqueryValidation
                      .required
                  : null,
              borderType: const OutlineInputBorder(),
              length: 200,
              lines: 7),
        ),
        // SizedBox(height: heightSpace(2)),
        // CustomText("Your ad title should highlight what makes your niche stand out from the crowd.",color: Color(greyText),size: 1.55),
        // SizedBox(height: heightSpace(4.5)),
        // CustomText("Internal name",size: 2.1),
        // SizedBox(height: heightSpace(2)),
        // FloatingTextField(labelText: "Internal name",borderType: OutlineInputBorder()),
        // SizedBox(height: heightSpace(1)),
        // CustomText("Only you can see this",color: Color(greyText),size: 1.55),
      ]),
      bottomNavigationBar: Container(
          margin:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          height: heightSpace(11),
          alignment: Alignment.centerRight,
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(viewPadding), vertical: widthSpace(4)),
          decoration: const BoxDecoration(
              border: Border(top: BorderSide(color: Color(greyBorder)))),
          child: Obx(
            () => CommonButton(
                title: "Save",
                backgroundBg: Colors.black,
                isLoading: c.isLoading.value,
                minimumSize: Size.fromWidth(widthSpace(30)),
                horizontalPadding: 6,
                onPressed: null),
          )),
    );
  }
}
