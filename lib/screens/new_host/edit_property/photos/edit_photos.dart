import 'dart:io';
import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/listing_helper/listing_helper.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/screens/new_host/edit_property/photos/edit_cover.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../helperMethods/translation_helper.dart';
import '../../../../models/host/property_photo.dart';

class EditPhotos extends StatelessWidget {
  const EditPhotos({super.key});
  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: CircleAvatar(
              maxRadius: 15,
              backgroundColor: Colors.grey[200],
              child: const Icon(Icons.chevron_left, color: Colors.black)),
          onPressed: Get.back,
        ),
        backgroundColor: Colors.transparent,
        centerTitle: false,
        title: CustomText(
            Get.find<TranslationHelper>().translations.hostDashboard.photos,
            size: 2.4,
            weight: FontWeight.bold),
      ),
      body: Obx(
        () {
          // String coverImage = (c.dwellingDetail!.propertyPhotos!
          //             .firstWhereOrNull((e) => e.coverPhoto == 1) ??
          //         c.dwellingDetail!.propertyPhotos![0])
          //     .image;
          return c.isLoading.value
              ? Center(
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset("assets/loader.gif", width: widthSpace(15))
                      ]),
                )
              : ListView(
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: heightSpace(1)),
                  children: [
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .addSomePhoto,
                          color: const Color(greyText)),
                      SizedBox(height: heightSpace(3)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .hostDashboard
                                  .coverPhoto,
                              color: Colors.black87,
                              size: 1.9),
                          InkWell(
                              onTap: () => Get.to(() => const EditCover()),
                              child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 10),
                                  child: CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .hostDashboard
                                          .changeCover,
                                      color: Colors.black87,
                                      size: 1.80,
                                      underline: true)))
                        ],
                      ),
                      // Container(
                      //     height: heightSpace(37),
                      //     alignment: Alignment.topLeft,
                      //     decoration: BoxDecoration(
                      //         image: DecorationImage(
                      //             image: coverImage.contains("images/")
                      //                 ? NetworkImage("$baseUrl/$coverImage")
                      //                 : FileImage(File(coverImage))
                      //                     as ImageProvider,
                      //             fit: BoxFit.fitWidth)),
                      //     child: Container(
                      //         height: widthSpace(6),
                      //         width: widthSpace(25),
                      //         margin: const EdgeInsets.only(left: 13, top: 13),
                      //         decoration: BoxDecoration(
                      //           color: Colors.white.withOpacity(.5),
                      //           borderRadius: BorderRadius.circular(4),
                      //         ),
                      //         child: Center(
                      //             child: CustomText(
                      //                 Get.find<TranslationHelper>().translations.hostDashboard.coverPhoto,
                      //                 color: Colors.white)))),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     const CustomText("Photo order",color: Colors.black87,size: 1.9),
                      //     InkWell(
                      //         onTap:()=>Get.to(()=>const EditOrder()),
                      //         child: const Padding(
                      //             padding: EdgeInsets.symmetric(vertical:10),
                      //             child: CustomText("Change",color: Colors.black87,size: 1.80, underline: true)))
                      //   ],
                      // ),
                      GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  crossAxisSpacing: widthSpace(3),
                                  mainAxisSpacing: widthSpace(3),
                                  childAspectRatio: .85),
                          itemBuilder: (context, index) {
                            int i = index -
                                (c.dwellingDetail?.propertyPhotos?.length ?? 0);
                            bool isApproved = i < 0;
                            int realIndex = isApproved ? index : i;
                            bool isLast = index ==
                                ((c.dwellingDetail?.propertyPhotos?.length ??
                                        0) +
                                    c.tempPhotos.length);
                            PropertyPhoto? item;
                            if (!isLast) {
                              item = isApproved
                                  ? c.dwellingDetail!.propertyPhotos![realIndex]
                                  : c.tempPhotos[realIndex];
                            }
                            return isLast
                                ? InkWell(
                                    onTap: c.addPhotos,
                                    borderRadius: BorderRadius.circular(12),
                                    child: Container(
                                      padding: EdgeInsets.all(widthSpace(2)),
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                              color: const Color(greyBorder))),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          const Icon(Icons.add_circle_outline,
                                              color: Color(greyText), size: 27),
                                          SizedBox(height: heightSpace(1)),
                                          CustomText(
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .listing
                                                  .addPhotos,
                                              size: 1.9,
                                              weight: FontWeight.w500,
                                              color: const Color(greyText)),
                                        ],
                                      ),
                                    ))
                                : Container(
                                    alignment: item!.softDelete
                                        ? null
                                        : Alignment.topRight,
                                    decoration: BoxDecoration(
                                        border: Border.all(
                                            color: const Color(greyBorder)),
                                        borderRadius: BorderRadius.circular(12),
                                        image: DecorationImage(
                                            image: item.image.contains('property/')
                                                ? GlobalHelper.buildNetworkImageProvider(url: item.image,)
                                                : FileImage(File(item.image))
                                                    as ImageProvider)),
                                    child: item.remove == '1'
                                        ? Container(
                                            width: double.maxFinite,
                                            height: double.maxFinite,
                                            color: Colors.black.withOpacity(.5),
                                            child: Icon(Icons.block,
                                                color: Colors.white))
                                        : item.softDelete
                                            ? Container(
                                                color: Colors.black
                                                    .withOpacity(.5),
                                                child: IconButton(
                                                    color: Colors.white,
                                                    icon: const Icon(Icons
                                                        .keyboard_return_rounded),
                                                    onPressed: () =>
                                                        c.removePhoto(realIndex, isApproved)))
                                            : Row(
                                                mainAxisAlignment:
                                                    item.coverPhoto == 1
                                                        ? MainAxisAlignment
                                                            .spaceBetween
                                                        : MainAxisAlignment.end,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  if (item.coverPhoto == 1)
                                                    Container(
                                                        height: widthSpace(6),
                                                        width: widthSpace(25),
                                                        margin: const EdgeInsets
                                                            .only(
                                                            left: 13, top: 13),
                                                        decoration:
                                                            BoxDecoration(
                                                          color: Colors.grey
                                                              .withOpacity(.6),
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(4),
                                                        ),
                                                        child: Center(
                                                            child: CustomText(
                                                                Get.find<
                                                                        TranslationHelper>()
                                                                    .translations
                                                                    .hostDashboard
                                                                    .coverPhoto,
                                                                color: Colors
                                                                    .white))),
                                                  IconButton(
                                                      icon: const Icon(
                                                          Icons.cancel,
                                                          color:
                                                              Color(greyText)),
                                                      onPressed: () =>
                                                          c.removePhoto(
                                                              realIndex,
                                                              isApproved)),
                                                ],
                                              ));
                          },
                          itemCount:
                              ((c.dwellingDetail!.propertyPhotos?.length ?? 0) +
                                      c.tempPhotos.length) +
                                  1)
                    ]);
        },
      ),
      bottomNavigationBar: BottomAppBar(
          height: heightSpace(10),
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(5), vertical: heightSpace(1.5)),
          child: Obx(
            () => CommonButton(
                title: Get.find<TranslationHelper>()
                    .translations
                    .usersProfile
                    .save,
                backgroundBg: const Color(themeColor),
                borderRadius: 7,
                isLoading: c.isLoading.value,
                verticalPadding: 1,
                onPressed: ListingHelper.uploadPhotos),
          )),
    );
  }
}
