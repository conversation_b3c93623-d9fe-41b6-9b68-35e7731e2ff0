import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/plus_minus.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class EditBasics extends StatelessWidget {
  const EditBasics({super.key});

  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return Obx(
      () => Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          centerTitle: false,
          title: CustomText(
              Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .roomsAndSpaces ??
                  '',
              size: 2.4,
              weight: FontWeight.bold),
          leading: IconButton(
            icon: CircleAvatar(
                maxRadius: 15,
                backgroundColor: Colors.grey[200],
                child: const Icon(Icons.chevron_left, color: Colors.black)),
            onPressed: Get.back,
          ),
          // title: CustomText("Step ${c.basicStep} of 3",size: 1.9,weight: FontWeight.w500),
          // actions: [
          //   InkWell(
          //     onTap:(){},
          //     child: const Padding(
          //         padding:EdgeInsets.all(15),
          //         child: CustomText(
          //             "Save and exit",
          //             size: 1.8,
          //             underline: true)),
          //   )
          // ],
          // bottom: PreferredSize(
          //     preferredSize: const Size(double.maxFinite,7),
          //     child:Container(
          //       width: double.maxFinite,
          //       alignment: Alignment.centerLeft,
          //   margin: EdgeInsets.symmetric(horizontal: widthSpace(viewPadding)),
          //   height: 7,
          //   decoration: BoxDecoration(
          //     color: const Color(greyBorder),
          //     borderRadius: BorderRadius.circular(20)),
          //       child: AnimatedContainer(
          //       height: 7,
          //         alignment: Alignment.centerLeft,
          //         width: widthSpace(31.5*c.basicStep),
          //         decoration: BoxDecoration(
          //             color: Colors.black,
          //             borderRadius: BorderRadius.circular(20)
          //         ), duration: const Duration(milliseconds: 200),
          //     ),
          // ))
        ),
        body: ListView(
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(viewPadding), vertical: heightSpace(1)),
            children: [
              // Divider(),
              // CustomText(Get.find<TranslationHelper>().translations.hostListing.basicsTitle,size: 2.3,weight: FontWeight.w500),
              // SizedBox(height: heightSpace(2)),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostListing
                      .basicsDescription,
                  size: 1.9,
                  color: const Color(greyText),
                  weight: FontWeight.w500,
                  lineSpacing: 1.5),
              Container(
                  padding: EdgeInsets.symmetric(vertical: heightSpace(2.5)),
                  decoration: const BoxDecoration(
                      border:
                          Border(bottom: BorderSide(color: Color(greyBorder)))),
                  child: PlusMinus(
                    title: Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .bedroom!,
                    value: c.propertyBedrooms,
                  )),
              if (c.propertyBedrooms.value > 0) ...[
                Container(
                    padding: EdgeInsets.symmetric(vertical: heightSpace(2.5)),
                    decoration: const BoxDecoration(
                        border: Border(
                            bottom: BorderSide(color: Color(greyBorder)))),
                    child: PlusMinus(
                      title: Get.find<TranslationHelper>()
                          .translations
                          .propertySingle
                          .singleBeds!,
                      value: c.propertySingleBeds,
                      icon: 'single_bed',
                    )),
                Container(
                    padding: EdgeInsets.symmetric(vertical: heightSpace(2.5)),
                    decoration: const BoxDecoration(
                        border: Border(
                            bottom: BorderSide(color: Color(greyBorder)))),
                    child: PlusMinus(
                      title: Get.find<TranslationHelper>()
                          .translations
                          .propertySingle
                          .doubleBeds!,
                      value: c.propertyDoubleBeds,
                      icon: 'double_bed',
                    )),
              ],
              Container(
                  padding: EdgeInsets.symmetric(vertical: heightSpace(2.5)),
                  decoration: const BoxDecoration(
                      border:
                          Border(bottom: BorderSide(color: Color(greyBorder)))),
                  child: PlusMinus(
                    title: Get.find<TranslationHelper>()
                        .translations
                        .propertySingle
                        .bathroom!,
                    value: c.propertyBathrooms,
                    minValue: 0.5,
                    icon: 'bathtub',
                  )),
            ]),
        bottomNavigationBar: BottomAppBar(
            height: heightSpace(10),
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(5), vertical: heightSpace(1.5)),
            child: CommonButton(
                title: Get.find<TranslationHelper>()
                    .translations
                    .usersProfile
                    .save,
                backgroundBg: Color(themeColor),
                borderRadius: 7,
                isLoading: c.isLoading.value,
                verticalPadding: 1,
                onPressed: c.submitNumberOfRoom)),
      ),
    );
  }
}
