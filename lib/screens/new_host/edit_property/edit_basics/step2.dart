import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Step2 extends StatelessWidget{
  @override
  Widget build(BuildContext context) {
    return Obx(()=>ListView(
          padding:EdgeInsets.all(widthSpace(viewPadding)),
          children: [
            Divider(),
            const CustomText("Check back now for these details",size: 2.3,weight: FontWeight.w500),
            SizedBox(height: heightSpace(2)),
            const CustomText(" Help guests know what to expect when they book your"
                "space. Please add the beds that you previously specified"
                "in the shared spaces as being included  specific room, suc"
                "as the living room or studio",
                size: 1.9,color: Color(greyText)),
            Container(
                margin: EdgeInsets.only(bottom: heightSpace(3)),
                padding: EdgeInsets.symmetric(vertical:heightSpace(2.5)),
                decoration: const BoxDecoration(border: Border(bottom: BorderSide(color: Color(greyBorder)))),
                child:CustomText("Bedroom",color: Colors.black87)),
            // HostListTile(title: "Common spaces",subtitle: "Guests may share this space with me, other guests, or someone else.",
            // trailing: HostSwitch(value:c.bedroomSpace.value),borderColor: const Color(greyBorder)),
            // HostListTile(title: "Sleeping arrangements",trailing: "Edit",borderColor: const Color(greyBorder))
          ]),
    );
  }
}