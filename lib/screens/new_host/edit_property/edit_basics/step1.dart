import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/plus_minus.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Step1 extends StatelessWidget{
  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return Obx(()=>ListView(
          padding: EdgeInsets.all(widthSpace(viewPadding)),
          children: [
            // Divider(),
            const CustomText("What areas can guests use?",size: 2.3,weight: FontWeight.w500),
            SizedBox(height: heightSpace(2)),
            const CustomText("Add all the rooms that guests can use - even the common areas. Note that none of the details previously defined for this new feature are saved in your listing and we will only update them if you change any of these details yourself.",
                size: 1.9,color: Color(greyText)),
            Container(
                padding: EdgeInsets.symmetric(vertical:heightSpace(2.5)),
                decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: Color(greyBorder)))),
                child:PlusMinus(title: "Bedrooms", value: c.propertyBedrooms)),
            if(c.propertyBedrooms.value>0)...[
              Container(
                  padding: EdgeInsets.symmetric(vertical:heightSpace(2.5)),
                  decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(color: Color(greyBorder)))
                  ),
                  child:PlusMinus(title: "Single Beds", value: c.propertySingleBeds)),
              Container(
                  padding: EdgeInsets.symmetric(vertical:heightSpace(2.5)),
                  decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(color: Color(greyBorder)))
                  ),
                  child:PlusMinus(title: "Double Beds", value: c.propertyDoubleBeds)),
            ],
            Container(
                padding: EdgeInsets.symmetric(vertical:heightSpace(2.5)),
                decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: Color(greyBorder)))
                ),
                child:PlusMinus(title: "Bathrooms", value: c.propertyBathrooms)),
      ]),
    );
  }
}