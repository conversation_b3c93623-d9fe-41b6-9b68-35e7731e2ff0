import 'package:darent/components/custom_text.dart';
import 'package:darent/components/warning_dialog.dart';
import 'package:darent/screens/host/calendar_export.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../components/host/calendar_widget.dart';
import '../../../components/views_common.dart';
import '../../../helperMethods/listing_helper/listing_helper.dart';
import '../../../helperMethods/translation_helper.dart';
import '../../../utils/constants.dart';
import '../../../utils/sizeconfig.dart';
import '../../host/calendar_import.dart';

class EditIcal extends StatelessWidget {
  const EditIcal({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0.0,
        leading: IconButton(
            icon: const Icon(Icons.chevron_left), onPressed: Get.back),
        title: Text(Get.find<TranslationHelper>()
            .translations
            .hostDashboard
            .availability!),
      ),
      body: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        SizedBox(height: heightSpace(1)),
        CalendarWidget(
            title:
                Get.find<TranslationHelper>().translations.ical.importCalendar,
            titleFontSize: 2.0,
            subTitleFontSize: 1.85,
            function: () => Get.to(() => CalendarImport())),
        SizedBox(height: heightSpace(3)),
        CalendarWidget(
            title:
                Get.find<TranslationHelper>().translations.ical.exportCalendar,
            titleFontSize: 2.0,
            subTitleFontSize: 1.85,
            function: exportICal
            //     (){
            //   ViewsCommon.showModalBottom(CalendarExport(id:ListingHelper.c.dwellingDetail?.id??0));
            // }
            ),
        SizedBox(height: heightSpace(3)),
        CustomText(
            Get.find<TranslationHelper>()
                .translations
                .hostDashboard
                .allCalImports,
            weight: FontWeight.bold),
        SizedBox(height: heightSpace(2)),
        Expanded(
            child: Obx(
          () => ListView.separated(
              itemBuilder: item,
              separatorBuilder: (c, i) => SizedBox(height: heightSpace(3)),
              itemCount: ListingHelper.c.dwellingDetail!.icalImports.length),
        ))
      ]).paddingAll(widthSpace(viewPadding)),
    );
  }

  exportICal() {
    if (!ListingHelper.c.isLoading.value) {
      ListingHelper.calendarExport(ListingHelper.c.dwellingDetail?.id)
          .then((link) {
        if (link != null) {
          Get.bottomSheet(BottomSheet(
              onClosing: () {},
              builder: (context) => CalendarExport(link: link)));
        }
      });
    }
  }

  Widget item(c, i) {
    final icalImport = ListingHelper.c.dwellingDetail!.icalImports[i];
    return Container(
      padding: EdgeInsets.all(widthSpace(viewPadding)),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Color(greyBorder)),
        borderRadius: BorderRadius.circular(14),
        boxShadow: ViewsCommon.boxShadow,
      ),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          CustomText(icalImport.icalendarName, weight: FontWeight.w500),
          iconButton(Colors.black87, Icons.copy, () {
            Clipboard.setData(
                ClipboardData(text: icalImport.icalendarUrl ?? ''));
            ViewsCommon.showSnackbar(
                Get.find<TranslationHelper>()
                    .translations
                    .listing
                    .copiedClipboard!,
                displayTime: 750);
          }),
        ]),
        CustomText(
          icalImport.icalendarUrl,
          size: 1.9,
          color: Colors.black54,
          maxlines: 2,
          textOverflow: TextOverflow.ellipsis,
        ).paddingSymmetric(vertical: heightSpace(2)),
        Row(mainAxisAlignment: MainAxisAlignment.end, children: [
          iconButton(Colors.blueAccent, Icons.edit_calendar_outlined, () {
            ListingHelper.c.calendarUrl.text = icalImport.icalendarUrl ?? '';
            ListingHelper.c.calendarName.text = icalImport.icalendarName ?? '';
            ListingHelper.c.calenderColor.value = '#7FFFD4';
            Get.to(() => CalendarImport(item: icalImport));
          }),
          SizedBox(width: widthSpace(2)),
          iconButton(Color(warningColor), Icons.delete_outline_rounded, () {
            Get.dialog(WarningDialog(
              title: Get.find<TranslationHelper>()
                  .translations
                  .jqueryValidation
                  .areYouSureToDelete,
              description: Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .icalDeleteNote!,
              onConfirmed: () {
                Get.back();
                ListingHelper.deleteIcal(i);
              },
            ));
          }),
        ])
      ]),
    );
  }

  iconButton(color, icon, function) {
    return GestureDetector(
      onTap: function,
      child: CircleAvatar(
          backgroundColor: color.withOpacity(.07),
          radius: 20,
          child: Icon(icon, color: color, size: 18)),
    );
  }
}
