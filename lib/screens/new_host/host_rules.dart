import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/floating_text_field.dart';
import 'package:darent/components/host/plus_minus.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/listing_helper/listing_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../components/host/new_radio.dart';
import '../../helperMethods/search_helper.dart';
import '../../helperMethods/translation_helper.dart';

class HostRules extends StatelessWidget {
  const HostRules({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            icon: CircleAvatar(
                maxRadius: 15,
                backgroundColor: Colors.grey[200],
                child: const Icon(Icons.chevron_left, color: Colors.black)),
            onPressed: Get.back,
          ),
          backgroundColor: Colors.transparent,
          centerTitle: false,
          title: CustomText(
              Get.find<TranslationHelper>().translations.payment.houseRule,
              size: 2.4,
              weight: FontWeight.bold),
        ),
        body: Obx(
          () => ListView(
              padding: EdgeInsets.all(widthSpace(viewPadding)),
              children: [
                // CustomText(Get.find<TranslationHelper>().translations.hostDashboard.setHouseRules,size: 2.3,weight: FontWeight.w500).paddingOnly(bottom: heightSpace(2.5)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .setHouseRulesNote,
                    color: Colors.black54,
                    size: 1.9),
                SizedBox(height: heightSpace(3)),
                for (var item
                    in SearchHelper.c.filters.value?.houseRules ?? []) ...[
                  NewRadio(
                      title: Get.locale?.languageCode == "ar"
                          ? item.titleAr
                          : item.title,
                      icon: item.iconImage,
                      // trailing:renderRadio(item.isChecked),
                      // subtitle: (item?.description??"").isEmpty?null:item?.description,
                      // borderColor: const Color(greyBorder),
                      onChanged: () {
                        item.isChecked = !item.isChecked;
                        SearchHelper.c.filters.refresh();
                      },
                      marginPadding: heightSpace(1.5),
                      selected: item.isChecked),
                  // HostListTile(
                  //     title: Get.locale?.languageCode=="ar"?item.titleAr:item.title,
                  //     trailing:renderRadio(item.isChecked),
                  //     subtitle: (item?.description??"").isEmpty?null:item?.description,
                  //     borderColor: const Color(greyBorder),screen: (){
                  //   item.isChecked = !item.isChecked;
                  //   SearchHelper.c.filters.refresh();
                  // },marginPadding: heightSpace(1.5)),
                ],
                // HostListTile(
                //     title: "Pets Allowed",
                //     trailing:renderRadio(c.petsAllowed.value),
                //     subtitle: RichText(
                //       text: TextSpan(
                //           text: "You may decline to accept pets, but you must take reasonable steps to accommodate service animals. ",
                //           style: TextStyle(
                //               color: const Color(greyText),
                //               fontSize: heightSpace(1.7),height: 1.8),
                //           children: <TextSpan>[
                //             TextSpan(
                //               text: "know more",
                //               style: TextStyle(color: Colors.black,decoration: TextDecoration.underline,fontSize:heightSpace(1.8)),
                //             )
                //           ]),
                //     ),
                //     borderColor: const Color(greyBorder),screen: ()=>c.petsAllowed.toggle(),marginPadding: heightSpace(1.5)),
                // HostListTile(
                //     title: "Allowed events",
                //     trailing:renderRadio(c.allowedEvents.value),
                //     borderColor: const Color(greyBorder),screen: ()=>c.allowedEvents.toggle(),marginPadding: heightSpace(1.5)),
                // HostListTile(
                //     title: "Smoking and the use of electronic cigarettes are permitted",
                //     trailing:renderRadio(c.smoking.value),
                //     borderColor: const Color(greyBorder),screen: ()=>c.smoking.toggle(),marginPadding: heightSpace(1.5)),
                // HostListTile(
                //     title: "Quiet hours",
                //     trailing:renderRadio(c.quietHours.value),
                //     borderColor: const Color(greyBorder),screen: ()=>c.quietHours.toggle(),marginPadding: heightSpace(1.5)),
                // HostListTile(
                //     title: "Photography and commercial photography are permitted",
                //     trailing:renderRadio(c.photography.value),
                //     borderColor: const Color(greyBorder),screen: ()=>c.photography.toggle(),marginPadding: heightSpace(1.5)),
                // HostListTile(
                //     title: "Arrival and departure times",
                //     trailing:"Edit",
                //     subtitle: "Arrival between 15:00 - 22:00\nDeparture before 12:00",
                //     borderColor: const Color(greyBorder),screen: ()=>c.photography.toggle(),marginPadding: heightSpace(1.5)),
                // for(int i=0 ;i < SearchHelper.c.customHouseRules.length ; i++)...[
                //   HostListTile(
                //       title: SearchHelper.c.customHouseRules[i].title!,
                //       trailing: renderRadio(!SearchHelper.c.customHouseRules[i].isChecked),
                //       subtitle: (SearchHelper.c.customHouseRules[i].description??"").isEmpty?null:SearchHelper.c.customHouseRules[i].description,
                //       borderColor: const Color(greyBorder),screen: (){
                //     SearchHelper.c.customHouseRules[i].isChecked = !SearchHelper.c.customHouseRules[i].isChecked;
                //     SearchHelper.c.filters.refresh();
                //     SearchHelper.c.removeMyCustomHouseRule(i);
                //   },marginPadding: heightSpace(1.5)),
                // ],
                // will enable when API is ready
                // HostListTile(
                //     title: Get.find<TranslationHelper>().translations.hostDashboard.addCustomRule ?? "Add Your Custom HouseRule",
                //     borderColor: const Color(greyBorder),screen: (){
                //   SearchHelper.c.addMyCustomHouseRule();
                // },marginPadding: heightSpace(1.5)),

                // CustomText(Get.find<TranslationHelper>().translations.hostDashboard.numberOfGuests!,size: 2.15,weight: FontWeight.bold),
                // SizedBox(height: heightSpace(2)),

                // PlusMinus(title:Get.find<TranslationHelper>().translations.hostDashboard.adults!,value:ListingHelper.c.adults,minValue: 1),
                // SizedBox(height: heightSpace(2.5)),
                // PlusMinus(title:Get.find<TranslationHelper>().translations.hostDashboard.children!,value:ListingHelper.c.children,minValue: 0),

                // HostListTile(
                //     title: Get.find<TranslationHelper>().translations.hostDashboard.numberOfGuests!,
                //     trailing:Get.find<TranslationHelper>().translations.hostDashboard.edit,
                //     subtitle: "${c.dwellingDetail?.adultGuest??0} ${Get.find<TranslationHelper>().translations.hostDashboard.adults}, ${c.dwellingDetail?.childrenGuest??0} ${Get.find<TranslationHelper>().translations.hostDashboard.children}",
                //     borderColor: const Color(greyBorder),
                //     screen:guestsSheet,
                //     marginPadding: heightSpace(1.5)),
              ]),
        ),
        bottomNavigationBar: BottomAppBar(
            height: heightSpace(10),
            padding: EdgeInsets.all(widthSpace(3.5)),
            child: Obx(
              () => CommonButton(
                  title: Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .save,
                  isLoading: ListingHelper.c.isLoading.value,
                  onPressed: ListingHelper.submitAmenities,
                  backgroundBg: const Color(themeColor),
                  borderRadius: 7,
                  verticalPadding: 1),
            )));
  }

  renderRadio(bool value) {
    return Row(children: [
      Container(
          padding: const EdgeInsets.all(5),
          decoration: BoxDecoration(
              color: value ? Colors.white : Colors.black,
              shape: BoxShape.circle,
              border: Border.all(color: value ? Colors.grey : Colors.black)),
          child: Icon(Icons.close,
              color: value ? Colors.grey : Colors.white,
              size: widthSpace(4.5))),
      SizedBox(width: widthSpace(3)),
      Container(
          padding: const EdgeInsets.all(5),
          decoration: BoxDecoration(
              color: value ? Colors.black : Colors.white,
              shape: BoxShape.circle,
              border: Border.all(color: !value ? Colors.grey : Colors.black)),
          child: Icon(Icons.check,
              color: value ? Colors.white : Colors.grey, size: widthSpace(4.5)))
    ]);
  }

  guestsSheet() {
    ViewsCommon.showModalBottom(
        DraggableScrollableSheet(
            maxChildSize: .4,
            initialChildSize: .4,
            expand: false,
            builder: (BuildContext context, ScrollController scrollController) {
              return Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Align(
                        alignment: Alignment.topRight,
                        child: InkWell(
                            onTap: Get.back, child: const Icon(Icons.clear))),
                    SizedBox(height: heightSpace(2)),
                    PlusMinus(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .adults!,
                        value: ListingHelper.c.adults,
                        minValue: 1),
                    SizedBox(height: heightSpace(3)),
                    PlusMinus(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .children!,
                        value: ListingHelper.c.children,
                        minValue: 0),
                    const Spacer(),
                    Container(
                        padding: EdgeInsets.symmetric(vertical: heightSpace(2)),
                        decoration: const BoxDecoration(
                            border: Border(
                                top: BorderSide(color: Color(greyBorder)))),
                        child: Obx(
                          () => CommonButton(
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .save,
                              isLoading: ListingHelper.c.isLoading.value,
                              onPressed: ListingHelper.submitBasic,
                              minimumSize: Size.fromHeight(heightSpace(7)),
                              backgroundBg: Colors.black),
                        ))
                  ]).paddingAll(widthSpace(viewPadding));
            }), then: (p0) {
      ListingHelper.c.adults.value =
          ListingHelper.c.dwellingDetail?.adultGuest ?? 0;
      ListingHelper.c.children.value =
          ListingHelper.c.dwellingDetail?.childrenGuest ?? 0;
    });
  }

  additionalLaws() {
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        maxChildSize: .57,
        initialChildSize: .57,
        expand: false,
        builder: (BuildContext context, ScrollController scrollController) {
          return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Align(
                    alignment: Alignment.topRight,
                    child: InkWell(
                        onTap: () => Get.back(),
                        child: const Icon(Icons.clear))),
                SizedBox(height: heightSpace(2)),
                const CustomText("Additional laws"),
                SizedBox(height: heightSpace(2)),
                const CustomText(
                    "Guests will not be able to book your space if they start their stay on these days.",
                    size: 1.9,
                    color: Color(greyText)),
                SizedBox(height: heightSpace(2)),
                FloatingTextField(
                  labelText: "Your text here",
                  borderType: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10)),
                  lines: 5,
                ),
                const Spacer(),
                Container(
                    padding: EdgeInsets.symmetric(vertical: heightSpace(2)),
                    decoration: const BoxDecoration(
                        border:
                            Border(top: BorderSide(color: Color(greyBorder)))),
                    child: CommonButton(
                        title: "Save",
                        onPressed: () {},
                        minimumSize: Size.fromHeight(heightSpace(7)),
                        backgroundBg: Colors.black))
              ]).paddingAll(widthSpace(viewPadding));
        }));
  }
}
