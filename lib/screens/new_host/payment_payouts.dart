import 'package:darent/components/custom_text.dart';
import 'package:darent/screens/bank.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../helperMethods/translation_helper.dart';

class PaymentAndPayouts extends StatelessWidget {
  const PaymentAndPayouts({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
            onPressed: Get.back,
            icon: const Icon(
              Icons.chevron_left,
              size: 40,
            )),
        // actions: [
        //   Container(
        //     padding: EdgeInsets.all(15),
        //     child: CustomText("\$ USD",size: 2.2, weight: FontWeight.w500,underline: true))]
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(3.5)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .paymentAndPayouts,
                size: 3.2),
            // SizedBox(height: heightSpace(3.5),),
            // CustomText("Travel",size: 2.2,),
            SizedBox(
              height: heightSpace(3.5),
            ),
            // extraComponent("payment",name: Get.find<TranslationHelper>().translations.wallet.paymentMethods,function: (){
            //   Get.to(()=> const PaymentMethod());
            // }),
            SizedBox(height: heightSpace(3.5)),
            extraComponent(Icons.account_balance_outlined,
                name: Get.find<TranslationHelper>()
                    .translations
                    .sidenav
                    .bankAccount, function: () {
              Get.to(() => const BankAccount());
            }),
            // extraComponent("payment_list",name: "Your Payment", function:  (){
            //   Get.to(()=> const YourPayment());
            // }),
            // SizedBox(height: heightSpace(2.5),),
            // Divider(),
            //  SizedBox(height: heightSpace(3.5),),
            // CustomText("Hosting",size: 2.2,),
            // SizedBox(height: heightSpace(3.5),),
            // extraComponent("return_payment",name: "Return Payment Methods",function: (){
            //   Get.to(()=> const ReturnPayment());
            //
            // }),
            // SizedBox(height: heightSpace(3.5),),
            // extraComponent("transaction_history",name: "Transaction History", function: (){
            //   Get.to( ()=> TransactionHistory());
            // }),
            // SizedBox(height: heightSpace(3.5),),
          ],
          //]
        ),
      ),
    );
  }

  extraComponent(image, {required name, Widget? screen, function}) {
    return InkWell(
      onTap: function ??
          () {
            if (screen != null) {
              Get.to(() => screen);
            }
          },
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            image is String
                ? SvgPicture.asset("assets/$image.svg",
                    width: widthSpace(6), color: Colors.black)
                : Icon(image, color: Colors.black54, size: widthSpace(6)),
            SizedBox(width: widthSpace(7.5)),
            Expanded(
              child: CustomText(
                name,
                color: Colors.grey[800],
                weight: FontWeight.w500,
                size: 2.1,
              ),
            ),
            SizedBox(width: widthSpace(8)),
            const Icon(
              Icons.arrow_forward_ios,
              size: 20,
            )
          ]),
    );
  }
}
