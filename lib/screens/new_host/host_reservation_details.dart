import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/models/bookingModel.dart';
import 'package:darent/screens/new_host/info_box.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../helperMethods/search_helper.dart';

class HostReservationDetails extends StatelessWidget {
  final BookingModel data;
  const HostReservationDetails({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        toolbarHeight: heightSpace(10),
        leading: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            children: [
              IconButton(
                  icon: const Icon(Icons.chevron_left), onPressed: Get.back),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .reservationDetail,
                  size: 3.2,
                  weight: FontWeight.bold,
                  color: Colors.black),
            ],
          ),
        ),
        leadingWidth: widthSpace(100),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(widthSpace(viewPadding)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.symmetric(
                  horizontal: widthSpace(2), vertical: heightSpace(2)),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
                color: Colors.white,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ClipRRect(
                              borderRadius: BorderRadius.circular(45),
                              child: (data.profile ?? "").contains(".svg")
                                  ? GlobalHelper.buildNetworkSvgWidget(
                                url:data.profile??"",
                                height: widthSpace(19),
                                width: widthSpace(19),
                                defaultOption: Image.asset(
                                    "assets/default-image.png",
                                    width: widthSpace(19),
                                    height: widthSpace(19),
                                    fit: BoxFit.fill),)
                                  : GlobalHelper.resolveImageUrl(data.profile ??"").isNotEmpty
                                  ? Image(
                                  image: GlobalHelper.buildNetworkImageProvider(url: data.profile ??"",),
                                      height: widthSpace(19),
                                      width: widthSpace(19),
                                      fit: BoxFit.fill,
                              )
                                  : Container(
                                width: widthSpace(19),
                                height: widthSpace(19),
                                clipBehavior: Clip.antiAlias,
                                decoration: const BoxDecoration(
                                    color: Color(themeColor),
                                    shape: BoxShape.circle,
                                    image: DecorationImage(
                                        image: AssetImage(
                                            'assets/icons/d_logo.png'))),
                              )),
                          SizedBox(height: heightSpace(2.3)),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              CustomText("${data.userName}",
                                  size: 2.1, weight: FontWeight.w500),
                              if (data.phone != null &&
                                  data.phone!.isNotEmpty) ...[
                                SizedBox(height: heightSpace(2)),
                                InkWell(
                                    onTap: () => launchUrl(
                                        Uri.parse('tel:${data.phone}')),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        CustomText(data.phone,
                                            size: 1.8,
                                            color: Colors.grey,
                                            weight: FontWeight.normal),
                                        SizedBox(
                                          width: widthSpace(1),
                                        ),
                                        Icon(
                                          Icons.copy,
                                          size: widthSpace(3),
                                        ),
                                      ],
                                    )),
                              ],
                              //Divider(height: heightSpace(4)),
                            ],
                          ),
                        ]),
                  ),
                  Expanded(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.star,
                                  color: const Color(themeColor),
                                  size: widthSpace(6)),
                              SizedBox(
                                width: widthSpace(1),
                              ),
                              RichText(
                                text: TextSpan(
                                  text: "${data.userRating}",
                                  style: TextStyle(
                                    fontSize: heightSpace(2.0),
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black,
                                  ),
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: "/5",
                                        style: TextStyle(
                                            fontSize: heightSpace(1.5),
                                            color: Colors.black,
                                            fontWeight: FontWeight.normal)),
                                  ],
                                ),
                              )
                            ],
                          ),
                          SizedBox(height: heightSpace(0.7)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .account
                                  .avrageRating, // ??offlineTranslations?["view_profile"]
                              size: 1.5,
                              color: const Color(greyText),
                              weight: FontWeight.normal),
                          SizedBox(height: heightSpace(1.7)),
                          Row(
                            children: [
                              data.idAchieved!
                                  ? SvgPicture.asset(
                                      "assets/icons/host_new/verify.svg",
                                      width: widthSpace(5))
                                  : Icon(Icons.close,
                                      size: widthSpace(5),
                                      color: Color(warningColor)),
                              SizedBox(width: widthSpace(2)),
                              RichText(
                                text: TextSpan(
                                  text: data.idAchieved!
                                      ? Get.find<TranslationHelper>()
                                          .translations
                                          .account
                                          .verified
                                      : Get.find<TranslationHelper>()
                                          .translations
                                          .account
                                          .unVerified,
                                  style: TextStyle(
                                    fontSize: heightSpace(2.0),
                                    fontWeight: FontWeight.w500,
                                    color: Colors.black,
                                  ),
                                ),
                              )
                            ],
                          ),
                          SizedBox(height: heightSpace(0.7)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .account
                                  .identity, // ??offlineTranslations?["view_profile"]
                              size: 1.5,
                              color: const Color(greyText),
                              weight: FontWeight.normal),
                          SizedBox(height: heightSpace(1.7)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .account
                                  .joinedSince,
                              size: 1.5,
                              color: const Color(greyText),
                              weight: FontWeight.normal),
                          SizedBox(height: heightSpace(0.7)),
                          CustomText("${data.joined}",
                              size: 1.5,
                              color: const Color(greyText),
                              weight: FontWeight.normal),
                        ]),
                  ),
                ],
              ),
            ),
            SizedBox(height: heightSpace(2)),
            renderReservationDetails(),
            SizedBox(height: heightSpace(3)),
            renderPaymentDetails(),
            SizedBox(height: heightSpace(2)),
            if (data.bookingCode != null) ...[
              SizedBox(height: heightSpace(2)),
              Obx(
                () => InkWell(
                  onTap: () => SearchHelper.c.viewReceipt(data.bookingCode),
                  child: ViewsCommon.shadowContainer(
                    padding: viewPadding * 3,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(width: widthSpace(2)),
                        Expanded(
                            child: CustomText(
                                SearchHelper.c.lazyLoader.value
                                    ? Get.find<TranslationHelper>()
                                        .translations
                                        .listing
                                        .pleaseWait
                                    : "${Get.find<TranslationHelper>().translations.hostDashboard.vatInvoice}",
                                size: 2.0)),
                        SearchHelper.c.lazyLoader.value
                            ? const SizedBox(
                                height: 18,
                                width: 18,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2))
                            : const Icon(Icons.chevron_right, size: 20)
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  renderReservationDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
            Get.find<TranslationHelper>()
                .translations
                .hostDashboard
                .reservationDetail,
            size: 2.2,
            weight: FontWeight.bold),
        SizedBox(
          height: heightSpace(2),
        ),
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Expanded(
              child: InfoBoxHorizontal(
            name: CustomText(
              "${Get.find<TranslationHelper>().translations.hostReservation.adults} No.",
              size: 1.5,
              weight: FontWeight.normal,
              textAlign: TextAlign.start,
              maxlines: 1,
              textOverflow: TextOverflow.clip,
            ),
            value: RichText(
              text: TextSpan(
                text:
                    "${data.adults} ${Get.find<TranslationHelper>().translations.hostReservation.adults}",
                style: TextStyle(
                  fontSize: heightSpace(2.0),
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
            ),
          )),
          SizedBox(width: widthSpace(1.5)),
          Expanded(
              child: InfoBoxHorizontal(
            name: CustomText(
              "${Get.find<TranslationHelper>().translations.hostReservation.children} No.",
              size: 1.5,
              textAlign: TextAlign.start,
              maxlines: 1,
              textOverflow: TextOverflow.clip,
            ),
            value: RichText(
              text: TextSpan(
                text:
                    "${data.children} ${Get.find<TranslationHelper>().translations.hostReservation.children}",
                style: TextStyle(
                  fontSize: heightSpace(2.0),
                  fontWeight: FontWeight.w500,
                  color: Colors.black,
                ),
              ),
            ),
          )),
        ]),
        SizedBox(
          height: heightSpace(1),
        ),
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Expanded(
              child: InfoBoxHorizontal(
            name: CustomText(
              Get.find<TranslationHelper>().translations.bookingDetail.checkIn,
              size: 1.5,
              weight: FontWeight.normal,
              textAlign: TextAlign.start,
              maxlines: 1,
              textOverflow: TextOverflow.clip,
              color: const Color(greyText),
            ),
            value: RichText(
              text: TextSpan(
                text:
                    "${DateFormat.yMMMMEEEEd().format(data.startDate!)}, at ${data.checkinTime} ",
                style: TextStyle(
                  fontSize: heightSpace(1.7),
                  fontWeight: FontWeight.normal,
                  color: Colors.black,
                ),
              ),
            ),
          )),
        ]),
        SizedBox(
          height: heightSpace(1),
        ),
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Expanded(
              child: InfoBoxHorizontal(
            name: CustomText(
              Get.find<TranslationHelper>().translations.bookingDetail.checkOut,
              size: 1.5,
              weight: FontWeight.normal,
              textAlign: TextAlign.start,
              maxlines: 1,
              textOverflow: TextOverflow.clip,
              color: const Color(greyText),
            ),
            value: RichText(
              text: TextSpan(
                text:
                    "${DateFormat.yMMMMEEEEd().format(data.endDate!)}, at ${data.checkoutTime} ",
                style: TextStyle(
                    fontSize: heightSpace(1.7),
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                    overflow: TextOverflow.clip),
              ),
            ),
          )),
        ]),
        SizedBox(
          height: heightSpace(1),
        ),
        if (data.departure != null) ...[
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Expanded(
                child: InfoBoxHorizontal(
              name: CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .hostReservation
                    .departure,
                size: 1.5,
                weight: FontWeight.normal,
                textAlign: TextAlign.start,
                maxlines: 1,
                textOverflow: TextOverflow.clip,
                color: const Color(greyText),
              ),
              value: RichText(
                text: TextSpan(
                  text: data.departure,
                  style: TextStyle(
                    fontSize: heightSpace(1.7),
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                ),
              ),
            )),
          ]),
          SizedBox(height: heightSpace(1)),
        ],
        if (data.propertyCode != null) ...[
          InfoBoxHorizontal(
              name: CustomText(
                Get.find<TranslationHelper>().translations.home.unitCode,
                size: 1.5,
                weight: FontWeight.normal,
                textAlign: TextAlign.start,
                maxlines: 1,
                textOverflow: TextOverflow.clip,
                color: const Color(greyText),
              ),
              value: RichText(
                text: TextSpan(
                  text: data.propertyCode,
                  style: TextStyle(
                    fontSize: heightSpace(1.7),
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                ),
              )),
          SizedBox(
            height: heightSpace(1),
          ),
        ],
        if (data.bookingCode != null) ...[
          InfoBoxHorizontal(
              name: CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .hostReservation
                    .confirmationCode,
                size: 1.5,
                weight: FontWeight.normal,
                textAlign: TextAlign.start,
                maxlines: 1,
                textOverflow: TextOverflow.clip,
                color: const Color(greyText),
              ),
              value: RichText(
                text: TextSpan(
                  text: data.bookingCode,
                  style: TextStyle(
                    fontSize: heightSpace(1.7),
                    fontWeight: FontWeight.normal,
                    color: Colors.black,
                  ),
                ),
              )),
          SizedBox(
            height: heightSpace(1),
          ),
        ],
      ],
    );
  }

  renderPaymentDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
            Get.find<TranslationHelper>().translations.account.paymentDetails,
            size: 2.2,
            weight: FontWeight.bold),
        SizedBox(height: heightSpace(2)),
        if (isHost) ...[
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .theGuestPaidIt,
              size: 1.7),
          SizedBox(height: heightSpace(1)),
          const Divider(),
          SizedBox(height: heightSpace(1)),
        ],
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .hostReservation
                  .accommodation,
              size: 1.8),
          CustomText(
              "${data.basePrice ?? data.totalAccomodation?.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
              size: 1.8),
        ]),
        if (data.cleaningFee! > 0) ...[
          SizedBox(height: heightSpace(2)),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .cleaningFee,
                size: 1.8),
            CustomText(
                "${data.cleaningFee?.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                size: 1.8),
          ]),
        ],
        SizedBox(
          height: heightSpace(2),
        ),
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .hostReservation
                  .guestServiceFee,
              size: 1.8),
          CustomText(
              "${data.serviceCharge?.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
              size: 1.8),
        ]),
        if (data.securityFee! > 0) ...[
          SizedBox(height: heightSpace(2)),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .propertySingle
                    .securityFee,
                size: 1.8),
            CustomText(
                "${data.securityFee?.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                size: 1.8),
          ]),
        ],
        if (data.totalDiscount! > 0) ...[
          SizedBox(height: heightSpace(2)),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .hostReservation
                    .darentDiscount,
                size: 1.8),
            CustomText(
                "${data.totalDiscount?.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                color: const Color(warningColor),
                size: 1.8),
          ]),
        ],
        if (data.discountType!=null && data.discountType!='no_discount') ...[
          SizedBox(height: heightSpace(2)),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            CustomText(
                Get.find<TranslationHelper>().translations.propertySingle.toJson()[data.discountType],
                size: 1.8),
            CustomText(
                "${data.youSaved} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                color: const Color(warningColor),
                size: 1.8),
          ]),
        ],
        SizedBox(
          height: heightSpace(2),
        ),
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          CustomText(
              "${Get.find<TranslationHelper>().translations.hostReservation.total} (${Get.find<TranslationHelper>().translations.hostDashboard.sar})",
              size: 2.0,
              weight: FontWeight.w500),
          CustomText(
            "${data.total?.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
            size: 2.0,
            weight: FontWeight.w500,
            color: const Color(themeColor),
          ),
        ]),
        SizedBox(
          height: heightSpace(3),
        ),
        if (isHost) ...[
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .hostReservation
                  .hostFinancialCollection,
              size: 1.7),
          SizedBox(height: heightSpace(1)),
          const Divider(),
          SizedBox(height: heightSpace(1)),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .hostReservation
                    .theTotalPriceOfTheStay,
                size: 1.8),
            CustomText(
                "${data.totalAccomodation?.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                size: 1.8),
          ]),
          // if (data.cleaningFee! > 0) ...[
          //   SizedBox(height: heightSpace(2)),
          //   Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          //     CustomText(
          //         Get.find<TranslationHelper>()
          //             .translations
          //             .hostReservation
          //             .cleaningFee,
          //         size: 1.8),
          //     CustomText(
          //         "${data.cleaningFee?.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
          //         size: 1.8),
          //   ]),
          // ],
          if (data.promoCodeCreatedBy=='User' && data.totalDiscount! > 0) ...[
            SizedBox(height: heightSpace(2)),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostReservation
                      .darentDiscount,
                  size: 1.8),
              CustomText(
                  "- ${data.totalDiscount?.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                  color: const Color(warningColor),
                  size: 1.8),
            ]),
          ],
          if (data.discountType!=null && data.discountType!='no_discount') ...[
            SizedBox(height: heightSpace(2)),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              CustomText(
                  Get.find<TranslationHelper>().translations.propertySingle.toJson()[data.discountType],
                  size: 1.8),
              CustomText(
                  "- ${data.youSaved} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                  color: const Color(warningColor),
                  size: 1.8),
            ]),
          ],
          SizedBox(height: heightSpace(2)),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Expanded(
                child: CustomText(
                    "${Get.find<TranslationHelper>().translations.hostReservation.hostServiceFee} (${data.insurancePercent}% ${Get.find<TranslationHelper>().translations.hostDashboard.insurance} "
                    "${data.hostCommission ?? 0}% ${Get.find<TranslationHelper>().translations.hostDashboard.vatIncluded})",
                    size: 1.8)),
            CustomText(
              "${(data.hostFee ?? 0.0).toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
              color: const Color(warningColor),
              size: 1.8,
            ),
          ]),
          SizedBox(height: heightSpace(2)),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            CustomText(
                "${Get.find<TranslationHelper>().translations.hostReservation.total} (${Get.find<TranslationHelper>().translations.hostDashboard.sar})",
                size: 2.0,
                weight: FontWeight.w500),
            CustomText(
                "${data.hostTotal?.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                size: 2.0,
                color: const Color(successColor),
                weight: FontWeight.w500),
          ]),
        ],
      ],
    );
  }
}
