import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../helperMethods/translation_helper.dart';
import '../../helperMethods/authHelper.dart';

class PaymentMethod extends StatelessWidget {
  const PaymentMethod({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
            onPressed: Get.back,
            icon: const Icon(
              Icons.chevron_left,
              size: 40,
            )),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(3.5)),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .wallet
                      .paymentMethods,
                  size: 3.2),
              SizedBox(height: heightSpace(3.5)),
              AuthHelper.c.myCards.isEmpty
                  ? CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .wallet
                          .paymentMethodNote,
                      size: 2)
                  : ListView.separated(
                      shrinkWrap: true,
                      primary: false,
                      padding: EdgeInsets.only(top: heightSpace(1)),
                      itemBuilder: (context, index) {
                        return InkWell(
                            onTap: () => AuthHelper.c.changeDefaultCard(
                                AuthHelper.c.myCards[index].id),
                            child: Row(children: [
                              SvgPicture.asset("assets/cvv-card.svg",
                                  height: 40, width: 80),
                              SizedBox(width: widthSpace(5)),
                              Expanded(
                                  child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                    CustomText(
                                      "**** **** **** ${AuthHelper.c.myCards[index].cardNumber[12]}${AuthHelper.c.myCards[index].cardNumber[13]}${AuthHelper.c.myCards[index].cardNumber[14]}${AuthHelper.c.myCards[index].cardNumber[15]}",
                                      color: Colors.grey[700],
                                    ),
                                    Radio(
                                        value: AuthHelper.c.myCards[index].id,
                                        groupValue:
                                            AuthHelper.c.defaultCardId.value,
                                        onChanged:
                                            AuthHelper.c.changeDefaultCard),
                                    AuthHelper.c.isLoading.value
                                        ? const SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                                strokeWidth: 1.5))
                                        : IconButton(
                                            onPressed: () => AuthHelper.c
                                                .deleteCard(AuthHelper
                                                    .c.myCards[index].id),
                                            icon: const Icon(Icons.clear))
                                  ]))
                            ]));
                      },
                      separatorBuilder: (context, index) =>
                          SizedBox(height: heightSpace(1)),
                      itemCount: AuthHelper.c.myCards.length),
              SizedBox(
                height: heightSpace(3.5),
              ),
              SizedBox(
                height: heightSpace(7),
                child: CommonButton(
                    title: Get.find<TranslationHelper>()
                        .translations
                        .wallet
                        .addCard,
                    backgroundBg: Colors.black.withOpacity(0.7),
                    horizontalPadding: 5,
                    onPressed: () {
                      Get.bottomSheet(
                        shape: const RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.vertical(top: Radius.circular(20.0)),
                        ),
                        backgroundColor: Colors.white,
                        SizedBox(
                          height: 300,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    SizedBox(
                                      width: widthSpace(5),
                                    ),
                                    CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .wallet
                                          .payBy,
                                      size: 2.3,
                                    ),
                                    IconButton(
                                      onPressed: Get.back,
                                      icon: const Icon(Icons.close),
                                    ),
                                  ],
                                ),
                              ),
                              const Divider(),
                              InkWell(
                                onTap: AuthHelper.c.showAddCardSheet,
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset("assets/credit_card.svg",
                                          width: widthSpace(6),
                                          color: Colors.black),
                                      const SizedBox(width: 16),
                                      CustomText(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .paymentStripe
                                            .creditDebitCard,
                                        color: Colors.grey[800],
                                        weight: FontWeight.w500,
                                        size: 2.1,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              const Divider(thickness: 1),
                              // Padding(
                              //   padding: EdgeInsets.all(16.0),
                              //   child: Row(
                              //     crossAxisAlignment: CrossAxisAlignment.center,
                              //     children: [
                              //       SvgPicture.asset("assets/paypal.svg",
                              //           width: widthSpace(9)),
                              //       SizedBox(width: 16),
                              //       CustomText(
                              //         "PayPal",
                              //         color: Colors.grey[800],
                              //         weight: FontWeight.w500,
                              //         size: 2.1,
                              //       ),
                              //     ],
                              //   ),
                              // ),
                            ],
                          ),
                        ),
                      );
                    }),
              )
            ],
            //]
          ),
        ),
      ),
    );
  }

  extraComponent(String image, {required name, Widget? screen, function}) {
    return InkWell(
      onTap: function ??
          () {
            if (screen != null) {
              Get.to(() => screen);
            }
          },
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset("assets/$image.svg",
                width: widthSpace(6), color: Colors.black),
            SizedBox(width: widthSpace(7.5)),
            Expanded(
              child: CustomText(
                name,
                color: Colors.grey[800],
                weight: FontWeight.w500,
                size: 2.1,
              ),
            ),
            SizedBox(width: widthSpace(8)),
            const Icon(
              Icons.arrow_forward_ios,
              size: 20,
            )
          ]),
    );
  }
}
