import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/empty_reservation.dart';
import 'package:darent/controllers/bookings_controller.dart';
import 'package:darent/screens/new_host/reservation_item.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shimmer/shimmer.dart';
import '../../components/custom_textfield.dart';
import '../../components/views_common.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../helperMethods/listing_helper/listing_helper.dart';
import '../../helperMethods/translation_helper.dart';

class HostReservations extends StatelessWidget {
  // final String? currentTab;
  //this.currentTab = 'pending-bookings'
  HostReservations({Key? key, String? tab}) : super(key: key);
  late BookingsController c;
  @override
  Widget build(BuildContext context) {
    c = Get.find();
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        toolbarHeight: heightSpace(10),
        leading: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            IconButton(
                onPressed: Get.back,
                icon: const Icon(
                  Icons.chevron_left,
                  size: 30,
                )),
            SizedBox(
              width: widthSpace(2),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .reservation
                        .reservation,
                    size: 3.2,
                    weight: FontWeight.bold,
                    color: Colors.black),
                RichText(
                  text: TextSpan(
                      text:
                          "${Get.find<TranslationHelper>().translations.account.currentlyHave} ",
                      style: TextStyle(
                          color: Color(lightBlack),
                          fontSize: heightSpace(1.6),
                          fontWeight: FontWeight.normal),
                      children: <TextSpan>[
                        TextSpan(
                          text: "${c.data.value['ongoing-bookings']?.length} ",
                          style: TextStyle(
                              color: const Color(themeColor),
                              fontSize: heightSpace(1.6),
                              fontWeight: FontWeight.w500),
                        ),
                        TextSpan(
                          text: Get.find<TranslationHelper>()
                              .translations
                              .account
                              .activeReservations,
                          style: TextStyle(
                              color: Color(lightBlack),
                              fontSize: heightSpace(1.6),
                              fontWeight: FontWeight.normal),
                        )
                      ]),
                ),
              ],
            ),
          ],
        ),
        leadingWidth: widthSpace(90),
      ),
      body: Container(
        color: Colors.white,
        child: Obx(
          () => Padding(
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(5), vertical: heightSpace(2)),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              // Divider(height: heig htSpace(2)),
              renderTabs(),
              // indicationWidget(),
              // Divider(height: heightSpace(2)),
              SizedBox(height: heightSpace(3)),
              Row(
                children: [
                  Expanded(
                    child: SizedBox(
                      height: heightSpace(5),
                      child: CustomTextField(
                          controller: c.bookingCodeController,
                          hint: Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .searchBookingCode,
                          filledColor: Colors.white,
                          isRoundedBorder: true,
                          borderRadius: 9,
                          prefixIcon: const Icon(Icons.search, size: 20),
                          textCapitalization: TextCapitalization.characters,
                          onChanged: (p0) {
                            ListingHelper.c.propertySearchKey.value = p0;
                            ListingHelper.c.properties.refresh();
                          }),
                    ),
                  ),
                  InkWell(
                    // Wrap calendar icon in InkWell for click handling
                    onTap: c.isExpandNow.toggle, // Toggle expansion state
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Container(
                        width: widthSpace(11),
                        height: heightSpace(5),
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(9),
                          color: const Color(themeColor),
                        ),
                        child: SvgPicture.asset(
                            "assets/icons/host_new/calendar.svg",
                            colorFilter: const ColorFilter.mode(
                                Colors.white, BlendMode.srcIn)),
                      ),
                    ),
                  )
                ],
              ),
              if (c.isExpandNow.value) filters(),
              SizedBox(height: heightSpace(3)),
              //   Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //   children: [
              //     Row(
              //       children: [
              //         shadowButton(icon: "sort",function: () =>  Get.to(()=>const Arrangement())),
              //         SizedBox(width: widthSpace(3)),
              //         shadowButton(icon: "filter",function: () => Get.to(()=>const HostFilters())),
              //       ],
              //     ),
              //   ],
              // ),
              //   SizedBox(height: heightSpace(2)),
              //   CustomText(Get.find<TranslationHelper>().translations.hostDashboard.reservation,size: 2.7),
              //   SizedBox(
              //     height:heightSpace(12),
              //     child: RefreshIndicator(
              //       onRefresh: ()async{
              //         c.getData(refresh: true);
              //       },
              //       child: SingleChildScrollView(
              //             scrollDirection: Axis.horizontal,
              //             child: Row(
              //               children: [
              //                 renderTab(Get.find<TranslationHelper>().translations.hostDashboard.pending!, "pending-bookings"),
              //                 renderTab(Get.find<TranslationHelper>().translations.hostDashboard.coming!, "upcoming-bookings"),
              //                 renderTab(Get.find<TranslationHelper>().translations.hostDashboard.ongoing!, "ongoing-bookings"),
              //                 renderTab(Get.find<TranslationHelper>().translations.reservation.history, "history-bookings"),
              //                 renderTab(Get.find<TranslationHelper>().translations.hostDashboard.canceled!, "cancelled-bookings"),
              //                 renderTab(Get.find<TranslationHelper>().translations.reservation.expired, "expired-bookings"),
              //                 renderTab(Get.find<TranslationHelper>().translations.hostDashboard.all!, "all_reservation"),
              //               ],
              //             ),
              //           ),
              //     ),
              //   ),
              c.isLoading.value
                  ? getShimmerLoading()
                  : Expanded(
                      child: NotificationListener<ScrollEndNotification>(
                          onNotification: (notification) {
                            final metrics = notification.metrics;
                            if (metrics.atEdge) {
                              bool isTop = metrics.pixels == 0;
                              if (!isTop &&
                                  c.pagination[c.tab].currentPage <
                                      c.pagination[c.tab].totalPages) {
                                c.getData(refresh: false);
                              }
                            }
                            return true;
                          },
                          child: TabBarView(
                              controller: c.tabController,
                              children: [
                                //here
                                renderListing(c.data.value['pending-bookings']),
                                renderListing(
                                    c.data.value['upcoming-bookings']),
                                renderListing(c.data.value['ongoing-bookings']),
                                renderListing(c.data.value['history-bookings']),
                                renderListing(
                                    c.data.value['cancelled-bookings']),
                                renderListing(c.data.value['expired-bookings']),
                                // renderListing(c.data.value['all_reservation']),
                              ])),
                    )
            ]),
          ),
        ),
      ),
    );
  }

  Widget filters() {
    return Column(
      // Additional column for better visual organization
      children: [
        SizedBox(height: heightSpace(2.5)),
        Container(
          height: heightSpace(8.5),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
            boxShadow: ViewsCommon.boxShadow,
            border: Border.all(color: const Color(greyBorder)),
          ),
          child: Row(children: [
            renderDateItem(
                c.start,
                Get.find<TranslationHelper>().translations.listingCalendar.startDate),
            const VerticalDivider(thickness: 1, color: Color(greyBorder)),
            renderDateItem(
                c.end,
                Get.find<TranslationHelper>().translations.hostReservation.completionDate ??'',
                slug: 'e'),
          ]),
        ),
        SizedBox(height: heightSpace(2.5)),
        Row(children: [
          Expanded(
              flex: 2,
              child: CommonButton(
                  title: Get.find<TranslationHelper>().translations.home.search,
                  backgroundBg: Colors.blue,
                  fontSize: 2.2,
                  fontWeight: FontWeight.w500,
                  verticalPadding: 2.7,
                  onPressed: c.applyFilter)),
          SizedBox(width: widthSpace(5)),
          Expanded(
              flex: 1,
              child: CommonButton(
                  title:
                      Get.find<TranslationHelper>().translations.search.cancel,
                  verticalPadding: 2.7,
                  fontSize: 2.2,
                  fontWeight: FontWeight.w500,
                  icon: Icons.close,
                  onPressed: () => c.clearFilterValues(alsoGetData: true))),
        ]),
      ],
    );
  }

  renderTabs() {
    return Container(
      height: heightSpace(5.3),
      decoration: BoxDecoration(
          color: Colors.grey[100], borderRadius: BorderRadius.circular(7)),
      alignment: Alignment.center,
      child: IgnorePointer(
        ignoring: c.isLoading.value,
        child: TabBar(
            controller: c.tabController,
            isScrollable: true,
            labelPadding: EdgeInsets.symmetric(horizontal: widthSpace(2)),
            labelStyle: TextStyle(
                fontSize: heightSpace(1.8),
                fontWeight: FontWeight.bold,
                fontFamily: 'PingAR+LT'),
            indicator: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(6.5)),
            automaticIndicatorColorAdjustment: true,
            tabs: [
              //here
              Tab(
                  text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .pending!),
              Tab(
                  text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .coming!),
              Tab(
                  text: Get.find<TranslationHelper>()
                      .translations
                      .reservation
                      .current),
              Tab(
                  text: Get.find<TranslationHelper>()
                      .translations
                      .account
                      .previous),
              Tab(
                  text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .canceled!),
              Tab(
                  text: Get.find<TranslationHelper>()
                      .translations
                      .reservation
                      .expired),
            ]),
      ),
    );
  }

  renderDateItem(Rxn<DateTime> date, String title, {String slug = 's'}) {
    return Expanded(
      child: InkWell(
        onTap: () => c.selectDate(date, slug),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomText(title, color: const Color(greyText), size: 1.7),
              SizedBox(height: heightSpace(0.5)),
              CustomText(
                  date.value == null
                      ? Get.find<TranslationHelper>()
                          .translations
                          .hostReservation
                          .addDate
                      : formDateFormatCservice.format(date.value!),
                  size: 1.9,
                  color: Colors.black87,
                  weight: FontWeight.w500),
            ]),
      ).paddingSymmetric(horizontal: widthSpace(4)),
    );
  }

  Widget shadowButton({String? icon, function}) {
    return InkWell(
      onTap: function ?? Get.back,
      child: Container(
          width: widthSpace(10),
          padding: EdgeInsets.all(widthSpace(2.6)),
          decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: ViewsCommon.boxShadow,
              shape: BoxShape.circle),
          child: icon != null
              ? SvgPicture.asset("assets/icons/$icon.svg")
              : const Center(child: Icon(Icons.chevron_left))),
    );
  }

  List indicators = [
    {
      "name": Get.find<TranslationHelper>().translations.hostDashboard.pending!,
      "color": themeColor
    },
    {
      "name": Get.find<TranslationHelper>().translations.hostDashboard.coming!,
      "color": Colors.blue,
    },
    {
      "name": Get.find<TranslationHelper>().translations.hostDashboard.ongoing!,
      "color": successColor
    },
    {
      "name": Get.find<TranslationHelper>().translations.reservation.history,
      "color": greyText
    },
    {
      "name":
          Get.find<TranslationHelper>().translations.hostDashboard.canceled!,
      "color": warningColor
    },
    {
      "name": Get.find<TranslationHelper>().translations.reservation.expired,
      "color": Colors.black
    },
  ];

  Widget indicationWidget() {
    return Container(
      height: heightSpace(4),
      margin: EdgeInsets.symmetric(vertical: heightSpace(2)),
      child: ListView.separated(
          scrollDirection: Axis.horizontal,
          shrinkWrap: true,
          itemBuilder: (context, index) {
            return item(
                color: indicators[index]['color'],
                name: indicators[index]['name']);
          },
          separatorBuilder: (context, index) {
            return SizedBox(
              width: widthSpace(4),
            );
          },
          itemCount: indicators.length),
    );
  }

  Widget item({required color, required String name}) {
    return Row(
      children: [
        Container(
          width: widthSpace(2),
          height: heightSpace(3),
          decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: color is int ? Color(color) : color),
        ),
        SizedBox(
          width: widthSpace(2),
        ),
        CustomText(name,
            size: 1.8, weight: FontWeight.w500, color: Color(greyText)),
      ],
    );
  }

  Widget renderListing(List? list) {
    return list == null || list.isEmpty
        ? EmptyReservation(
            msg: Get.find<TranslationHelper>()
                .translations
                .hostDashboard
                .doNotHaveBooking!)
        : RefreshIndicator(
            onRefresh: () async {
              c.getData(refresh: true);
            },
            child: ListView.separated(
                itemBuilder: (context, index) {
                  if (index == list.length) {
                    if (c.pagination[c.tab]?.currentPage != null &&
                        c.pagination[c.tab].currentPage <
                            c.pagination[c.tab].totalPages) {
                      return Image.asset("assets/loader.gif",
                          height: heightSpace(4));
                    }
                    return const SizedBox();
                  } else {
                    return ReservationItem(
                        data: list[index],
                        accept: c.acceptBooking,
                        decline: c.declineBooking,
                        displayPhone: c.tab == 'upcoming-bookings' ||
                            c.tab == 'ongoing-bookings');
                  }
                },
                separatorBuilder: (context, index) =>
                    SizedBox(height: heightSpace(3)),
                itemCount: list.length + 1),
          );
  }

  Shimmer getShimmerLoading() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: widthSpace(20),
                      ),
                      CircleAvatar(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(100),
                            color: Colors.white,
                          ),
                          width: double.infinity,
                          height: 18.0,
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: heightSpace(1)),
                  Container(
                    width: double.infinity,
                    height: 18.0,
                    color: Colors.white,
                  ),
                  SizedBox(height: heightSpace(0.7)),
                  Container(
                    width: double.infinity,
                    height: 18.0,
                    color: Colors.white,
                  ),
                  SizedBox(height: heightSpace(.7)),
                  Container(
                    width: double.infinity,
                    height: 18.0,
                    color: Colors.white,
                  ),
                  SizedBox(height: heightSpace(0.2)),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: widthSpace(3)),
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                  border: Border(top: BorderSide(color: Colors.white))),
              child: Container(
                width: widthSpace(80),
                height: 18.0,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  renderTab(String title, String slug) {
    bool isSelected = c.tab == slug;
    return InkWell(
        child: Container(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(2), vertical: heightSpace(1)),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: isSelected ? Colors.black : Colors.grey,
                width: isSelected ? 3 : 2,
              ),
            ),
          ),
          child: CustomText(
            title,
            size: 2,
            color: isSelected ? Colors.black : const Color(greyText),
            weight: FontWeight.w500,
          ),
        ),
        onTap: () {
          // c.changeTab(slug);
        });
  }
}
