import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/floating_text_field.dart';
import 'package:darent/components/host/host_listtile.dart';
import 'package:darent/components/host/host_switch.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/host_calendar_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/models/host/dwelling_model.dart';
import 'package:darent/screens/host/calendar_export.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import '../../helperMethods/listing_helper/listing_helper.dart';
import '../../helperMethods/translation_helper.dart';

class HostCalendar1 extends StatelessWidget {
  final DwellingModel data;
  HostCalendar1({super.key, required this.data});
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: GetBuilder<HostCalendarController>(
      id: 'calendar',
      init: HostCalendarController(id: data.id ?? 0),
      builder: (c) => Scaffold(
        appBar: AppBar(
            toolbarHeight: heightSpace(7),
            leading: IconButton(
                onPressed: Get.back, icon: const Icon(Icons.chevron_left)),
            title: CustomText(data.name ?? '',
                size: 2.2, color: Color(lightBlack), weight: FontWeight.w500),
            actions: [
              IconButton(
                  icon: const Icon(Icons.refresh),
                  tooltip: 'Refresh',
                  iconSize: 20,
                  onPressed: () => c.getData()),
              Obx(
                () => ListingHelper.c.isLoading.value
                    ? Image.asset('assets/loader.gif', width: 30).paddingSymmetric(horizontal: 8)
                    : IconButton(
                        icon: const Icon(Icons.ios_share_rounded),
                        iconSize: 20,
                        onPressed: exportICal,
                        tooltip: Get.find<TranslationHelper>().translations.hostReservation.exporT),
              ),
              // IconButton(
              //       icon: const Icon(Icons.calendar_today_outlined),
              //       iconSize: 20,onPressed: ()=>ViewsCommon.showModalBottom(const CalendarViewSettings())),
              // IconButton(
              //     icon: SvgPicture.asset("assets/icons/settings.svg",color:Colors.black,width:20),
              //     padding: EdgeInsets.zero,
              //     onPressed: ()=>Get.to(()=>const CalendarSettings()))
            ]),
        body: Directionality(
          textDirection: TextDirection.ltr,
          child: c.isLoading
              ? Center(
                  child:
                      Image.asset("assets/loader.gif", height: heightSpace(5)))
              :
              // :c.monthViewD?
              true
                  ? NotificationListener<ScrollEndNotification>(
                      onNotification: (notification) {
                        final metrics = notification.metrics;
                        if (metrics.atEdge) {
                          bool isTop = metrics.pixels <= 0;
                          if (!isTop) {
                            // c.current = DateTime(c.current.year+1);
                            c.getData(paginate: true);
                          }
                        }
                        return true;
                      },
                      child: RefreshIndicator(
                          onRefresh: () async {
                            c.getData();
                          },
                          child: monthsNewList(c)))
                  : const Column(children: [
                      // Row(
                      //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //     children: [
                      //   Row(
                      //     children: [
                      //       IconButton(onPressed:c.current.isAfter(now)?c.minusMonth:null, icon: const Icon(Icons.chevron_left)),
                      //       CustomText(months[Get.locale?.languageCode??'en']![c.current.month-1],size:2.1,weight: FontWeight.w500),
                      //       IconButton(onPressed:c.plusMonth, icon: const Icon(Icons.chevron_right))
                      //     ],
                      //   ),
                      //   CustomText(c.current.year.toString(),size:2.1,weight: FontWeight.w500)
                      // ]).paddingOnly(bottom: heightSpace(2)),
                      // ..monthsNewList(c)
                    ]),
        ),
      ),
    ));
  }

  exportICal() {
    if (!ListingHelper.c.isLoading.value) {
      ListingHelper.calendarExport(data.id).then((link) {
        if (link != null) {
          Get.bottomSheet(BottomSheet(
              onClosing: () {},
              builder: (context) => CalendarExport(link: link)));
        }
      });
    }
  }

  // List<Widget> yearsList(HostCalendarController c){
  //   List<Widget> widgets = [];
  //   for(int month=1;month<=12;month++){
  //     final firstDate = DateTime(c.current.year,month,1);
  //     final lastDay = DateTime(c.current.year,month+1,0);
  //     bool isBefore = lastDay.isBefore(DateTime.now());
  //     widgets.add(Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         CustomText(months[Get.locale?.languageCode??'en']![month-1],color: isBefore?Colors.grey:Colors.black),
  //         SizedBox(height: heightSpace(2.5)),
  //         InkWell(
  //           onTap:isBefore?null:(){
  //             c.calendarStart.value = firstDate.isBefore(DateTime.now())?DateTime.now():firstDate;
  //             c.calendarEnd.value = DateTime(c.current.year,month+1,0);
  //             c.customPrice.text = c.perNight.toString();
  //             c.availability.value = true;
  //             ViewsCommon.showModalBottom(SizedBox(
  //               height: heightSpace(58),
  //               child: BottomSheet(
  //                   backgroundColor: Colors.white,
  //                   enableDrag: false,
  //                   builder: (context) => Padding(
  //                     padding: EdgeInsets.all(widthSpace(viewPadding)),
  //                     child: Obx(()=>Column(
  //                         crossAxisAlignment: CrossAxisAlignment.stretch,
  //                         children: [
  //                           AppBar(
  //                             automaticallyImplyLeading: false,
  //                             actions: [
  //                               IconButton(onPressed: Get.back,padding: EdgeInsets.zero, icon: const Icon(Icons.close))
  //                             ],
  //                             elevation: 0.0,
  //                           ),
  //                           SizedBox(height: heightSpace(1)),
  //                           Row(
  //                             children: [
  //                               Expanded(
  //                                   child: InkWell(
  //                                     onTap:(){
  //                                       showDatePicker(
  //                                           context:context,
  //                                           initialDate:c.calendarStart.value,
  //                                           firstDate:DateTime.now(),
  //                                           lastDate: DateTime(c.calendarEnd.value.year+1,1,0)).then((dateTime) {
  //                                         if (dateTime !=null) {
  //                                           if (dateTime.isBefore(c.calendarEnd.value)) {
  //                                             c.calendarStart.value =dateTime;
  //                                           } else {
  //                                             c.calendarStart.value = c.calendarEnd.value;
  //                                             c.calendarEnd.value = dateTime;
  //                                           }
  //                                         }});
  //                                     },
  //                                     borderRadius:BorderRadius.circular(9),
  //                                     child: Container(
  //                                         padding:EdgeInsets.all(widthSpace(3)),
  //                                         decoration: BoxDecoration(
  //                                             border:Border.all(color:Colors.black54),
  //                                             borderRadius:BorderRadius.circular(9)),
  //                                         child: CustomText(DateFormat.MMMEd().format(c.calendarStart.value))
  //                                     ),
  //                                   )),
  //                               CustomText("    ${Get.find<TranslationHelper>().translations.hostReservation.to}    "),
  //                               Expanded(
  //                                   child: InkWell(
  //                                     onTap:(){
  //                                       showDatePicker(
  //                                           context:context,
  //                                           initialDate:c.calendarEnd.value,
  //                                           firstDate:c.calendarStart.value,
  //                                           lastDate: DateTime(c.calendarEnd.value.year+1,1,0)).then((dateTime) {
  //                                         if (dateTime !=null) {
  //                                           if (dateTime.isAfter(c.calendarStart.value)) {
  //                                             c.calendarEnd.value = dateTime;
  //                                           } else {
  //                                             c.calendarEnd.value = c.calendarStart.value;
  //                                             c.calendarStart.value = dateTime;
  //                                           }
  //                                         }});
  //                                     },
  //                                     borderRadius:BorderRadius.circular(9),
  //                                     child: Container(
  //                                         padding:EdgeInsets.all(widthSpace(3)),
  //                                         decoration: BoxDecoration(
  //                                             border:Border.all(color:Colors.black54),
  //                                             borderRadius:BorderRadius.circular(9)),
  //                                         child: CustomText(DateFormat.MMMEd().format(c.calendarEnd.value))
  //                                     ),
  //                                   ))
  //                             ],
  //                           ),
  //                           // CustomText("${months[Get.locale?.languageCode??'en']![month-1]}, ${c.calendarStart.value.day} - ${c.calendarEnd.value.day} ${c.current.year}"),
  //                           SizedBox(height: heightSpace(3)),
  //                           CustomText(Get.find<TranslationHelper>().translations.hostDashboard.customPrice),
  //                           SizedBox(height: heightSpace(1.5)),
  //                           FloatingTextField(
  //                               controller: c.customPrice,
  //                               labelText: Get.find<TranslationHelper>().translations.hostDashboard.sar!,
  //                               inputType: TextInputType.number,
  //                               formatters:[
  //                                 LengthLimitingTextInputFormatter(4),
  //                                 FilteringTextInputFormatter.digitsOnly
  //                               ],
  //                               borderType:OutlineInputBorder(borderRadius: BorderRadius.circular(9))),
  //                           SizedBox(height: heightSpace(3)),
  //                           HostListTile(
  //                               title: Get.find<TranslationHelper>().translations.hostDashboard.availability!,
  //                               subtitle: c.availability.value?Get.find<TranslationHelper>().translations.listingCalendar.open:Get.find<TranslationHelper>().translations.listingCalendar.close,
  //                               showBorder: false,
  //                               trailing:HostSwitch(value: c.availability.value,onConfirm:c.availability.toggle)),
  //                           const Spacer(),
  //                           CommonButton(
  //                               title: Get.find<TranslationHelper>().translations.usersProfile.save,
  //                               backgroundBg: Colors.black,
  //                               isLoading: c.isBtnLoading.value,
  //                               minimumSize: Size.fromHeight(heightSpace(6)),
  //                               onPressed: c.submitAvailability)
  //                         ]),
  //                     ),
  //                   ), onClosing: () {}),
  //             ));
  //           },
  //           child: GridView.builder(
  //             shrinkWrap: true,
  //             primary: false,
  //             gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
  //                 crossAxisCount: 7,mainAxisSpacing: widthSpace(2),crossAxisSpacing: widthSpace(2),childAspectRatio: 1.5
  //             ),
  //             itemCount: 42,
  //             itemBuilder: (context, index) {
  //               int day = (index+2)- firstDate.weekday;
  //               DateTime date = DateTime(2023,month,day);
  //               ReservationsModel? obj = c.reservations.firstWhereOrNull((item)=>date.isAtSameMomentAs(item.start));
  //               if(obj!=null){
  //               }else{
  //                 obj = c.reservations.firstWhereOrNull((item)=>date.isAtSameMomentAs(item.end));
  //                 if(obj!=null){
  //                 }else{
  //                   obj = c.reservations.firstWhereOrNull((e)=> GlobalHelper.isBetweenDates(date, e.start, e.end));
  //                 }
  //               }
  //               if(day>0 && day<=lastDay.day){
  //                 return CircleAvatar(backgroundColor: isBefore || obj!=null?Colors.grey:Colors.black87);
  //               }
  //               return const SizedBox();
  //
  //             },
  //           ),
  //         ),
  //       ],
  //     ));
  //   }
  //   return widgets;
  // }
  bool sheetClosed = true;
  monthsNewList(HostCalendarController c) {
    return SfDateRangePicker(
      controller: c.calendarC,
      selectionMode: DateRangePickerSelectionMode.range,
      startRangeSelectionColor: const Color(themeColor).withOpacity(.5),
      endRangeSelectionColor: const Color(themeColor).withOpacity(.5),
      rangeSelectionColor: const Color(themeColor).withOpacity(.1),
      // navigationMode: DateRangePickerNavigationMode.scroll,
      // navigationDirection:  DateRangePickerNavigationDirection.horizontal,
      enablePastDates: false,
      minDate: DateTime.now(),
      // maxDate: c.lastDate.ad,
      selectableDayPredicate: (date) {
        bool enabled = true;
        ReservationsModel? obj = c.reservations
            .firstWhereOrNull((item) => date.isAtSameMomentAs(item.start));
        if (obj != null) {
          enabled = false;
        } else {
          obj = c.reservations
              .firstWhereOrNull((item) => date.isAtSameMomentAs(item.end));
          if (obj != null) {
            enabled = false;
          } else {
            obj = c.reservations.firstWhereOrNull(
                (e) => GlobalHelper.isBetweenDates(date, e.start, e.end));
            if (obj != null) {
              enabled = false;
            }
          }
        }
        return enabled;
      },
      onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
        if (sheetClosed) {
          sheetClosed = false;
          Future.delayed(const Duration(milliseconds: 500)).then((value) {
            PickerDateRange? dateRange = c.calendarC.selectedRange;
            if (dateRange?.startDate != null) {
              Map? priceObj =
                  c.customPrices[formDateFormat.format(args.value.startDate)];
              // if((priceObj?['discount']??0)>0){
              //   double price = priceObj!['price'] is String?double.parse(priceObj['price']):priceObj['price'];
              // }
              c.calendarStart.value = dateRange!.startDate!;
              c.calendarEnd.value = dateRange.endDate ?? dateRange.startDate!;
              c.customPrice.text =
                  '${priceObj?['is_price_custom'] == true ? (priceObj?['price'] ?? c.perNight) : 0}';
              c.customDiscount.text = (priceObj?['discount'] ?? 0).toString();
              c.availability.value = priceObj?['available'] != false;
              ViewsCommon.showModalBottom(
                  DraggableScrollableSheet(
                      maxChildSize: .8,
                      initialChildSize: .8,
                      expand: false,
                      builder: (context, s) => SingleChildScrollView(
                            padding: EdgeInsets.all(widthSpace(viewPadding)),
                            child: Obx(
                              () => Column(
                                  crossAxisAlignment:CrossAxisAlignment.stretch,
                                  children: [
                                    AppBar(
                                      automaticallyImplyLeading: false,
                                      title: Text(Get.find<TranslationHelper>().translations.hostDashboard.pricingAndAvailibility!),
                                      actions: [
                                        IconButton(
                                            onPressed: Get.back,
                                            padding: EdgeInsets.zero,
                                            icon: const Icon(Icons.close))
                                      ],
                                      elevation: 0.0,
                                    ),

                                    Container(
                                        margin: EdgeInsets.symmetric(vertical: heightSpace(4)),
                                        height: heightSpace(7),
                                        decoration: BoxDecoration(
                                            color: Colors.grey[100]!,
                                            borderRadius: BorderRadius.circular(10),
                                            border: Border.all(color: const Color(greyBorder))),
                                        child: Row(
                                            children: [
                                              discountTab('calendar',Get.find<TranslationHelper>().translations.hostDashboard.calendar,c.discountType),
                                              discountTab('daily_discount', Get.find<TranslationHelper>().translations.listingPrice.dailyDiscount,c.discountType),
                                            ])),

                                    Row(
                                      children: [
                                        Expanded(
                                            child: InkWell(
                                          onTap: () {
                                            showDatePicker(
                                                    context: context,
                                                    initialDate:
                                                        c.calendarStart.value,
                                                    firstDate: DateTime.now(),
                                                    lastDate: DateTime.now()
                                                        .add(const Duration(
                                                            days: 365)))
                                                .then((dateTime) {
                                              if (dateTime != null) {
                                                if (dateTime.isBefore(
                                                    c.calendarEnd.value)) {
                                                  c.calendarStart.value =
                                                      dateTime;
                                                } else {
                                                  c.calendarStart.value =
                                                      c.calendarEnd.value;
                                                  c.calendarEnd.value =
                                                      dateTime;
                                                }
                                              }
                                            });
                                          },
                                          borderRadius:
                                              BorderRadius.circular(9),
                                          child: Container(
                                              padding:
                                                  EdgeInsets.all(widthSpace(3)),
                                              decoration: BoxDecoration(
                                                  border: Border.all(
                                                      color: Colors.black54),
                                                  borderRadius:
                                                      BorderRadius.circular(9)),
                                              child: CustomText(
                                                  DateFormat.MMMEd().format(
                                                      c.calendarStart.value))),
                                        )),
                                        CustomText(
                                            "    ${Get.find<TranslationHelper>().translations.hostReservation.to}    "),
                                        Expanded(
                                            child: InkWell(
                                          onTap: () {
                                            showDatePicker(
                                                    context: context,
                                                    initialDate:
                                                        c.calendarEnd.value,
                                                    firstDate: DateTime.now(),
                                                    lastDate: DateTime(
                                                        c.calendarEnd.value
                                                            .year,
                                                        c.calendarEnd.value
                                                                .month +
                                                            1,
                                                        0))
                                                .then((dateTime) {
                                              if (dateTime != null) {
                                                if (dateTime.isAfter(
                                                    c.calendarStart.value)) {
                                                  c.calendarEnd.value =
                                                      dateTime;
                                                } else {
                                                  c.calendarEnd.value =
                                                      c.calendarStart.value;
                                                  c.calendarStart.value =
                                                      dateTime;
                                                }
                                              }
                                            });
                                          },
                                          borderRadius:
                                              BorderRadius.circular(9),
                                          child: Container(
                                              padding:
                                                  EdgeInsets.all(widthSpace(3)),
                                              decoration: BoxDecoration(
                                                  border: Border.all(
                                                      color: Colors.black54),
                                                  borderRadius:
                                                      BorderRadius.circular(9)),
                                              child: CustomText(
                                                  DateFormat.MMMEd().format(
                                                      c.calendarEnd.value))),
                                        ))
                                      ],
                                    ),
                                    SizedBox(height: heightSpace(3)),

                                    if(c.discountType=='calendar')...[
                                      CustomText(Get.find<TranslationHelper>().translations.hostDashboard.customPrice),
                                      SizedBox(height: heightSpace(1.5)),
                                      FloatingTextField(
                                          controller: c.customPrice,
                                          labelText: Get.find<TranslationHelper>()
                                              .translations
                                              .hostDashboard
                                              .sar!,
                                          inputType: TextInputType.number,
                                          formatters: [
                                            LengthLimitingTextInputFormatter(4),
                                            FilteringTextInputFormatter.digitsOnly
                                          ],
                                          borderType: OutlineInputBorder(
                                              borderRadius:
                                              BorderRadius.circular(9))),
                                      SizedBox(height: heightSpace(2)),
                                    ],

                                    CustomText(c.discountType.value=='calendar'
                                        ?Get.find<TranslationHelper>().translations.listingPrice.customDiscount
                                        :Get.find<TranslationHelper>().translations.listingPrice.dailyDiscount),
                                    SizedBox(height: heightSpace(1.5)),
                                    FloatingTextField(
                                        controller: c.customDiscount,
                                        labelText: '%',
                                        inputType: TextInputType.number,
                                        formatters: [
                                          LengthLimitingTextInputFormatter(4),
                                          FilteringTextInputFormatter.digitsOnly
                                        ],
                                        onChanged: (val) {
                                          int value = int.tryParse(val) ?? 0;
                                          if (value > 100) {
                                            c.customDiscount.text = '100';
                                          }
                                        },
                                        borderType: OutlineInputBorder(borderRadius:BorderRadius.circular(9))),

                                    SizedBox(height: heightSpace(3)),
                                    HostListTile(
                                        title: Get.find<TranslationHelper>().translations.hostDashboard.availability!,
                                        subtitle: c.availability.value
                                            ? Get.find<TranslationHelper>().translations.listingCalendar.open
                                            : Get.find<TranslationHelper>().translations.listingCalendar.close,
                                        showBorder: false,
                                        trailing: HostSwitch(
                                            value: c.availability.value,
                                            onConfirm: c.availability.toggle)),
                                    CommonButton(
                                        title: Get.find<TranslationHelper>().translations.usersProfile.save,
                                        backgroundBg: Colors.black,
                                        isLoading: c.isBtnLoading.value,
                                        minimumSize: Size.fromHeight(heightSpace(6)),
                                        onPressed: c.submitAvailability)
                                  ]),
                            ),
                          )),
                  then: (_) => sheetClosed = true);
            } else {
              sheetClosed = true;
            }
          });
        }
      },
      cellBuilder: (context, cellDetails) {
        String dateSlug = "";
        ReservationsModel? obj = c.reservations.firstWhereOrNull(
            (item) => cellDetails.date.isAtSameMomentAs(item.start));
        if (obj != null) {
          dateSlug = "s";
        } else {
          obj = c.reservations.firstWhereOrNull(
              (item) => cellDetails.date.isAtSameMomentAs(item.end));
          if (obj != null) {
            dateSlug = "e";
          } else {
            obj = c.reservations.firstWhereOrNull((e) =>
                GlobalHelper.isBetweenDates(cellDetails.date, e.start, e.end));
            if (obj != null) {
              dateSlug = "b";
            }
          }
        }
        Map? priceObj = c.customPrices[formDateFormat.format(cellDetails.date)];
        double? discounted;
        if ((priceObj?['discount'] ?? 0) > 0) {
          num price = priceObj!['price'] is String? double.parse(priceObj['price']): priceObj['price'];
          if(price==0){
            priceObj['price'] = c.perNight;
            price = c.perNight;
          }
          discounted = price - (price * priceObj['discount'] / 100);
        }
        bool isPastDate = cellDetails.date.isBefore(DateTime(
            DateTime.now().year, DateTime.now().month, DateTime.now().day));
        bool dateInRange = false;
        if (c.calendarC.selectedRange?.startDate != null) {
          dateInRange = GlobalHelper.isBetweenDates(
              cellDetails.date,
              c.calendarC.selectedRange!.startDate!,
              c.calendarC.selectedRange?.endDate ??
                  c.calendarC.selectedRange!.startDate!);
        }
        return Container(
          decoration: BoxDecoration(
              color: dateInRange
                  ? null
                  : priceObj?['available'] == false
                      ? Colors.grey[200]
                      : null),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Container(
                height: heightSpace(4),
                margin: EdgeInsets.all(widthSpace(1.5)),
                // alignment:Alignment.topLeft,
                child: discounted != null
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                            CustomText('${priceObj?['price'] ?? 0} ',
                                strikeThrough: true,
                                size: 1.45,
                                color: isPastDate
                                    ? Colors.black38
                                    : Colors.black54,
                                weight: FontWeight.w500),
                            CustomText(discounted.toStringAsFixed(2),
                                size: 1.6,
                                weight: FontWeight.w500,
                                color: isPastDate ? Colors.black38 : null)
                          ])
                    : CustomText(
                        (priceObj?['price'] ??
                                c.specialDays[DateFormat.EEEE()
                                    .format(cellDetails.date)
                                    .toLowerCase()] ??
                                c.perNight)
                            .toString(),
                        size: 1.6,
                        weight: FontWeight.w500,
                        color: isPastDate ? Colors.black38 : null,
                      ),
              ),
              // SizedBox(height:heightSpace(.2)),
              CustomText('${cellDetails.date.day}',
                  textAlign: TextAlign.center,
                  weight: isPastDate ? FontWeight.normal : FontWeight.bold,
                  color: isPastDate ? Colors.black38 : null),
              SizedBox(height: heightSpace(.4)),
              if (dateSlug.isNotEmpty) ...[
                if (dateSlug == "s") ...[
                  Tooltip(
                    richMessage: WidgetSpan(
                        child: Row(mainAxisSize: MainAxisSize.min, children: [
                      Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomText(obj!.guestName, weight: FontWeight.w500),
                            CustomText(obj.guests, weight: FontWeight.w500),
                          ]),
                      SizedBox(width: widthSpace(2)),
                      Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 5),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              border:
                                  Border.all(color: Colors.black54, width: 1.5),
                              borderRadius: BorderRadius.circular(100)),
                          child: CustomText(
                              "${obj.total} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}",
                              weight: FontWeight.w500))
                    ])),
                    decoration: BoxDecoration(
                        color: const Color(greyBorder),
                        borderRadius: BorderRadius.circular(100)),
                    showDuration: const Duration(minutes: 1),
                    triggerMode: TooltipTriggerMode.tap,
                    child: Container(
                      height: widthSpace(8),
                      margin: EdgeInsets.only(left: widthSpace(3)),
                      padding: EdgeInsets.all(widthSpace(1)),
                      decoration: BoxDecoration(
                          color: Color(themeColor).withOpacity(.4),
                          borderRadius: const BorderRadius.horizontal(
                              left: Radius.circular(60))),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(100),
                        child: obj.guestImage.contains(".svg")
                            ? GlobalHelper.buildNetworkSvgWidget(
                          url:obj.guestImage,
                          height: 35, width: 35,
                          defaultOption: const Icon(Icons.person_2),)
                            : GlobalHelper.resolveImageUrl(obj.guestImage).isNotEmpty
                            ? Image(
                            image: GlobalHelper.buildNetworkImageProvider(
                                url: obj.guestImage,
                            ),
                        )
                            : const Icon(Icons.person_2),
                      ),
                    ),
                  )
                ] else
                  Container(
                      height: widthSpace(8),
                      margin: dateSlug == "e"
                          ? EdgeInsets.only(right: widthSpace(3))
                          : null,
                      decoration: BoxDecoration(
                          color: const Color(themeColor).withOpacity(.4),
                          borderRadius: dateSlug == "e"
                              ? const BorderRadius.horizontal(
                                  right: Radius.circular(60))
                              : null))
              ],
            ],
          ),
        );
      },
    );
  }
  discountTab(value, title,RxString discountType) {
    bool selected = discountType.value == value;
    return Expanded(
      child: InkWell(
        onTap: () {
          discountType.value = value;
        },
        child: Container(
            alignment: Alignment.center,
            decoration: selected
                ? BoxDecoration(
              color: Color(lightBlack),
              borderRadius: BorderRadius.circular(10),
            )
                : null,
            child: CustomText(title, size: 1.9, weight: FontWeight.w500,color: selected?Colors.white:null)),
      ),
    );
  }
  TableRow tableRow(int no, int fWeekday, int lastDay) {
    int last = no * 7;
    int first = last - 6;
    List<Widget> widgets = [];
    for (int n = first; n <= last; n++) {
      int day = (n + 1) - fWeekday;
      if (day > 0 && day <= lastDay)
        widgets.add(Text("${day}", textAlign: TextAlign.center));
      else {
        widgets.add(const SizedBox());
      }
    }
    return TableRow(children: widgets);
  }
}
