import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_iqama_sheet.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/screens/account_info.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../helperMethods/remote_config.dart';
import '../../helperMethods/translation_helper.dart';
import '../../helperMethods/authHelper.dart';

class Personal extends StatelessWidget {
  const Personal({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          elevation: 0,
          leading: IconButton(
              onPressed: Get.back, icon: const Icon(Icons.chevron_left)),
          actions: [
            SizedBox(
              width: widthSpace(20),
              child: IconButton(
                  onPressed: () => Get.to(() => const AccountInfo()),
                  icon: CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .edit,
                      size: 2.4,
                      weight: FontWeight.w500,
                      textOverflow: TextOverflow.visible,
                      underline: true)),
            ),
          ]),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(5)),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.3),
                      spreadRadius: 0.1,
                      blurRadius: 4,
                      offset: const Offset(1, 2),
                    ),
                  ],
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                                color: Colors.grey[200],
                                border:
                                    Border.all(color: Colors.black, width: 1),
                                shape: BoxShape.circle),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(45),
                              child: (userModel.value?.profile_image ?? "")
                                      .contains(".svg")
                                  ? GlobalHelper.buildNetworkSvgWidget(
                                url:userModel.value?.profile_image??"",
                                  defaultOption: const Icon(Icons.person),)
                                  : GlobalHelper.resolveImageUrl(userModel.value?.profile_image ??"").isNotEmpty
                                  ? Image(
                                  image: GlobalHelper.buildNetworkImageProvider(
                                      url: userModel.value?.profile_image ??"",),
                                  width: 90,
                                  height: 90,
                                  fit: BoxFit.fill,
                              )
                                  :  Padding(
                                padding: const EdgeInsets.all(30),
                                child: Icon(Icons.person,
                                    size: widthSpace(14)),
                              ),
                            )),
                        SizedBox(height: heightSpace(0.5)),
                        CustomText(
                            "${userModel.value?.first_name} ${userModel.value?.last_name}",
                            size: 2.4,
                            weight: FontWeight.w500),
                      ],
                    ),
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomText("1", size: 4, weight: FontWeight.bold),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .hostDashboard
                                  .monthOnDarent,
                              size: 1.9)
                        ])
                  ],
                ),
              ),

              SizedBox(height: heightSpace(4.5)),
              CustomText(
                  "${userModel.value!.first_name} ${Get.find<TranslationHelper>().translations.hostDashboard.confirmInfo}",
                  size: 2.4,
                  weight: FontWeight.w500),

              SizedBox(height: heightSpace(2)),
              Row(children: [
                Icon(
                  userModel.value?.userVerification?.email == "yes"
                      ? Icons.check
                      : Icons.close,
                  size: 22,
                  color: userModel.value?.userVerification?.email == "yes"
                      ? Colors.black
                      : Color(warningColor),
                ),
                SizedBox(width: widthSpace(5)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .emailAddress,
                    color: Colors.grey[800]),
              ]),
              Divider(height: heightSpace(5)),
              if (userModel.value?.yaqeenVerified == true) ...[
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .usersDashboard
                              .identityVerified,
                          size: 2.4,
                          weight: FontWeight.w500),
                      const Icon(Icons.check_circle, color: Color(successColor))
                    ])
              ] else ...[
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .verifyIdentity,
                    size: 2.4,
                    weight: FontWeight.w500),
                SizedBox(height: heightSpace(2)),
                CustomText(Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .beforeYouBookOrHost),
                SizedBox(height: heightSpace(2)),
                CommonButton(
                  title: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .getVerified,
                  onPressed: () {
                    AuthHelper.c.iqamaValue.value = 5;
                    customIqamaSheet();
                  },
                  backgroundBg: Colors.white,
                  buttonThemeColor: Colors.black,
                  horizontalPadding: 5,
                ),
              ],
              // Divider(height: heightSpace(5)),
              // SizedBox(height: heightSpace(2)),
              // CustomText(Get.find<TranslationHelper>().translations.hostDashboard.timeToCreateProfile,size: 2.3, weight: FontWeight.w500),
              // SizedBox(height: heightSpace(1)),
              // CustomText(Get.find<TranslationHelper>().translations.hostDashboard.timeToCreateProfileNote,size: 1.7),
              // SizedBox(height: heightSpace(3)),
              // SizedBox(
              //   width: double.infinity,
              //   height: heightSpace(7),
              //   child: CommonButton(title: Get.find<TranslationHelper>().translations.hostDashboard.createProfile,
              //    onPressed: (){},
              //    backgroundBg: Colors.red.withOpacity(0.9),
              //    ),
              // )
            ],
          ),
        ),
      ),
    );
  }
}
