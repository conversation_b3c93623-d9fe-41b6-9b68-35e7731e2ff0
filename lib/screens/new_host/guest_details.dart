import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/models/bookingModel.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' show DateFormat;
import 'package:url_launcher/url_launcher.dart';
import '../../helperMethods/remote_config.dart';
import '../../helperMethods/search_helper.dart';

class GuestDetails extends StatelessWidget {
  final BookingModel data;
  final bool displayPhone;
  const GuestDetails({Key? key, required this.data, this.displayPhone = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(
          horizontal: widthSpace(5), vertical: heightSpace(5)),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(3.5), vertical: widthSpace(3)),
        child: SingleChildScrollView(
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Align(
                    alignment: Alignment.topRight,
                    child: IconButton(
                        icon: const Icon(Icons.close), onPressed: Get.back)),
                SizedBox(height: heightSpace(3)),
                Row(
                    mainAxisAlignment: isHost
                        ? MainAxisAlignment.spaceBetween
                        : MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (isHost)
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .hostDashboard
                                .previousGuest!,
                            color: const Color(greyText)),
                      data.profile?.contains("svg") ?? false
                          ? GlobalHelper.buildNetworkSvgWidget(
                        url:data.profile??"",
                          height: 35, width: 35,
                        defaultOption: const Icon(Icons.person
                        ),)
                          : CircleAvatar(
                              backgroundImage: GlobalHelper.buildNetworkImageProvider(url: data.profile ??"",)
                            ),
                    ]),
                if (data.userName != null) ...[
                  CustomText(data.userName, size: 2.2),
                  SizedBox(height: heightSpace(2)),
                ],
                if (data.propertyName != null) ...[
                  CustomText(data.propertyName, color: const Color(greyText)),
                  SizedBox(height: heightSpace(2))
                ],
                CustomText(
                    "${DateFormat.MMMd().format(data.startDate!)} - ${DateFormat.MMMd().format(data.endDate!)} (${data.totalNights ?? 1} ${Get.find<TranslationHelper>().translations.hostDashboard.nights})",
                    color: const Color(greyText)),
                SizedBox(height: heightSpace(2)),
                CustomText(
                    "${data.guest} ${Get.find<TranslationHelper>().translations.hostDashboard.guests} ${Get.find<TranslationHelper>().translations.hostDashboard.sar} ${data.total}",
                    color: const Color(greyText)),
                SizedBox(height: heightSpace(2)),
                Divider(color: Colors.black.withOpacity(0.050), thickness: 10),
                if (isHost) ...[
                  SizedBox(height: heightSpace(2)),
                  CustomText(
                      "${Get.find<TranslationHelper>().translations.hostDashboard.about} ${data.userName}",
                      size: 2.2),
                  SizedBox(
                    height: heightSpace(2),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(Icons.star_border_rounded),
                      SizedBox(width: widthSpace(3)),
                      Expanded(
                          child: CustomText(
                              formatRatingString(Get.find<TranslationHelper>().translations.hostDashboard.averageRatingOutOf!,data.userRating),
                              size: 2.1,
                              color: Colors.grey[800])),
                    ],
                  ),
                  SizedBox(height: heightSpace(2)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(data.idAchieved!
                          ? Icons.verified_user_outlined
                          : Icons.privacy_tip_outlined),
                      SizedBox(width: widthSpace(3)),
                      Expanded(
                          child: CustomText(
                              data.idAchieved!
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .hostDashboard
                                      .identityAchieved
                                  : "Identity not achieved",
                              size: 2.1,
                              color: Colors.grey[800])),
                    ],
                  ),
                  SizedBox(height: heightSpace(2)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SvgPicture.asset("assets/icons/logo_black.svg",
                          width: 24,
                          colorFilter: const ColorFilter.mode(
                              Colors.black, BlendMode.srcIn)),
                      SizedBox(width: widthSpace(3)),
                      Expanded(
                          child: CustomText(
                              "${Get.find<TranslationHelper>().translations.hostDashboard.dateOfJoiningDarent} ${data.joined}",
                              size: 2.1,
                              color: Colors.grey[800])),
                    ],
                  ),
                  if (data.userAddress != null) ...[
                    SizedBox(height: heightSpace(2)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const Icon(Icons.house_outlined),
                        SizedBox(width: widthSpace(3)),
                        Expanded(
                            child: CustomText(
                          "${Get.find<TranslationHelper>().translations.hostDashboard.livesIn} ${data.userAddress}",
                          size: 2.1,
                          color: Colors.grey[800],
                        )),
                      ],
                    ),
                  ],
                  // SizedBox(height: heightSpace(2)),
                  // CustomText("View profile",size: 2.2,underline: true),
                  // SizedBox(height: heightSpace(2),),
                  // SizedBox(
                  //   height: heightSpace(7),
                  //   width: double.infinity,
                  //   child: CommonButton(title: "Send or request money", onPressed: (){},
                  //   backgroundBg: Colors.white,
                  //   buttonThemeColor: Colors.black,
                  //
                  //   ),
                  // ),
                  // SizedBox(height: heightSpace(2)),
                  // SizedBox(
                  //   height: heightSpace(7),
                  //   width: double.infinity,
                  //   child: CommonButton(title: "Communication", onPressed: (){},
                  //   backgroundBg: Colors.white,
                  //   buttonThemeColor: Colors.black,
                  //   )),
                  if (displayPhone &&
                      data.phone != null &&
                      data.phone!.isNotEmpty) ...[
                    SizedBox(height: heightSpace(2)),
                    InkWell(
                        onTap: () => launchUrl(Uri.parse('tel:${data.phone}')),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomText(
                                '${Get.find<TranslationHelper>().translations.hostDashboard.thePhone} : ',
                                color: const Color(greyText)),
                            Directionality(
                                textDirection: TextDirection.ltr,
                                child: CustomText(data.phone,
                                    color: const Color(greyText))),
                          ],
                        )),
                  ],
                  SizedBox(height: heightSpace(2)),
                  Divider(
                      color: Colors.black.withOpacity(0.050), thickness: 10),
                ],
                SizedBox(height: heightSpace(2)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .reservationDetail,
                    size: 2.2),
                SizedBox(height: heightSpace(2)),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostReservation
                              .guests,
                          size: 2),
                      // CustomText("An Offer",size: 1.9,underline: true,),
                    ]),
                SizedBox(height: heightSpace(2)),
                CustomText(
                    "${data.adults} ${Get.find<TranslationHelper>().translations.hostReservation.adults}, ${data.children} ${Get.find<TranslationHelper>().translations.hostReservation.children}",
                    size: 1.9,
                    color: const Color(greyText)),
                const Divider(),
                SizedBox(height: heightSpace(2)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostReservation
                        .accessTime,
                    size: 2),
                CustomText("${data.checkinTime} - ${data.checkoutTime}",
                    size: 1.9, color: const Color(greyText)),
                const Divider(),
                SizedBox(height: heightSpace(2)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostReservation
                        .access,
                    size: 2),
                CustomText(DateFormat.yMMMMEEEEd().format(data.startDate!),
                    size: 1.9, color: const Color(greyText)),
                if (data.departure != null) ...[
                  const Divider(),
                  SizedBox(height: heightSpace(2)),
                  CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostReservation
                          .departure,
                      size: 2),
                  CustomText(data.departure,
                      size: 1.9, color: const Color(greyText)),
                ],
                if (data.bookingCode != null) ...[
                  const Divider(),
                  SizedBox(height: heightSpace(2)),
                  CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostReservation
                          .confirmationCode,
                      size: 2),
                  CustomText(data.bookingCode,
                      size: 1.9, color: const Color(greyText)),
                ],
                SizedBox(height: heightSpace(2)),
                Divider(color: Colors.black.withOpacity(0.050), thickness: 10),
                if (isHost) ...[
                  SizedBox(height: heightSpace(2)),
                  CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .theGuestPaidIt,
                      size: 2.2),
                ],
                SizedBox(height: heightSpace(2)),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostReservation
                              .accommodation,
                          size: 2),
                      CustomText(
                          "${data.basePrice ?? data.totalAccomodation} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                          size: 2),
                    ]),
                if (data.cleaningFee! > 0) ...[
                  SizedBox(height: heightSpace(2)),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .hostDashboard
                                .cleaningFee,
                            size: 2),
                        CustomText(
                            "${data.cleaningFee} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                            size: 2),
                      ]),
                ],
                SizedBox(height: heightSpace(2)),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostReservation
                              .guestServiceFee,
                          size: 2),
                      CustomText(
                          "${data.serviceCharge} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                          size: 2),
                    ]),
                if (data.securityFee! > 0) ...[
                  SizedBox(height: heightSpace(2)),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .propertySingle
                                .securityFee,
                            size: 2),
                        CustomText(
                            "${data.securityFee} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                            size: 2),
                      ]),
                ],
                if (data.totalDiscount! > 0) ...[
                  SizedBox(height: heightSpace(2)),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .hostReservation
                                .darentDiscount,
                            size: 2),
                        CustomText(
                            "${data.totalDiscount} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                            color: const Color(warningColor),
                            size: 2),
                      ]),
                ],

                SizedBox(height: heightSpace(2)),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                          "${Get.find<TranslationHelper>().translations.hostReservation.total} (${Get.find<TranslationHelper>().translations.hostDashboard.sar})",
                          size: 2.3,
                          weight: FontWeight.w500),
                      CustomText(
                          "${data.total} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                          size: 2.3,
                          weight: FontWeight.w500),
                    ]),
                if (isHost) ...[
                  SizedBox(height: heightSpace(2)),
                  Divider(
                    color: Colors.black.withOpacity(0.050),
                    thickness: 10,
                  ),
                  SizedBox(height: heightSpace(2)),
                  CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostReservation
                          .hostFinancialCollection,
                      size: 2.2),
                  SizedBox(height: heightSpace(2)),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .hostReservation
                                .theTotalPriceOfTheStay,
                            size: 2),
                        CustomText(
                            "${data.totalAccomodation} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                            size: 2),
                      ]),
                  if (data.cleaningFee! > 0) ...[
                    SizedBox(height: heightSpace(2)),
                    Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .hostReservation
                                  .cleaningFee,
                              size: 2),
                          CustomText(
                              "${data.cleaningFee} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                              size: 2),
                        ]),
                  ],
                  SizedBox(height: heightSpace(2)),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                            child: CustomText(
                                "${Get.find<TranslationHelper>().translations.hostReservation.hostServiceFee} (${data.insurancePercent??0.0}% ${Get.find<TranslationHelper>().translations.hostDashboard.insurance} "
                                "${data.hostCommission ?? 0}% ${Get.find<TranslationHelper>().translations.hostDashboard.vatIncluded})",
                                size: 2)),
                        CustomText(
                            "${(data.hostFee ?? 0.0).toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                            color: const Color(warningColor)),
                      ]),
                  SizedBox(height: heightSpace(2)),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                            "${Get.find<TranslationHelper>().translations.hostDashboard.total} (${Get.find<TranslationHelper>().translations.hostDashboard.sar})",
                            size: 2.3,
                            weight: FontWeight.w500),
                        CustomText(
                            "${data.hostTotal} ${Get.find<TranslationHelper>().translations.hostDashboard.sr}",
                            size: 2.3,
                            color: const Color(successColor),
                            weight: FontWeight.w500),
                      ]),
                  // Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //     children: [
                  //       CustomText("Financial transactions",size: 2.3),
                  //       Icon(Icons.arrow_forward_ios)
                  //     ]),
                  //     SizedBox(height: heightSpace(2)),
                ],
                if (data.bookingCode != null) ...[
                  Divider(height: heightSpace(4)),
                  // SizedBox(height: heightSpace(2)),
                  Obx(
                    () => InkWell(
                      onTap: () async {
                        SearchHelper.c.viewReceipt(data.bookingCode);
                      },
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                                SearchHelper.c.lazyLoader.value
                                    ? Get.find<TranslationHelper>()
                                        .translations
                                        .listing
                                        .pleaseWait
                                    : "${Get.find<TranslationHelper>().translations.hostDashboard.vatInvoice} *${data.bookingCode}",
                                size: 2.3),
                            SearchHelper.c.lazyLoader.value
                                ? const SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2))
                                : const Icon(Icons.arrow_forward_ios)
                          ]),
                    ),
                  ),
                ],
                // SizedBox(height: heightSpace(2)),
                // Divider(color: Colors.black.withOpacity(0.050),thickness: 10,),
                // SizedBox(height: heightSpace(2)),
                // CustomText("Note on the calender",size: 2.2,),
                // SizedBox(height: heightSpace(2),),
                // CustomText("Add a private reminder for these dates that only you can view",size: 2,),
                // SizedBox(height: heightSpace(2),),
                // TextFormField(
                //   maxLines: 4,
                //     // inputFormatters: [LengthLimitingTextInputFormatter(640)],
                //     onChanged: (text) => (){},
                //     decoration: InputDecoration(
                //         hintText: "Write a Note",
                //         hintStyle: const TextStyle(color: Color(greyText), fontSize: 15),
                //         filled: true,
                //         fillColor: Colors.white,
                //         border: OutlineInputBorder(
                //             borderRadius: BorderRadius.circular(20),
                //             borderSide: const BorderSide(color: Colors.grey, width: 1)),
                //         enabledBorder: OutlineInputBorder(
                //             borderRadius: BorderRadius.circular(20),
                //             borderSide: const BorderSide(color: Colors.grey, width: 1)))),
                // SizedBox(height: heightSpace(2)),
                // SizedBox(
                //   height: heightSpace(7),
                //   width: double.infinity,
                //   child: CommonButton(title: "Save", onPressed: (){},
                //     backgroundBg: Colors.white,
                //     buttonThemeColor: Colors.black,
                //   )),
              ]),
        ),
      ),
    );
  }
  String formatRatingString(String backendString, dynamicRating) {
    String formattedString = backendString.replaceAll(
      RegExp(r'<span id="user-avg_rating"></span>'),
      dynamicRating, // Formats to 1 decimal place, e.g., 3.0
    );
    return formattedString;
  }
}
