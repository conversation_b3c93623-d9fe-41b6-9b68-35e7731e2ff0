import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/screens/new_host/host_calendar1.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../helperMethods/translation_helper.dart';

class HostCalendar extends StatelessWidget {
  const HostCalendar({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final currentDate = DateTime.now();
    HostDashboardController c = Get.find();
    return Obx(
      () => Padding(
        padding: EdgeInsets.all(widthSpace(viewPadding)),
        child: RefreshIndicator(
          onRefresh: () async {
            c.getCalendarProperties();
          },
          child: CustomScrollView(
            slivers: [
              // SliverToBoxAdapter(child: CustomText(Get.find<TranslationHelper>().translations.hostDashboard.calendar,size: 2.7)),
              SliverToBoxAdapter(child: SizedBox(height: heightSpace(2))),
              SliverToBoxAdapter(
                  child: CustomText(
                      "${months[Get.locale?.languageCode ?? 'en']![currentDate.month - 1]} ${currentDate.year}",
                      size: 2.2,
                      weight: FontWeight.bold,
                      color: const Color(greyText))),
              SliverToBoxAdapter(child: SizedBox(height: heightSpace(2))),
              c.calendarProperties.isEmpty
                  ? SliverToBoxAdapter(
                      child: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .youHaveNoListings,
                          size: 2.5,
                          color: Color(greyText)))
                  : SliverList.separated(
                      itemBuilder: (context, index) {
                        return InkWell(
                          onTap: () => Get.to(() =>
                              HostCalendar1(data: c.calendarProperties[index])),
                          borderRadius: BorderRadius.circular(10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                  width: widthSpace(65),
                                  child: CustomText(
                                    c.calendarProperties[index].name ?? "",
                                    size: 2.3,
                                    weight: FontWeight.bold,
                                    maxlines: 1,
                                    textOverflow: TextOverflow.ellipsis,
                                  )),
                              SizedBox(height: heightSpace(1)),
                              CustomText(
                                c.calendarProperties[index].code ?? "",
                                size: 1.9,
                                maxlines: 1,
                                textOverflow: TextOverflow.ellipsis,
                                color: const Color(greyText),
                              ),
                              SizedBox(height: heightSpace(2)),
                              Image.asset(
                                "assets/icons/calendar_dates.png",
                                scale: 1.3,
                                color: const Color(themeColor),
                              ),
                              SizedBox(height: heightSpace(3)),
                              SizedBox(
                                  width: widthSpace(25),
                                  child: CustomText(
                                      c.calendarProperties[index].slug,
                                      size: 2.3,
                                      color: const Color(themeColor),
                                      maxlines: 2,
                                      textOverflow: TextOverflow.ellipsis)),
                            ],
                          ),
                        );
                      },
                      separatorBuilder: (context, index) =>
                          Divider(height: heightSpace(5)),
                      itemCount: c.calendarProperties.length)
            ],
          ),
        ),
      ),
    );
  }
}
