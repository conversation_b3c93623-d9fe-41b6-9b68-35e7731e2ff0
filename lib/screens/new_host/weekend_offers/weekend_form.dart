import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../utils/constants.dart';

class WeekendForm extends StatelessWidget {
  const WeekendForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.chevron_left, color: Color(lightBlack)),
            onPressed: Get.back),
        title: Text('Add a new Holiday Offer'),
      ),
      body: ListView(
          padding: EdgeInsets.all(widthSpace(viewPadding)),
          children: [
            CustomText('Vacation name',size: 2.15,weight: FontWeight.bold),
            CustomText('It will not be visible to customers, it will only be visible to you',size: 1.9,color: Color(greyText)).paddingSymmetric(vertical: heightSpace(1.5)),
            CustomTextField(hint: 'Type offer name',isRoundedBorder: true,borderRadius: 10),
            SizedBox(height: heightSpace(4)),

            CustomText('The units to which you want the prices to be applied',size: 2.15,weight: FontWeight.bold),
            SizedBox(height: heightSpace(2)),
            checkBox('Check All',false),
            SizedBox(height: heightSpace(1.5)),
            checkBox('Property 1',true),
            SizedBox(height: heightSpace(4)),

            CustomText('Price application date',size: 2.15,weight: FontWeight.bold),
            SizedBox(height: heightSpace(1)),
            InkWell(
                onTap:(){},
                child: Container(
                    padding: EdgeInsets.all(widthSpace(3.5)),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(color: Color(greyBorder))
                    ),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText('Select the date',color: Color(greyText)),
                          Icon(Icons.calendar_today_outlined,color: Color(greyText),size: 20)
                        ]))),
            SizedBox(height: heightSpace(4)),

            CustomText('Vacation prices',size: 2.15,weight: FontWeight.bold),
            SizedBox(height: heightSpace(2)),
            weekendField('Thursday'),
            SizedBox(height: heightSpace(1.5)),
            weekendField('Friday'),
            SizedBox(height: heightSpace(1.5)),
            weekendField('Saturday'),
            SizedBox(height: heightSpace(1.5)),
            weekendField('Middle of the week'),

          ]),
      bottomNavigationBar: Container(
        height: heightSpace(10),
        padding: EdgeInsets.symmetric(vertical: heightSpace(1.7),horizontal: widthSpace(10)),
        decoration:BoxDecoration(
            border: Border(top:BorderSide(color: Color(greyBorder))),
            borderRadius: BorderRadius.vertical(top: Radius.circular(20))
        ),child: CommonButton(title: 'Save',onPressed: (){},backgroundBg: Color(lightBlack)),
      ),
    );
  }
  weekendField(hint){
    return Row(children: [
      SizedBox(width:widthSpace(25),child: CustomText(hint,weight: FontWeight.w500)),
      SizedBox(width: widthSpace(7)),
      Expanded(child: CustomTextField(hint: 'Write the price',isRoundedBorder: true,borderRadius: 10)),
      SizedBox(width: widthSpace(2)),
      CustomText('Riyal',weight: FontWeight.w500)
    ]);
  }
  checkBox(title,selected){
    return Row(
        mainAxisSize: MainAxisSize.min,children: [
      InkWell(
        onTap: (){},
        child: Container(
          height: 21,width: 21,
          // margin:  bottomMargin!=null? EdgeInsets.only(bottom: bottomMargin):null,
          decoration: BoxDecoration(
            color: Color(selected?lightBlack:greyBorder),
            borderRadius: BorderRadius.circular(5),
          ),
          child: selected? Icon(Icons.check,size: 14,color:Colors.white):null,
        ),
      ),
      SizedBox(width: widthSpace(2)),
      CustomText(title,color: Color(greyText))]);
  }
}