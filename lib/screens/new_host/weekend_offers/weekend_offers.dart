import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../components/common_button.dart';
import '../../../components/common_switch.dart';
import '../../../components/custom_text.dart';
import '../../../components/views_common.dart';
import 'weekend_form.dart';
import '../../../utils/constants.dart';
import '../../../utils/sizeconfig.dart';

class WeekendOffers extends StatelessWidget {
  WeekendOffers({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.chevron_left, color: Color(lightBlack)),
            onPressed: Get.back),
        title: Text('Holiday Offers'),
      ),body: ListView.separated(
        padding: EdgeInsets.all(widthSpace(viewPadding)),
        itemBuilder: item,
        separatorBuilder: (c,i)=>SizedBox(height: heightSpace(2)),
        itemCount: 4),
    );
  }
  final border = OutlineInputBorder(
      borderSide: BorderSide(color: Color(greyBorder)),
      borderRadius: BorderRadius.circular(12)
  );
  Widget item(c,i){
    return ExpansionTile(
        iconColor: Colors.black,
        shape: border,collapsedShape: border,
        clipBehavior: Clip.antiAlias,
        title: Text.rich(TextSpan(
            text: 'This offer ',
            style: TextStyle(fontWeight: FontWeight.w500,color: Colors.black),
            children: [
              TextSpan(
                  text: ' Active',
                  style: TextStyle(color: Color(successColor)))
            ])),
        tilePadding: EdgeInsets.symmetric(horizontal: widthSpace(4)),
        children: [
          Column(children: [
            CustomText('From Tuesday, May 14, 2024 to Wednesday, May 15, 2024',color:Color(greyText),size: 1.9),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText('Activation Status',size: 2.1,weight: FontWeight.w500),
                CommonSwitch(
                    onChanged: ()=>displayWarning('Are you sure to activate the prices for this offer?','Activate'),
                    isSelected: true,
                    selectedColor: true?Colors.white:Color(lightBlack),
                    unSelectedColor: Colors.black,
                    bgColor:Color(true?successColor:greyBorder),
                    selectorSize: 3,
                    padding:1.1
                ),
              ],
            ).paddingSymmetric(vertical: heightSpace(1.5)),
          ]).paddingAll(widthSpace(4)),
          Container(
            padding: EdgeInsets.all(widthSpace(4)),
            color:Color(lightBg),
            child: Row(children: [
              Expanded(child:SizedBox()),
              Expanded(child: CustomText('Thursday',size: 1.85,weight: FontWeight.w500,textAlign: TextAlign.center)),
              Expanded(child: CustomText('Friday',size: 1.85,weight: FontWeight.w500,textAlign: TextAlign.center)),
              Expanded(child: CustomText('Saturday',size: 1.85,weight: FontWeight.w500,textAlign: TextAlign.center)),
              Expanded(child: CustomText('Middle of the week',size: 1.85,weight: FontWeight.w500,textAlign: TextAlign.center))
            ]),
          ),
          Column(children: [
            SizedBox(
              height: heightSpace(20),
              child: Row(children: [
                Expanded(child:CustomText('Appartment in Jeddah, Mekkah',size: 1.9,weight: FontWeight.bold,textAlign:TextAlign.center)),
                VerticalDivider(width: 0),
                Expanded(child: CustomText('0',size: 1.9,color: Color(greyText),weight: FontWeight.w500,textAlign: TextAlign.center)),
                VerticalDivider(width: 0),
                Expanded(child: CustomText('0',size: 1.9,color: Color(greyText),weight: FontWeight.w500,textAlign: TextAlign.center)),
                VerticalDivider(width: 0),
                Expanded(child: CustomText('0',size: 1.9,color: Color(greyText),weight: FontWeight.w500,textAlign: TextAlign.center)),
                VerticalDivider(width: 0),
                Expanded(child: CustomText('10055',size: 1.9,color: Color(greyText),weight: FontWeight.w500,textAlign: TextAlign.center))
              ]),
            ),
          ]).paddingSymmetric(horizontal:widthSpace(4)),

          Row(children: [
            Expanded(child: InkWell(
              onTap: ()=>Get.to(()=>WeekendForm()),
              child: Container(
                  height: heightSpace(7),
                  color: Colors.blue.withOpacity(.1),
                  child:Center(child: CustomText('Edit',color: Colors.blue,weight: FontWeight.w500))),
            )),
            Expanded(child: InkWell(
              onTap: ()=>displayWarning('Are you sure to delete the prices for this offer?','Confirm'),
              child: Container(
                  height: heightSpace(7),
                  color: Color(warningColor).withOpacity(.1),
                  child:Center(child: CustomText('Cancel',color: Color(warningColor),weight: FontWeight.w500))),
            ))
          ])
        ]);
  }
  displayWarning(content,btnText){
    ViewsCommon.showModalBottom(BottomSheet(onClosing: () {},
        showDragHandle: false, builder:(context) {
          return SizedBox(
            height: heightSpace(30),
            child: Column(
              children: [
                AppBar(
                  automaticallyImplyLeading: false,
                  backgroundColor: Colors.white,
                  actions: [
                    IconButton(onPressed: Get.back,padding: EdgeInsets.zero, icon: Icon(Icons.close,color: Color(lightBlack)))
                  ],
                  title: CustomText('Warning',color: Color(lightBlack),weight: FontWeight.bold),
                  // elevation: 0.0,
                ),
                Divider(height: 0),
                CustomText(content,weight: FontWeight.w500,size:2.1).paddingAll(widthSpace(4.5)),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(vertical: heightSpace(2)),
                  decoration: BoxDecoration(
                      border: Border(top: BorderSide(color: Color(greyBorder))),
                      borderRadius: BorderRadius.circular(12)
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      CommonButton(title: btnText, onPressed: Get.back,backgroundBg: Color(lightBlack),horizontalPadding: widthSpace(3),verticalPadding: widthSpace(.8)),
                      CommonButton(title: 'Cancel', onPressed: Get.back,isBorder: true,backgroundBg: Color(lightBlack),horizontalPadding: widthSpace(3),verticalPadding: widthSpace(.8)),
                    ],
                  ),
                )
              ],
            ),
          );
        }));
  }
}
