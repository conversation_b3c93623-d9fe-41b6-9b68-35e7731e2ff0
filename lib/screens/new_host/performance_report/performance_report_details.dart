import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/models/host/ReportModel.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PerformanceReportDetailsScreen extends StatelessWidget {
  final Report data;
  const PerformanceReportDetailsScreen({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.chevron_left, color: Color(lightBlack)),
            onPressed: Get.back),
        title: CustomText(
            Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .performanceReports ??
                '',
            weight: FontWeight.bold,
            size: 2.5),
      ),
      body: ListView(
        padding: EdgeInsets.all(widthSpace(viewPadding)),
        children: [
          instructionBox(),
          SizedBox(height: heightSpace(2)),
          indicationWidget(),
          SizedBox(height: heightSpace(2)),
          viewsInfoCard(),
          SizedBox(height: heightSpace(4)),
          salesInfoCard(),
          SizedBox(
            height: heightSpace(4),
          ),
          reviewsInfoCard(),
          SizedBox(
            height: heightSpace(4),
          ),
          pointsInfoCard(),
        ],
      ),
    );
  }

  Widget instructionBox() {
    return Container(
      padding: EdgeInsets.all(widthSpace(viewPadding)),
      decoration: BoxDecoration(
          color: Colors.blue[100]!, borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          CustomText(
              '- ${Get.find<TranslationHelper>().translations.hostDashboard.performanceReport1}',
              size: 2.1,
              color: Colors.blueAccent,
              weight: FontWeight.w500,
              textAlign: TextAlign.start),
          const SizedBox(height: 5),
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .performanceReport2,
              size: 2.1,
              color: Colors.blueAccent,
              weight: FontWeight.w500,
              textAlign: TextAlign.start),
          const SizedBox(height: 5),
          CustomText(
              "- ${Get.find<TranslationHelper>().translations.hostDashboard.performanceReport3}",
              size: 2.1,
              color: Colors.blueAccent,
              weight: FontWeight.w500,
              textAlign: TextAlign.start),
          const SizedBox(height: 5),
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .performanceReport4,
              size: 2.1,
              color: Colors.blueAccent,
              weight: FontWeight.w500,
              textAlign: TextAlign.start),
          const SizedBox(height: 5),
          CustomText(
              '- ${Get.find<TranslationHelper>().translations.hostDashboard.performanceReport5}',
              size: 2.1,
              color: Colors.blueAccent,
              weight: FontWeight.w500,
              textAlign: TextAlign.start),
        ],
      ),
    );
  }

  indicationWidget() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            indicator(successColor),
            SizedBox(width: widthSpace(1)),
            SizedBox(
                width: widthSpace(80),
                child: CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .performanceReportPositive,
                  weight: FontWeight.w500,
                  textAlign: TextAlign.start,
                  color: Color(greyText),
                )),
          ],
        ),
        SizedBox(
          height: heightSpace(1),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            indicator(warningColor),
            SizedBox(width: widthSpace(1)),
            SizedBox(
                width: widthSpace(80),
                child: CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .performanceReportNegative,
                    weight: FontWeight.w500,
                    textAlign: TextAlign.start,
                    color: const Color(greyText))),
          ],
        ),
      ],
    );
  }

  indicator(int color) {
    return Container(
      width: widthSpace(5),
      height: heightSpace(2),
      decoration: BoxDecoration(color: Color(color), shape: BoxShape.circle),
    );
  }

  viewsInfoCard() {
    return Container(
      padding: EdgeInsets.all(widthSpace(viewPadding)),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: ViewsCommon.boxShadow),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: heightSpace(2)),
          headingOfCard(
              Get.find<TranslationHelper>().translations.utility.views),
          SizedBox(height: heightSpace(2)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .numberOfViews ??
                  '',
              value: data.views?.numberOfViews ?? 0),
          // SizedBox(height: heightSpace(1),),
          // entityOfCard(text: 'Number of clicks', value: ),
          // SizedBox(height: heightSpace(1),),
          // entityOfCard(text: 'Click rate on property', value: '20.83%')
        ],
      ),
    );
  }

  salesInfoCard() {
    return Container(
      padding: EdgeInsets.all(widthSpace(viewPadding)),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: ViewsCommon.boxShadow),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: heightSpace(2)),
          headingOfCard(
              Get.find<TranslationHelper>().translations.hostDashboard.sales!),
          SizedBox(height: heightSpace(3)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                  .translations
                  .hostDashboard
                  .sales!,
              value: data.sales?.sales ?? 0),
          SizedBox(height: heightSpace(2)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .totalReservations ??
                  '',
              value: data.sales?.totalReservations ?? 0),
          SizedBox(
            height: heightSpace(2),
          ),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .clickRateOnBooking ??
                  '',
              value: data.sales?.clickRateBooking ?? 0)
        ],
      ),
    );
  }

  reviewsInfoCard() {
    return Container(
      padding: EdgeInsets.all(widthSpace(viewPadding)),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: ViewsCommon.boxShadow),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: heightSpace(2)),
          headingOfCard(
              Get.find<TranslationHelper>().translations.sidenav.reviews,
              showIcon: true),
          SizedBox(height: heightSpace(3)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .numberOfReviews ??
                  '',
              value: data.reviews?.numberOfReviews ?? 0,
              showIcon: false),
          SizedBox(height: heightSpace(2)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                  .translations
                  .propertySingle
                  .cleanliness!,
              value: data.reviews?.cleanliness ?? 0,
              showIcon: false),
          SizedBox(height: heightSpace(2)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                  .translations
                  .tripsActive
                  .location,
              value: data.reviews?.location ?? 0,
              showIcon: false),
          SizedBox(height: heightSpace(2)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .accuracy ??
                  '',
              value: data.reviews?.accuracy ?? 0,
              showIcon: false),
          SizedBox(height: heightSpace(2)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .communication ??
                  '',
              value: data.reviews?.communication ?? 0,
              showIcon: false),
        ],
      ),
    );
  }

  pointsInfoCard() {
    return Container(
      padding: EdgeInsets.all(widthSpace(viewPadding)),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: ViewsCommon.boxShadow),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: heightSpace(2),
          ),
          headingOfCard(
              Get.find<TranslationHelper>().translations.hostDashboard.points ??
                  '',
              showIcon: true),
          SizedBox(height: heightSpace(3)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .pointsThisWeek ??
                  '',
              value: data.points?.cancellationRate ?? 0,
              showIcon: false),
          SizedBox(height: heightSpace(2)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .changesFromThePrevious ??
                  '',
              value: data.points?.acceptanceRate ?? 0,
              showIcon: false),
          SizedBox(height: heightSpace(2)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .nightsPoints ??
                  '',
              value: data.points?.decisionTime ?? 0,
              showIcon: false),
          SizedBox(height: heightSpace(2)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .ratingsPoints ??
                  '',
              value: data.points?.decisionTime ?? 0,
              showIcon: false),
          SizedBox(height: heightSpace(2)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .responseRateScore ??
                  '',
              value: data.points?.responseRate ?? 0,
              showIcon: false),
          SizedBox(height: heightSpace(2)),
          entityOfCard(
              text: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .responseSpeedPoints ??
                  '',
              value: data.points?.timelyResponse ?? 0,
              showIcon: false),
        ],
      ),
    );
  }

  headingOfCard(String text, {bool showIcon = false}) {
    return Wrap(
      direction: Axis.horizontal,
      spacing: 4,
      runAlignment: WrapAlignment.start,
      children: [
        CustomText(
          text,
          weight: FontWeight.w900,
          textAlign: TextAlign.start,
          color: Colors.black,
          size: 2.8,
        ),
        if (showIcon)
          Icon(
            Icons.info,
            color: Colors.grey,
            size: widthSpace(5),
          ),
      ],
    );
  }

  entityOfCard(
      {required String text, required double value, bool showIcon = true}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
            width: widthSpace(50),
            child: Wrap(
              direction: Axis.horizontal,
              spacing: 4,
              runAlignment: WrapAlignment.start,
              children: [
                CustomText(
                  text,
                  weight: FontWeight.w500,
                  textAlign: TextAlign.start,
                  color: Colors.black,
                  size: 2.1,
                  maxlines: 2,
                ),
                if (showIcon)
                  Icon(
                    Icons.info,
                    color: Colors.grey,
                    size: widthSpace(5),
                  ),
              ],
            )),
        const Spacer(),
        CustomText(
          '$value',
          weight: FontWeight.w600,
          textAlign: TextAlign.start,
          color: Colors.grey,
          size: 2.1,
        ),
      ],
    );
  }
}
