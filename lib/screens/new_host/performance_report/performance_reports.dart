import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/new_host/performance_report/performance_report_details.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';

import '../../../models/host/host_property.dart';
import '../../../utils/constants.dart';

class PerformanceReports extends StatelessWidget {
  PerformanceReports({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.chevron_left, color: Color(lightBlack)),
            onPressed: Get.back),
        title: CustomText(
            Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .performanceReports ??
                '',
            weight: FontWeight.bold,
            size: 2.5),
      ),
      body: Obx(
        () => Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
          Container(
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(3), vertical: widthSpace(1)),
            decoration: BoxDecoration(
              border: Border.all(
                // color:const Color(greyBorder),
                width: .5,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton(
                  isExpanded: true,
                  value: c.selectedProperty.value,
                  hint: CustomText(
                      c.selectedProperty.value?.name ?? c.properties.first.name,
                      weight: FontWeight.bold,
                      maxlines: 2,
                      textOverflow: TextOverflow.ellipsis),
                  items: c.properties
                      .map((e) => DropdownMenuItem(
                            value: e,
                            child: Row(
                              children: [
                                Expanded(
                                    child: CustomText(e.name,
                                        maxlines: 2,
                                        weight: FontWeight.bold,
                                        textOverflow: TextOverflow.ellipsis)),
                                CustomText(e.propertyCode,
                                    size: 2.1,
                                    weight: FontWeight.w500,
                                    textOverflow: TextOverflow.ellipsis)
                              ],
                            ),
                            // trailingIcon: CustomText(e.propertyCode,size:2.1,weight: FontWeight.w500,textOverflow: TextOverflow.ellipsis)
                          ))
                      .toList(),
                  onChanged: (HostProperty? property) {
                    c.getWeeklyScoring(property!.id);
                    c.selectedProperty.value = property;
                  }),
            ),
          ),
          // DropdownMenu<HostProperty>(
          //     expandedInsets: EdgeInsets.zero,
          //     menuHeight: heightSpace(40),
          //     initialSelection: c.properties.first,
          //     onSelected: (HostProperty? property){
          //       c.getWeeklyScoring(property!.id);
          //       c.selectedProperty.value = property;
          //     },
          //     hintText: 'Select a Property to view Reports',
          //     inputDecorationTheme:  InputDecorationTheme(
          //         border: OutlineInputBorder(
          //             borderRadius: BorderRadius.circular(4))),
          //     dropdownMenuEntries: c.properties.map((e) =>DropdownMenuEntry(value: e, label: e.name,
          //         trailingIcon: CustomText(e.propertyCode,size:2.1,weight: FontWeight.w500)
          //     )).toList()),
          Container(
            margin: EdgeInsets.only(top: widthSpace(viewPadding)),
            padding: EdgeInsets.symmetric(vertical: widthSpace(4)),
            decoration: BoxDecoration(
                color: Colors.blue[100]!,
                borderRadius: BorderRadius.circular(12)),
            child: Center(
                child: CustomText(
                    Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .weeklyNote ??
                        '',
                    size: 2.1,
                    color: Colors.blueAccent,
                    weight: FontWeight.w500)),
          ),
          if (c.isLoading.value) ...[
            SizedBox(height: heightSpace(1)),
            shimmer,
            SizedBox(height: heightSpace(1)),
            shimmer,
            SizedBox(height: heightSpace(1)),
            shimmer,
            SizedBox(height: heightSpace(1)),
            shimmer
          ] else
            Expanded(
              child: ListView.separated(
                  padding: EdgeInsets.only(top: widthSpace(viewPadding)),
                  itemBuilder: (context, index) => InkWell(
                        onTap: () {
                          Get.to(() => PerformanceReportDetailsScreen(
                              data: c.performanceReports[index].report!));
                        },
                        child: Container(
                          padding: EdgeInsets.all(widthSpace(4.5)),
                          decoration: BoxDecoration(
                              color: Colors.grey[300],
                              borderRadius: BorderRadius.circular(13)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: CustomText(
                                    '${Get.find<TranslationHelper>().translations.hostDashboard.performanceReports} ${c.performanceReports[index].startDate}',
                                    weight: FontWeight.w500),
                              ),
                              const Icon(Icons.chevron_right)
                            ],
                          ),
                        ),
                      ),
                  separatorBuilder: (c, i) => SizedBox(height: heightSpace(1)),
                  itemCount: c.performanceReports.length),
            )
        ]).paddingAll(widthSpace(viewPadding)),
      ),
    );
  }

  Widget shimmer = Shimmer(
      gradient: LinearGradient(colors: [
        Colors.grey[300]!,
        Colors.grey[100]!,
      ]),
      child: Container(
        height: heightSpace(7),
        decoration: BoxDecoration(
            color: Colors.grey[300], borderRadius: BorderRadius.circular(13)),
      ));
}
