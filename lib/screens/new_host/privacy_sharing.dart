import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/host_listtile.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../helperMethods/translation_helper.dart';
import '../../helperMethods/authHelper.dart';

class PrivacyAndSharing extends StatelessWidget {
  const PrivacyAndSharing({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          elevation: 0,
          leading: IconButton(
              onPressed: Get.back,
              icon: const Icon(
                Icons.chevron_left,
                size: 40,
              )),
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(viewPadding), vertical: heightSpace(3.5)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .privacySharing,
                  size: 3.2),
              SizedBox(height: heightSpace(3.5)),
              // extraComponent(name:  "Request your personal data",function: (){}),
              // SizedBox(height: heightSpace(2)),
              // CustomText("We'll create a file for you to download your personal data",color: Colors.black54,size: 1.9),
              // SizedBox(height: heightSpace(2),),
              // Divider(),
              // SizedBox(height: heightSpace(2)),
              HostListTile(
                title: Get.find<TranslationHelper>()
                    .translations
                    .account
                    .deleteAcc,
                subtitle: Get.find<TranslationHelper>()
                    .translations
                    .account
                    .deleteAccNote,
                showBorder: true,
                screen: AuthHelper.c.deleteAccount,
              ),
              // Divider(),
              // SizedBox(height: heightSpace(2)),

              // extraComponent(name:  "Sharing",function: (){}),
              // SizedBox(height: heightSpace(2),),
              // CustomText("Decide how your profile and activity will be shown to others",color: Colors.black54,
              //     size: 1.9),
              // SizedBox(height: heightSpace(2)),
            ],
          ),
        ));
  }
}
