import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/screens/new_host/host_reservation_details.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../helperMethods/translation_helper.dart';
import '../../models/bookingModel.dart';
import 'package:flutter/material.dart';
import '../../components/custom_text.dart';
import '../../utils/constants.dart';
import '../../utils/sizeconfig.dart';

class ReservationItem extends StatelessWidget {
  final BookingModel data;
  final Function(BookingModel)? contactHost, accept, decline;
  final bool isLoading;
  final bool isPendingReview, displayPhone;
  const ReservationItem(
      {super.key,
      required this.data,
      this.contactHost,
      this.isLoading = false,
      this.accept,
      this.decline,
      this.isPendingReview = false,
      this.displayPhone = false});
  @override
  Widget build(BuildContext context) {
    bool checkingOut = data.status == "Accepted" &&
        data.endDate!.difference(DateTime.now()).inDays < 1 &&
        data.endDate!.difference(DateTime.now()).inDays > -1;
    return InkWell(
      borderRadius: BorderRadius.circular(15.0),
      onTap: () {
        Get.to(() => HostReservationDetails(
              data: data,
            ));
        // Get.dialog(GuestDetails(data: data,displayPhone:displayPhone));
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
          color: const Color(greyBorder).withOpacity(0.15),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: heightSpace(1)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      data.profile?.contains("svg") ?? false
                          ?  GlobalHelper.buildNetworkSvgWidget(
                        url:data.profile??"",
                        height: heightSpace(5),
                        width: widthSpace(5),
                        defaultOption: const Icon(Icons.person),)
                          : CircleAvatar(
                              backgroundImage: GlobalHelper.buildNetworkImageProvider(url: data.profile ??"",)
                            ),
                      SizedBox(
                        width: widthSpace(2),
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomText(data.userName ?? "",
                              size: 2.2, weight: FontWeight.w500),
                          SizedBox(
                            height: heightSpace(1),
                          ),
                          LayoutBuilder(
                            builder: (context, constraints) {
                              final maxWidth =
                                  widthSpace(45); // 45% of available width
                              final minWidth = widthSpace(
                                  20); // Set your desired minimum width

                              return Container(
                                constraints: BoxConstraints(
                                  minWidth: minWidth,
                                  maxWidth: maxWidth,
                                ),
                                padding: const EdgeInsets.all(viewPadding),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(5.0),
                                  border: Border.all(color: Colors.grey[300]!),
                                ),
                                child: CustomText(
                                  data.propertyName ?? "",
                                  size: 1.3,
                                  weight: FontWeight.normal,
                                  color: const Color(greyText),
                                  maxlines: 1,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                      const Spacer(),
                      CustomText(
                        "#${data.bookingCode ?? ""}",
                        size: 1.5,
                        weight: FontWeight.w500,
                        color: Color(greyText),
                      ),
                      // CustomText("${Get.find<TranslationHelper>().translations.hostDashboard.sar} ${data.total??" "}",size: 2.2, weight: FontWeight.w600,color: const Color(themeColor),),
                    ],
                  ),
                  SizedBox(height: heightSpace(1.7)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              SvgPicture.asset(
                                  "assets/icons/host_new/calendar.svg",
                                  height: heightSpace(2.5),
                                  colorFilter: const ColorFilter.mode(
                                      Color(0xff7AA826), BlendMode.srcIn)),
                              SizedBox(
                                width: widthSpace(2),
                              ),
                              const CustomText("Reservation Date",
                                  size: 1.5,
                                  weight: FontWeight.w500,
                                  color: Color(greyText)),
                            ],
                          ),
                          SizedBox(height: heightSpace(0.7)),
                          SizedBox(
                            width: widthSpace(45),
                            child: CustomText(
                                "From ${DateFormat.MMMd().format(data.startDate!)} to ${DateFormat.MMMd().format(data.endDate!)}",
                                size: 1.5,
                                weight: FontWeight.w500),
                          ),
                        ],
                      ),
                      const Spacer(),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              SvgPicture.asset(
                                  "assets/icons/host_new/guest.svg",
                                  height: heightSpace(2.5),
                                  colorFilter: const ColorFilter.mode(
                                      Color(0xff7AA826), BlendMode.srcIn)),
                              SizedBox(
                                width: widthSpace(2),
                              ),
                              const CustomText("Guest No",
                                  size: 1.5,
                                  weight: FontWeight.w500,
                                  color: Color(greyText)),
                            ],
                          ),
                          SizedBox(height: heightSpace(0.7)),
                          CustomText(
                              "${data.adults ?? 0} Adult ${data.children} Kids",
                              size: 1.5,
                              weight: FontWeight.w500),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: heightSpace(1.0)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (checkingOut)
                        CustomText(
                          "${data.condition!}!",
                          color: const Color(0xff005248),
                          weight: FontWeight.w500,
                          size: 1.5,
                        ),
                      const Spacer(),
                      // SvgPicture.asset("assets/icons/host_new/sale.svg",width: widthSpace(4),),
                      CustomText(
                        "${data.total!} SAR",
                        color: const Color(0xff7AA826),
                        weight: FontWeight.w500,
                        size: 1.5,
                      ),
                    ],
                  )
                ],
              ),
            ),
            if (data.status == "Unpaid" || data.status == "Processing")
              renderBottomView(data.status!),
            if (data.status == 'Pending')
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () => accept!(data),
                      borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(12)),
                      child: renderBottomView(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostReservation
                              .accept!,
                          success: true),
                    ),
                  ),
                  Expanded(
                    child: InkWell(
                      onTap: () => decline!(data),
                      borderRadius: const BorderRadius.only(
                          bottomRight: Radius.circular(12)),
                      child: renderBottomView(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostReservation
                              .decline!,
                          success: false),
                    ),
                  )
                ],
              ),
            if (contactHost != null && !data.isUserDelete!)
              InkWell(
                onTap: isLoading ? null : () => contactHost!(data),
                borderRadius:
                    const BorderRadius.vertical(bottom: Radius.circular(12)),
                child: Container(
                    height: heightSpace(6.7),
                    padding: EdgeInsets.symmetric(vertical: widthSpace(3)),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        border:
                            Border(top: BorderSide(color: Colors.grey[400]!))),
                    child: isLoading
                        ? Image.asset("assets/loader.gif")
                        : isPendingReview
                            ? CustomText(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .hostDashboard
                                    .evaluation,
                                size: 2.3)
                            : CustomText(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .hostDashboard
                                    .message,
                                size: 2.3)),
              ),
          ],
        ),
      ),
    );
  }

  renderBottomView(String title, {bool? success}) {
    return Container(
        height: heightSpace(4.7),
        padding: EdgeInsets.symmetric(vertical: widthSpace(1)),
        alignment: Alignment.center,
        decoration: BoxDecoration(
            border: Border(
          top: BorderSide(color: Colors.grey[400]!),
        )),
        child: CustomText(title,
            color: success == null
                ? null
                : Color(success ? successColor : warningColor),
            size: 2.3));
  }
}
