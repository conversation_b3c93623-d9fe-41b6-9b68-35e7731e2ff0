import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../utils/constants.dart';

class PriceForm extends StatelessWidget {
  const PriceForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.chevron_left, color: Color(lightBlack)),
            onPressed: Get.back),
        title: Text('Add a new offer'),
      ),
      body: ListView(
          padding: EdgeInsets.all(widthSpace(viewPadding)),
          children: [
            CustomText('Offer name',size: 2.15,weight: FontWeight.bold),
            CustomText('It will not be visible to customers, it will only be visible to you',size: 1.9,color: Color(greyText)).paddingSymmetric(vertical: heightSpace(1.5)),
            CustomTextField(hint: 'Type offer name',isRoundedBorder: true,borderRadius: 10),
            SizedBox(height: heightSpace(4)),

            CustomText('Discount value',size: 2.15,weight: FontWeight.bold),
            CustomText('The discount value entered will be deducted from the base pric',size: 1.9,color: Color(greyText)).paddingSymmetric(vertical: heightSpace(2)),
            Container(
              height: 50,
              child: Row(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                Expanded(
                  child:CustomTextField(hint: '0',isRoundedBorder: true,borderRadius: 10,suffixIcon: Text('Riyal')),
                ),
                SizedBox(width:widthSpace(2)),
                Expanded(
                  child: Container(
                    padding: EdgeInsets.only(left:widthSpace(2)),
                    decoration:BoxDecoration(
                      border: Border.all(color: Color(greyBorder),width: .5),
                      borderRadius: BorderRadius.circular(10)
                    ),
                    child: DropdownButton<String>(
                      underline: SizedBox(),
                        hint: CustomText('Percentage or Amount',size: 1.8,color: Color(greyText)),
                        icon: Icon(Icons.keyboard_arrow_down,color: Color(greyText)),
                        items: [
                          DropdownMenuItem(value: 'percent', child: Text('Percentage')),
                          DropdownMenuItem(value: 'amount', child: Text('Amount'))],
                        onChanged: (_){}),
                  ),
                ),
              ]),
            ),
            SizedBox(height: heightSpace(4)),

            CustomText('The units you want the offer to apply to',size: 2.15,weight: FontWeight.bold),
            SizedBox(height: heightSpace(2)),
            checkBox('Check All',false,noWidth: true),
            SizedBox(height: heightSpace(1.5)),
            checkBox('Property 1',true,noWidth: true),
            SizedBox(height: heightSpace(4)),

            CustomText('Date of application of the offer',size: 2.15,weight: FontWeight.bold),
            SizedBox(height: heightSpace(1)),
            CustomText('The offer will apply to the date of arrival and not the date of booking confirmation',size: 1.9,color: Color(greyText)).paddingSymmetric(vertical: heightSpace(1)),
            InkWell(
                onTap:(){},
                child: Container(
                  padding: EdgeInsets.all(widthSpace(3.5)),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Color(greyBorder))
                    ),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                      CustomText('Select the date',color: Color(greyText)),
                      Icon(Icons.calendar_today_outlined,color: Color(greyText),size: 20)
                    ]))),
            SizedBox(height: heightSpace(4)),

            CustomText('Days the offer price applies',size: 2.15,weight: FontWeight.bold),
            SizedBox(height: heightSpace(2)),
            Wrap(
                runSpacing: heightSpace(1.5),
                children: [
              checkBox('Sunday',true),
              checkBox('Sunday',true),
              checkBox('Sunday',false),
              checkBox('Sunday',true),
              checkBox('Sunday',false)
            ])

      ]),
      bottomNavigationBar: Container(
        height: heightSpace(10),
        padding: EdgeInsets.symmetric(vertical: heightSpace(1.7),horizontal: widthSpace(10)),
        decoration:BoxDecoration(
          border: Border(top:BorderSide(color: Color(greyBorder))),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20))
        ),child: CommonButton(title: 'Save',onPressed: (){},backgroundBg: Color(lightBlack)),
      ),
    );
  }
  checkBox(title,selected,{noWidth=false}){
    return SizedBox(
      width: noWidth?null:widthSpace(40),
      child: Row(
          mainAxisSize: MainAxisSize.min,children: [
        InkWell(
          onTap: (){},
          child: Container(
            height: 21,width: 21,
            // margin:  bottomMargin!=null? EdgeInsets.only(bottom: bottomMargin):null,
            decoration: BoxDecoration(
              color: Color(selected?lightBlack:greyBorder),
              borderRadius: BorderRadius.circular(5),
            ),
            child: selected? Icon(Icons.check,size: 14,color:Colors.white):null,
          ),
        ),
      SizedBox(width: widthSpace(2)),
      CustomText(title,color: Color(greyText))]),
    );
  }
}
