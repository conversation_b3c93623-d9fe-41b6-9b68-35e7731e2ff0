import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/chat_helper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';

import '../../components/views_common.dart';
import '../../helperMethods/remote_config.dart';
import '../../utils/constants.dart';
import '../../utils/routes.dart';

class HostInbox extends StatelessWidget {
  const HostInbox({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding),
            vertical: heightSpace(viewPadding / 2)),
        child: NotificationListener<ScrollEndNotification>(
          onNotification: (notification) {
            final metrics = notification.metrics;
            if (metrics.atEdge) {
              bool isTop = metrics.pixels == 0;
              if (!isTop && ChatHelper.c.hasMoreChatHeads) {
                ChatHelper.c.isLazyLoading.value = true;
                ChatHelper.c.getChatHeads(paginate: true);
              }
            }
            return true;
          },
          child: RefreshIndicator(
            onRefresh: () async {
              ChatHelper.c.getChatHeads();
            },
            child: Obx(() => CustomScrollView(
                  slivers: [
                    if (ChatHelper.c.isLoading.value)
                      shimmers()
                    else if (ChatHelper.c.chatHeads.isEmpty) ...[
                      SliverFillRemaining(
                          child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                            SvgPicture.asset('assets/noChat.svg',
                                height: heightSpace(25)),
                            SizedBox(height: heightSpace(2)),
                            CustomText(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .message
                                    .emptyInbox,
                                size: 1.8,
                                textAlign: TextAlign.center,
                                maxlines: 2)
                          ]))
                    ] else ...[
                      SliverList.separated(
                          itemBuilder: (context, index) {
                            String? stringDate;
                            DateTime? date;
                            if (ChatHelper
                                    .c.chatHeads[index].latestMessageDate !=
                                null) {
                              date = DateTime.parse(ChatHelper
                                  .c.chatHeads[index].latestMessageDate!);
                              int days = DateTime.now().difference(date).inDays;
                              stringDate = days > 1
                                  ? DateFormat.yMMMEd().format(date)
                                  : days == 1
                                      ? "yesterday"
                                      : "Today";
                            }
                            return InkWell(
                              onTap: () {
                                ChatHelper.c.selectedChatHead =
                                    ChatHelper.c.chatHeads[index];
                                ChatHelper.c.scrollController =
                                    ScrollController();
                                ChatHelper.c
                                    .getChats(ChatHelper.c.chatHeads[index].id);
                                Get.toNamed(Routes.inbox)?.then((value) {
                                  ChatHelper.c.clearMessages();
                                  ChatHelper.c.scrollController.dispose();
                                  ChatHelper.c.isLazyLoading.value = false;
                                });
                                ChatHelper.c.chatHeads[index].unread = false;
                                ChatHelper.c.chatHeads.refresh();
                              },
                              child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    profileImage(ChatHelper.c.chatHeads[index]
                                        .converser?.profileImage),
                                    SizedBox(width: widthSpace(4)),
                                    Expanded(
                                      child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                if (stringDate != null)
                                                  CustomText(stringDate,
                                                      size: 1.8,
                                                      color:
                                                          const Color(greyText))
                                              ],
                                            ),
                                            Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  CustomText(
                                                      ChatHelper
                                                          .c
                                                          .chatHeads[index]
                                                          .converser!
                                                          .firstName!,
                                                      size: 2.1),
                                                  if (ChatHelper
                                                          .c
                                                          .chatHeads[index]
                                                          .converser
                                                          ?.location !=
                                                      null) ...[
                                                    Container(
                                                      width: 2,
                                                      height: 2,
                                                      margin:
                                                          EdgeInsets.symmetric(
                                                              horizontal:
                                                                  widthSpace(
                                                                      1)),
                                                      decoration:
                                                          const BoxDecoration(
                                                        color: Colors.black54,
                                                      ),
                                                    ),
                                                    CustomText(
                                                        ChatHelper
                                                            .c
                                                            .chatHeads[index]
                                                            .converser
                                                            ?.location!,
                                                        color: Colors.black54),
                                                  ]
                                                ]),
                                            CustomText(
                                                ChatHelper.c.chatHeads[index]
                                                        .latestMessage ??
                                                    "",
                                                size: 1.9,
                                                weight: ChatHelper
                                                        .c
                                                        .chatHeads[index]
                                                        .unread!
                                                    ? FontWeight.bold
                                                    : null,
                                                color: ChatHelper
                                                        .c
                                                        .chatHeads[index]
                                                        .unread!
                                                    ? Colors.black
                                                    : const Color(greyText),
                                                maxlines: 1,
                                                textOverflow:
                                                    TextOverflow.ellipsis),
                                            if (ChatHelper.c.chatHeads[index]
                                                    .details !=
                                                null) ...[
                                              // Row(children: [
                                              //   CustomText(
                                              //       "${ChatHelper.c.chatHeads[index].details?.type}",
                                              //       size: 1.9),
                                              //
                                              //   if(ChatHelper.c.chatHeads[index].details?.status!=null)...[
                                              //     Container(
                                              //       width: 2,height: 2,
                                              //       margin: EdgeInsets.symmetric(horizontal: widthSpace(1)),
                                              //       decoration: const BoxDecoration(
                                              //           color: Colors.black54,
                                              //           shape: BoxShape.circle
                                              //       ),
                                              //     ),
                                              //     CustomText(
                                              //         ChatHelper.c.chatHeads[index].details!.status!),
                                              //   ],
                                              // ]),
                                              if (ChatHelper.c.chatHeads[index]
                                                      .details?.startDate !=
                                                  null)
                                                CustomText(
                                                    "${months[Get.locale?.languageCode ?? 'en']![ChatHelper.c.chatHeads[index].details!.startDate!.month - 1]} ${ChatHelper.c.chatHeads[index].details!.startDate!.day} - "
                                                    "${months[Get.locale?.languageCode ?? 'en']![ChatHelper.c.chatHeads[index].details!.endDate!.month - 1]} ${ChatHelper.c.chatHeads[index].details!.endDate!.day}",
                                                    size: 1.9,
                                                    color: Colors.black54)
                                            ]
                                          ]),
                                    ),
                                  ]),
                            );
                          },
                          separatorBuilder: (context, index) =>
                              Divider(height: heightSpace(3)),
                          itemCount: ChatHelper.c.chatHeads.length),
                    ]
                  ],
                )),
          ),
        ),
      ),
    );
  }

  shimmers() {
    return SliverFillRemaining(
        child: Shimmer(
      gradient: ViewsCommon.shimmerGradient,
      child: ListView.separated(
        itemBuilder: (ctx, i) {
          return Row(
            children: [
              CircleAvatar(radius: widthSpace(7)),
              SizedBox(width: widthSpace(4)),
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                Container(
                  height: 10,
                  width: 100,
                  color: Colors.white,
                ),
                Container(
                  height: 10,
                  margin: const EdgeInsets.symmetric(vertical: 5),
                  width: 60,
                  color: Colors.white,
                ),
                Container(
                  margin: EdgeInsets.only(bottom: 5),
                  height: 10,
                  width: 30,
                  color: Colors.white,
                ),
                Container(
                  height: 10,
                  width: 120,
                  color: Colors.white,
                ),
              ]),
            ],
          );
        },
        separatorBuilder: (ctx, i) =>
            Divider(height: heightSpace(3), color: Colors.black),
        itemCount: 5,
      ),
    ));
  }

  profileImage(String? image) {
    return ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: (image ?? "").contains(".svg")
            ? GlobalHelper.buildNetworkSvgWidget(
          url:image??"",
          height: widthSpace(14),
          width: widthSpace(14),
          defaultOption: Image.asset(
              "assets/default-image.png",
              width: widthSpace(14),
              height: widthSpace(14),
              fit: BoxFit.fill),)
            : Image(
            image: GlobalHelper.buildNetworkImageProvider(url: image ??""),
                width: widthSpace(14),
                height: widthSpace(14),
                fit: BoxFit.fill,
        ));
  }
}
