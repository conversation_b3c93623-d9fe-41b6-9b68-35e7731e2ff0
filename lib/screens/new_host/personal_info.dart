import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_iqama_sheet.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/floating_text_field.dart';
import 'package:darent/components/host/host_radio.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../helperMethods/translation_helper.dart';
import '../../helperMethods/authHelper.dart';

class PersonalInfo extends StatelessWidget {
  const PersonalInfo({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          elevation: 0,
          leading: IconButton(
              onPressed: Get.back, icon: const Icon(Icons.chevron_left))),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(3)),
        child: Obx(
          () => Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Align(
                  alignment: Alignment.centerLeft,
                  child: CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .editPersonalInfo,
                      size: 2.5,
                      weight: FontWeight.w500)),
              SizedBox(height: heightSpace(2)),
              InkWell(
                  onTap: AuthHelper.c.toggleFirst,
                  child: CustomText(
                      AuthHelper.c.firstNameEnabled.value
                          ? Get.find<TranslationHelper>()
                              .translations
                              .usersProfile
                              .save
                          : Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .edit,
                      size: 1.9,
                      underline: true,
                      weight: FontWeight.w500)),
              const SizedBox(height: 5),
              FloatingTextField(
                controller: AuthHelper.c.firstName,
                labelText: Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .firstName!,
                errorText: AuthHelper.c.firstError.value,
                enabled: AuthHelper.c.firstNameEnabled.value,
                textStyle: TextStyle(fontSize: heightSpace(2.3)),
                borderType: const UnderlineInputBorder(),
              ),
              SizedBox(height: heightSpace(1.5)),
              InkWell(
                  onTap: AuthHelper.c.toggleLast,
                  child: CustomText(
                      AuthHelper.c.lastNameEnabled.value
                          ? Get.find<TranslationHelper>()
                              .translations
                              .usersProfile
                              .save
                          : Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .edit,
                      size: 1.9,
                      underline: true,
                      weight: FontWeight.w500)),
              const SizedBox(height: 5),
              FloatingTextField(
                controller: AuthHelper.c.lastName,
                labelText: Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .lastName!,
                errorText: AuthHelper.c.lastError.value,
                enabled: AuthHelper.c.lastNameEnabled.value,
                textStyle: TextStyle(fontSize: heightSpace(2.3)),
                borderType: const UnderlineInputBorder(),
              ),
              SizedBox(height: heightSpace(1.5)),
              InkWell(
                  onTap: () => AuthHelper.c
                      .openChangeEmailSheet(), //AuthHelper.c.toggleEmail,
                  child: CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .edit,
                      size: 1.9,
                      underline: true,
                      weight: FontWeight.w500)),
              const SizedBox(height: 5),
              FloatingTextField(
                controller: AuthHelper.c.email,
                labelText: Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .emailAddress!,
                inputType: TextInputType.emailAddress,
                errorText: AuthHelper.c.emailError.value,
                enabled: false,
                textStyle: TextStyle(fontSize: heightSpace(2.3)),
                borderType: const UnderlineInputBorder(),
              ),
              SizedBox(height: heightSpace(1.5)),
              Align(
                  alignment: Alignment.centerLeft,
                  child: CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .usersProfile
                          .birthDate,
                      size: 1.8,
                      color: const Color(greyText))),
              Container(
                  width: double.maxFinite,
                  margin: AuthHelper.c.dobError.value
                      ? const EdgeInsets.only(bottom: 8)
                      : null,
                  padding: EdgeInsets.only(
                      bottom: heightSpace(2), top: heightSpace(1.7)),
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              color: AuthHelper.c.dobError.value
                                  ? Colors.red[700]!
                                  : Colors.black))),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                          AuthHelper.c.dob.value == null
                              ? Get.find<TranslationHelper>()
                                  .translations
                                  .hostDashboard
                                  .notProvided
                              : formDateFormat.format(AuthHelper.c.dob.value!),
                          size: 2.1,
                          color: AuthHelper.c.dob.value == null
                              ? const Color(greyText)
                              : null),
                      InkWell(
                          onTap: AuthHelper.c.selectDob,
                          child: const Icon(Icons.calendar_month_outlined))
                    ],
                  )),
              if (AuthHelper.c.dobError.value)
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .jqueryValidation
                        .required,
                    color: Colors.red[700],
                    size: 1.55),
              SizedBox(height: heightSpace(1.5)),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .usersProfile
                          .gender,
                      size: 1.8,
                      color: const Color(greyText)),
                  InkWell(
                      onTap: showGenderSheet,
                      child: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .edit,
                          size: 1.9,
                          underline: true,
                          weight: FontWeight.w500)),
                ],
              ),
              Container(
                  width: double.maxFinite,
                  padding: EdgeInsets.only(
                      bottom: heightSpace(2), top: heightSpace(.6)),
                  decoration: const BoxDecoration(
                      border: Border(bottom: BorderSide(color: Colors.black))),
                  child: CustomText(
                      userModel.value?.gender == null
                          ? Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .notProvided
                          : userModel.value!.gender == "male"
                              ? Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .male
                              : Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .female,
                      color: AuthHelper.c.dob.value == null
                          ? const Color(greyText)
                          : null)),

              SizedBox(height: heightSpace(1.5)),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .governmentId,
                    color: const Color(greyText),
                    size: 2.1,
                    weight: FontWeight.w500),
                userModel.value?.yaqeenVerified == true
                    ? const Icon(Icons.check_circle, color: Color(successColor))
                    : InkWell(
                        onTap: () {
                          AuthHelper.c.iqamaValue.value = 5;
                          customIqamaSheet();
                        }, //AuthHelper.c.verificationCode.toggle,
                        child: CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .hostDashboard
                                .edit,
                            size: 1.9,
                            underline: true,
                            weight: FontWeight.w500)),
              ]),
              // if (AuthHelper.c.verificationCode.value) ...[
              //   SizedBox(height: heightSpace(3)),
              //   Container(
              //     width: double.maxFinite,
              //     padding:
              //     EdgeInsets.only(bottom: heightSpace(2), top: heightSpace(1.7)),
              //     decoration: const BoxDecoration(
              //       border: Border(
              //         bottom: BorderSide(
              //           color:  Color.fromARGB(255, 230, 230, 230),
              //         ),
              //       ),
              //     ),
              //     child: Row(
              //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //       children: [
              //         CustomText(
              //           AuthHelper.c.iqamaValue.value==0
              //               ?AuthHelper.c.dobHijriDate.value==null
              //               ?Get.find<TranslationHelper>().translations.hostDashboard.dateOfBirth
              //               :"${AuthHelper.c.dobHijriDate.value!.year}-${GlobalHelper.twoNumberFormat(AuthHelper.c.dobHijriDate.value!.month)}-${GlobalHelper.twoNumberFormat(AuthHelper.c.dobHijriDate.value!.day)}"
              //               :AuthHelper.c.dobVerification.value == null
              //               ?Get.find<TranslationHelper>().translations.hostDashboard.dateOfBirth
              //               : formDateFormat.format(AuthHelper.c.dobVerification.value!),
              //           size: 2.1,
              //           color: AuthHelper.c.dobVerification.value == null
              //               ? const Color(greyText)
              //               : null,
              //           weight: FontWeight.w500,
              //         ),
              //         InkWell(
              //           onTap:AuthHelper.c.selectDobVerification,
              //           child: const Icon(Icons.calendar_month_outlined),
              //         ),
              //       ],
              //     ),
              //   ),
              //   SizedBox(height:heightSpace(2.5)),
              //   CustomTextField(
              //     controller: AuthHelper.c.iqma,
              //     maxLength: 10,
              //     hint: Get.find<TranslationHelper>().translations.usersProfile.enterYourIqama,
              //     inputType: TextInputType.number,
              //   ),
              //   SizedBox(height: heightSpace(2.5)),
              //   Row(
              //     mainAxisAlignment: MainAxisAlignment.end,
              //     children: [
              //       Row(
              //         children: [
              //           Radio<int>(
              //             value: 0,
              //             groupValue: AuthHelper.c.iqamaValue.value,
              //             onChanged:AuthHelper.c.updateIqamaValue,
              //           ),
              //           const CustomText("Hijri"),
              //         ],
              //       ),
              //       Row(
              //         children: [
              //           Radio<int>(
              //             value: 1,
              //             groupValue: AuthHelper.c.iqamaValue.value,
              //             onChanged:AuthHelper.c.updateIqamaValue,
              //           ),
              //           const CustomText("Gregorian"),
              //         ],
              //       ),
              //     ],
              //   ),
              //   SizedBox(height: heightSpace(2.5)),
              //   Align(
              //       alignment: Alignment.bottomRight,
              //       child: CommonButton(title: Get.find<TranslationHelper>().translations.usersProfile.save,
              //           onPressed: AuthHelper.c.submitIqma,
              //           isLoading: AuthHelper.c.isLoading.isTrue,
              //           backgroundBg: Colors.black,
              //           horizontalPadding: 8))
              // ],
              Divider(color: Colors.black, height: heightSpace(6))
            ],
          ),
        ),
      ),
    );
  }

  showGenderSheet() {
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        maxChildSize: .45,
        initialChildSize: .45,
        expand: false,
        builder: (context, scrollController) {
          return Obx(
            () =>
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Align(
                  alignment: Alignment.topRight,
                  child:
                      InkWell(onTap: Get.back, child: const Icon(Icons.clear))),
              SizedBox(height: heightSpace(2)),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .gender,
                  size: 2.3,
                  weight: FontWeight.w500),
              SizedBox(height: heightSpace(3)),
              HostRadio<String?>(
                  title: Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .male,
                  value: "male",
                  parent: userModel.value?.gender,
                  onPressed: selectGender),
              SizedBox(height: heightSpace(1.5)),
              HostRadio<String?>(
                  title: Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .female,
                  value: "female",
                  parent: userModel.value?.gender,
                  onPressed: selectGender),
              const Spacer(),
              Container(
                  height: heightSpace(9),
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(5), vertical: heightSpace(1.5)),
                  decoration: const BoxDecoration(
                      border:
                          Border(top: BorderSide(color: Color(greyBorder)))),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Obx(
                      () => CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .usersProfile
                              .save,
                          backgroundBg: Colors.black,
                          borderRadius: 5,
                          isLoading: AuthHelper.c.isLoading.value,
                          minimumSize: Size.fromWidth(widthSpace(25)),
                          onPressed: userModel.value?.gender == null
                              ? null
                              : AuthHelper.c.submitGender),
                    ),
                  ))
            ]).paddingAll(widthSpace(viewPadding)),
          );
        }));
  }

  selectGender(String? value) {
    userModel.value?.gender = value;
    userModel.refresh();
  }
}
