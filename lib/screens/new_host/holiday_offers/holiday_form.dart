import 'package:darent/components/common_checkbox.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';

import '../../../components/custom_text.dart';
import 'package:get/get.dart';

import '../../../components/custom_textfield.dart';

class HolidayForm extends StatelessWidget {
  const HolidayForm({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.chevron_left, color: Color(lightBlack)),
            onPressed: Get.back),
        title: Text('Add a new Eid price'),
      ),
      body: ListView(
        padding: EdgeInsets.all(widthSpace(viewPadding)),
        children: [
          CustomText('Select an Eid',size: 2.15,weight: FontWeight.bold),
          SizedBox(height: heightSpace(1.5)),
          Row(children: [
            eidComponent('Eid-ul-Fitr',true),
            SizedBox(width: widthSpace(4)),
            eidComponent('Eid-ul-Adha',false),
          ]),
          SizedBox(height: heightSpace(4)),
          CustomText('Offer name',size: 2.15,weight: FontWeight.bold),
          CustomText('It will not be visible to customers, it will only be visible to you',size: 1.9,color: Color(greyText)).paddingSymmetric(vertical: heightSpace(1.5)),
          CustomTextField(hint: 'Type offer name',isRoundedBorder: true,borderRadius: 10),

          SizedBox(height: heightSpace(4)),
          CustomText('The units to which you want the prices to be applied',size: 2.15,weight: FontWeight.bold),
          CommonCheckBox(onPressed: (){}, title: 'ChEck All',isSelected: true,isBg:true),
          SizedBox(height: heightSpace(2)),
          checkBox('Check All',false),
          SizedBox(height: heightSpace(1.5)),
          checkBox('Property 1',true),
          // SizedBox(height: heightSpace(4)),


        ]),
    );
  }
  checkBox(title,selected){
    return Row(
        mainAxisSize: MainAxisSize.min,children: [
      InkWell(
        onTap: (){},
        child: Container(
          height: 21,width: 21,
          // margin:  bottomMargin!=null? EdgeInsets.only(bottom: bottomMargin):null,
          decoration: BoxDecoration(
            color: Color(selected?lightBlack:greyBorder),
            borderRadius: BorderRadius.circular(5),
          ),
          child: selected? Icon(Icons.check,size: 14,color:Colors.white):null,
        ),
      ),
      SizedBox(width: widthSpace(2)),
      CustomText(title,color: Color(greyText))]);
  }
  eidComponent(title,selected){
    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(vertical: widthSpace(4)),
        decoration: BoxDecoration(
            color:selected?Color(lightBlack):Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: ViewsCommon.boxShadow,
        ),alignment: Alignment.center,child: CustomText(title,color: selected?Colors.white:null,weight: FontWeight.w500),
      ),
    );
  }
}
