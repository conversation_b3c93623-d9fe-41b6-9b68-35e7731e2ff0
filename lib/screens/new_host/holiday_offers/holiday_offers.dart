import 'package:darent/components/common_switch.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../components/common_button.dart';
import '../../../components/custom_text.dart';
import '../../../utils/constants.dart';
import 'holiday_form.dart';

class HolidayOffers extends StatelessWidget {
  HolidayOffers({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.chevron_left, color: Color(lightBlack)),
            onPressed: Get.back),
        title: Text('Holiday Offers'),
      ),
      body: ListView.separated(
          padding: EdgeInsets.all(widthSpace(viewPadding)),
          itemBuilder: item,
          separatorBuilder: (c, i) => SizedBox(height: heightSpace(2)),
          itemCount: 4),
      bottomNavigationBar: Container(
        height: heightSpace(10),
        padding: EdgeInsets.symmetric(vertical: heightSpace(1.7),horizontal: widthSpace(10)),
        decoration:BoxDecoration(
            border: Border(top:BorderSide(color: Color(greyBorder))),
            borderRadius: BorderRadius.vertical(top: Radius.circular(20))
        ),child: CommonButton(
          title: 'Add a new offer',
          onPressed: ()=>Get.to(()=>HolidayForm()),
          backgroundBg: Color(lightBlack)),
      ),
    );
  }
  final border = OutlineInputBorder(
      borderSide: BorderSide(color: Color(greyBorder)),
      borderRadius: BorderRadius.circular(12)
  );
  Widget item(c,i){
    return ExpansionTile(
        iconColor: Colors.black,
        shape: border,collapsedShape: border,
        clipBehavior: Clip.antiAlias,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText('Eid-ul-Fitr',size: 2.1,weight: FontWeight.w500),
            Text.rich(TextSpan(
                text: '1 ',
                style: TextStyle(fontWeight: FontWeight.w500,color: Colors.black),
                children: [
                  TextSpan(
                      text: ' Disaled',
                      style: TextStyle(color: Color(greyText),fontWeight: FontWeight.normal))
                ])),
          ],
        ),
        tilePadding: EdgeInsets.symmetric(horizontal: widthSpace(4)),
        children: [
          Column(children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText('Activation Status',size: 2.1,weight: FontWeight.w500),
                CommonSwitch(
                    onChanged: ()=>displayWarning('Are you sure to activate the prices for this offer?','Activate'),
                    isSelected: false,
                    selectedColor: Color(lightBlack),
                    unSelectedColor: Colors.black,
                    bgColor:Color(greyBorder),
                    selectorSize: 3,
                    padding:1.1
                ),
              ],
            ).paddingSymmetric(vertical: heightSpace(1.5)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText('Applied to',size: 2.1,weight: FontWeight.w500),
                SizedBox(width: widthSpace(2)),
                Expanded(child: CustomText('An apartment characterized by a simple and integrated character',weight: FontWeight.w500,color: Color(greyText),textOverflow: TextOverflow.ellipsis))
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText('Applied in',size: 2.1,weight: FontWeight.w500),
                SizedBox(width: widthSpace(2)),
                CustomText('Eid-ul-Fitr',weight: FontWeight.w500,color: Color(greyText),textOverflow: TextOverflow.ellipsis)
              ],
            ).paddingSymmetric(vertical: heightSpace(1.5)),
            Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText('Tuesday, April 9, 2024',size: 2.1,weight: FontWeight.w500),
                  CustomText('3000 Riyal',weight: FontWeight.w500,color: Color(greyText))
                ]),
            SizedBox(height: heightSpace(1.5)),
            Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText('Wednesday, April 10, 2024',size: 2.1,weight: FontWeight.w500),
                  CustomText('3000 Riyal',weight: FontWeight.w500,color: Color(greyText))
                ]),
            SizedBox(height: heightSpace(1.5)),
            Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText('Thursday, April 11, 2024',size: 2.1,weight: FontWeight.w500),
                  CustomText('3000 Riyal',weight: FontWeight.w500,color: Color(greyText))
                ]),
            SizedBox(height: heightSpace(1.5)),
            Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText('Friday, April 12, 2024',size: 2.1,weight: FontWeight.w500),
                  CustomText('3200 Riyal',weight: FontWeight.w500,color: Color(greyText))
                ]),
          ]).paddingSymmetric(horizontal: widthSpace(4)),
          SizedBox(height: widthSpace(4)),
          Row(children: [
            Expanded(child: InkWell(
              onTap: ()=>Get.to(()=>HolidayForm()),
              child: Container(
                  height: heightSpace(7),
                  color: Colors.blue.withOpacity(.1),
                  child:Center(child: CustomText('Edit',color: Colors.blue,weight: FontWeight.w500))),
            )),
            Expanded(child: InkWell(
              onTap: ()=>displayWarning('Are you sure to delete the prices for this offer?','Confirm'),
              child: Container(
                  height: heightSpace(7),
                  color: Color(warningColor).withOpacity(.1),
                  child:Center(child: CustomText('Cancel',color: Color(warningColor),weight: FontWeight.w500))),
            ))
          ])
        ]);
  }
  displayWarning(content,btnText){
    ViewsCommon.showModalBottom(BottomSheet(onClosing: () {},
        showDragHandle: false, builder:(context) {
          return SizedBox(
            height: heightSpace(30),
            child: Column(
              children: [
                AppBar(
                  automaticallyImplyLeading: false,
                  backgroundColor: Colors.white,
                  actions: [
                    IconButton(onPressed: Get.back,padding: EdgeInsets.zero, icon: Icon(Icons.close,color: Color(lightBlack)))
                  ],
                  title: CustomText('Warning',color: Color(lightBlack),weight: FontWeight.bold),
                  // elevation: 0.0,
                ),
                Divider(height: 0),
                CustomText(content,weight: FontWeight.w500,size:2.1).paddingAll(widthSpace(4.5)),
                Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(vertical: heightSpace(2)),
                  decoration: BoxDecoration(
                    border: Border(top: BorderSide(color: Color(greyBorder))),
                    borderRadius: BorderRadius.circular(12)
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      CommonButton(title: btnText, onPressed: Get.back,backgroundBg: Color(lightBlack),horizontalPadding: widthSpace(3),verticalPadding: widthSpace(.8)),
                      CommonButton(title: 'Cancel', onPressed: Get.back,isBorder: true,backgroundBg: Color(lightBlack),horizontalPadding: widthSpace(3),verticalPadding: widthSpace(.8)),
                    ],
                  ),
                )
              ],
            ),
          );
        }));
  }
}
