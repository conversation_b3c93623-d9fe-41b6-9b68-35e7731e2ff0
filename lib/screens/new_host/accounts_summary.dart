import 'package:darent/components/views_common.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../components/custom_text.dart';
import '../../utils/constants.dart';
import 'package:get/get.dart';
class AccountsSummary extends StatelessWidget {
  const AccountsSummary({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(icon:Icon(Icons.chevron_left,color: Color(lightBlack)),onPressed: Get.back),
        title: Text('Accounts Summary'),
      ),
      body: ListView(
          padding: EdgeInsets.all(widthSpace(viewPadding)),
          children: [
            Row(children: [
          CustomText('Select the period',weight: FontWeight.w500),
          SizedBox(width: widthSpace(3)),
          Container(
            // height:heightSpace(7),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(11),
              border: Border.all(color:Color(greyBorder))
            ),child:Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal:widthSpace(5),vertical: widthSpace(3)),
                  decoration: BoxDecoration(
                    border: Border(right: BorderSide(color:Color(greyBorder)))
                  ),
                  child: Icon(Icons.calendar_month,color: Color(lightBlack))),
            Container(
                padding:EdgeInsets.symmetric(horizontal: widthSpace(2)),
                child: CustomText('March 2024'))
          ])
          )
        ]),
            SizedBox(height: heightSpace(5)),
            CustomText('Select the property',weight: FontWeight.w500),
            SizedBox(height: heightSpace(2)),
            linkWidget('A simple and quiet apartment',func:propertySelection),
            SizedBox(height: heightSpace(4)),
            CustomText('Select unit',weight: FontWeight.w500),
            SizedBox(height: heightSpace(1.5)),
            linkWidget('All units',color:Color(greyText),func: unitSelection),
            SizedBox(height: heightSpace(3)),
            Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
              CustomText('A simple and quiet apartment',weight: FontWeight.bold),
              IconButton(icon: Icon(Icons.info,color: Color(greyText),size: 20),onPressed: showTerminologies)
            ]),
            // SizedBox(height: heightSpace(1.5)),
            saleDetails(),
            SizedBox(height: heightSpace(3)),
            paymentDetails(),
            paidLocation(),
            reservations()
      ])
    );
  }
  showTerminologies(){
    List list = [{
      'key':'sales','title':'Sales'},{
      'key':'commission','title': 'Commission',
    },
    {
      'key':'net-sales','title':'Net Sales',
    },{
        'key':'the-due','title':'The Due',
      },
      {
        'key':'paid','title':'Paid',
      },{
        'key':'paid-location','title':'Paid at the Location',
      },{
      'key':'reparations','title':'Reparations',
      },{
        'key':'fine','title':'Fines'
      }
    ];
    ViewsCommon.showModalBottom(BottomSheet(onClosing: () {

    },showDragHandle: false, builder:(context) {
      return SizedBox(
        height: heightSpace(95),
        // color: Colors.white,
        child: Column(
          children: [
            AppBar(
              automaticallyImplyLeading: false,
              backgroundColor: Colors.white,
              actions: [
                IconButton(onPressed: Get.back,padding: EdgeInsets.zero, icon: Icon(Icons.close,color: Color(lightBlack)))
              ],
              title: CustomText('Summary of Accounts Terminoloogy',color: Color(lightBlack),weight: FontWeight.bold),
              // elevation: 0.0,
            ),
            Divider(height: 0),
            Expanded(
              child: GridView.builder(
                padding: EdgeInsets.all(widthSpace(4)),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    mainAxisSpacing: widthSpace(5),
                    crossAxisSpacing: widthSpace(5)
                  ),
                  itemCount: list.length,
                  itemBuilder: (context, index) => Container(
                    padding: EdgeInsets.all(widthSpace(4)),
                      decoration: BoxDecoration(
                        color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                        boxShadow: ViewsCommon.boxShadow
                      ),child:Column(
                    children: [
                      Container(
                          padding: EdgeInsets.all(widthSpace(3)),
                          decoration: BoxDecoration(
                            color: Color(lightBlack).withOpacity(.1),
                            shape: BoxShape.circle
                          ),
                          child: SvgPicture.asset('assets/icons/${list[index]['key']}.svg',color: Color(lightBlack),width: widthSpace(6),fit: BoxFit.fitHeight)),
                      CustomText(list[index]['title'],weight: FontWeight.bold,size: 2.1,color: Color(lightBlack)).paddingSymmetric(vertical: heightSpace(1)),
                      CustomText('Payment is deducted from the commission and its tax',size: 1.9,textAlign: TextAlign.center)
                    ],
                  )
                  )),
            )
          ],
        ),
      );
    }));
  }
  propertySelection(){
    ViewsCommon.showModalBottom(BottomSheet(onClosing: () {

    },showDragHandle: false, builder:(context) {
      return SizedBox(
        height: heightSpace(30),
        child: Column(mainAxisSize: MainAxisSize.min,children: [
          AppBar(
            automaticallyImplyLeading: false,
            backgroundColor: Colors.white,
            actions: [
              IconButton(onPressed: Get.back,padding: EdgeInsets.zero, icon: Icon(Icons.close,color: Color(lightBlack)))
            ],
            title: CustomText('Select the property',color: Color(lightBlack),weight: FontWeight.bold),
            // elevation: 0.0,
          ),
          Divider(height: 0),
          ListTile(
              title: CustomText('A simple and quiet appartment',weight: FontWeight.w500),
              trailing: Icon(Icons.check,color: Color(lightBlack)),contentPadding: EdgeInsets.all(widthSpace(6))),
          // Icon(Icons.check)
        ]),
      );
    }));
  }
  unitSelection(){
    ViewsCommon.showModalBottom(BottomSheet(onClosing: () {

    },showDragHandle: false, builder:(context) {
      return SizedBox(
        height: heightSpace(30),
        child: Column(
          children: [
            AppBar(
              automaticallyImplyLeading: false,
              backgroundColor: Colors.white,
              actions: [
                IconButton(onPressed: Get.back,padding: EdgeInsets.zero, icon: Icon(Icons.close,color: Color(lightBlack)))
              ],
              title: CustomText('Select a Unit',color: Color(lightBlack),weight: FontWeight.bold),
              // elevation: 0.0,
            ),
            Divider(height: 0),
            Column(mainAxisSize: MainAxisSize.min,children: [
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,children: [
                CustomText('All units',weight: FontWeight.w500),
                Icon(Icons.check,color: Color(lightBlack))
              ]).paddingOnly(bottom: heightSpace(1)),
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,children: [
                CustomText('A simple and quiet place',weight: FontWeight.w500),
              ]),
              // Icon(Icons.check)
            ]).paddingAll(widthSpace(4)),
          ],
        ),
      );
    }));
  }
  paidLocation(){
    return Container(
        margin: EdgeInsets.symmetric(vertical: heightSpace(3)),
        padding: EdgeInsets.all(widthSpace(4)),
        decoration: BoxDecoration(
            border: Border.all(color:Color(greyBorder)),
            borderRadius: BorderRadius.circular(10)
        ),child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomText('Paid at the location',weight: FontWeight.w500),
          CustomText('0.00 SAR',color: Color(greyText))
        ])
    );
  }
  reservations(){
    return Container(
        padding: EdgeInsets.all(widthSpace(4)),
        decoration: BoxDecoration(
            border: Border.all(color:Color(greyBorder)),
            borderRadius: BorderRadius.circular(10)
        ),child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CustomText('Reservations',weight: FontWeight.bold),
          Row(
            children: [
              CustomText('Two reservations'),
              Icon(Icons.chevron_right,color: Color(greyText))
            ],
          )
        ])
    );
  }
  paymentDetails(){
    return Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Color(greyBorder))
        ),child:Column(
      children: [
        Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText('Payment details',weight: FontWeight.bold),
              SizedBox(height: heightSpace(3)),
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText('Paid up',weight: FontWeight.w500),
                    CustomText('525 SAR',color: Color(greyText))
                  ]),
              SizedBox(height: heightSpace(1.5)),
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText('The commission includes tax',weight: FontWeight.w500),
                    CustomText('55.27 SAR',color: Color(warningColor))
                  ])]).paddingAll(widthSpace(4)),
        Divider(),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText('The due',weight: FontWeight.bold),
                  CustomText('469.26 SAR',weight: FontWeight.bold)
                ]),
            SizedBox(height: heightSpace(1)),
            CustomText('What is due is what is paid, plus compensation, minus fines, commission and their taxes, and paid at the location',color: Color(greyText),size: 1.85)
          ],
        ).paddingAll(widthSpace(4))
      ],
    )
    );
  }
  saleDetails(){
    return Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Color(greyBorder))
        ),child:Column(
      children: [
        Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText('Sales details',weight: FontWeight.bold),
              SizedBox(height: heightSpace(3)),
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText('Sales',weight: FontWeight.w500),
                    CustomText('525 SAR',color: Color(greyText))
                  ]),
              SizedBox(height: heightSpace(1.5)),
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText('Commission',weight: FontWeight.w500),
                    CustomText('-48.27 SAR',color: Color(warningColor))
                  ]),
              SizedBox(height: heightSpace(1.5)),
              Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText('Commission tax',weight: FontWeight.w500),
                    CustomText('-7.27 SAR',color: Color(warningColor))
                  ])]).paddingAll(widthSpace(4)),
        Divider(),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText('Net sales',weight: FontWeight.bold),
                  CustomText('469.26 SAR',weight: FontWeight.bold)
                ]),
            SizedBox(height: heightSpace(1)),
            CustomText('It is sales minus commission and tax',color: Color(greyText),size: 1.85)
          ],
        ).paddingAll(widthSpace(4))
      ],
    )
    );
  }
  linkWidget(String title,{Color? color,func}){
    return GestureDetector(
      onTap: func,
      child: Container(
        padding: EdgeInsets.all(widthSpace(3)),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Color(greyBorder)),
          boxShadow: ViewsCommon.boxShadow
        ),child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText(title,weight: FontWeight.bold,color: color),
            Icon(Icons.chevron_right)
      ]),
      ),
    );
  }
}
