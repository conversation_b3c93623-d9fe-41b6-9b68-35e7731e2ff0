import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/empty_reservation.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/bookings_controller.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/helperMethods/authHelper.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/new_host/host_reservations.dart';
import 'package:darent/screens/new_host/performance_report/performance_reports.dart';
import 'package:darent/screens/new_host/prices.dart';
import 'package:darent/screens/new_host/reservation_item.dart';
import 'package:darent/screens/new_host/reviews/reviews.dart';
import 'package:darent/screens/new_host/scory_points/scoring_points.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:intl/intl.dart';
// import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:get/get.dart';

import '../../models/avg_scoring_model.dart';
import '../../utils/constants.dart';
import 'info_box.dart';

class HostHome extends StatelessWidget {
  final bookingTab;
  HostHome({Key? key, this.bookingTab}) : super(key: key);

  late BookingsController bc;
  @override
  Widget build(BuildContext context) {
    bc = Get.put(BookingsController(val: bookingTab));
    HostDashboardController c = Get.find();
    return RefreshIndicator(
      onRefresh: () async {
        c.updateOnRefresh();
      },
      child: ListView(children: [
        // Obx(()=>renderPerformanceReports(c.performanceReports.value)),
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Obx(() =>
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Expanded(
                    child: InfoBoxHorizontal(
                  name: CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .account
                        .currentBalance,
                    size: 1.8,
                    weight: FontWeight.normal,
                    textAlign: TextAlign.start,
                    maxlines: 1,
                    textOverflow: TextOverflow.clip,
                  ),
                  value: RichText(
                    text: TextSpan(
                      text: "${(AuthHelper.c.totalSales.value ?? 0.0)}",
                      style: TextStyle(
                        fontSize: heightSpace(2.35),
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                      children: <TextSpan>[
                        TextSpan(
                            text: " SAR",
                            style: TextStyle(
                                fontSize: heightSpace(1.8),
                                color: Colors.black,
                                fontWeight: FontWeight.normal)),
                      ],
                    ),
                  ),
                )),
                SizedBox(width: widthSpace(1.5)),
                Expanded(
                    child: InfoBoxHorizontal(
                        boxColor: const Color(themeColor).withOpacity(0.4),
                        name: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .account
                              .yourPoints,
                          size: 1.8,
                          textAlign: TextAlign.start,
                          maxlines: 1,
                          textOverflow: TextOverflow.clip,
                        ),
                        value: RichText(
                          text: TextSpan(
                            text: "${c.avgScoring.value?.score ?? 0}",
                            style: TextStyle(
                              fontSize: heightSpace(2.35),
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                            ),
                            children: <TextSpan>[
                              TextSpan(
                                  text:
                                      " ${Get.find<TranslationHelper>().translations.account.points}",
                                  style: TextStyle(
                                      fontSize: heightSpace(1.8),
                                      color: Colors.black,
                                      fontWeight: FontWeight.normal)),
                            ],
                          ),
                        ),
                        function: () {
                          if (c.selectedProperty.value == null) {
                            if (c.properties.isNotEmpty) {
                              Get.to(() => ScoringPoints());
                            } else {
                              ViewsCommon.showSnackbar(
                                  Get.find<TranslationHelper>()
                                      .translations
                                      .hostDashboard
                                      .commonMessage!,
                                  displayTime: 750,
                                  keyword: DialogKeyword.info);
                            }
                          } else {
                            Get.to(() => ScoringPoints());
                          }
                        })),
              ])),
          SizedBox(height: heightSpace(3)),
          Obx(upcomingGuests),
          SizedBox(height: heightSpace(3)),
          Obx(() => latestReviews(c, bc)),
          // renderUnitPointsH(),
          // Obx(()=>renderUnitPoints(c.avgScoring.value,c.properties)),
          // SizedBox(height: heightSpace(3)),
          // rowIcons(),
          // SizedBox(height: heightSpace(3)),
          // Obx(() => reservations()),
          // SizedBox(height: heightSpace(3)),
          // reservations('Latest Transfers',LatestTranfers()),
          // SizedBox(height: heightSpace(3)),
          // renderSalesGraph(),
          // SizedBox(height: heightSpace(3)),
          // financialAmount(),
          // SizedBox(height: heightSpace(3)),
          // lastReports()
        ]).paddingAll(widthSpace(viewPadding)),
      ]),
    );
  }

  lastReports() {
    return ViewsCommon.shadowContainer(
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      const Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        CustomText('Last Reports', size: 2.1, weight: FontWeight.bold),
        Center(child: CustomText('No report found', color: Color(greyText)))
      ]).paddingAll(widthSpace(3)),
      const Divider(),
      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        CustomText(
            Get.find<TranslationHelper>().translations.propertySingle.more),
        const Icon(Icons.navigate_next_rounded, color: Color(greyText))
      ]).paddingAll(widthSpace(2.5))
    ]));
  }

  ratingBox(String title) {
    return Expanded(
        child: ViewsCommon.shadowContainer(
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                RichText(
                  text: TextSpan(
                      text: "10/",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: heightSpace(1.7),
                          fontWeight: FontWeight.w500),
                      children: <TextSpan>[
                        TextSpan(
                            text: "10",
                            style: TextStyle(
                                color: const Color(successColor),
                                fontSize: heightSpace(1.95)))
                      ]),
                ),
                CustomText(title, size: 1.7, weight: FontWeight.w500)
              ],
            ),
            padding: widthSpace(2),
            height: heightSpace(12)));
  }

  Widget upcomingGuests() {
    return ViewsCommon.shadowContainer(
      Column(children: [
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          CustomText(
              Get.find<TranslationHelper>().translations.account.upcomingReserv,
              size: 2.2,
              weight: FontWeight.bold),

          // CustomText(Get.find<TranslationHelper>().translations.bookingMy.upcomingBook,size: 2, weight: FontWeight.bold),
          TextButton(
              onPressed: () {
                Get.to(() => HostReservations());
              },
              child: Row(
                children: [
                  CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .account
                          .viewMore,
                      size: 1.4,
                      weight: FontWeight.normal),
                  Icon(
                    Icons.navigate_next_rounded,
                    size: widthSpace(4),
                    color: Colors.black,
                  )
                ],
              ))
        ]),
        SizedBox(height: heightSpace(1)),
        bc.isLoading.value
            ? getShimmerLoading()
            : bc.data.value['upcoming-bookings'] == null ||
                    bc.data.value['upcoming-bookings']!.isEmpty
                ? CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .reservation
                        .noUpcomingBookings,
                    color: const Color(greyText))
                : ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (context, index) {
                      var item = bc.data.value['upcoming-bookings']![index];
                      return ReservationItem(
                          data: item,
                          accept: bc.acceptBooking,
                          decline: bc.declineBooking,
                          displayPhone: true);
                      Row(children: [
                        Expanded(
                            child: Center(
                                child: CustomText("#${item.bookingCode ?? ""}",
                                    size: 1.7,
                                    color: Colors.grey[700],
                                    weight: FontWeight.w500))),
                        Expanded(
                            child: Center(
                                child: CustomText(item.userName,
                                    size: 1.7,
                                    color: Colors.grey[700],
                                    weight: FontWeight.w500,
                                    textOverflow: TextOverflow.ellipsis))),
                        Expanded(
                            child: Center(
                                child: CustomText(
                                    DateFormat.MMMd('ar_SA')
                                        .format(item.startDate),
                                    size: 1.7,
                                    color: Colors.grey[700],
                                    weight: FontWeight.w500))),
                        Expanded(
                          child: Center(
                            child: Container(
                                width: widthSpace(18),
                                padding: EdgeInsets.symmetric(
                                    vertical: widthSpace(1)),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                    color: Colors.green[100],
                                    borderRadius: BorderRadius.circular(4)),
                                child: CustomText(item.status,
                                    size: 1.65, color: Colors.white)),
                          ),
                        ),
                        Expanded(
                            child: Center(
                                child: CustomText(
                                    '${item.total} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}',
                                    size: 1.7,
                                    color: Colors.grey[700],
                                    weight: FontWeight.w500))),
                      ]);
                    },
                    separatorBuilder: (context, index) =>
                        SizedBox(height: heightSpace(1.5)),
                    itemCount:
                        bc.data.value['upcoming-bookings']!.length.clamp(0, 1)),
      ]).paddingAll(widthSpace(3)),
    );
  }

  latestReviews(HostDashboardController c, BookingsController bc) {
    return ViewsCommon.shadowContainer(
      //   Column(
      // children: [
      bc.isLoading.value
          ? ViewsCommon.getShimmerLoading()
          : bc.hostReviewData.value["receiver_id"] == null ||
                  bc.hostReviewData.value["receiver_id"]!.isEmpty
              ? Padding(
                  padding:
                      EdgeInsets.symmetric(vertical: heightSpace(viewPadding)),
                  child: EmptyReservation(
                      msg: Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .noReviewAvailable!),
                )
              : Column(children: [
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .account
                                .weekRating,
                            size: 2,
                            weight: FontWeight.bold),
                        // CustomText(Get.find<TranslationHelper>().translations.hostDashboard.todayAllReview,size: 2,weight: FontWeight.bold),
                        TextButton(
                            onPressed: () {
                              if (c.performanceReports.isNotEmpty &&
                                  (c.selectedProperty.value != null ||
                                      c.properties.isNotEmpty)) {
                                Get.to(() => PerformanceReports());
                              } else {
                                ViewsCommon.showSnackbar(
                                    Get.find<TranslationHelper>()
                                        .translations
                                        .hostDashboard
                                        .commonMessage!,
                                    displayTime: 750,
                                    keyword: DialogKeyword.info);
                              }
                            },
                            child: Row(
                              children: [
                                CustomText(
                                    Get.find<TranslationHelper>()
                                        .translations
                                        .account
                                        .weekReport,
                                    size: 1.4,
                                    weight: FontWeight.normal),
                                Icon(
                                  Icons.navigate_next_rounded,
                                  size: widthSpace(4),
                                  color: Colors.black,
                                )
                              ],
                            ))
                      ]),
                  SizedBox(height: heightSpace(1)),
                  Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                            (bc.hostReviewOverAllRating["receiver_id"].rating ??
                                    0.0)
                                .toStringAsFixed(1),
                            size: 2.5,
                            weight: FontWeight.bold),
                        SizedBox(
                          width: widthSpace(2),
                        ),
                        // CustomText(Get.find<TranslationHelper>().translations.hostDashboard.todayAllReview,size: 2,weight: FontWeight.bold),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            renderRatingBar(bc
                                    .hostReviewOverAllRating["receiver_id"]
                                    .rating ??
                                0.0),
                            SizedBox(
                              height: heightSpace(1),
                            ),
                            CustomText(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .account
                                    .basedOn
                                    .replaceAll("\$number",
                                        "${bc.hostReviewData.value["receiver_id"]!.length}"),
                                size: 1.4,
                                weight: FontWeight.normal),
                          ],
                        ),
                        const Spacer(),
                        TextButton(
                            onPressed: () {
                              Get.to(() => Reviews());
                            },
                            child: Row(
                              children: [
                                CustomText(
                                    Get.find<TranslationHelper>()
                                        .translations
                                        .account
                                        .viewMore,
                                    size: 1.4,
                                    weight: FontWeight.normal),
                                Icon(
                                  Icons.navigate_next_rounded,
                                  size: widthSpace(4),
                                  color: Colors.black,
                                )
                              ],
                            ))
                      ]),
                  SizedBox(height: heightSpace(2.5)),
                  buildProgressBar(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .accuracy!,
                      bc.hostReviewOverAllRating["receiver_id"].accuracy),
                  SizedBox(height: heightSpace(1.5)),
                  buildProgressBar(
                      Get.find<TranslationHelper>()
                          .translations
                          .hostDashboard
                          .communication!,
                      bc.hostReviewOverAllRating["receiver_id"].communication),
                  SizedBox(height: heightSpace(1.5)),
                  buildProgressBar(
                      Get.find<TranslationHelper>()
                          .translations
                          .propertySingle
                          .cleanliness!,
                      bc.hostReviewOverAllRating["receiver_id"].cleanliness),
        SizedBox(height: heightSpace(1.5)),
        buildProgressBar(
            Get.find<TranslationHelper>().translations.tripsActive.location,
            bc.hostReviewOverAllRating["receiver_id"].location),
                  SizedBox(height: heightSpace(1.5)),
                  // Row(
                  //     mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  //     children: [
                  //   ratingBox(Get.find<TranslationHelper>().translations.hostDashboard.accuracy!),
                  //   ratingBox(Get.find<TranslationHelper>().translations.hostDashboard.communication!),
                  //   ratingBox(Get.find<TranslationHelper>().translations.tripsActive.location),
                  //   ratingBox(Get.find<TranslationHelper>().translations.propertySingle.cleanliness!),
                  // ]),
                  // bc.isLoading.value
                  //     ? getShimmerLoading()
                  //     : bc.hostReviewData.value["receiver_id"] == null || bc.hostReviewData.value["receiver_id"]!.isEmpty
                  //     ? const CustomText("No Review available right now",color: Color(greyText))
                  //     : ListView.separated(
                  //     shrinkWrap: true,
                  //     physics: const NeverScrollableScrollPhysics(),
                  //     itemBuilder: (context,index){
                  //       return Row(children: [
                  //         CustomText(bc.hostReviewData.value["receiver_id"]?[index]?.reviewer.name??"Faiz Ahmed",weight: FontWeight.w500, size: 1.7,),
                  //         SizedBox(width: widthSpace(2)),
                  //         Expanded(child:CustomText(bc.hostReviewData.value["receiver_id"]?[index]?.message??'Thank your for tht best service you paid',size: 1.7,weight: FontWeight.w500,textOverflow: TextOverflow.ellipsis)),
                  //         Image.asset('assets/icons/smile.png',height: widthSpace(6.5),fit: BoxFit.fitHeight,color: Color(successColor)),
                  //         // Icon(Icons.emoji_emotions_outlined,size: widthSpace(9),color: Color(lightBlack))
                  //       ]);
                  //     },
                  //     separatorBuilder: (context, index) => SizedBox(height: heightSpace(1.5)),
                  //     itemCount: bc.hostReviewData.value["receiver_id"]!.length).paddingAll(widthSpace(3)),
                ]).paddingAll(widthSpace(3)),
      // const Divider(),
      // InkWell(
      //   onTap:()=>Get.to(()=> Reviews()),
      //   child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
      //       children: [
      //         CustomText(Get.find<TranslationHelper>().translations.propertySingle.more,size: 1.8,weight: FontWeight.w500),
      //         const Icon(Icons.navigate_next_rounded,color:Color(greyText))
      //       ]).paddingSymmetric(horizontal:widthSpace(3),vertical: widthSpace(2)),
      // )
      //   ],
      // )
    );
  }

  renderRatingBar(rating) {
    return RatingBar(
      ignoreGestures: true,
      initialRating: rating,
      minRating: 0,
      direction: Axis.horizontal,
      allowHalfRating: true,
      unratedColor: Colors.grey.withOpacity(.5),
      itemCount: 5,
      itemSize: widthSpace(4),
      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
      ratingWidget: RatingWidget(
        full: const Icon(
          Icons.star,
          color: Color(themeColor),
        ),
        half: const Icon(
          Icons.star_half,
          color: Color(themeColor),
        ),
        empty: const Icon(
          Icons.star,
          color: Color(greyBorder),
        ),
      ),
      onRatingUpdate: (rating) {
        debugPrint(rating.toString());
      },
    );
  }

  Widget buildProgressBar(title, double rating) {
    final double fillWidth =
        rating / 5.0 * widthSpace(85).toDouble(); // Ensure double type

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText(title, size: 1.7, weight: FontWeight.w500),
            CustomText(rating.toStringAsFixed(2),
                size: 1.7, weight: FontWeight.w500)
          ],
        ),
        SizedBox(
          height: heightSpace(1),
        ),
        Container(
          width: widthSpace(85),
          height: heightSpace(0.5),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
            color: const Color(greyBorder),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Stack(
            // Use Stack for layering
            children: [
              Container(
                width: widthSpace(85), // Full width for background
                decoration: BoxDecoration(
                  color: const Color(greyBorder),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              Container(
                // Inner bar with dynamic width
                width: fillWidth
                    .clamp(0.0, widthSpace(85))
                    .toDouble(), // Clamp with double values
                decoration: BoxDecoration(
                  color: rating.isEqual(5.0)
                      ? const Color(successColor)
                      : rating.isGreaterThan(3.5)
                          ? const Color(themeColor)
                          : const Color(warningColor),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  balanceGuide() {
    String content1 =
        'It is the due balance that will be issued for all future invoices (the godfathers) collected from the platform, minus the commission and taxValue Added)';
    ViewsCommon.showModalBottom(BottomSheet(
        onClosing: () {},
        showDragHandle: false,
        builder: (context) {
          return SizedBox(
            height: heightSpace(35),
            child: Column(
              children: [
                AppBar(
                  automaticallyImplyLeading: false,
                  backgroundColor: Colors.white,
                  actions: [
                    IconButton(
                        onPressed: Get.back,
                        padding: EdgeInsets.zero,
                        icon: Icon(Icons.close, color: Color(lightBlack)))
                  ],
                  title: CustomText('AleFinancialTransactionrt',
                      color: Color(lightBlack), weight: FontWeight.bold),
                  // elevation: 0.0,
                ),
                Divider(height: 0),
                CustomText(content1, weight: FontWeight.w500, size: 2.1)
                    .paddingAll(widthSpace(4.5)),
                SizedBox(height: heightSpace(4)),
                Center(
                    child: CommonButton(
                        title: 'Confirm',
                        onPressed: Get.back,
                        backgroundBg: Color(lightBlack),
                        horizontalPadding: widthSpace(3),
                        verticalPadding: widthSpace(.8)))
              ],
            ),
          );
        }));
  }

//   financialGuide(){
//     String content1 = 'In cooperation with Saudi banks and STC Pay, we worked on one of the best financial transaction systems.',
//     content2 = '''First: You can specify the method of transferring funds:
// 1. Bank transfer to all banks in the Kingdom of Saudi Arabia
// Saudi Arabia.
// 2. Or via stc pay''',content3='''Second: You can specify the period for receiving the amounts: 1. Automatic direct transfer 48 hours after the guest leaves without having to wait longer than that. 2. Convert on a weekly basis choosing one day in
// week and the amount will be transferred on this day).''';
//     ViewsCommon.showModalBottom(BottomSheet(onClosing: () {},
//         showDragHandle: false, builder:(context) {
//       return SizedBox(
//         height: heightSpace(80),
//         child: Column(
//           children: [
//             AppBar(
//               automaticallyImplyLeading: false,
//               backgroundColor: Colors.white,
//               actions: [
//                 IconButton(onPressed: Get.back,padding: EdgeInsets.zero, icon: Icon(Icons.close,color: Color(lightBlack)))
//               ],
//               title: CustomText('Alert',color: Color(lightBlack),weight: FontWeight.bold),
//               // elevation: 0.0,
//             ),
//             Divider(height: 0),
//             Column(crossAxisAlignment: CrossAxisAlignment.start,children: [
//               CustomText('Our generous guest,',size: 2.2,weight: FontWeight.bold),
//               SizedBox(height: heightSpace(1)),
//               CustomText(content1,weight: FontWeight.w500,size:2.1),
//               SizedBox(height: heightSpace(3)),
//               CustomText('Advantages of the new financial transaction system',size: 2.2,weight:FontWeight.bold),
//               SizedBox(height: heightSpace(1)),
//               CustomText(content2,weight: FontWeight.w500,size:2.1),
//               SizedBox(height: heightSpace(3)),
//               CustomText(content3,weight: FontWeight.w500,size:2.1),
//               SizedBox(height: heightSpace(4)),
//               Center(child: CommonButton(title: 'Confirm', onPressed: (){},backgroundBg: Color(lightBlack),horizontalPadding: widthSpace(3),verticalPadding: widthSpace(.8)))
//               // Icon(Icons.check)
//             ]).paddingAll(widthSpace(4.5)),
//           ],
//         ),
//       );
//     }));
//   }
  // financialAmount(){
  //   return ViewsCommon.shadowContainer(
  //     Row(crossAxisAlignment: CrossAxisAlignment.stretch,children: [
  //       Expanded(
  //         child: InkWell(
  //           onTap: financialGuide,
  //             child: Center(child: CustomText("To receive financial amount",size:2.15,color: Color(greyText),textAlign: TextAlign.center))),
  //       ),
  //       VerticalDivider(),
  //       Expanded(
  //         child: InkWell(
  //           onTap: balanceGuide,
  //           child: Column(mainAxisAlignment: MainAxisAlignment.spaceEvenly,children: [
  //             CustomText('0.03',weight: FontWeight.bold,size: 2.3),
  //             CustomText("Account statement balance",size:1.9,textAlign: TextAlign.center,color: Color(greyText)),
  //           ]),
  //         ),
  //       )
  //     ]),padding:widthSpace(2),height: heightSpace(12)
  //   );
  // }
  rowIcons() {
    return InfoBox(
        image: 'icons/wallet',
        name: Get.find<TranslationHelper>().translations.listingPrice.price,
        function: () {
          Get.to(() => PriesScreen());
        });

    Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      InfoBox(
          image: 'icons/wallet_1',
          name: 'Prices',
          function: () {
            Get.to(() => PriesScreen());
          }),
      SizedBox(width: widthSpace(3)),
      Expanded(child: SizedBox()),
      SizedBox(width: widthSpace(3)),
      Expanded(child: const SizedBox()),
      // InfoBox(image: 'icons/accounting', name:'Accounts Summary',function: (){
      //   Get.to(()=>const AccountsSummary());
      // }),
      // SizedBox(width: widthSpace(3)),
      // InfoBox(image: 'icons/financial', name:'Financial Factors',function: (){
      //   Get.to(()=>const FinancialTransaction());
      // }),
    ]);
  }

  Widget reservations([Widget? screen]) {
    var list = bc.data.value["all_reservation"];
    return ViewsCommon.shadowContainer(
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: heightSpace(2)),
          CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .usersDashboard
                      .latestBookings,
                  size: 2,
                  weight: FontWeight.bold)
              .paddingSymmetric(horizontal: widthSpace(2)),
          SizedBox(height: heightSpace(1)),
          bc.isLoading.value
              ? getShimmerLoading()
              : list == null || list.isEmpty
                  ? Center(
                      child: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .doNotHaveBooking!,
                          color: const Color(greyText)))
                  : ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            var item = list[index];
                            return Row(children: [
                              Expanded(
                                  child: Center(
                                      child: CustomText(
                                          "#${item.bookingCode ?? ""}",
                                          size: 1.7,
                                          color: Colors.grey[700],
                                          weight: FontWeight.w500))),
                              Expanded(
                                  child: Center(
                                      child: CustomText(item.userName,
                                          size: 1.8,
                                          weight: FontWeight.w500,
                                          textOverflow: TextOverflow.ellipsis,
                                          color: Colors.grey[700]))),
                              Expanded(
                                  child: Center(
                                      child: CustomText(
                                          DateFormat.MMMd('ar_SA')
                                              .format(item.startDate),
                                          size: 1.8,
                                          weight: FontWeight.w500,
                                          color: Colors.grey[700]))),
                              Expanded(
                                child: Center(
                                  child: Container(
                                      padding: EdgeInsets.symmetric(
                                          vertical: widthSpace(1)),
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                          color: Colors.green[100],
                                          borderRadius:
                                              BorderRadius.circular(4)),
                                      child: CustomText(item.status,
                                          size: 1.65,
                                          color: Colors.white,
                                          weight: FontWeight.w500)),
                                ),
                              ),
                              Expanded(
                                  child: Center(
                                child: CustomText(
                                    '${item.total} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}',
                                    size: 1.8,
                                    weight: FontWeight.w500,
                                    color: Colors.grey[700]),
                              )),
                            ]);
                          },
                          separatorBuilder: (context, index) =>
                              SizedBox(height: heightSpace(1.5)),
                          itemCount: list.length.clamp(0, 3))
                      .paddingAll(widthSpace(3)),

          // for(var item in bc.data.value[bc.tab.value]!)
          // Row(children: [
          //   Expanded(child: CustomText(item.bookingCode??"")),
          //   Expanded(child: CustomText(item.userName,textOverflow: TextOverflow.ellipsis)),
          //   Expanded(child: CustomText(item.startDate)),
          //   Container(
          //     width: widthSpace(18),
          //     padding: EdgeInsets.symmetric(vertical:widthSpace(1)),
          //     margin: EdgeInsets.only(bottom: heightSpace(1)),
          //     alignment: Alignment.center,
          //     decoration: BoxDecoration(
          //       color: Colors.green[100],
          //       borderRadius: BorderRadius.circular(4)
          //     ),child:CustomText("pending",size: 1.65,color: Colors.white)
          //   )
          // ])
          //     ]).paddingAll(widthSpace(3)),
          const Divider(height: 0),
          InkWell(
              onTap: () {
                HostDashboardController c = Get.find();
                c.changeIndex(2);
              },
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .propertySingle
                            .more,
                        size: 1.8,
                        weight: FontWeight.w500),
                    const Icon(Icons.navigate_next_rounded,
                        color: Color(greyText))
                  ]).paddingSymmetric(
                  horizontal: widthSpace(3), vertical: widthSpace(2)))
        ],
      ),
    );
  }

  renderBox(icon, text, {function}) {
    return Expanded(
      child: GestureDetector(
        onTap: function,
        child: InfoBox(image: icon, name: text),
      ),
    );
  }

  renderUnitPointsH() {
    return RichText(
      text: TextSpan(
          text: Get.find<TranslationHelper>()
              .translations
              .hostDashboard
              .yourUnitPoints,
          style: TextStyle(
              color: Colors.black,
              fontSize: heightSpace(2.4),
              fontWeight: FontWeight.bold,
              fontFamily: 'PingAR+LT'),
          children: <TextSpan>[
            TextSpan(
                text: "   تتحدث يومياً",
                style: TextStyle(
                    color: const Color(greyText),
                    fontWeight: FontWeight.w500,
                    fontSize: heightSpace(1.8)))
          ]),
    ).paddingSymmetric(vertical: heightSpace(2));
  }

  // unitPointsLoader(){
  //   return Shimmer(
  //       gradient: const LinearGradient(colors: [
  //       Colors.black12,
  //       Colors.white
  //       ]),
  //       child:Container(
  //         padding:EdgeInsets.all(widthSpace(2.5)),
  //         decoration: BoxDecoration(
  //           border: Border.all(color: Color(greyBorder)),
  //           borderRadius: BorderRadius.circular(10),
  //         ),
  //         child: Column(children: [
  //           Row(children: [
  //             Container(
  //               height: widthSpace(20),
  //               width: widthSpace(20),
  //               decoration: BoxDecoration(
  //                 // color: Colors.white,
  //                 border: Border.all(width: 8),
  //                 shape: BoxShape.circle
  //               ),
  //             ),
  //             SizedBox(width: widthSpace(6)),
  //             Expanded(child: Column(
  //                 crossAxisAlignment: CrossAxisAlignment.start,children: [
  //                   Container(
  //                     height: 8,
  //                     width: widthSpace(40),
  //                     color: Colors.grey),
  //               SizedBox(height: heightSpace(1.5)),
  //               Container(
  //                   height: 8,
  //                   width: widthSpace(40),
  //                   color: Colors.grey),
  //             ]))
  //           ]).paddingAll(widthSpace(6)),
  //           const Divider(),
  //           const Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //               children: [
  //                 CustomText("More"),
  //                 Icon(Icons.navigate_next_rounded,color:Color(greyText))
  //               ]).paddingAll(widthSpace(2.5))
  //         ]),
  //       ),
  //   );
  // }
  renderUnitPoints(AvgScoringModel? scoring, List properties) {
    final score = scoring?.score ?? 0;
    return GestureDetector(
        onTap: () {
          if (properties.isNotEmpty) {
            Get.to(() => ScoringPoints());
          }
        },
        child: ViewsCommon.shadowContainer(
          Column(children: [
            Row(children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  SizedBox(
                      height: widthSpace(16),
                      width: widthSpace(16),
                      child: CircularProgressIndicator(
                          backgroundColor: const Color(greyBorder),
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Color(lightBlack)),
                          value: score / 100,
                          strokeWidth: 5,
                          strokeCap: StrokeCap.round)),
                  Column(children: [
                    CustomText('$score',
                        size: 3.1,
                        weight: FontWeight.bold,
                        color: Color(lightBlack),
                        lineSpacing: 1.0),
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .points,
                        size: 1.8,
                        color: Color(lightBlack),
                        lineSpacing: 1.0)
                  ])
                ],
              ),
              SizedBox(width: widthSpace(4)),
              Expanded(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                    CustomText(
                        Get.locale?.languageCode == 'ar'
                            ? scoring?.nameAr ?? ''
                            : scoring?.name ?? '',
                        size: 1.9,
                        weight: FontWeight.bold,
                        textOverflow: TextOverflow.ellipsis),
                    SizedBox(height: heightSpace(1.5)),
                    CustomText(
                        Get.locale?.languageCode == 'ar'
                            ? scoring?.descriptionAr ?? ''
                            : scoring?.description ?? '',
                        color: const Color(greyText),
                        size: 1.6,
                        weight: FontWeight.w500,
                        maxlines: 2,
                        textOverflow: TextOverflow.ellipsis)
                  ]))
            ]).paddingAll(widthSpace(6)),
            const Divider(height: 0),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .propertySingle
                      .more,
                  size: 1.8,
                  weight: FontWeight.w500),
              const Icon(Icons.navigate_next_rounded, color: Color(greyText))
            ]).paddingSymmetric(
                horizontal: widthSpace(3), vertical: widthSpace(2))
          ]),
        ));
  }

  renderPerformanceReports(List data) {
    return Container(
      decoration: BoxDecoration(
          gradient: LinearGradient(
              colors: [Color(lightBlack), Colors.white],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: const [0.5, 0])),
      child: InkWell(
        onTap: () {
          if (data.isNotEmpty) {
            Get.to(() => PerformanceReports());
          }
        },
        child: Container(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(4), vertical: heightSpace(2.2)),
          margin: EdgeInsets.symmetric(horizontal: widthSpace(4)),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: ViewsCommon.boxShadow),
          child:
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Row(
              children: [
                CustomText(
                    '${Get.find<TranslationHelper>().translations.hostDashboard.performanceReport}  ${formatter.format(DateTime.now())}',
                    size: 1.8,
                    weight: FontWeight.bold),
              ],
            ),
            const Icon(Icons.navigate_next_rounded, color: Color(greyText))
          ]),
        ),
      ),
    );
  }

  renderSalesGraph() {
    // final List<ChartData> chartData = [
    //   ChartData(2010, 35, 23),
    //   ChartData(2011, 38, 49),
    //   // ChartData(2012, 34, 12),
    //   // ChartData(2013, 52, 33),
    //   // ChartData(2014, 40, 30)
    // ];
    //
    //
    // return ViewsCommon.shadowContainer(SfCartesianChart(
    //     series: <CartesianSeries<ChartData, int>>[
    //       ColumnSeries<ChartData, int>(
    //           opacity: 0.9,
    //           width: 0.4,
    //           color: Color(lightBlack),
    //           dataSource: chartData,
    //           xValueMapper: (ChartData data, _) => data.x,
    //           yValueMapper: (ChartData data, _) => data.y1
    //       )
    //     ]
    // ));
  }

  Shimmer getShimmerLoading() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: widthSpace(20),
                      ),
                      CircleAvatar(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(100),
                            color: Colors.white,
                          ),
                          width: double.infinity,
                          height: 18.0,
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: heightSpace(1)),
                  Container(
                    width: double.infinity,
                    height: 18.0,
                    color: Colors.white,
                  ),
                  SizedBox(height: heightSpace(0.7)),
                  Container(
                    width: double.infinity,
                    height: 18.0,
                    color: Colors.white,
                  ),
                  SizedBox(height: heightSpace(.7)),
                  Container(
                    width: double.infinity,
                    height: 18.0,
                    color: Colors.white,
                  ),
                  SizedBox(height: heightSpace(0.2)),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: widthSpace(3)),
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                  border: Border(top: BorderSide(color: Colors.white))),
              child: Container(
                width: widthSpace(80),
                height: 18.0,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ChartData {
  ChartData(this.x, this.y, this.y1);
  final int x;
  final double y;
  final double y1;
}
