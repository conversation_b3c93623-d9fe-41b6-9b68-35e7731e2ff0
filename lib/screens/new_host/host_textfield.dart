import 'package:flutter/material.dart';

import '../../utils/constants.dart';

class HostTextField extends StatelessWidget {
  final name, controller, hint;
  final TextCapitalization textCapitalization;
  final String? Function(String?)? validator;
  final TextInputType? inputType;
  final Widget? suffix;
  final formatters;
  final int? maxlines;
  HostTextField({super.key, this.name, this.controller,this.hint,this.textCapitalization=TextCapitalization.sentences,this.validator,
    this.inputType,this.suffix,this.formatters,this.maxlines});

  @override
  Widget build(BuildContext context) {
    return TextFormField(
        controller: controller,
        textCapitalization: textCapitalization,
        validator:validator,
        inputFormatters: formatters,
        keyboardType: inputType,
        maxLines: maxlines,
        decoration: InputDecoration(
          labelText: hint,
          alignLabelWithHint: true,
          contentPadding: EdgeInsets.symmetric(vertical: 20,horizontal: 12),
          suffixIcon: suffix,
          suffixIconColor: Colors.black,
          border: inputBorder,
          enabledBorder: inputBorder
        ));
  }
  final inputBorder = OutlineInputBorder(
      borderSide: const BorderSide(color: Color(greyBorder)),
      borderRadius: BorderRadius.circular(10));
}