import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';


class InfoBox extends StatelessWidget {
  final String? image,name,value;
  final double verticalPadding,horizontalPadding;
  dynamic function;
    InfoBox({
     Key? key,
     required this.image,
     required this.name,
     this.value,
     this.function,this.verticalPadding=3.3,this.horizontalPadding=8
   }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: function,
      child: Container(
        height: heightSpace(15),
          padding: EdgeInsets.symmetric(horizontal:widthSpace(horizontalPadding/2),vertical: widthSpace(verticalPadding)),
          decoration: BoxDecoration(
              color:Colors.white,
              // boxShadow: ViewsCommon.boxShadow,
              border: Border.all(color: const Color(greyBorder)),
              borderRadius: BorderRadius.circular(8)
          ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset("assets/$image.svg",colorFilter:ColorFilter.mode(Color(lightBlack), BlendMode.srcIn),width:widthSpace(8)),
            if(value != null)...[
              // SizedBox(height: heightSpace(.5)),
              CustomText(
                value,
                textAlign: TextAlign.center,
                size: 1.8,
              ),
            ],
            // SizedBox(height: heightSpace(.5)),
            CustomText(
              name,
              size: 1.65,
              weight: FontWeight.w500,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class InfoBoxHorizontal extends StatelessWidget {
  final String? image;
  final Widget name, value;
  int? color;
  Color? boxColor;
  bool minSize;
  final double verticalPadding,horizontalPadding;
  dynamic function;
  InfoBoxHorizontal({
    Key? key,
    this.image,
    required this.name,
    this.color,
    this.boxColor,
    this.minSize = false,
    required this.value,
    this.function,this.verticalPadding=3.3,this.horizontalPadding=8
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: function,
      child: Container(
        height: minSize ? heightSpace(9) : heightSpace(11),
        padding: EdgeInsets.all(widthSpace(verticalPadding)),
        decoration: BoxDecoration(
            color:  boxColor??Colors.white,
            border: Border.all(color: const Color(greyBorder)),
            borderRadius: BorderRadius.circular(8)
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if(image != null)...[
            if(color != null)...[
              SvgPicture.asset("assets/$image.svg",
                  colorFilter: ColorFilter.mode(Color(color!), BlendMode.srcIn),width:widthSpace(6)),
            ]
            else...[
              SvgPicture.asset("assets/$image.svg",width:widthSpace(6 )),
            ],
            ],
            SizedBox(width: widthSpace(2),),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    name,
                    if(function!= null)
                    Icon(Icons.navigate_next_rounded,size: widthSpace(5))
                  ],
                ),
                SizedBox(height: heightSpace(1)),
                value
              ],
            ),
          ],
        ),
      ),
    );
  }
}
