import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class YourPayment extends StatelessWidget {
  const YourPayment({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
      elevation: 0,
          leading: IconButton(
              onPressed: Get.back, icon: const Icon(Icons.chevron_left,size: 40,)),
              
          
          ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(3.5)),
        child:  Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const CustomText("Your Payments",size: 3.2,),
          SizedBox(height: heightSpace(3.5),),
          CustomText("Once You Have A Reservation, You can Track Your Payment and Refunds Here.",size: 2,),
          


          

              ],
       
        ),
      ),
      bottomNavigationBar: Container(
        height: heightSpace(15),
        child:  Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
           const  Divider(),
            Padding(padding: EdgeInsets.symmetric(horizontal: widthSpace(7), vertical: heightSpace(1)),
            child:   Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
              const CustomText("To Find Another Batch, Please "),
              SizedBox(height: heightSpace(1),),
              const CustomText("Help Center",size: 2.2,underline: true,),
            ],),
            ),
          ],
        )
      ),
    );
  }
}