import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';


class ReturnPayment extends StatelessWidget {
  const ReturnPayment({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    
    return Scaffold(
      appBar: AppBar(
      elevation: 0,
          leading: IconButton(
              onPressed: Get.back, icon: const Icon(Icons.chevron_left,size: 40,)),
              
          
          ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(3.5)),
        child:  Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          const CustomText("Return Payment Methods",size: 3.2,),
          SizedBox(height: heightSpace(3.5),),
          CustomText("Once You Have A Reservation, You Can Track Your Payment And Refunds Here",size: 2,),
          SizedBox(height: heightSpace(3.5),),
          SizedBox(
            height: heightSpace(7),
            child: CommonButton(title: "Set Payout Payments",
            backgroundBg: Colors.black.withOpacity(0.7),
            horizontalPadding: 5,
             onPressed: (){
              Get.bottomSheet(
                shape:  RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.0),
                    topRight: Radius.circular(20.0),
                  ),
                ),
              
                backgroundColor: Colors.white,
                SizedBox(
                  height: 300,
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              SizedBox(width: widthSpace(5),),
                              CustomText("Pay By",size: 2.3,),
                              IconButton(
                  onPressed: () {
                    Get.back(); // Close the bottom sheet
                  },
                  icon: Icon(Icons.close),
                              ),
                            ],
                          ),
                        ),
                        Divider(
                          thickness: 1,
                        ),
                        Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                               SvgPicture.asset("assets/credit_card.svg",width: widthSpace(6) ,color: Colors.black),
                              SizedBox(width: 16),
                              CustomText(
                              "Credit card or debit card",
                              color: Colors.grey[800],
                              weight: FontWeight.w500,
                              size: 2.1,),
                            ],
                          ),
                        ),
                        Divider(
                          thickness: 1,
                        ),
                        Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                               SvgPicture.asset("assets/paypal.svg",width: widthSpace(9)),
                              SizedBox(width: 16),
                              CustomText(
                              "PayPal",
                              color: Colors.grey[800],
                              weight: FontWeight.w500,
                              size: 2.1,),
                            ],
                          ),
                        ),
                        
                      ],
                    ),
                ),
              );
              
             }),
          )


          

              ],
        //]
        ),
      ),
    );
  }
  extraComponent(String image, {required name,Widget? screen, function}) {
    return InkWell(
      onTap: function ??
          () {
            if (screen != null) {
              Get.to(() => screen);
            }
          },
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset("assets/$image.svg",width: widthSpace(6) ,color: Colors.black),
        SizedBox(width: widthSpace(7.5)),
        Expanded(
          child: CustomText(
              name,
              color: Colors.grey[800],
              weight: FontWeight.w500,
              size: 2.1,),
        ),
          SizedBox(width: widthSpace(8)),
          const Icon(Icons.arrow_forward_ios, size: 20,)
      ]),
    );
  }
}
