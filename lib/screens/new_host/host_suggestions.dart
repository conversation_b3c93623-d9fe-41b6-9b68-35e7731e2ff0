import 'package:darent/components/common_button.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../components/custom_text.dart';
import '../../components/custom_textfield.dart';
import '../../utils/constants.dart';
class HostSuggestions extends StatelessWidget {
  const HostSuggestions({Key? key}) : super(key: key);
  final suggestion = '''I suggest adding a profile for the host to write an overview about himself and also rate him
the page
**
A message from the Gather In platform **
“This good proposal is being worked on.”''';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          leading: IconButton(icon:Icon(Icons.chevron_left,color: Color(lightBlack)),onPressed: Get.back),
          title: Text("Host's Suggestions")),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(widthSpace(6)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText('We at Darent platform are keen to ensure that Enjoy the best hosting experience possible',
                  size: 2.15,weight: FontWeight.w500,lineSpacing: 1.5),
              CustomText(
                'Therefore, from this page, you can add a proposal for us to work on, and the other hosts will vote on your proposal, and you can also Vote on their proposals',
                  size: 2.15,weight: FontWeight.w500,lineSpacing: 1.5),
              SizedBox(height: heightSpace(4)),
              CustomText('The proposals that will receive the highest votes, we will study them, work on them, and provide them',size: 2.15,weight: FontWeight.w500,lineSpacing: 1.5),
              SizedBox(height: heightSpace(4)),
              headingText('Important ','= affects my experience directly and from It is necessary to add or improve it'),
              headingText('Average ','= somewhat affected and it would be nice to add it Or improve it'),
              headingText('Not important ','= it has no effect on my experience and is not important'),
              SizedBox(height: heightSpace(2)),
              SizedBox(
                width: widthSpace(60),
                child: ElevatedButton.icon(
                    onPressed: addSuggestion,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.grey[100],shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10))),
                    label: CustomText('Send',weight: FontWeight.bold),icon: Icon(Icons.add_circle_outline_rounded,color: Colors.black)),
              ),
              ListView.separated(
                  shrinkWrap: true,
                  primary: false,
                  padding: EdgeInsets.only(top: heightSpace(2)),
                  itemBuilder: (context, index) => suggestionItem(index%2==0),
                  separatorBuilder:(c, i) =>SizedBox(height: heightSpace(2.5)),
                  itemCount: 10)
        ]),
      ),
    );
  }
  addSuggestion(){
    return ViewsCommon.showModalBottom(BottomSheet(onClosing: () {},
        showDragHandle: false, builder:(context) {
          return SizedBox(
            height: heightSpace(45),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                AppBar(
                    automaticallyImplyLeading: false,
                    backgroundColor: Colors.white,
                    actions: [
                      IconButton(onPressed: Get.back,padding: EdgeInsets.zero, icon: Icon(Icons.close,color: Color(lightBlack)))
                    ],
                    title: CustomText('Add a suggestion',color: Color(lightBlack),weight: FontWeight.bold)),
                Divider(height: 0),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText('Add a suggestion',weight: FontWeight. bold).paddingSymmetric(vertical: heightSpace(2)),
                    CustomTextField(hint: 'Write here',isRoundedBorder: true,maxlines: 5),
                    CustomText('200 charcters',size: 1.9,weight: FontWeight.w500)
                  ],
                ).paddingAll(widthSpace(viewPadding)),
                Spacer(),
                Container(
                  padding: EdgeInsets.all(widthSpace(3)),
                decoration: BoxDecoration(
                  border: Border(top: BorderSide(color: Color(greyBorder))),
                  borderRadius: BorderRadius.vertical(top:Radius.circular(14))
                ),
                    child: CommonButton(title: 'Send', onPressed: (){}))
              ],
            ),
          );
        }));;
  }
  suggestionItem(bool isVoted){
    return Container(
      clipBehavior:  Clip.antiAlias,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Color(greyBorder)),
        boxShadow: ViewsCommon.boxShadow
      ),child: Column(
        children: [
        Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText('Host / Adbullah',weight: FontWeight.bold,size: 2.1),
              SizedBox(height: heightSpace(1.5)),
              CustomText(suggestion,weight: FontWeight.w500,color: Color(greyText)),
              SizedBox(height: heightSpace(1.5)),
              CustomText(isVoted?'Voting results':'Vote to see the voting results',weight: FontWeight.w500),
        ]).paddingAll(widthSpace(3.5)),
          isVoted
              ?Row(children: [
            votingResult('90.3','Important',Colors.green),
            votingResult('7.2','Moderate',Colors.blue),
            votingResult('2.6','Not Important',Colors.yellow[600]!)
          ])
          :Row(children: [
            coloredContainer('Important',Colors.green),
            coloredContainer('Moderate',Colors.blue),
            coloredContainer('Not Important',Colors.yellow[600]!)
          ]),
    ]),
    );
  }
  votingResult(text1,text2,Color color){
    return Expanded(child: SizedBox(
        height: heightSpace(7),
        child:Center(child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomText('$text1%',color: color,size: 1.9,weight: FontWeight.w500),
            CustomText(text2,color: color,weight: FontWeight.w500),

          ],
        ))));
  }
  coloredContainer(text,Color color){
    return Expanded(child: Container(
        height: heightSpace(7),
        color: color.withOpacity(.1),
        child:Center(child: CustomText(text,color: color,weight: FontWeight.w500))));
  }
  headingText(head,content){
    return RichText(
      text: TextSpan(
          text: head,
          style: TextStyle(
              color: Colors.black,
              fontSize: heightSpace(2.15),fontWeight: FontWeight.bold,fontFamily:'PingAR+LT',height: 1.5),
          children: <TextSpan>[
            TextSpan(
                text:content,
                style: TextStyle(fontWeight: FontWeight.w500))
          ]),
    );
  }
}
