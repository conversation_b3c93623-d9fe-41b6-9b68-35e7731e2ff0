import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/screens/promotion/promo_form.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../helperMethods/translation_helper.dart';

class Promotions extends StatelessWidget {
  const Promotions({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    HostDashboardController c = Get.find();
    return Scaffold(
        appBar: AppBar(
          centerTitle: false,
          leading: IconButton(
            icon: CircleAvatar(
                maxRadius: 15,
                backgroundColor: Colors.grey[200],
                child: const Icon(Icons.chevron_left, color: Colors.black)),
            onPressed: Get.back,
          ),
          title: CustomText(
              Get.find<TranslationHelper>().translations.promocode.promotions,
              size: 2.4,
              weight: FontWeight.bold),
          elevation: 0,
          backgroundColor: Colors.transparent,
          actions: [
            // IconButton(
            //     onPressed: ()=>Get.to(()=>const PromoForm()),
            //     icon: const Icon(Icons.add)),
            IconButton(
                icon: CircleAvatar(
                    maxRadius: 16,
                    backgroundColor: Colors.grey[200],
                    child: const Icon(Icons.add_circle_outline,
                        color: Colors.black)),
                onPressed: () => Get.to(() => const PromoForm())),
          ],
        ),
        body: Obx(
          () => Column(
            children: [
              c.promotions.isEmpty
                  ? Center(
                      child: Padding(
                        padding: EdgeInsets.only(top: heightSpace(30)),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.card_giftcard,
                              size: 80,
                              color: Colors.grey,
                            ),
                            CustomText(Get.find<TranslationHelper>()
                                .translations
                                .promocode
                                .noPromotionsAvailable),
                          ],
                        ),
                      ),
                    )
                  : Expanded(
                      child: ListView.separated(
                        padding: EdgeInsets.all(widthSpace(viewPadding)),
                        itemBuilder: (context, index) {
                          final promotion = c.promotions[index];
                          DateTime? expiry;

                          if (promotion.expiryDate != null) {
                            expiry = DateTime.parse(promotion.expiryDate!);
                          }

                          return Stack(children: [
                            Container(
                              height: heightSpace(24),
                              decoration: BoxDecoration(
                                  image: DecorationImage(
                                      image: AssetImage(
                                          'assets/icons/host_new/promobg1.png'),
                                      fit: BoxFit.fill)),
                              child: Row(children: [
                                Expanded(
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                      Align(
                                          alignment:
                                              Get.locale?.languageCode == 'ar'
                                                  ? Alignment.topLeft
                                                  : Alignment.topRight,
                                          child: PopupMenuButton<String>(
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                            onSelected: (val) =>
                                                c.promoOptionSelected(
                                                    val, index),
                                            itemBuilder: (context) {
                                              return [
                                                if (!promotion.isExpired)
                                                  PopupMenuItem(
                                                    value: "edit",
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        CustomText(
                                                          Get.find<
                                                                  TranslationHelper>()
                                                              .translations
                                                              .promocode
                                                              .editPromo,
                                                          weight:
                                                              FontWeight.w500,
                                                          size: 2.1,
                                                        ),
                                                        const Icon(
                                                          Icons.edit,
                                                          size: 18,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                PopupMenuItem(
                                                  value: "delete",
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      CustomText(
                                                        Get.find<
                                                                TranslationHelper>()
                                                            .translations
                                                            .promocode
                                                            .deletePromo,
                                                        weight: FontWeight.w500,
                                                        size: 2.1,
                                                      ),
                                                      const Icon(
                                                        Icons.delete,
                                                        size: 18,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ];
                                            },
                                            child: Container(
                                              padding: const EdgeInsets.all(3),
                                              decoration: BoxDecoration(
                                                color: Colors.blueGrey[50],
                                                shape: BoxShape.circle,
                                              ),
                                              child: const Icon(
                                                Icons.more_vert,
                                                size: 20,
                                              ),
                                            ),
                                          )),
                                      CustomText(promotion.title!,
                                          weight: FontWeight.w500),
                                      if (promotion.description != null) ...[
                                        SizedBox(height: heightSpace(1)),
                                        CustomText(
                                          promotion.description!,
                                          color: const Color(greyText),
                                          textOverflow: TextOverflow.ellipsis,
                                          maxlines: 2,
                                          size: 1.8,
                                        ),
                                      ],
                                      SizedBox(height: heightSpace(1)),
                                      CustomText(
                                        "${Get.find<TranslationHelper>().translations.promocode.remainingUsage}: ${promotion.usageCount}/${promotion.maxUsage}",
                                        color: Colors.blueGrey,
                                        size: 1.9,
                                      ),
                                      if (expiry != null) ...[
                                        SizedBox(height: heightSpace(1)),
                                        Row(
                                          children: [
                                            CustomText(
                                              "${promotion.isExpired ? Get.find<TranslationHelper>().translations.promocode.expireAt : Get.find<TranslationHelper>().translations.promocode.validUntil} : ",
                                              color: const Color(greyText),
                                              size: 1.7,
                                            ),
                                            CustomText(
                                              "${expiry.day} ${months[Get.locale?.languageCode ?? 'en']![expiry.month - 1]} ${expiry.year}",
                                              size: 1.9,
                                            ),
                                          ],
                                        ),
                                      ],
                                    ]).paddingOnly(
                                        left: Get.locale?.languageCode == 'en'
                                            ? widthSpace(7)
                                            : widthSpace(2),
                                        right: Get.locale?.languageCode == 'ar'
                                            ? widthSpace(7)
                                            : widthSpace(2))),
                                Container(
                                  width: widthSpace(35),
                                  height: double.maxFinite,
                                  decoration: BoxDecoration(
                                      image: DecorationImage(
                                          image: AssetImage(
                                              'assets/icons/host_new/promobg2${Get.locale?.languageCode ?? 'en'}.png'),
                                          fit: BoxFit.fill)),
                                  child: progressBar(promotion.percentage),
                                )
                              ]),
                            )
                          ]);

                          Stack(
                            children: [
                              Container(
                                clipBehavior: Clip.antiAlias,
                                padding: EdgeInsets.only(
                                    left: widthSpace(10), top: widthSpace(10)),
                                decoration: BoxDecoration(
                                    image: DecorationImage(
                                        image: AssetImage(
                                            'assets/icons/host_new/promobg1.png'))),
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          width: widthSpace(23),
                                          height: widthSpace(27),
                                          decoration: BoxDecoration(
                                            color: Colors.blueGrey[50],
                                          ),
                                          child: Center(
                                            child: CustomText(
                                              "${promotion.percentage!}%",
                                              size: 3.2,
                                              weight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          child: Padding(
                                            padding: EdgeInsets.symmetric(
                                              vertical: heightSpace(1),
                                              horizontal: widthSpace(2),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceEvenly,
                                              children: [
                                                CustomText(
                                                  promotion.title!,
                                                  weight: FontWeight.w500,
                                                ),
                                                if (promotion.description !=
                                                    null) ...[
                                                  SizedBox(
                                                      height: heightSpace(1)),
                                                  CustomText(
                                                    promotion.description!,
                                                    color:
                                                        const Color(greyText),
                                                    textOverflow:
                                                        TextOverflow.ellipsis,
                                                    maxlines: 2,
                                                    size: 1.8,
                                                  ),
                                                ],
                                                SizedBox(
                                                    height: heightSpace(1)),
                                                CustomText(
                                                  "${Get.find<TranslationHelper>().translations.promocode.remainingUsage}: ${promotion.usageCount}/${promotion.maxUsage}",
                                                  color: Colors.blueGrey,
                                                  size: 1.9,
                                                ),
                                                if (expiry != null) ...[
                                                  SizedBox(
                                                      height: heightSpace(1)),
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      CustomText(
                                                        "${promotion.isExpired ? Get.find<TranslationHelper>().translations.promocode.expireAt : Get.find<TranslationHelper>().translations.promocode.validUntil} : ",
                                                        color: const Color(
                                                            greyText),
                                                        size: 1.7,
                                                      ),
                                                      CustomText(
                                                        "${expiry.day} ${months[Get.locale?.languageCode ?? 'en']![expiry.month - 1]} ${expiry.year}",
                                                        size: 1.9,
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ],
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: PopupMenuButton<String>(
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                            onSelected: (val) =>
                                                c.promoOptionSelected(
                                                    val, index),
                                            itemBuilder: (context) {
                                              return [
                                                if (!promotion.isExpired)
                                                  PopupMenuItem(
                                                    value: "edit",
                                                    child: Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .center,
                                                      children: [
                                                        CustomText(
                                                          Get.find<
                                                                  TranslationHelper>()
                                                              .translations
                                                              .promocode
                                                              .editPromo,
                                                          weight:
                                                              FontWeight.w500,
                                                          size: 2.1,
                                                        ),
                                                        const Icon(
                                                          Icons.edit,
                                                          size: 18,
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                PopupMenuItem(
                                                  value: "delete",
                                                  child: Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    children: [
                                                      CustomText(
                                                        Get.find<
                                                                TranslationHelper>()
                                                            .translations
                                                            .promocode
                                                            .deletePromo,
                                                        weight: FontWeight.w500,
                                                        size: 2.1,
                                                      ),
                                                      const Icon(
                                                        Icons.delete,
                                                        size: 18,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ];
                                            },
                                            child: Container(
                                              padding: const EdgeInsets.all(3),
                                              decoration: BoxDecoration(
                                                color: Colors.blueGrey[50],
                                                shape: BoxShape.circle,
                                              ),
                                              child: const Icon(
                                                Icons.more_vert,
                                                size: 20,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: widthSpace(4),
                                        vertical: widthSpace(3),
                                      ),
                                      decoration: const BoxDecoration(
                                        color: Colors.black,
                                      ),
                                      child: InkWell(
                                        onTap: promotion.isExpired
                                            ? null
                                            : () {
                                                Clipboard.setData(ClipboardData(
                                                        text: promotion.code!))
                                                    .then((_) {
                                                  ViewsCommon.showSnackbar(
                                                      Get.find<
                                                              TranslationHelper>()
                                                          .translations
                                                          .reservation
                                                          .copied);
                                                });
                                              },
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            CustomText(
                                              promotion.code!,
                                              size: 2.3,
                                              color: Colors.white,
                                              weight: FontWeight.w500,
                                            ),
                                            const Spacer(),
                                            if (!promotion.isExpired) ...[
                                              CustomText(
                                                Get.find<TranslationHelper>()
                                                    .translations
                                                    .usersProfile
                                                    .clickToCopy,
                                                color: Colors.white,
                                                size: 1.9,
                                              ),
                                              SizedBox(width: widthSpace(1)),
                                              Icon(
                                                Icons.copy,
                                                color: Colors.white,
                                                size: widthSpace(3.7),
                                              ),
                                            ]
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (promotion.isExpired == true)
                                Image.asset(
                                  "assets/expiredLabel.png",
                                  height: 42,
                                ),
                            ],
                          );
                        },
                        separatorBuilder: (context, index) =>
                            SizedBox(height: heightSpace(3)),
                        itemCount: c.promotions.length,
                      ),
                    ),
              if (c.promoPage < c.promoLastPage)
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 10.0),
                  child: CircularProgressIndicator(),
                ),
            ],
          ),
        ));
  }

  progressBar(value) {
    return Stack(
      alignment: Alignment.center,
      children: [
        SizedBox(
            height: widthSpace(17),
            width: widthSpace(17),
            child: CircularProgressIndicator(
                backgroundColor: Colors.white.withOpacity(.3),
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                value: value / 100,
                strokeWidth: 7,
                strokeCap: StrokeCap.round)),
        CustomText('$value%', size: 2.5, weight: FontWeight.w800),
      ],
    );
  }
}
