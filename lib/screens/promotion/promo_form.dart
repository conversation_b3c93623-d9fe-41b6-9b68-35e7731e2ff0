import 'package:darent/components/common_button.dart';
import 'package:darent/components/common_checkbox.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/inputFormatters/percentageInputFormatter.dart';
import 'package:darent/inputFormatters/promoCodeInputFormatter.dart';
import 'package:darent/screens/new_host/host_textfield.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import '../../components/custom_text.dart';
import '../../components/views_common.dart';
import '../../helperMethods/translation_helper.dart';
import '../../utils/constants.dart';

class PromoForm extends StatelessWidget {
  final int? id;
  const PromoForm({Key? key, this.id}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final trans = Get.find<TranslationHelper>().translations;
    return GetBuilder<HostDashboardController>(
      id: "promo_form",
      builder: (c) => Scaffold(
        appBar: AppBar(
            leading: IconButton(
                icon: CircleAvatar(
                    maxRadius: 15,
                    backgroundColor: Colors.grey[200],
                    child: const Icon(Icons.chevron_left, color: Colors.black)),
                onPressed: Get.back),
            backgroundColor: Colors.transparent,
            centerTitle: false,
            title: CustomText(trans.promocode.createPromo,
                size: 2.4, weight: FontWeight.bold)),
        body: PopScope(
          onPopInvoked: (pop) {
            if (pop) {
              c.clearPromoFields();
            }
          },
          child: SingleChildScrollView(
            padding: EdgeInsets.all(widthSpace(5)),
            child: Form(
              key: c.promoFormKey,
              child: Obx(() => Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // CustomText(
                        //   Get.find<TranslationHelper>().translations.promocode.title,
                        //   weight: FontWeight.w500,
                        //   size: 2.1,
                        // ),
                        // SizedBox(height: heightSpace(1.2)),
                        HostTextField(
                            controller: c.promoTitle,
                            hint: trans.promocode.title,
                            textCapitalization: TextCapitalization.words,
                            validator: (val) =>
                                val!.trim().isEmpty ? 'emptyError'.tr : null,
                            formatters: [
                              LengthLimitingTextInputFormatter(40),
                            ]),
                        SizedBox(height: heightSpace(2)),
                        // CustomText(
                        //   "${trans.promocode.discount} %",
                        //   weight: FontWeight.w500,
                        //   size: 2.1,
                        // ),
                        // SizedBox(height: heightSpace(1.2)),
                        HostTextField(
                            controller: c.promoDiscount,
                            inputType: TextInputType.number,
                            hint: trans.promocode.discount,
                            suffix: const Icon(Icons.percent, size: 19),
                            formatters: [PercentageInputFormatter()],
                            validator: (val) =>
                                val!.trim().isEmpty ? 'emptyError'.tr : null),
                        SizedBox(height: heightSpace(2)),
                        // CustomText(
                        //   trans.promocode.generateCode,
                        //   weight: FontWeight.w500,
                        //   size: 2.1,
                        // ),
                        // SizedBox(height: heightSpace(1.2)),
                        HostTextField(
                            controller: c.promoCode,
                            hint: trans.promocode.promoCode,
                            formatters: [
                              PromoCodeInputFormatter()
                              // FilteringTextInputFormatter.allow(alphaNumericRegex8)
                            ],
                            validator: (val) => val!.trim().isEmpty
                                ? 'emptyError'.tr
                                // : val.trim().length < 8
                                //   ? trans.promocode.promoMustBeEight
                                : alphaNumericRegex8.hasMatch(val)
                                    ? null
                                    : trans.jqueryValidation.specialCharacter,
                            suffix: IconButton(
                                icon: const Icon(Icons.sync, size: 19),
                                onPressed: c.generateCode)),
                        SizedBox(height: heightSpace(2)),

                        InkWell(
                          borderRadius: BorderRadius.circular(10),
                          onTap: () {
                            c.selectAll(false);
                            openPropertySelection(c);
                          },
                          child: Container(
                            clipBehavior: Clip.antiAlias,
                            height: heightSpace(9),
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            alignment: Alignment.centerLeft,
                            decoration: BoxDecoration(
                              border: Border.all(
                                  color: c.selectPropsError.value != null
                                      ? Colors.redAccent[700]!
                                      : const Color(greyBorder)),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: CustomText(
                                (c.selectedProps.value ?? '').isNotEmpty
                                    ? c.selectedProps.value!
                                    : trans.promocode.clickHere),
                          ),
                        ),
                        if (c.selectPropsError.value != null) ...[
                          SizedBox(height: heightSpace(1)),
                          Padding(
                            padding: const EdgeInsets.only(left: 15.0),
                            child: Text(c.selectPropsError.value!,
                                style: TextStyle(
                                    color: Colors.redAccent[700],
                                    fontSize: 12)),
                          )
                        ],
                        SizedBox(height: heightSpace(2)),

                        // CustomText(
                        //   trans.promocode.discountUpto,
                        //   weight: FontWeight.w500,
                        //   size: 2.1,
                        // ),
                        // SizedBox(height: heightSpace(1.2)),

                        HostTextField(
                            controller: c.promoMaxUsage,
                            hint: trans.promocode.maxUsage,
                            inputType: TextInputType.number,
                            formatters: [
                              LengthLimitingTextInputFormatter(3),
                            ],
                            validator: (val) {
                              if (val!.trim().isEmpty) {
                                return trans.jqueryValidation.required;
                              } else if (val == '0' ||
                                  val == '00' ||
                                  val == '000') {
                                return trans.jqueryValidation.zeroUsage;
                              }
                              return null;
                            }),
                        SizedBox(height: heightSpace(2)),
                        // CustomText(
                        //     trans.promocode.maxUsage,
                        //   weight: FontWeight.w500,
                        //   size: 2.1,
                        // ),
                        // SizedBox(height: heightSpace(1.2)),

                        HostTextField(
                            controller: c.promoUpto,
                            inputType: TextInputType.number,
                            hint: trans.promocode.discountUpto,
                            formatters: [
                              FilteringTextInputFormatter.digitsOnly
                            ],
                            validator: (val) =>
                                val!.trim().isEmpty ? 'emptyError'.tr : null),
                        SizedBox(height: heightSpace(2)),

                        // CustomText(
                        //     trans.promocode.perUserUsage,
                        //   weight: FontWeight.w500,
                        //   size: 2.1,
                        // ),
                        // SizedBox(height: heightSpace(1.2)),
                        // CustomText(
                        //     trans.promocode.selectProperty,
                        //   weight: FontWeight.w500,
                        //   size: 2.1,
                        // ),

                        HostTextField(
                            controller: c.promoUserUsage,
                            hint: trans.promocode.perUserUsage,
                            inputType: TextInputType.number,
                            validator: (val) =>
                                val!.trim().isEmpty ? 'emptyError'.tr : null),
                        SizedBox(height: heightSpace(2)),

                        c.isRangeCalendarOpen
                            ? renderContainer(
                                c.toggleCalendarOpen,
                                Column(
                                  children: [
                                    Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          renderBigHead(trans.header.whenQ),
                                          CustomText(
                                              c.rangeController.selectedRange
                                                              ?.startDate ==
                                                          null ||
                                                      c
                                                              .rangeController
                                                              .selectedRange
                                                              ?.endDate ==
                                                          null
                                                  ? ""
                                                  : "${formDateFormatCservice.format(c.rangeController.selectedRange!.startDate!)} - ${formDateFormatCservice.format(c.rangeController.selectedRange!.endDate!)} (${c.rangeController.selectedRange!.endDate!.difference(c.rangeController.selectedRange!.startDate!).inDays} ${trans.listingBasic.nights})",
                                              size: 1.9),
                                        ]),
                                    Divider(height: heightSpace(4)),
                                    SfDateRangePicker(
                                      onSelectionChanged: (args) {
                                        c.update(["promo_form"]);
                                      },
                                      controller: c.rangeController,
                                      selectionMode:
                                          DateRangePickerSelectionMode.range,
                                      toggleDaySelection: c.toggleDaySelection,
                                      rangeSelectionColor:
                                          const Color(themeColor)
                                              .withOpacity(.4),
                                      enablePastDates: false,
                                    ),
                                  ],
                                ))
                            : renderHead(
                                trans.header.whenQ,
                                c.rangeController.selectedRange?.startDate ==
                                            null ||
                                        c.rangeController.selectedRange
                                                ?.endDate ==
                                            null
                                    ? ""
                                    : "${formDateFormatCservice.format(c.rangeController.selectedRange!.startDate!)}"
                                        " ${trans.hostReservation.to} ${formDateFormatCservice.format(c.rangeController.selectedRange!.endDate!)} (${c.rangeController.selectedRange!.endDate!.difference(c.rangeController.selectedRange!.startDate!).inDays} ${trans.listingBasic.nights})",
                                c.toggleCalendarOpen),
                        SizedBox(height: heightSpace(2)),

                        // CustomText(
                        //   trans.promocode.description,
                        //   weight: FontWeight.w500,
                        //   size: 2.1,
                        // ),
                        // SizedBox(height: heightSpace(2)),
                        HostTextField(
                            controller: c.promoDescription,
                            hint: trans.promocode.placeProper,
                            maxlines: 5),
                        SizedBox(height: heightSpace(2)),
                        CommonCheckBox(
                            title: CustomText(trans.promocode.forNewUser),
                            isSelected: c.forNewUser.value,
                            onPressed: c.forNewUser.toggle),
                        // Row(
                        //   mainAxisAlignment: MainAxisAlignment.start,
                        //   children: [
                        //     Checkbox(
                        //       activeColor: Colors.black,
                        //           value: c.forNewUser.value,
                        //           onChanged: (value) => c.forNewUser.toggle(),
                        //         ),
                        //     CustomText(trans.promocode.forNewUser)
                        //   ],
                        // ),
                        SizedBox(height: heightSpace(3.5)),
                        CommonButton(
                            title: trans.listingCalendar.submit,
                            isLoading: c.isLoading.value,
                            verticalPadding: 4,
                            backgroundBg: const Color(themeColor),
                            onPressed: () {
                              if (!c.isLoading.value) {
                                c.createPromo(id: id);
                              }
                            })
                      ])),
            ),
          ),
        ),
      ),
    );
  }

  openPropertySelection(HostDashboardController c) {
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        maxChildSize: .75,
        initialChildSize: .75,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(
                height: heightSpace(3.5),
              ),
              CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .promocode
                    .selectProperty,
                weight: FontWeight.w500,
                size: 2.4,
              ),
              Expanded(
                child: Obx(
                  () => ListView.builder(
                      itemCount: c.properties.length+1,
                      padding: const EdgeInsets.only(top: 10.0),
                      itemBuilder: (context, index) {
                        if (index == 0) {
                          // "Select All" tile
                          return CheckboxListTile(
                            title: CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .promocode
                                  .selectAll,
                              size: 2.1,
                              textOverflow: TextOverflow.ellipsis,
                            ),
                            value: c.isAllSelected.value,
                            onChanged: (bool? value) {
                              c.selectAll(value ?? false);
                            },
                          );
                        } else {
                          final propertyIndex = index - 1;
                          if (c.propertySearchKey.isEmpty ||
                              c.properties[index].name.toLowerCase().contains(
                                  c.propertySearchKey.toLowerCase())) {
                            return CheckboxListTile(
                              title: CustomText(
                                c.properties[propertyIndex].name,
                                size: 2.1,
                                textOverflow: TextOverflow.ellipsis,
                              ),
                              value: c.properties[propertyIndex].checked,
                              onChanged: (bool? value) {
                                if (c.isAllSelected.value) {
                                  c.isAllSelected.value = false;
                                }
                                c.properties[propertyIndex].checked =
                                    value ?? false;
                                if (c.properties[propertyIndex].id == 0) {
                                  c.toggleCheckAll();
                                }
                                c.properties.refresh();
                              },
                            );
                          } else {
                            return const SizedBox();
                          }
                        }
                      }),
                ),
              ),
              Align(
                alignment: Alignment.centerRight,
                child: Padding(
                  padding: const EdgeInsets.only(right: 20.0),
                  child: CommonButton(
                      onPressed: () {
                        Get.back();
                        final temp = c.properties
                            .where((item) => item.checked && item.id != 0)
                            .map((filter) => filter.name);
                        c.selectedProps.value = temp.join(", ");
                        c.selectPropsError.value = null;
                      },
                      title: Get.find<TranslationHelper>()
                          .translations
                          .usersProfile
                          .save,
                      horizontalPadding: 5),
                ),
              )
            ],
          );
        }));
  }

  renderContainer(toggleView, Widget child) {
    return InkWell(
      onTap: toggleView,
      borderRadius: BorderRadius.circular(10),
      child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(greyBorder)),
            borderRadius: BorderRadius.circular(10),
          ),
          child: child),
    );
  }

  renderBigHead(text) =>
      CustomText(text, size: 2.3, weight: FontWeight.w500, maxlines: 5);
  renderHead(text1, text2, toggleView) {
    return renderContainer(
        toggleView,
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          CustomText(text1, size: 2.0, color: const Color(greyText)),
          SizedBox(height: heightSpace(.5)),
          CustomText(text2, size: 2.1),
        ]));
  }
}
