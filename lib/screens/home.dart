import 'package:carousel_slider/carousel_slider.dart';
import 'package:custom_info_window/custom_info_window.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/product_item.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:darent/screens/home_filter.dart';
import 'package:darent/screens/plan_trip.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:shimmer/shimmer.dart';
import 'package:webengage_flutter/webengage_flutter.dart';
import '../components/products_shimmer.dart';
import '../components/views_common.dart';
import '../helperMethods/translation_helper.dart';

class Home extends StatelessWidget {
  const Home({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => SearchHelper.c.listView.value
        ? NotificationListener<ScrollEndNotification>(
            onNotification: (notification) {
              final metrics = notification.metrics;
              if (metrics.atEdge) {
                bool isTop = metrics.pixels == 0;
                if (!isTop && SearchHelper.c.dataTotalPages > 1) {
                  if (SearchHelper.c.dataPage < SearchHelper.c.dataTotalPages) {
                    SearchHelper.c.dataPage++;
                  } else {
                    SearchHelper.c.dataPage = 1;
                  }
                  SearchHelper.c.getData(isLazyLoading: true);
                }
              }
              return true;
            },
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: widthSpace(3)),
              child: CustomScrollView(
                  controller: SearchHelper.c.scrollController,
                  slivers: [
                    SliverToBoxAdapter(
                        child: Container(
                      margin: EdgeInsets.only(bottom: heightSpace(2.5)),
                      decoration:
                          BoxDecoration(color: Colors.white, boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 6,
                          offset:
                              const Offset(3, 7), // horizontal, vertical offset
                        ),
                      ]),
                      child: Column(children: [
                        renderSearchHere(),
                        //First
                        renderPropertyTypes(),
                      ]),
                    )),
                    if (SearchHelper.c.banners.isNotEmpty &&
                        SearchHelper.c.searcText.value == null)
                      SliverToBoxAdapter(
                        child: Stack(
                          alignment: Alignment.bottomCenter,
                          children: [
                            CarouselSlider(
                                carouselController: SearchHelper.c.sliderBtn,
                                items: SearchHelper.c.banners
                                    .map((item) => Container(
                                          clipBehavior: Clip.antiAlias,
                                          decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(7)),
                                          child: Image(
                                              image: GlobalHelper.buildNetworkImageProvider(url: item['image_url'] ??"",),
                                              fit: BoxFit.fill,
                                              width: double.maxFinite,
                                              ),
                                        ))
                                    .toList(),
                                options: CarouselOptions(
                                    autoPlay: true,
                                    height: widthSpace(55),
                                    viewportFraction: 1,
                                    onPageChanged: (page, reason) {
                                      SearchHelper.c.bannerIndex.value =
                                          SearchHelper.c.banners[page]
                                              ['image_url'];
                                    })),
                            Positioned(
                              bottom: 17,
                              child: Row(
                                  children: SearchHelper.c.banners
                                      .map<Widget>((e) => Container(
                                            margin:
                                                const EdgeInsets.only(left: 5),
                                            width: widthSpace(SearchHelper
                                                        .c.bannerIndex.value ==
                                                    e['image_url']
                                                ? 3
                                                : 2),
                                            height: widthSpace(SearchHelper
                                                        .c.bannerIndex.value ==
                                                    e
                                                ? 3
                                                : 2),
                                            decoration: BoxDecoration(
                                                color: Color(SearchHelper
                                                            .c
                                                            .bannerIndex
                                                            .value ==
                                                        e['image_url']
                                                    ? themeColor
                                                    : greyBorder),
                                                shape: BoxShape.circle),
                                          ))
                                      .toList()),
                            )
                          ],
                        ),
                      ),
                    if (SearchHelper.c.dataLoading.value)
                      const SliverToBoxAdapter(child: ProductsShimmer())
                    else if (SearchHelper.c.data.isNotEmpty) ...[
                      SliverPadding(
                        padding: EdgeInsets.symmetric(vertical: heightSpace(3)),
                        sliver: SliverList(
                            delegate: SliverChildBuilderDelegate(
                          (context, index) {
                            final int itemIndex = index ~/ 2;
                            if (index.isOdd) {
                              return SizedBox(height: heightSpace(3.5));
                            } else {
                              return ProductItem(
                                data: SearchHelper.c.data[itemIndex],
                                wishlistLoader:
                                    SearchHelper.c.wishlistLoading.value,
                                translations:
                                    Get.find<TranslationHelper>().translations,
                                onPhotoChanged: (id) {
                                  SearchHelper
                                      .c.data[itemIndex].currentSliderId = id;
                                  SearchHelper.c.data.refresh();
                                },
                              );
                            }
                          },
                          childCount: SearchHelper.c.data.length * 2 - 1,
                        )),
                      )
                    ] else ...[
                      SliverFillRemaining(
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset('assets/notFound.png', height: 25),
                              CustomText(Get.find<TranslationHelper>()
                                  .translations
                                  .search
                                  .noResultFound)
                            ]),
                      )
                    ],
                    if (!SearchHelper.c.dataLoading.value &&
                        SearchHelper.c.dataTotalPages > 1)
                      SliverToBoxAdapter(
                          child: Container(
                              padding: EdgeInsets.only(bottom: heightSpace(15)),
                              alignment: Alignment.center,
                              child: const CircularProgressIndicator()))
                  ]),
            ))
        : Column(children: [
            Container(
              padding:
                  EdgeInsets.symmetric(horizontal: widthSpace(viewPadding)),
              decoration: BoxDecoration(color: Colors.white, boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 6,
                  offset: const Offset(3, 7), // horizontal, vertical offset
                ),
              ]),
              child: Column(
                children: [
                  renderSearchHere(),
                  renderPropertyTypes(),
                ],
              ),
            ),
            Expanded(
                child: Stack(children: [
              GoogleMap(
                  initialCameraPosition: CameraPosition(
                      target: SearchHelper.c.mapCenter, zoom: 10),
                  markers: SearchHelper.c.markers,
                  myLocationButtonEnabled: false,
                  zoomControlsEnabled: false,
                  onMapCreated: onmapCreated,
                  onTap: (argument) =>
                      SearchHelper.c.customInfoWindow.hideInfoWindow!(),
                  onCameraMove: (position) =>
                      SearchHelper.c.customInfoWindow.onCameraMove!()),
              CustomInfoWindow(
                controller: SearchHelper.c.customInfoWindow,
                offset: 40,
                width: widthSpace(55),
                height: heightSpace(41),
              )
            ]))
          ]));
  }

  void onmapCreated(GoogleMapController controller) {
    SearchHelper.c.customInfoWindow.googleMapController = controller;
  }

  renderSearchHere() {
    return Container(
        margin:
            EdgeInsets.only(left: 4, right: 4, top: widthSpace(viewPadding)),
        padding: EdgeInsets.all(widthSpace(3)),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(100),
            border: Border.all(color: const Color(greyBorder))),
        child: InkWell(
          onTap: () async {
            DateTime? start, end;
            if (SearchHelper.filterForm['checkin'] != null &&
                SearchHelper.filterForm['checkout'] != null) {
              start = formDateFormatCservice
                  .parse(SearchHelper.filterForm['checkin']);
              end = formDateFormatCservice
                  .parse(SearchHelper.filterForm['checkout']);
            }
            Get.to(() => PlanTrip(
                    location: SearchHelper.c.locationText,
                    dates: start != null ? [start, end!] : null))!
                .then(SearchHelper.planTripSearch);
          },
          child: Row(children: [
            SizedBox(width: widthSpace(5)),
            Expanded(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SearchHelper.c.locationText != null
                        ? CustomText(SearchHelper.c.locationText,
                            size: 2,
                            maxlines: 1,
                            textOverflow: TextOverflow.ellipsis)
                        : CustomText(
                            Get.find<TranslationHelper>()
                                    .translations
                                    .header
                                    .searchHere ??
                                "Search Here",
                            size: 2),
                    //${Get.find<TranslationHelper>().translations.listing.guests ?? "Guests"}
                    CustomText(
                        SearchHelper.c.searcText.value ??
                            "${Get.find<TranslationHelper>().translations.tripsActive.location}, ${Get.find<TranslationHelper>().translations.tripsActive.date ?? "Date"}",
                        size: 1.7,
                        color: Colors.grey)
                  ]),
            ),
            if (!SearchHelper.filterCleared ||
                !SearchHelper.planTripCleared) ...[
              sortPopMenu(),
              if (SearchHelper.c.filters.value != null) ...[
                SizedBox(width: widthSpace(3)),
                InkWell(
                  onTap: (){
                    Get.to(() => HomeFilter(data: SearchHelper.c.filters))?.then((_){
                      if(SearchHelper.c.backupPropertyType!=SearchHelper.c.selectedPropertyType.value){
                        SearchHelper.c.selectedPropertyType.value=SearchHelper.c.backupPropertyType;
                      }
                    });
                  },
                  child: Container(
                      width: widthSpace(8),
                      padding: EdgeInsets.all(widthSpace(2.6)),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          boxShadow: ViewsCommon.boxShadow,
                          shape: BoxShape.circle),
                      child: SvgPicture.asset("assets/icons/filter.svg")),
                ),
              ]
            ],
          ]),
        ));
  }

  renderPropertyTypes() {
    return Container(
      margin: EdgeInsets.only(top: heightSpace(3)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(
          SearchHelper.c.filters.value?.property_type.length ?? 0,
          (index) {
            bool selected = SearchHelper.c.selectedPropertyType.value == SearchHelper.c.filters.value?.property_type[index].id;
            return InkWell(
              onTap: () {
                analytics.logEvent(
                  name: "unit_event",
                  parameters: {
                    'event_name': SearchHelper
                            .c.filters.value?.property_type[index].name ??
                        '',
                    'id':
                        SearchHelper.c.filters.value?.property_type[index].id ??
                            0,
                  },
                );
                SearchHelper.c.checkPropertyType(
                  SearchHelper.c.filters.value?.property_type[index].id,
                  toApplyFilter: true
                );

                categorySelectEventCalled(
                    SearchHelper.c.filters.value!.property_type[index].name!);
              },
              child: SizedBox(
                width: widthSpace(18),
                height: heightSpace(7),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SearchHelper.c.filters.value?.property_type[index].image == null
                        ? Image.asset("assets/icons/apartment.png")
                        : GlobalHelper.buildNetworkSvgWidget(
                      url: SearchHelper.c.filters.value?.property_type[index].image ??"",
                      width: widthSpace(6.5),
                      myColor: selected
                          ? const Color(themeColor)
                          : Colors.grey,
                      defaultOption: Image.asset("assets/icons/apartment.png",
                        width: widthSpace(6.5),
                        color: selected
                            ? const Color(themeColor)
                            : Colors.grey,),),
                    CustomText(
                      Get.locale?.languageCode == "ar"
                          ? SearchHelper.c.filters.value?.property_type[index].nameAr
                          : SearchHelper
                          .c.filters.value?.property_type[index].name,
                      textAlign: TextAlign.center,
                      size: 1.4,
                      color: selected ? const Color(themeColor) : Colors.grey,
                    ),
                    selected
                        ? Container(
                            height: 3,
                            decoration: BoxDecoration(
                                color: const Color(themeColor),
                                borderRadius: BorderRadius.circular(10)))
                        : const SizedBox()
                  ],
                ),
              ),
            );
          },
        )
      ),
    );
  }

  categorySelectEventCalled(String category) async {
    await WebEngagePlugin.trackEvent('Category Selected',
        {"Category Name": category, "User": isHost ? "Host" : "Customer"});
  }

  sortPopMenu() {
    return PopupMenuButton<String>(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        onSelected: SearchHelper.onSortChanged,
        itemBuilder: (context) {
          return [
            PopupMenuItem(
                value: "asc",
                child: Text(
                    Get.find<TranslationHelper>()
                        .translations
                        .sort
                        .lowPricesFirst,
                    style: const TextStyle(fontWeight: FontWeight.w500))),
            PopupMenuItem(
                value: "desc",
                child: Text(
                    Get.find<TranslationHelper>()
                        .translations
                        .sort
                        .highPricesFirst,
                    style: const TextStyle(fontWeight: FontWeight.w500))),
            PopupMenuItem(
                value: "mostRatedFirst",
                child: Text(
                    Get.find<TranslationHelper>()
                        .translations
                        .sort
                        .mostRatedFirst,
                    style: const TextStyle(fontWeight: FontWeight.w500))),
            if ((SearchHelper.c.locationText ?? '').isNotEmpty)
              PopupMenuItem(
                  value: "nearestToCity",
                  child: Text(
                      Get.find<TranslationHelper>()
                          .translations
                          .sort
                          .newestToTheCity,
                      style: const TextStyle(fontWeight: FontWeight.w500))),
          ];
        },
        child: Container(
            width: widthSpace(8),
            padding: EdgeInsets.all(widthSpace(2.6)),
            decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: ViewsCommon.boxShadow,
                shape: BoxShape.circle),
            child: SvgPicture.asset("assets/icons/sort.svg")));
  }
}

iconItem(text, image) {
  return Column(children: [
    Image.asset(
      image,
      scale: 2,
    ),
    const SizedBox(height: 10),
    CustomText(text, size: 1.7, color: Colors.grey)
  ]);
}
