import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:shimmer/shimmer.dart';

import '../helperMethods/chat_helper.dart';
import '../helperMethods/search_helper.dart';
import '../helperMethods/translation_helper.dart';
import '../utils/routes.dart';

class Inbox extends StatelessWidget {
  const Inbox({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(widthSpace(viewPadding)),
      child: NotificationListener<ScrollEndNotification>(
        onNotification: (notification) {
          final metrics = notification.metrics;
          if (metrics.atEdge) {
            bool isTop = metrics.pixels == 0;
            if (ChatHelper.c.currentTab.value == "inbox") {
              if (!isTop && ChatHelper.c.hasMoreChatHeads) {
                ChatHelper.c.isLazyLoading.value = true;
                ChatHelper.c.getChatHeads(paginate: true);
              }
            } else if (!isTop &&
                SearchHelper.c.notifyPage < SearchHelper.c.notifyLastPage) {
              SearchHelper.c.notifyPage++;
              ChatHelper.c.isLazyLoading.value = true;
              SearchHelper.c.getNotifications();
            }
          }
          return true;
        },
        child: RefreshIndicator(
          onRefresh: () async {
            if (ChatHelper.c.currentTab.value == "inbox") {
              ChatHelper.c.getChatHeads();
            } else {
              SearchHelper.c.getNotifications(refresh: true);
            }
          },
          child: Obx(
            () => CustomScrollView(slivers: [
              SliverToBoxAdapter(
                  child: Row(children: [
                tabItem("inbox"),
                SizedBox(width: widthSpace(4)),
                tabItem("notification")
              ])),
              if (ChatHelper.c.currentTab.value == "inbox")
                if (ChatHelper.c.isLoading.value)
                  shimmers()
                else if (ChatHelper.c.chatHeads.isEmpty)
                  SliverFillRemaining(
                      child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                        SvgPicture.asset('assets/noChat.svg',
                            height: heightSpace(25)),
                        SizedBox(height: heightSpace(2)),
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .message
                                .emptyInbox,
                            size: 1.8,
                            textAlign: TextAlign.center,
                            maxlines: 2)
                      ]))
                else ...[
                  SliverToBoxAdapter(child: SizedBox(height: heightSpace(3))),
                  // SliverToBoxAdapter(
                  //   child: TextField(
                  //       style: TextStyle(fontSize: heightSpace(2.0)),
                  //       decoration:InputDecoration(
                  //           hintText: translation.general.search,
                  //           hintStyle: TextStyle(color: Colors.grey[400]),
                  //           fillColor: const Color(lightBg),
                  //           filled: true,
                  //           contentPadding: const EdgeInsets.only(left:20, right: 20),
                  //           border: OutlineInputBorder(borderRadius: BorderRadius.circular(10),borderSide: const BorderSide(color: Color(greyBorder))),
                  //           enabledBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(10),borderSide: const BorderSide(color: Color(greyBorder))))),
                  // ),
                  SliverPadding(
                    padding: EdgeInsets.symmetric(vertical: heightSpace(4)),
                    sliver: SliverList.separated(
                        itemBuilder: (context, index) {
                          String? stringDate;
                          DateTime? date;
                          if (ChatHelper.c.chatHeads[index].latestMessageDate !=
                              null) {
                            date = DateTime.parse(ChatHelper
                                .c.chatHeads[index].latestMessageDate!);
                            int days = DateTime.now().difference(date).inDays;
                            stringDate = days > 1
                                ? DateFormat.yMMMEd().format(date)
                                : days == 1
                                    ? "yesterday"
                                    : "Today";
                          }
                          return InkWell(
                            onTap: () {
                              ChatHelper.c.selectedChatHead =
                                  ChatHelper.c.chatHeads[index];
                              ChatHelper.c.scrollController =
                                  ScrollController();
                              ChatHelper.c
                                  .getChats(ChatHelper.c.chatHeads[index].id);
                              Get.toNamed(Routes.inbox)?.then((value) {
                                ChatHelper.c.clearMessages();
                                ChatHelper.c.scrollController.dispose();
                                ChatHelper.c.isLazyLoading.value = false;
                              });
                              ChatHelper.c.chatHeads[index].unread = false;
                              ChatHelper.c.chatHeads.refresh();
                            },
                            child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  profileImage(ChatHelper.c.chatHeads[index]
                                      .converser?.profileImage),
                                  SizedBox(width: widthSpace(4)),
                                  Expanded(
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          if (stringDate != null)
                                            CustomText(stringDate,
                                                size: 1.8,
                                                color: const Color(greyText)),
                                          Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                CustomText(
                                                    ChatHelper
                                                        .c
                                                        .chatHeads[index]
                                                        .converser!
                                                        .firstName!,
                                                    size: 2.1),
                                                if (ChatHelper
                                                        .c
                                                        .chatHeads[index]
                                                        .converser
                                                        ?.location !=
                                                    null) ...[
                                                  Container(
                                                    width: 2,
                                                    height: 2,
                                                    margin:
                                                        EdgeInsets.symmetric(
                                                            horizontal:
                                                                widthSpace(1)),
                                                    decoration:
                                                        const BoxDecoration(
                                                      color: Colors.black54,
                                                    ),
                                                  ),
                                                  Expanded(
                                                    child: CustomText(
                                                        ChatHelper
                                                            .c
                                                            .chatHeads[index]
                                                            .converser
                                                            ?.location!,
                                                        color: Colors.black54,
                                                        textOverflow:
                                                            TextOverflow
                                                                .ellipsis),
                                                  ),
                                                ]
                                              ]),
                                          CustomText(
                                              ChatHelper.c.chatHeads[index]
                                                      .latestMessage ??
                                                  "",
                                              size: 1.9,
                                              weight: ChatHelper.c
                                                      .chatHeads[index].unread!
                                                  ? FontWeight.bold
                                                  : null,
                                              color: ChatHelper.c
                                                      .chatHeads[index].unread!
                                                  ? Colors.black
                                                  : const Color(greyText),
                                              maxlines: 1,
                                              textOverflow:
                                                  TextOverflow.ellipsis),
                                          if (ChatHelper
                                                  .c.chatHeads[index].details !=
                                              null) ...[
                                            // Row(children: [
                                            //   CustomText(
                                            //       "${ChatHelper.c.chatHeads[index].details?.type}",
                                            //       size: 1.9),
                                            //
                                            //   if(ChatHelper.c.chatHeads[index].details?.status!=null)...[
                                            //     Container(
                                            //       width: 2,height: 2,
                                            //       margin: EdgeInsets.symmetric(horizontal: widthSpace(1)),
                                            //       decoration: const BoxDecoration(
                                            //           color: Colors.black54,
                                            //           shape: BoxShape.circle
                                            //       ),
                                            //     ),
                                            //     CustomText(
                                            //         ChatHelper.c.chatHeads[index].details!.status!),
                                            //   ],
                                            // ]),
                                            if (ChatHelper.c.chatHeads[index]
                                                    .details?.startDate !=
                                                null)
                                              CustomText(
                                                  "${months[Get.locale?.languageCode ?? 'en']![ChatHelper.c.chatHeads[index].details!.startDate!.month - 1]} ${ChatHelper.c.chatHeads[index].details!.startDate!.day} - "
                                                  "${months[Get.locale?.languageCode ?? 'en']![ChatHelper.c.chatHeads[index].details!.endDate!.month - 1]} ${ChatHelper.c.chatHeads[index].details!.endDate!.day}",
                                                  size: 1.9,
                                                  color: Colors.black54)
                                          ]
                                        ]),
                                  )
                                ]),
                          );
                        },
                        separatorBuilder: (context, index) =>
                            Divider(height: heightSpace(3)),
                        itemCount: ChatHelper.c.chatHeads.length),
                  ),
                ]
              else
                SearchHelper.c.notifyList.isEmpty
                    ? SliverFillRemaining(
                        child: Center(
                            child: CustomText(Get.find<TranslationHelper>()
                                .translations
                                .notification
                                .noNotification)),
                      )
                    : SliverPadding(
                        padding: listPadding(),
                        sliver: SearchHelper.c.generalLoader.value
                            ? shimmers(isNotify: true)
                            : SliverList.separated(
                                itemBuilder: (context, index) {
                                  // String? dateTimeString;
                                  // DateTime? dateTime;
                                  // if (c.notifyList[index].createdAt != null) {
                                  //   dateTime = DateTime.parse(c.notifyList[index].createdAt!).toLocal();
                                  //   if (dateTime.day == now.day) {
                                  //     if (dateTime.hour == now.hour) {
                                  //       int minute = now.minute - dateTime.minute;
                                  //       dateTimeString =
                                  //           (minute > 1 ? "minutesAgo" : "oneMinuteAgo").trArgs([minute.toString()]);
                                  //     } else {
                                  //       int hour = now.hour - dateTime.hour;
                                  //       dateTimeString =
                                  //           (hour > 1 ? "hoursAgo" : "oneHourAgo").trArgs([hour.toString()]);
                                  //     }
                                  //   } else {
                                  //     dateTimeString = "${dateTime.day} ${months[Get.locale?.languageCode??'en']![dateTime.month - 1]} ${dateTime.year}";
                                  //   }
                                  // }
                                  final notifyObj =
                                      SearchHelper.c.notifyList[index];
                                  return InkWell(
                                    onTap: SearchHelper.c.loadingNotify != null
                                        ? null
                                        : () async {
                                            SearchHelper.c
                                                .handleMessage(notifyObj.data!);
                                            if (notifyObj.data?['slug']
                                                    .contains(Routes.inbox) &&
                                                notifyObj.data?[
                                                        'chat_head_id'] !=
                                                    null) {
                                              SearchHelper.c.loadingNotify =
                                                  index;
                                              SearchHelper.c.notifyList
                                                  .refresh();
                                            }
                                            // if(c.notifyList[index].slug=="booking"){
                                            //   if(!isHost){
                                            //     isHost = true;
                                            //     Get.offAll(()=>Host());
                                            //     await Future.delayed(const Duration(milliseconds: 500));
                                            //   }
                                            //   Get.to(()=>HostReservations(currentTab:c.notifyList[index].data?.hostId));
                                            // }else if(c.notifyList[index].slug=="reservation"){
                                            //   if(isHost){
                                            //     isHost = false;
                                            //     Get.offAll(()=> const Dashboard());
                                            //   }
                                            //   if(c.notifyList[index].data!=null){
                                            //     c.reservationTab.animateTo(c.notifyList[index].data!.hostId!.isEmpty || c.notifyList[index].data!.hostId=="coming"?0
                                            //         :c.notifyList[index].data?.hostId=='ongoing'?1:c.notifyList[index].data?.hostId=='history'?2:c.notifyList[index].data?.hostId=='Cancelled'?3:0);
                                            //     c.update(['bookings']);
                                            //       if(c.notifyList[index].data!.hostId!.isNotEmpty &&
                                            //     ((c.reservationTab.index==0 && c.notifyList[index].data!.hostId!='coming') || (c.reservationTab.index==3 && c.notifyList[index].data!.hostId!='Cancelled'))){
                                            //         Get.to(()=>ReservationDetail(code: c.notifyList[index].data!.hostId!));
                                            //       }
                                            //   }
                                            //   c.changeIndex(2);
                                            // }else if(c.notifyList[index].slug=="guest_review"){
                                            //   c.changeIndex(4);
                                            //   Get.to(()=>const Profile());
                                            // }else if(c.notifyList[index].slug=="host_review"){
                                            //   // isHost = true;
                                            //   // HostController hostC = Get.find();
                                            //   // hostC.changeIndex(4);
                                            //   // Get.offAll(()=>const HostDashboard());
                                            //   // Get.to(()=>const Profile());
                                            // }else if(c.notifyList[index].slug=="hosthome"){
                                            //   isHost = true;
                                            //   Get.offAll(()=>Host());
                                            // }else if(c.notifyList[index].slug=="dwelling"){
                                            //   late HostDashboardController host;
                                            //   if(isHost){
                                            //     host = Get.find();
                                            //   }else{
                                            //     host = Get.put(HostDashboardController());
                                            //   }
                                            //   Get.to(()=>Dwellings());
                                            //   if(c.notifyList[index].data?.hostId=="listed"){
                                            //     host.changeDwellingTab("Listed");
                                            //   }
                                            // }else if(c.notifyList[index].slug=="chat" && (c.notifyList[index].data?.hostId??'').isNotEmpty){
                                            //   ChatHelper.gotoChats(c.notifyList[index].data!.hostId);
                                            // }
                                          },
                                    child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          CircleAvatar(
                                              minRadius: widthSpace(7),
                                              backgroundColor:const Color(lightBg),
                                              child: notifyObj.data?['imagePath'] != null
                                                  ? GlobalHelper.resolveImageUrl(notifyObj.data?['imagePath']).isNotEmpty
                                                  ? Image(
                                                      image: GlobalHelper.buildNetworkImageProvider(url: notifyObj.data?['imagePath'] ??"",),
                                                      height: 23)
                                                  : Icon(
                                                Icons.person,
                                                color: Colors.black
                                                    .withOpacity(0.7),
                                                size: 23,
                                              )
                                                  : Icon(
                                                      Icons.person,
                                                      color: Colors.black
                                                          .withOpacity(0.7),
                                                      size: 23,
                                                    )),
                                          SizedBox(width: widthSpace(4)),
                                          Expanded(
                                              child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              if (notifyObj.createdAt != null)
                                                Container(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal:
                                                                widthSpace(1),
                                                            vertical:
                                                                widthSpace(1)),
                                                    decoration: BoxDecoration(
                                                        color: Colors.white,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(4),
                                                        boxShadow: ViewsCommon
                                                            .boxShadow),
                                                    child: CustomText(
                                                        notifyObj.createdAt,
                                                        size: 1.6,
                                                        color: const Color(
                                                                themeColor)
                                                            .withOpacity(.8),
                                                        weight:
                                                            FontWeight.w500)),
                                              CustomText(
                                                  SearchHelper
                                                      .c
                                                      .notifyList[index]
                                                      .message,
                                                  color: const Color(greyText),
                                                  size: 1.9)
                                            ],
                                          )),
                                          if (SearchHelper.c.loadingNotify ==
                                              index) ...[
                                            const Spacer(),
                                            Column(children: [
                                              CustomText(
                                                  Get.find<TranslationHelper>()
                                                      .translations
                                                      .listing
                                                      .pleaseWait,
                                                  size: 1.5,
                                                  weight: FontWeight.w500,
                                                  color: Colors.black54),
                                              Image.asset('assets/loader.gif',
                                                  height: 20)
                                            ])
                                          ],
                                        ]),
                                  );
                                },
                                separatorBuilder: (context, index) =>
                                    SizedBox(height: heightSpace(2.5)),
                                itemCount: SearchHelper.c.notifyList.length),
                      ),
              if (ChatHelper.c.currentTab.value == "inbox" &&
                  ChatHelper.c.hasMoreChatHeads)
                const SliverToBoxAdapter(
                  child: Center(
                      child: SizedBox(
                          height: 35,
                          width: 35,
                          child: CircularProgressIndicator())),
                )
            ]),
          ),
        ),
      ),
    );
  }

  listPadding() =>
      EdgeInsets.symmetric(horizontal: widthSpace(1), vertical: heightSpace(5));
  // shimmers({isNotify=false}){
  //   return SliverFillRemaining(child: Shimmer(
  //     gradient: ViewsCommon.shimmerGradient,
  //     child: ListView.separated(
  //       padding:isNotify?null:listPadding(),
  //       itemBuilder: (ctx,i) {
  //         return Row(
  //           children: [
  //             CircleAvatar(
  //                 radius: widthSpace(7)),
  //             SizedBox(width:widthSpace(4)),
  //             Expanded(
  //               child: Column(
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   children: [
  //                     Container(
  //                         height: 15,
  //                         width: 30,
  //                         color: Colors.white),
  //                     Container(
  //                       height: 10,
  //                       width: double.maxFinite,
  //                       color: Colors.white,
  //                     ),
  //                     Container(
  //                       height: 10,
  //                       width: widthSpace(45),
  //                       color: Colors.white,
  //                     ),                ]),
  //             ),
  //           ],
  //         );
  //       },
  //       separatorBuilder: (ctx, i) => isNotify?SizedBox(height: heightSpace(2.5)):Divider(height: heightSpace(3),color: Colors.black),
  //       itemCount: 5,
  //     ),
  //   ));
  // }
  shimmers({isNotify = false}) {
    return SliverFillRemaining(
        child: Shimmer(
      gradient: ViewsCommon.shimmerGradient,
      child: ListView.separated(
        padding: isNotify ? null : listPadding(),
        itemBuilder: (ctx, i) {
          return Row(
            children: [
              CircleAvatar(radius: widthSpace(7)),
              SizedBox(width: widthSpace(4)),
              isNotify
                  ? Expanded(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                                height: 15, width: 30, color: Colors.white),
                            Container(
                              height: 10,
                              width: double.maxFinite,
                              color: Colors.white,
                            ),
                            Container(
                              height: 10,
                              width: widthSpace(45),
                              color: Colors.white,
                            )
                          ]),
                    )
                  : Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                          Container(
                            height: 10,
                            width: 100,
                            color: Colors.white,
                          ),
                          Container(
                            height: 10,
                            margin: EdgeInsets.symmetric(vertical: 5),
                            width: 60,
                            color: Colors.white,
                          ),
                          Container(
                            margin: EdgeInsets.only(bottom: 5),
                            height: 10,
                            width: 30,
                            color: Colors.white,
                          ),
                          Container(
                            height: 10,
                            width: 120,
                            color: Colors.white,
                          ),
                        ]),
            ],
          );
        },
        separatorBuilder: (ctx, i) => isNotify
            ? SizedBox(height: heightSpace(2.5))
            : Divider(height: heightSpace(3), color: Colors.black),
        itemCount: 5,
      ),
    ));
  }

  profileImage(String? image) {
    return ClipRRect(
        borderRadius: BorderRadius.circular(25),
        child: (image ?? "").contains(".svg")
            ? GlobalHelper.buildNetworkSvgWidget(
          url:image ??"",
          height: widthSpace(14),
          width: widthSpace(14),
          defaultOption: Image.asset(
              "assets/default-image.png",
              width: widthSpace(14),
              height: widthSpace(14),
              fit: BoxFit.fill),)
            : Image(
            image: GlobalHelper.buildNetworkImageProvider(url: image ??"",),
                width: widthSpace(14),
                height: widthSpace(14),
                fit: BoxFit.fill,
                ));
  }

  tabItem(String value) {
    return Expanded(
      child: InkWell(
          child: Container(
            height: heightSpace(7.5),
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(8), vertical: widthSpace(2)),
            decoration: BoxDecoration(
                color: ChatHelper.c.currentTab.value == value
                    ? Colors.white
                    : const Color(lightBg),
                borderRadius: BorderRadius.circular(7),
                boxShadow: ChatHelper.c.currentTab.value == value
                    ? ViewsCommon.boxShadow
                    : null),
            alignment: Alignment.center,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CustomText(
                  value == "inbox"
                      ? Get.find<TranslationHelper>().translations.header.inbox
                      : Get.find<TranslationHelper>()
                          .translations
                          .notification
                          .notification,
                  size: 2.1,
                  color: ChatHelper.c.currentTab.value == value
                      ? null
                      : Colors.grey,
                  weight: ChatHelper.c.currentTab.value == value
                      ? FontWeight.w500
                      : null,
                ),
                if (value == "notification")
                  const CircleAvatar(
                      maxRadius: 3, backgroundColor: Color(themeColor))
              ],
            ),
          ),
          onTap: () => ChatHelper.c.changeTab(value)),
    );
  }
}
