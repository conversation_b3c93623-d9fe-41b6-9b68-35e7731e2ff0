import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/planTrip_controller.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:webengage_flutter/webengage_flutter.dart';
import '../components/views_common.dart';
import '../helperMethods/search_helper.dart';
import '../helperMethods/translation_helper.dart';

class PlanTrip extends StatelessWidget {
  final String? location;
  final List<DateTime>? dates;
  const PlanTrip({Key? key, this.location, this.dates}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<PlanTripController>(
      autoRemove: false,
      init: PlanTripController(location: location, dates: dates),
      builder: (c) => Scaffold(
          appBar: AppBar(
              leading: IconButton(
                  onPressed: Get.back,
                  icon: CircleAvatar(
                      backgroundColor: Colors.grey[200],
                      child:
                          const Icon(Icons.chevron_left, color: Colors.black))),
              title: Text(
                  Get.find<TranslationHelper>().translations.search.planTrip)),
          body: ListView(padding: EdgeInsets.all(widthSpace(5)), children: [
            Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              renderBigHead(
                  Get.find<TranslationHelper>().translations.header.whereToQ),
              SizedBox(height: heightSpace(2)),
              TextField(
                readOnly: true,
                controller: c.searchC,
                focusNode: c.searchFocus,
                style: TextStyle(
                    fontWeight: FontWeight.w500, fontSize: heightSpace(2.0)),
                decoration: InputDecoration(
                    hintText: Get.find<TranslationHelper>()
                        .translations
                        .header
                        .searchHere,
                    contentPadding: const EdgeInsets.only(left: 20, right: 20),
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide(color: Colors.grey[400]!)),
                    enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide(color: Colors.grey[400]!))),
                onTap: () async {
                  c.updatedList.clear();
                  c.updatedList.addAll(SearchHelper.c.cities);
                  c.toggleLocationOpen();
                  c.filterFocus.requestFocus();
                },
              ),
              SizedBox(height: widthSpace(3)),
              if (c.isLocationOpen) ...[
                SizedBox(
                    height: heightSpace(20),
                    child: ListView.builder(
                      shrinkWrap: false,
                      itemCount: c.updatedList.length + 1,
                      itemBuilder: (context, index) {
                        int i = index - 1;
                        if (index == 0) {
                          return SizedBox(
                            width: widthSpace(85),
                            height: heightSpace(5),
                            child: TextField(
                              controller: c.filterC,
                              focusNode: c.filterFocus,
                              style: TextStyle(
                                  fontWeight: FontWeight.normal,
                                  fontSize: heightSpace(1.5)),
                              decoration: InputDecoration(
                                  hintText: Get.find<TranslationHelper>()
                                      .translations
                                      .header
                                      .searchHere,
                                  contentPadding: const EdgeInsets.only(
                                      left: 20, right: 20),
                                  prefixIcon: const Icon(Icons.search),
                                  border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide:
                                          BorderSide(color: Colors.grey[400]!)),
                                  enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: BorderSide(
                                          color: Colors.grey[400]!))),
                              onChanged: (value) {
                                if (value == "") {
                                  c.updatedList.clear();
                                  c.updatedList.addAll(SearchHelper.c.cities);
                                  c.update();
                                } else {
                                  c.updatedList.clear();
                                  for (int i = 0;
                                      i < SearchHelper.c.cities.length;
                                      i++) {
                                    if ((Get.locale?.languageCode ?? 'en') ==
                                        'en') {
                                      if (SearchHelper.c.cities[i].name
                                          .toLowerCase()
                                          .contains(value.toLowerCase())) {
                                        c.updatedList
                                            .add(SearchHelper.c.cities[i]);
                                      }
                                    } else {
                                      if (SearchHelper.c.cities[i].nameAr
                                          .toLowerCase()
                                          .contains(value.toLowerCase())) {
                                        c.updatedList
                                            .add(SearchHelper.c.cities[i]);
                                      }
                                    }
                                  }
                                  c.update();
                                }
                              },
                            ),
                          );
                        } else {
                          return InkWell(
                              onTap: () {
                                c.searchC.text =
                                    (Get.locale?.languageCode ?? 'en') == 'en'
                                        ? c.updatedList[i].name
                                        : c.updatedList[i].nameAr;
                                final containsCity = SearchHelper.c.cities
                                    .where((e) => e.isChecked);
                                if (containsCity.isNotEmpty) {
                                  for (var city in SearchHelper.c.cities) {
                                    city.isChecked = false;
                                  }
                                }
                                int cityIndex = SearchHelper.c.cities
                                    .indexWhere(
                                        (e) => e.id == c.updatedList[i].id);
                                SearchHelper.c.cities[cityIndex].isChecked =
                                    !SearchHelper.c.cities[cityIndex].isChecked;
                                SearchHelper.c.selectedCity.value =
                                    c.updatedList[i];
                                SearchHelper.c.cities.refresh();
                                c.toggleLocationClosed();
                              },
                              child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    CustomText(
                                        (Get.locale?.languageCode ?? 'en') ==
                                                'en'
                                            ? c.updatedList[i].name
                                            : c.updatedList[i].nameAr,
                                        textOverflow: TextOverflow.ellipsis,
                                        color: c.searchC.text
                                                    .trim()
                                                    .toLowerCase() ==
                                                c.updatedList[i].name
                                                    .toLowerCase()
                                            ? Colors.black87
                                            : const Color(greyText),
                                        weight: c.searchC.text
                                                    .trim()
                                                    .toLowerCase() ==
                                                c.updatedList[i].name
                                                    .toLowerCase()
                                            ? FontWeight.w500
                                            : null),
                                  ]).paddingAll(
                                widthSpace(3),
                              ));
                        }
                      },
                    ))
              ]
            ]),
            SizedBox(height: heightSpace(4)),
            c.isCalendarOpen
                ? renderContainer(
                    c.toggleCalendarOpen,
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        renderBigHead(Get.find<TranslationHelper>()
                            .translations
                            .header
                            .whenQ),
                        CustomText(
                            c.rangeC.selectedRange?.startDate == null ||
                                    c.rangeC.selectedRange?.endDate == null
                                ? ""
                                : "${formDateFormatCservice.format(c.rangeC.selectedRange!.startDate!)} - ${formDateFormatCservice.format(c.rangeC.selectedRange!.endDate!)} (${c.rangeC.selectedRange!.endDate!.difference(c.rangeC.selectedRange!.startDate!).inDays} ${Get.find<TranslationHelper>().translations.listingBasic.nights})",
                            size: 1.9),
                        Divider(height: heightSpace(4)),
                        SfDateRangePicker(
                          controller: c.rangeC,
                          onSelectionChanged: c.onRangeSelected,
                          selectionMode: DateRangePickerSelectionMode.range,
                          toggleDaySelection: c.toggleDaySelection,
                          rangeSelectionColor:
                              const Color(themeColor).withOpacity(.4),
                          // enablePastDates: false,
                          minDate: DateTime.now(),
                          maxDate: DateTime.now().add(Duration(days: 365)),
                        ),
                      ],
                    ))
                : renderHead(
                    Get.find<TranslationHelper>().translations.header.whenQ,
                    c.rangeC.selectedRange?.startDate == null ||
                            c.rangeC.selectedRange?.endDate == null
                        ? ""
                        : "${formDateFormatCservice.format(c.rangeC.selectedRange!.startDate!)} - ${formDateFormatCservice.format(c.rangeC.selectedRange!.endDate!)} (${c.rangeC.selectedRange!.endDate!.difference(c.rangeC.selectedRange!.startDate!).inDays} ${Get.find<TranslationHelper>().translations.listingBasic.nights})",
                    c.toggleCalendarOpen),
            // SizedBox(height: heightSpace(4)),
            // c.isGuestsOpen
            //     ?renderContainer(c.toggleGuestsOpen,Column(crossAxisAlignment: CrossAxisAlignment.start,children: [
            //       Row(
            //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //           children: [
            //             renderBigHead(Get.find<TranslationHelper>().translations.header.guestQ),
            //             CustomText("${c.adults} ${Get.find<TranslationHelper>().translations.listing.adults}, ${c.children} ${c.children<=1?Get.find<TranslationHelper>().translations.hostDashboard.child:Get.find<TranslationHelper>().translations.listing.children}",size: 2.2),
            //           ]),
            //       SizedBox(height: heightSpace(4)),
            //       Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,children: [
            //         Column(crossAxisAlignment: CrossAxisAlignment.start,children:  [
            //           CustomText(Get.find<TranslationHelper>().translations.listing.adults,size: 2.1,weight: FontWeight.w500),
            //           const SizedBox(height: 5),
            //           renderBlueText("Age 13+"),
            //         ]),
            //         Row(children: [
            //           InkWell(
            //             onTap:()=>c.plusMinusAdults("-"),
            //             child: Container(
            //               padding: const EdgeInsets.all(5),
            //               decoration: BoxDecoration(border: Border.all(color: Colors.grey),shape: BoxShape.circle),
            //               child: const Icon(Icons.remove,color: Colors.grey,size: 21),
            //             ),
            //           ),
            //           SizedBox(width: widthSpace(4)),
            //           CustomText(c.adults.toString(),
            //               weight: FontWeight.w500),
            //           SizedBox(width: widthSpace(4)),
            //           InkWell(
            //             onTap:()=>c.plusMinusAdults("+"),
            //             child: Container(
            //               padding: const EdgeInsets.all(5),
            //               decoration: BoxDecoration(border: Border.all(color: Colors.grey),shape: BoxShape.circle),
            //               child: const Icon(Icons.add,color: Colors.grey,size: 21),
            //             ),
            //           )])
            //       ]),
            //       Divider(height: heightSpace(5)),
            //       Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,children: [
            //         Column(crossAxisAlignment: CrossAxisAlignment.start,children:  [
            //           CustomText(Get.find<TranslationHelper>().translations.listing.children,size: 2.1,weight: FontWeight.w500),
            //           const SizedBox(height: 5),
            //           renderBlueText("Ages 2-12"),
            //         ]),
            //         Row(children: [
            //           InkWell(
            //             onTap: ()=>c.plusMinusChildren("-"),
            //             child: Container(
            //               padding: const EdgeInsets.all(5),
            //               decoration: BoxDecoration(border: Border.all(color: Colors.grey),shape: BoxShape.circle),
            //               child: const Icon(Icons.remove,color: Colors.grey,size: 21),
            //             ),
            //           ),
            //           SizedBox(width: widthSpace(4)),
            //           CustomText(c.children.toString(),weight: FontWeight.w500),
            //           SizedBox(width: widthSpace(4)),
            //           InkWell(
            //             onTap:()=>c.plusMinusChildren("+"),
            //             child: Container(
            //               padding: const EdgeInsets.all(5),
            //               decoration: BoxDecoration(border: Border.all(color: Colors.grey),shape: BoxShape.circle),
            //               child: const Icon(Icons.add,color: Colors.grey,size: 21),
            //             ),
            //           )])
            //       ]),
            //
            //       // Divider(height: heightSpace(5)),
            //       // Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,children: [
            //       //   Column(crossAxisAlignment: CrossAxisAlignment.start,children:  [
            //       //     const CustomText("Infants",size: 2.1,weight: FontWeight.w500),
            //       //     const SizedBox(height: 5),
            //       //     renderBlueText("Under 2"),
            //       //   ]),
            //       //   Row(children: [
            //       //     InkWell(
            //       //       onTap: ()=>c.plusMinusInfants("-"),
            //       //       child: Container(
            //       //         padding: const EdgeInsets.all(5),
            //       //         decoration: BoxDecoration(border: Border.all(color: Colors.grey),shape: BoxShape.circle),
            //       //         child: const Icon(Icons.remove,color: Colors.grey,size: 21),
            //       //       ),
            //       //     ),
            //       //     SizedBox(width: widthSpace(4)),
            //       //     CustomText(c.infants.toString(),weight: FontWeight.w500),
            //       //     SizedBox(width: widthSpace(4)),
            //       //     InkWell(
            //       //       onTap:()=>c.plusMinusInfants("+"),
            //       //       child: Container(
            //       //         padding: const EdgeInsets.all(5),
            //       //         decoration: BoxDecoration(border: Border.all(color: Colors.grey),shape: BoxShape.circle),
            //       //         child: const Icon(Icons.add,color: Colors.grey,size: 21),
            //       //       ),
            //       //     )])
            //       // ]),
            //       // Divider(height: heightSpace(5)),
            //       // Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,children: [
            //       //   Column(crossAxisAlignment: CrossAxisAlignment.start,children:  [
            //       //     const CustomText("Pets",size: 2.1,weight: FontWeight.w500),
            //       //     const SizedBox(height: 5),
            //       //     renderBlueText(" Beging a Service animals"),
            //       //   ]),
            //       //   Row(children: [
            //       //     InkWell(
            //       //       onTap: ()=>c.plusMinusPets("-"),
            //       //       child: Container(
            //       //         padding: const EdgeInsets.all(5),
            //       //         decoration: BoxDecoration(border: Border.all(color: Colors.grey),shape: BoxShape.circle),
            //       //         child: const Icon(Icons.remove,color: Colors.grey,size: 21),
            //       //       ),
            //       //     ),
            //       //     SizedBox(width: widthSpace(4)),
            //       //     CustomText(c.pets.toString(),weight: FontWeight.w500),
            //       //     SizedBox(width: widthSpace(4)),
            //       //     InkWell(
            //       //       onTap:()=>c.plusMinusPets("+"),
            //       //       child: Container(
            //       //         padding: const EdgeInsets.all(5),
            //       //         decoration: BoxDecoration(border: Border.all(color: Colors.grey),shape: BoxShape.circle),
            //       //         child: const Icon(Icons.add,color: Colors.grey,size: 21),
            //       //       ),
            //       //     )])
            //       // ]),
            //     ]))
            //     :renderHead(Get.find<TranslationHelper>().translations.header.guestQ, "${c.adults} ${Get.find<TranslationHelper>().translations.listing.adults}, ${c.children} ${c.children<=1?Get.find<TranslationHelper>().translations.hostDashboard.child:Get.find<TranslationHelper>().translations.listing.children}", c.toggleGuestsOpen),
          ]),
          bottomNavigationBar: Container(
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(viewPadding), vertical: widthSpace(3)),
            decoration: const BoxDecoration(
                border: Border(top: BorderSide(color: Color(greyBorder)))),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonButton(
                      onPressed: c.clearFilter,
                      title: Get.find<TranslationHelper>()
                          .translations
                          .search
                          .clearValues,
                      isBorder: true),
                  CommonButton(
                      title: Get.find<TranslationHelper>()
                          .translations
                          .header
                          .explore,
                      horizontalPadding: 7,
                      onPressed: () {
                        checkCityAndGetItsDistricts();
                        if (SearchHelper.c.selectedCity.value != null ||
                            c.searchC.text.isNotEmpty) {
                          destinationSelectedEventCalled(c.searchC.text);
                          noOfGuestEventCalled(c);
                          searchEventCalled(c);
                          c.onSubmit(
                              SearchHelper.c.selectedCity.value!.id.toString());
                        }
                      })
                ]),
          )),
    );
  }

  searchEventCalled(PlanTripController c) {
    String duration = '1';
    if(c.rangeC.selectedRange?.endDate!=null && c.rangeC.selectedRange?.startDate!=null){
      duration = '${c.rangeC.selectedRange!.endDate!.difference(c.rangeC.selectedRange!.startDate!).inDays}';
    }
      analytics.logSearch(
          searchTerm: '${SearchHelper.c.selectedCity.value?.name}',
          parameters: {
            'search_month':
            DateFormat('MMMM').format(c.rangeC.selectedRange!.startDate!),
            'search_duration':duration
          });
    }

  checkCityAndGetItsDistricts() {
    SearchHelper.c.selectedDistricts.clear();
    for (var item in SearchHelper.c.cities) {
      if (item.isChecked) {
        SearchHelper.c.selectedCity.value = item;
        SearchHelper.c.districts.value = List.from(item.districts);
      }
    }
    SearchHelper.c.districts.refresh();
    SearchHelper.c.update();
  }

  destinationSelectedEventCalled(String destinationName) async {
    await WebEngagePlugin.trackEvent(
        'Destination Selected', {'Destination Name': destinationName});
  }

  noOfGuestEventCalled(PlanTripController c) async {
    await WebEngagePlugin.trackEvent('Number of guest Entered', {
      "Number of Adults": "${c.adults}",
      "Number of Children": "${c.children}",
      "User": isHost ? "Host" : "Customer"
    });
  }

  renderBlueText(text) =>
      CustomText(text, size: 1.9, color: const Color(0xff332496));
  renderContainer(toggleView, Widget child) {
    return InkWell(
      onTap: toggleView,
      child: Container(
          padding: EdgeInsets.all(widthSpace(4.8)),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: ViewsCommon.boxShadow,
            borderRadius: BorderRadius.circular(22),
          ),
          child: child),
    );
  }

  renderHead(text1, text2, toggleView) {
    return renderContainer(
        toggleView,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            renderHeadText(text1),
            CustomText(text2,
                size: 2.0,
                color: const Color(greyText),
                textOverflow: TextOverflow.ellipsis,
                textAlign: TextAlign.end),
          ],
        ));
  }

  renderBigHead(text) =>
      CustomText(text, size: 2.3, weight: FontWeight.w500, maxlines: 5);
  renderHeadText(text) => CustomText(text, size: 2.1, weight: FontWeight.w500);
}
