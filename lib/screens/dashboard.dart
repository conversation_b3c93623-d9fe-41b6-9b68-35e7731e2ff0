import 'package:darent/analytics/analytics.dart';
import 'package:darent/components/no_internet_connection.dart';
import 'package:darent/controllers/dashboard_controller.dart';
import 'package:darent/screens/account.dart';
import 'package:darent/screens/authentication/account_login.dart';
import 'package:darent/screens/home.dart';
import 'package:darent/screens/inbox.dart';
import 'package:darent/screens/reservations/reservations.dart';
import 'package:darent/screens/wishlist/wishist_group.dart';
import 'package:darent/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../helperMethods/translation_helper.dart';
import '../utils/sizeconfig.dart';
import 'authentication/required_login.dart';

class Dashboard extends StatelessWidget {
  const Dashboard({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return GetBuilder<DashboardController>(
      builder: (c) {
        return WillPopScope(
          onWillPop: () async {
            if (c.index == 0) {
              if (c.scrollController.hasClients &&
                  c.scrollController.position.pixels !=
                      c.scrollController.position.minScrollExtent) {
                c.scrollController.animateTo(0.0,
                    duration: const Duration(milliseconds: 200),
                    curve: Curves.easeIn);
                return false;
              } else {
                return true;
              }
            } else {
              c.changeIndex(0);
              return false;
            }
          },
          child: Scaffold(
            extendBodyBehindAppBar: false,
            appBar: c.index == 0 ||
                    c.index == 4 ||
                    c.index == 3 ||
                    Get.find<TranslationHelper>().translateKeywords.isEmpty
                ? null
                : AppBar(
                    leading: null,
                    title: Text(
                        c.index == 1
                            ? Get.find<TranslationHelper>()
                                .translations
                                .wishlist
                                .wishlist
                            : c.index == 2
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .listingSidebar
                                    .booking
                                : Get.find<TranslationHelper>()
                                    .translations
                                    .header
                                    .inbox,
                        style: TextStyle(
                            fontSize: userModel.value != null ? 20 : 23)),
                    backgroundColor: userModel.value == null
                        ? Colors.grey.withOpacity(0.2)
                        : null,
                    centerTitle: true,
                    elevation: userModel.value == null ? 0.0 : null,
                  ),

            body: SafeArea(
                child: c.isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : Get.find<TranslationHelper>().translateKeywords.isEmpty
                        ? NoInternetConnection(onRefresh: c.refreshHome)
                        : userModel.value != null
                            ? IndexedStack(index: c.index, children: const [
                                Home(),
                                WishlistGroup(),
                                Reservations(),
                                Inbox(),
                                Account()
                              ])
                            : IndexedStack(index: c.index, children: [
                                const Home(),
                                RequiredLogin(
                                    subTitle: Get.find<TranslationHelper>()
                                        .translations
                                        .footer
                                        .wishlistSubTitle,
                                    description: Get.find<TranslationHelper>()
                                        .translations
                                        .footer
                                        .wishlistDescription,
                                    picture: 'assets/wishList.svg'),
                                RequiredLogin(
                                    subTitle: Get.find<TranslationHelper>()
                                        .translations
                                        .footer
                                        .reservationSubTitle,
                                    description: Get.find<TranslationHelper>()
                                        .translations
                                        .footer
                                        .reservationDescription,
                                    picture: 'assets/nobookings.svg'),
                                RequiredLogin(
                                    subTitle: Get.find<TranslationHelper>()
                                        .translations
                                        .footer
                                        .inboxSubTitle,
                                    description: Get.find<TranslationHelper>()
                                        .translations
                                        .footer
                                        .inboxDescription,
                                    picture: 'assets/noChat.svg'),
                                const AccountLogin()
                              ])
                // Home(),

                ),
            bottomNavigationBar:
                // userModel.value == null ||
                Get.find<TranslationHelper>().translateKeywords.isEmpty
                    ? null
                    : BottomNavigationBar(
                        currentIndex: c.index,
                        type: BottomNavigationBarType.fixed,
                        selectedItemColor: const Color(themeColor),
                        unselectedItemColor: Colors.grey,
                        showUnselectedLabels: true,
                        selectedLabelStyle:
                            const TextStyle(fontSize: 12, height: 2),
                        unselectedLabelStyle:
                            const TextStyle(fontSize: 12, height: 2),
                        onTap: c.changeIndex,
                        items: [
                            renderItem(
                                'home',
                                c.index == 0,
                                Get.find<TranslationHelper>()
                                    .translations
                                    .footer
                                    .home),
                            renderItem(
                                'wishlist',
                                c.index == 1,
                                Get.find<TranslationHelper>()
                                    .translations
                                    .wishlist
                                    .wishlist),
                            renderItem(
                                'bookings',
                                c.index == 2,
                                Get.find<TranslationHelper>()
                                    .translations
                                    .listingSidebar
                                    .booking),
                            renderItem(
                                'inbox',
                                c.index == 3,
                                Get.find<TranslationHelper>()
                                    .translations
                                    .header
                                    .inbox),
                            renderItem(
                                'user',
                                c.index == 4,
                                Get.find<TranslationHelper>()
                                    .translations
                                    .header
                                    .account),
                            // BottomNavigationBarItem(
                            //     icon: SvgPicture.asset(
                            //   "assets/icons/${c.index == 0 ? 'home-fill' : 'home'}.svg",
                            //   height: 16,
                            //   ),
                            //   label:Get.find<TranslationHelper>().translations.footer.home),
                            // BottomNavigationBarItem(
                            //     icon: SvgPicture.asset(
                            //       "assets/icons/${c.index == 1 ? 'wishlist-fill' : 'wishlist'}.svg",
                            //      height: 16,
                            //     ),
                            //     label:Get.find<TranslationHelper>().translations.wishlist.wishlist),
                            // BottomNavigationBarItem(
                            //     icon: SvgPicture.asset(
                            //       "assets/icons/${c.index == 2 ? 'booking-fill' : 'booking'}.svg",
                            //         height: 16,
                            //         ),
                            //
                            //     label:Get.find<TranslationHelper>().translations.listingSidebar.booking),
                            // BottomNavigationBarItem(
                            //     icon: SvgPicture.asset(
                            //       "assets/icons/${c.index == 3 ? 'inbox-fill' : 'inbox'}.svg",
                            //         height: 16,
                            //         ),
                            //     label:Get.find<TranslationHelper>().translations.header.inbox ),
                            // BottomNavigationBarItem(
                            //   icon: SvgPicture.asset(
                            //       "assets/icons/${c.index == 4 ? 'user-fill' : 'user'}.svg",
                            //         height: 16,
                            //         ),
                            //   label:Get.find<TranslationHelper>().translations.header.account,
                            // ),
                          ]),
            // floatingActionButton:translateKeywords.isEmpty || c.data.isEmpty || c.index>0 ?null: FloatingActionButton(onPressed: c.toggleHomeView,backgroundColor: Colors.white,child: Obx(()=>SvgPicture.asset("assets/icons/show-${c.listView.value?"map":"list"}.svg",width: widthSpace(7)))),
            // bottomSheet: userModel.value != null || translateKeywords.isEmpty
            //     ? null
            //     : DraggableScrollableSheet(
            //         controller: AuthHelper.homeAuthSheet,
            //         expand: false,
            //         initialChildSize: .13,
            //         minChildSize: .13,
            //         maxChildSize: .75,
            //         builder: (BuildContext context,
            //             ScrollController scrollController) {
            //           return Container(
            //               decoration: const BoxDecoration(
            //                 color: Colors.white,
            //                 border:Border(top: BorderSide(color: Color(greyBorder))),
            //               ),
            //               child: LoginComponent(
            //                   header: SizedBox(
            //                       width: widthSpace(65),
            //                       child: CustomText(
            //                           "${translateKeywords[Get.locale?.languageCode??"en"]!.homepage.discover} ${translateKeywords[Get.locale?.languageCode??"en"]!.homepage.newWorld}",
            //                           size: 4.5)),
            //                   scrollController: scrollController));
            //         },
            //       ),
          ),
        );
      },
    );
  }

  renderItem(icon, selected, title) {
    //selected?'$icon-fill':icon
    return BottomNavigationBarItem(
      icon: SvgPicture.asset("assets/icons/$icon.svg",
          colorFilter: ColorFilter.mode(
              Color(selected ? themeColor : greyText), BlendMode.srcIn),
          height: widthSpace(8)),
      label: title,
    );
  }
}
