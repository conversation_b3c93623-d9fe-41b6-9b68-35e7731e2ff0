import 'dart:async';
import 'dart:io';
import 'package:darent/components/custom_image_picker_sheet.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/account_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';

import '../helperMethods/translation_helper.dart';
import '../helperMethods/authHelper.dart';

class CustomerServices extends StatelessWidget {
  const CustomerServices({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvoked: (pop) async {
        if (pop) {
          AuthHelper.c.serviceImage.value = null;
        }
      },
      child: Scaffold(
        appBar: AppBar(
            leading: IconButton(
                onPressed: () {
                  AuthHelper.c.serviceImage.value = null;
                  Get.back();
                },
                icon: const Icon(Icons.chevron_left)),
            title: Text(Get.find<TranslationHelper>()
                .translations
                .accountMobile
                .customerServices!)),
        body: GetBuilder<AccountController>(
            id: "support",
            initState: (state) {
              Timer(Duration.zero, () {
                state.controller!.getServiceMessages(onInit: true);
              });
            },
            builder: (c) => NotificationListener<ScrollEndNotification>(
                onNotification: (notification) {
                  final metrics = notification.metrics;
                  if (metrics.atEdge) {
                    bool isTop = metrics.pixels == 0;
                    if (!isTop && c.serviceMessagesPage < c.totalMessagesPage) {
                      c.serviceMessagesPage++;
                      c.getServiceMessages();
                    }
                  }
                  return true;
                },
                child: SafeArea(
                    child: SingleChildScrollView(
                        controller: c.serviceScroll,
                        reverse: true,
                        padding:
                            EdgeInsets.symmetric(horizontal: widthSpace(6)),
                        child: Column(children: [
                          SizedBox(height: heightSpace(1.5)),
                          Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const CircleAvatar(
                                    radius: 25,
                                    backgroundImage:
                                        AssetImage('assets/icons/d_logo.png')),
                                SizedBox(
                                  width: widthSpace(4),
                                ),
                                Expanded(
                                  child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        CustomText(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .accountMobile
                                                .darentAgent,
                                            size: 2.5,
                                            weight: FontWeight.w500),
                                        SizedBox(height: heightSpace(0.5)),
                                        CustomText(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .accountMobile
                                                .sendMsgAndOneOurGuestContact,
                                            size: 1.8,
                                            color: Colors.grey[800])
                                      ]),
                                )
                              ]),
                          SizedBox(height: heightSpace(2.5)),
                          const Divider(),
                          c.serviceMessages.isEmpty
                              ? SvgPicture.asset('assets/noChat.svg',
                                  height: heightSpace(50))
                              : ListView.separated(
                                  padding: EdgeInsets.only(top: heightSpace(4)),
                                  itemCount: c.serviceMessages.length,
                                  shrinkWrap: true,
                                  primary: false,
                                  itemBuilder: (context, index) {
                                    DateTime createdAt = DateTime.parse(
                                        c.serviceMessages[index]['created_at']);
                                    bool isUser = c.serviceMessages[index]
                                            ['is_user'] ==
                                        1;
                                    return Align(
                                      alignment: isUser
                                          ? Alignment.centerRight
                                          : Alignment.centerLeft,
                                      child: Column(
                                        crossAxisAlignment: isUser
                                            ? CrossAxisAlignment.end
                                            : CrossAxisAlignment.start,
                                        children: [
                                          Container(
                                            padding:
                                                EdgeInsets.all(widthSpace(1.5)),
                                            decoration: BoxDecoration(
                                              color: Color(isUser
                                                  ? themeColor
                                                  : lightBg),
                                              borderRadius:
                                                  BorderRadius.circular(
                                                      heightSpace(1.7)),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                if (c.serviceMessages[index]['file'].toString() != 'null')
                                                  c.serviceMessages[index]['file'].contains('/customer_support')
                                                      ? Image(
                                                    image: GlobalHelper.buildNetworkImageProvider(
                                                        url: c.serviceMessages[index]['file']??"",),
                                                    height:heightSpace(45),
                                                    loadingBuilder:(context, child, l) =>
                                                                  l == null
                                                                      ? child
                                                                      : Center(
                                                                          child:
                                                                              CircularProgressIndicator(
                                                                            value: l.expectedTotalBytes != null
                                                                                ? l.cumulativeBytesLoaded / l.expectedTotalBytes!
                                                                                : null,
                                                                            color:
                                                                                Colors.white,
                                                                            strokeWidth:
                                                                                .8,
                                                                          ),
                                                                        ),
                                                        )
                                                      : Image.file(
                                                          File(
                                                              c.serviceMessages[
                                                                      index]
                                                                  ['file']),
                                                          height:
                                                              heightSpace(45)),
                                                if ((c.serviceMessages[index]
                                                            ['message'] ??
                                                        '')
                                                    .isNotEmpty)
                                                  CustomText(
                                                      c.serviceMessages[index]
                                                          ['message'],
                                                      size: 2.2,
                                                      weight: isUser
                                                          ? null
                                                          : FontWeight.w500,
                                                      color: isUser
                                                          ? Colors.white
                                                          : null),
                                              ],
                                            ),
                                          ),
                                          Row(
                                              mainAxisAlignment: isUser
                                                  ? MainAxisAlignment.end
                                                  : MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                CustomText(
                                                  DateFormat('hh:mm a').format(
                                                      createdAt.toLocal()),
                                                  color: const Color(greyText),
                                                  size: 1.8,
                                                ),
                                                const CustomText(" - ",
                                                    color: Color(greyText),
                                                    weight: FontWeight.bold),
                                                CustomText(
                                                  formDateFormatCservice.format(
                                                      createdAt.toLocal()),
                                                  color: const Color(greyText),
                                                  size: 1.8,
                                                )
                                              ])
                                        ],
                                      ),
                                    );
                                  },
                                  separatorBuilder: (context, index) =>
                                      SizedBox(height: heightSpace(4)))
                        ]))))),
        bottomNavigationBar: Container(
          margin: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom + 15,
              left: 15,
              right: 15),
          padding: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20.0),
              color: Colors.grey[200]),
          child: Obx(
            () => Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                if (AuthHelper.c.serviceImage.value != null)
                  Container(
                      width: widthSpace(25),
                      height: widthSpace(45),
                      alignment: Alignment.topRight,
                      clipBehavior: Clip.antiAlias,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                            image: FileImage(
                                File(AuthHelper.c.serviceImage.value!))),
                        borderRadius: BorderRadius.circular(9.0),
                      ),
                      child: AuthHelper.c.isLoadingGoogle.value
                          ? const SizedBox.square(
                                  dimension: 24.0,
                                  child: Padding(
                                    padding: EdgeInsets.all(4.0),
                                    child: CircularProgressIndicator(
                                      strokeWidth: 1.0,
                                    ),
                                  ),)
                          : InkWell(
                              onTap: () =>
                                  AuthHelper.c.serviceImage.value = null,
                              child: const CircleAvatar(
                                  backgroundColor: Colors.white,
                                  radius: 12,
                                  child: Icon(Icons.close, size: 16)))),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: AuthHelper.c.serviceMessageField,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 15.0, horizontal: 15.0),
                          border: InputBorder.none,
                          hintText: Get.find<TranslationHelper>()
                              .translations
                              .accountMobile
                              .enterYourMsgHere!,
                          hintStyle:
                              const TextStyle(fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        imagePickerModes((ImageSource source) {
                          AuthHelper.c.attachServiceImage(source);
                          Get.back();
                        });
                      },
                      child: AuthHelper.c.isLoading.value
                          ? const SizedBox(
                              height: 22,
                              width: 22,
                              child: CircularProgressIndicator(
                                  strokeWidth: 1.5, color: Colors.black))
                          : const Icon(Icons.attach_file_outlined, size: 20),
                    ),
                    const SizedBox(width: 5),
                    InkWell(
                      onTap: AuthHelper.c.sendServiceMsg,
                      child: AuthHelper.c.isLoadingGoogle.value
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child:
                                  CircularProgressIndicator(strokeWidth: 1.5))
                          : Container(
                              height: 40,
                              width: 40,
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                  color: const Color(themeColor),
                                  borderRadius: BorderRadius.circular(13)),
                              child: SvgPicture.asset("assets/icons/send.svg")),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
