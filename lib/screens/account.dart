import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/screens/new_host/payment_method.dart';
import '../helperMethods/remote_config.dart';
import '../helperMethods/search_helper.dart';
import '../helperMethods/translation_helper.dart';
import '../helperMethods/authHelper.dart';
import 'package:darent/screens/account_info.dart';
import 'package:darent/screens/customer_services.dart';
import 'package:darent/screens/login_security.dart';
import 'package:darent/screens/problem/problem_listing.dart';
import 'package:darent/screens/problem/report_problem.dart';
import 'package:darent/screens/wallet_payments.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../helperMethods/update_helper.dart';
import '../utils/routes.dart';
import 'accountManger/account_manage_list.dart';

class Account extends StatelessWidget {
  const Account({super.key});
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => SingleChildScrollView(
        padding: EdgeInsets.all(widthSpace(viewPadding)),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .utility
                  .account, // ??offlineTranslations?["account"]
              size: 2.6,
              weight: FontWeight.w500),
          SizedBox(height: heightSpace(1.2)),
          InkWell(
            onTap: () async {
              // await ConnectivityHelper.checkInternet().then((value) {
              //   if(value){
              Get.toNamed(Routes.profile);
              //   }else{
              //     showConnectivityErrorToast();
              //   }
              // });
            },
            child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(45),
                    child: (userModel.value?.profile_image ?? "").contains(".svg")
                        ? GlobalHelper.buildNetworkSvgWidget(
                      url: userModel.value?.profile_image ??"",
                      height: widthSpace(15),
                      width: widthSpace(15),
                      defaultOption: const Icon(Icons.person,
                      size:48,
                    ),)
                        : GlobalHelper.resolveImageUrl(userModel.value?.profile_image??"").isNotEmpty
                         ? Image(
                        image: GlobalHelper.buildNetworkImageProvider(
                          url: userModel.value?.profile_image??"",),
                            height: widthSpace(15),
                            width: widthSpace(15),
                            fit: BoxFit.fill,
                    )
                         : Padding(
                      padding: const EdgeInsets.all(30),
                      child:
                      Icon(Icons.person, size: widthSpace(14)),
                    ),
                  ),
                  SizedBox(width: widthSpace(5.5)),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                            "${userModel.value?.first_name} ${userModel.value?.last_name}",
                            size: 2.1,
                            weight: FontWeight.w500),
                        SizedBox(height: heightSpace(0.3)),
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .accountMobile
                                .viewProfile, // ??offlineTranslations?["view_profile"]
                            size: 2,
                            color: Colors.grey,
                            weight: FontWeight.w500),
                        //Divider(height: heightSpace(4)),
                      ],
                    ),
                  ),
                  SizedBox(width: widthSpace(5)),
                  const Icon(Icons.arrow_forward_ios, size: 20)
                ]),
          ),
          SizedBox(height: heightSpace(1)),
          const Divider(),
          SizedBox(height: heightSpace(1)),
          InkWell(
            onTap: () {
              SearchHelper.c.switchMode(index: 3);
            },
            child: Container(
              height: heightSpace(14),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    spreadRadius: 1,
                    blurRadius: 2,
                    offset: const Offset(3, 3),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .listing
                                .showYourHouseOnDarent,
                            size: 1.7,
                            weight: FontWeight.bold,
                          ),
                          // SizedBox(height: heightSpace(1)),
                          // CustomText(
                          //     Get.find<TranslationHelper>().translations.listing.showHouseOnDarentDescription,size: 1.4)
                        ]),
                  ),
                  Image.asset("assets/icons/homeL3.png", width: widthSpace(25)),
                ],
              ),
            ),
          ),
          SizedBox(height: heightSpace(1)),
          const Divider(),
          SizedBox(height: heightSpace(1)),
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .header
                  .settings, //??offlineTranslations?["setting"]
              size: 2.4,
              weight: FontWeight.w500),
          SizedBox(height: heightSpace(3)),
          extraComponent("account-info",
              name: Get.find<TranslationHelper>()
                  .translations
                  .accountMobile
                  .accountInfo,
              screen: const AccountInfo()),
          if (baseUrl.contains("dev")) ...[
            SizedBox(height: heightSpace(2)),
            InkWell(
                onTap: () async {
                  // await ConnectivityHelper.checkInternet().then((value) {
                  //   if(value){
                  Get.to(() => const AccountManageList());
                  //   }else{
                  //     showConnectivityErrorToast();
                  //   }
                  // });
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(Icons.person_add_alt_1_outlined),
                    SizedBox(width: widthSpace(7.5)),
                    Expanded(
                        child: CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .sidenav
                          .accountManager,
                      size: 2.1,
                      weight: FontWeight.w500,
                      color: Colors.grey[800],
                    )),
                    const Icon(
                      Icons.arrow_forward_ios_sharp,
                      size: 20,
                    )
                  ],
                )),
          ],
          SizedBox(height: heightSpace(2)),
          extraComponent("security",
              name: Get.find<TranslationHelper>()
                  .translations
                  .usersProfile
                  .loginSecurity,
              screen: const LoginSecurity()),
          SizedBox(height: heightSpace(2)),
          extraComponent("wallet",
              name: Get.find<TranslationHelper>().translations.sidenav.wallet,
              screen: const WalletPayments()),
          SizedBox(height: heightSpace(2)),
          extraComponent("credit_card",
              name: Get.find<TranslationHelper>()
                  .translations
                  .wallet
                  .paymentMethods,
              screen: const PaymentMethod()),

          // SizedBox(height: heightSpace(2)),
          // InkWell(
          //     onTap: () => Get.to(() => const BankAccount()),
          //     child:
          //     Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //       crossAxisAlignment: CrossAxisAlignment.center,
          //       children:  [
          //         const Icon(Icons.account_balance),
          //         SizedBox(width: widthSpace(7.5)),
          //         Expanded(child:  CustomText(translation.sidenav.bankAccount, size: 2.1,weight: FontWeight.w500,color: Colors.grey[800],)),
          //         const Icon(Icons.arrow_forward_ios_sharp, size: 20)
          //       ],
          //     )
          // ),
          // if(isHost)...[
          //   Divider(height: heightSpace(4)),
          //   CustomText( translation.listing.promotion??"Promotion".tr, size: 2.2, weight: FontWeight.w500),
          //   SizedBox(height: heightSpace(3)),
          //    InkWell(
          //       onTap:  () => Get.to(() => const PromoCode()),
          //       child:
          //       Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //         crossAxisAlignment: CrossAxisAlignment.center,
          //         children:  [
          //           const Icon(Icons.discount),
          //           SizedBox(width: widthSpace(7.5)),
          //           Expanded(child:  CustomText(translation.listing.createPromo??"Create Promo", size: 2.1,weight: FontWeight.w500,color: Colors.grey[800],)),
          //           const Icon(Icons.arrow_forward_ios_sharp,size: 20,)
          //         ],
          //       )
          //       ),
          //   SizedBox(height: heightSpace(2)),
          //   InkWell(
          //       onTap:  () => Get.to(() => const Promotions()),
          //       child:
          //       Row(mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //         crossAxisAlignment: CrossAxisAlignment.center,
          //         children:  [
          //           const Icon(Icons.discount,),
          //           SizedBox(width: widthSpace(7.5)),
          //           Expanded(child:  CustomText(translation.listing.promotions??"Promotions", size: 2.1,weight: FontWeight.w500,color: Colors.grey[800],)),
          //           const Icon(Icons.arrow_forward_ios_sharp,size: 20)
          //         ],
          //       )
          //       ),
          //       ],
          Divider(height: heightSpace(4)),
          CustomText(Get.find<TranslationHelper>().translations.footer.hosting,
              size: 2.2, weight: FontWeight.w500),
          SizedBox(height: heightSpace(3)),
          SizedBox(
            height: heightSpace(3.2),
            child: extraComponent(
              "switch",
              name: Get.find<TranslationHelper>()
                  .translations
                  .accountMobile
                  .switchToHosting,
              function: SearchHelper.c.switchMode,
            ),
          ),

          SizedBox(height: heightSpace(2)),
          // SizedBox(height: heightSpace(3.2),
          // child:extraComponent("list",name:translation.footer.becomeAHost, function: ()=>Get.to(()=>const AddProperty())),
          // ),
          Divider(height: heightSpace(5)),
          CustomText(
              Get.find<TranslationHelper>().translations.accountMobile.pages,
              size: 2.3,
              weight: FontWeight.w500),
          SizedBox(height: heightSpace(3)),

          InkWell(
              onTap: () async {
                // await ConnectivityHelper.checkInternet().then((value) {
                //   if(value){
                AuthHelper.c.openWebView('about',
                    Get.find<TranslationHelper>().translations.footer.aboutUs);
                //   }else{
                //     showConnectivityErrorToast();
                //   }
                // });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(Icons.description),
                  SizedBox(width: widthSpace(7.5)),
                  Expanded(
                      child: CustomText(
                    Get.find<TranslationHelper>().translations.footer.aboutUs,
                    size: 2.1,
                    weight: FontWeight.w500,
                    color: Colors.grey[800],
                  )),
                  const Icon(Icons.arrow_forward_ios_sharp, size: 20)
                ],
              )),
          SizedBox(height: heightSpace(2)),
          SizedBox(
              height: heightSpace(3.2),
              child: extraComponent(
                "list",
                name: Get.find<TranslationHelper>()
                    .translations
                    .accountMobile
                    .termsAndConditions,
                function: () async {
                  // await ConnectivityHelper.checkInternet().then((value) {
                  //   if(value){
                  AuthHelper.c.openWebView(
                      "term_condition",
                      Get.find<TranslationHelper>()
                          .translations
                          .accountMobile
                          .termsAndConditions);
                  //   }else{
                  //     showConnectivityErrorToast();
                  //   }
                  // });
                },
              )),
          SizedBox(height: heightSpace(2)),
          InkWell(
              onTap: () async {
                // await ConnectivityHelper.checkInternet().then((value) {
                //   if(value){
                AuthHelper.c.openWebView(
                    'privacy-policy',
                    Get.find<TranslationHelper>()
                        .translations
                        .footer
                        .privacyPolicy);
                //   }else{
                //     showConnectivityErrorToast();
                //   }
                // });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(Icons.privacy_tip_outlined),
                  SizedBox(width: widthSpace(7.5)),
                  Expanded(
                      child: CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .footer
                        .privacyPolicy,
                    size: 2.1,
                    weight: FontWeight.w500,
                    color: Colors.grey[800],
                  )),
                  const Icon(Icons.arrow_forward_ios_sharp, size: 20)
                ],
              )),
          SizedBox(height: heightSpace(2)),
          InkWell(
              onTap: () async {
                // await ConnectivityHelper.checkInternet().then((value) {
                //   if(value){
                AuthHelper.c.openWebView('blog',
                    Get.find<TranslationHelper>().translations.general.blog);
                //   }else{
                //     showConnectivityErrorToast();
                //   }
                // });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(Icons.list_alt),
                  SizedBox(width: widthSpace(7.5)),
                  Expanded(
                      child: CustomText(
                    Get.find<TranslationHelper>().translations.general.blog,
                    size: 2.1,
                    weight: FontWeight.w500,
                    color: Colors.grey[800],
                  )),
                  const Icon(Icons.arrow_forward_ios_sharp, size: 20)
                ],
              )),

          Divider(height: heightSpace(5)),
          CustomText(Get.find<TranslationHelper>().translations.footer.support,
              size: 2.3, weight: FontWeight.w500),
          SizedBox(height: heightSpace(3)),
          InkWell(
              onTap: () async {
                // await ConnectivityHelper.checkInternet().then((value) {
                //   if(value){
                AuthHelper.c.openWebView('faqs',
                    Get.find<TranslationHelper>().translations.footer.faq);
                //   }else{
                //     showConnectivityErrorToast();
                //   }
                // });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(Icons.question_answer_outlined),
                  SizedBox(width: widthSpace(7.5)),
                  Expanded(
                      child: CustomText(
                    Get.find<TranslationHelper>().translations.footer.faq,
                    size: 2.1,
                    weight: FontWeight.w500,
                    color: Colors.grey[800],
                  )),
                  const Icon(Icons.arrow_forward_ios_sharp, size: 20)
                ],
              )),
          SizedBox(height: heightSpace(2)),
          extraComponent("service",
              name: Get.find<TranslationHelper>()
                  .translations
                  .accountMobile
                  .customerServices,
              screen: const CustomerServices()),
          SizedBox(height: heightSpace(2)),
          SizedBox(height: heightSpace(2)),
          InkWell(
              onTap: () async {
                // await ConnectivityHelper.checkInternet().then((value) {
                //   if(value){
                Get.to(() => const ReportProblem());
                // }
                //   else{
                //     showConnectivityErrorToast();
                //   }
                // });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(Icons.report_gmailerrorred),
                  SizedBox(width: widthSpace(7.5)),
                  Expanded(
                      child: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .wallet
                              .createTicket,
                          size: 2.1,
                          weight: FontWeight.w500,
                          color: Colors.grey[800])),
                  const Icon(Icons.arrow_forward_ios_sharp, size: 20)
                ],
              )),
          SizedBox(height: heightSpace(2)),
          InkWell(
              onTap: () async {
                // await ConnectivityHelper.checkInternet().then((value) {
                //   if(value){
                Get.to(() => const ProblemListing());
                //   }
                //   else{
                //     showConnectivityErrorToast();
                //   }
                // });
              },
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(Icons.list),
                    SizedBox(width: widthSpace(7.5)),
                    Expanded(
                        child: CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .wallet
                                .ticketListing,
                            size: 2.1,
                            weight: FontWeight.w500,
                            color: Colors.grey[800])),
                    const Icon(Icons.arrow_forward_ios_sharp, size: 20)
                  ])),
          Divider(height: heightSpace(5)),
          CustomText(Get.find<TranslationHelper>().translations.footer.language,
              size: 2.3, weight: FontWeight.w500),
          SizedBox(height: heightSpace(3)),
          InkWell(
              onTap: () async {
                // await ConnectivityHelper.checkInternet().then((value) {
                //   if(value){
                AuthHelper.c.selectLanguage(
                    Get.locale?.languageCode == 'ar' ? 'en' : 'ar');
                //   }
                //   else{
                //     showConnectivityErrorToast();
                //   }
                // });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const Icon(Icons.language),
                  SizedBox(width: widthSpace(7.5)),
                  Expanded(
                      child: CustomText(
                          '${Get.find<TranslationHelper>().translations.accountMobile.browseIn} ${Get.locale?.languageCode == 'ar' ? 'English' : 'عربي'}',
                          size: 2.1,
                          weight: FontWeight.w500,
                          color: Colors.grey[800])),
                  const Icon(Icons.arrow_forward_ios_sharp, size: 20)
                ],
              )),
          Divider(height: heightSpace(5)),
          CustomText(
              Get.find<TranslationHelper>().translations.accountMobile.appInfo,
              size: 2.3,
              weight: FontWeight.w500),
          SizedBox(height: heightSpace(3)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Icon(Icons.update),
              SizedBox(width: widthSpace(7.5)),
              Expanded(
                  child: CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .accountMobile
                          .appVersion,
                      size: 2.1,
                      weight: FontWeight.w500,
                      color: Colors.grey[800])),
              CustomText(UpdateHelper.appVersion,
                  size: 2.2,
                  weight: FontWeight.w500,
                  color: const Color(themeColor)),
            ],
          ),
          SizedBox(height: heightSpace(3)),
          InkWell(
              onTap: () async {
                // await ConnectivityHelper.checkInternet().then((value) {
                //   if(value){
                AuthHelper.c.showLogout();
                //   }
                //   else{
                //     showConnectivityErrorToast();
                //   }
                // });
              },
              child: CustomText(
                  Get.find<TranslationHelper>().translations.header.logout,
                  size: 2.4,
                  underline: true,
                  weight: FontWeight.w500)),
        ]),
      ),
    );
  }

  showConnectivityErrorToast() {
    ViewsCommon.showSnackbar("No Internet Connection",
        keyword: DialogKeyword.warning);
  }

  extraComponent(String image, {required name, Widget? screen, function}) {
    return InkWell(
      onTap: function ??
          () async {
            // await ConnectivityHelper.checkInternet().then((value) {
            //   if(value){
            if (screen != null) {
              Get.to(() => screen);
            }
            //   }else{
            //     showConnectivityErrorToast();
            //   }
            // });
          },
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset("assets/$image.svg",
                width: widthSpace(6), color: Colors.black),
            SizedBox(width: widthSpace(7.5)),
            Expanded(
              child: CustomText(
                name,
                color: Colors.grey[800],
                weight: FontWeight.w500,
                size: 2.1,
              ),
            ),
            SizedBox(width: widthSpace(8)),
            const Icon(
              Icons.arrow_forward_ios,
              size: 20,
            )
          ]),
    );
  }
}
