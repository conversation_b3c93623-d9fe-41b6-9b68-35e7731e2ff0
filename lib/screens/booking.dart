import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/components/payment_method_icon.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/checkout_controller.dart';
import 'package:darent/controllers/property_detail_controller.dart';
import 'package:darent/helperMethods/authHelper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/payment_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:webengage_flutter/webengage_flutter.dart';

import '../components/common_switch.dart';
import '../helperMethods/remote_config.dart';
import '../helperMethods/translation_helper.dart';

class Booking extends StatelessWidget {
  Booking({Key? key}) : super(key: key);
  late CheckoutController c;
  late PropertyDetailController propertyC;
  @override
  Widget build(BuildContext context) {
    propertyC = Get.find();
    c = Get.put(CheckoutController(propertyC));
    final trans = Get.find<TranslationHelper>().translations;
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            c.clearPromo();
            propertyC.isGuestsOpen.value = false;
            Get.back();
          },
          icon: const Icon(Icons.chevron_left),
        ),
        title: Text(
            Get.find<TranslationHelper>().translations.reservation.reservation),
      ),
      body: Obx(
        () => c.propertyPrice.value ==
                null //|| (propertyC.data.value?.bookingType=="instant" && c.paymentMethods.isEmpty)
            ? const Center(child: CircularProgressIndicator())
            : PopScope(
                onPopInvoked: (pop) {
                  if (pop) {
                    c.clearPromo();
                    propertyC.isGuestsOpen.value = false;
                  }
                },
                child: ListView(
                    padding: EdgeInsets.symmetric(
                        horizontal: widthSpace(viewPadding)),
                    children: [
                      // propertyC.data.value?.propertyPhotos!.isNotEmpty==true?
                      // CarouselSlider.builder(
                      //       itemBuilder: (context, index, realIndex) {
                      //         return
                      Container(
                          height: heightSpace(27),
                          width: widthSpace(double.maxFinite),
                          clipBehavior: Clip.antiAlias,
                          decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.circular(heightSpace(1.6)),
                          ),
                          child: propertyC
                                      .data.value?.propertyPhotos!.isNotEmpty ==
                                  false
                              ? Image(
                                  image: GlobalHelper.buildNetworkImageProvider(url: defaultImage ??''),
                                  fit: BoxFit.cover)
                              : propertyC.data.value?.propertyPhotos?.length ==
                                      1
                                  ? Image(
                                   image: GlobalHelper.buildNetworkImageProvider(url: propertyC.data.value?.propertyPhotos?.first.photo ??''),
                                   fit: BoxFit.cover)
                                  : Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.stretch,
                                      children: [
                                          renderImage(propertyC.data.value
                                              ?.propertyPhotos![0].photo),
                                          propertyC.data.value!.propertyPhotos!
                                                      .length >=
                                                  3
                                              ? Expanded(
                                                  child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .stretch,
                                                      children: [
                                                        renderImage(propertyC
                                                            .data
                                                            .value
                                                            ?.propertyPhotos![1]
                                                            .photo),
                                                        renderImage(propertyC
                                                            .data
                                                            .value
                                                            ?.propertyPhotos![2]
                                                            .photo),
                                                      ]),
                                                )
                                              : renderImage(propertyC.data.value
                                                  ?.propertyPhotos![1].photo),
                                        ])),
                      SizedBox(height: heightSpace(3)),
                      CustomText(propertyC.data.value?.name,
                          size: 2.4, weight: FontWeight.w500),
                      SizedBox(height: heightSpace(1)),
                      if (propertyC.data.value?.propertyAddress!.addressLine1 !=
                          null) ...[
                        CustomText(
                            propertyC.data.value?.propertyAddress?.addressLine1,
                            size: 2,
                            color: Colors.black.withOpacity(0.6),
                            maxlines: 1),
                      ],
                      SizedBox(height: heightSpace(3)),
                      InkWell(
                          onTap: () =>
                              c.propertyC.selectReserveDates(fromBooking: true),
                          child: renderGrey(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .search
                                  .dates,
                              '${DateFormat.yMMMd().format(propertyC.startDate.value)} - ${DateFormat.yMMMd().format(propertyC.endDate.value)}',
                              "(${c.propertyPrice.value!.dateWithPrice.length} ${Get.find<TranslationHelper>().translations.payment.nights})"
                              // "${propertyC.startDate.value.day} ${months[Get.locale?.languageCode??'en']![propertyC.startDate.value.month - 1]} ${propertyC.startDate.value.year % 100} -"
                              //     "${propertyC.endDate.value.day} ${months[Get.locale?.languageCode??'en']![propertyC.endDate.value.month - 1]} ${propertyC.endDate.value.year % 100}
                              //     (${c.propertyPrice.value!.dateWithPrice.length} ${Get.find<TranslationHelper>().translations.payment.nights})"
                              )),
                      SizedBox(height: heightSpace(2)),
                      // propertyC.isGuestsOpen.isTrue
                      //     ? GuestsSelection(c:propertyC,translations:Get.find<TranslationHelper>().translations)
                      //     : InkWell(
                      //         onTap: propertyC.toggleGuests,
                      //         child: renderGrey(Get.find<TranslationHelper>().translations.listing.guests,
                      //             "${propertyC.adults} ${Get.find<TranslationHelper>().translations.listing.adults}, ${propertyC.children} ${propertyC.children <= 1 ? Get.find<TranslationHelper>().translations.listing.child : Get.find<TranslationHelper>().translations.listing.children}")),
                      // SizedBox(height: heightSpace(2)),
                      DecoratedBox(
                          decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.circular(heightSpace(2)),
                              border:
                                  Border.all(color: const Color(greyBorder))),
                          child: Column(children: [
                            Container(
                                padding: EdgeInsets.all(widthSpace(5)),
                                decoration: BoxDecoration(
                                    border: Border(
                                        bottom:
                                            propertyC.data.value!.bookingType ==
                                                    "instant"
                                                ? const BorderSide(
                                                    color: Color(greyText),
                                                    width: .5)
                                                : BorderSide.none)),
                                child: Column(children: [
                                  if (c.propertyPrice.value!.dateWithPrice.isNotEmpty) ...[
                                    // for(var item in c.propertyPrice.value!.dateWithPrice)
                                    //   serviceFeeView(item.date, item.price),
                                    // const Divider(),
                                    Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          dateDisplay(
                                              Get.find<TranslationHelper>()
                                                      .translations
                                                      .propertySingle
                                                      .checkIn ??
                                                  "Check In",
                                              propertyC.startDate.value),
                                          dateDisplay(
                                              Get.find<TranslationHelper>()
                                                      .translations
                                                      .propertySingle
                                                      .checkOut ??
                                                  "Check Out",
                                              propertyC.endDate.value),
                                        ]),
                                    Divider(height: heightSpace(4)),
                                    serviceFeeView(
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            CustomText("${c.propertyPrice.value!.dateWithPrice.length} ${Get.find<TranslationHelper>().translations.listingBasic.nights}"),
                                            const CustomText(" x "),
                                            CustomText("${c.propertyPrice.value!.propertyPrice} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}"),
                                          ],
                                        ),
                                        // "${c.propertyPrice.value!.propertyPrice} x ${c.propertyPrice.value!.dateWithPrice.length} ${Get.find<TranslationHelper>().translations.listingBasic.nights}",
                                        c.propertyPrice.value!
                                            .totalNightPrice
                                            ?.toStringAsFixed(2)),
                                    if((c.propertyPrice.value?.yousavedWithoutSymbol??0)>0)...[
                                      SizedBox(height: heightSpace(1)),
                                      serviceFeeView(
                                          (propertyC.data.value?.propertyPrice?.weeklyDiscount??0)>1 && (propertyC.data.value?.propertyPrice?.monthlyDiscount??0)>1
                                              ?Get.find<TranslationHelper>().translations.hostDashboard.weeklyMonthlyDiscount
                                              :(propertyC.data.value?.propertyPrice?.weeklyDiscount??0)>1
                                              ?Get.find<TranslationHelper>().translations.propertySingle.weeklyDiscount
                                              :Get.find<TranslationHelper>().translations.propertySingle.monthlyDiscount,
                                          c.propertyPrice.value?.yousavedWithoutSymbol?.toStringAsFixed(2),discount: true),
                                    ]else if((c.propertyPrice.value?.dailyDiscountAmount??0)>0)...[
                                      SizedBox(height: heightSpace(1)),
                                      serviceFeeView(
                                          Get.find<TranslationHelper>().translations.propertySingle.dailyDiscountAmount??'',
                                          c.propertyPrice.value?.dailyDiscountAmount!.toStringAsFixed(2),
                                          discount: true),
                                    ]
                                  ],
                                  if (c.propertyPrice.value!.serviceFee! >
                                      0) ...[
                                    SizedBox(height: heightSpace(1)),
                                    serviceFeeView(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .propertySingle
                                            .serviceFee,
                                        (c.propertyPrice.value
                                                    ?.serviceFeeDiscount ??
                                                c.propertyPrice.value
                                                    ?.serviceFeeWithDiscount ??
                                                0.0)
                                            .toStringAsFixed(2)),
                                  ],
                                  if ((c.propertyPrice.value?.cleaningFee ??
                                          0) >
                                      0) ...[
                                    SizedBox(height: heightSpace(1)),
                                    serviceFeeView(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .propertySingle
                                            .cleaningFee,
                                        c.propertyPrice.value!.cleaningFee)
                                  ],
                                  if ((c.propertyPrice.value?.securityFee ??
                                          0) >
                                      0) ...[
                                    SizedBox(height: heightSpace(1)),
                                    serviceFeeView(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .propertySingle
                                            .securityFee,
                                        c.propertyPrice.value!.securityFee)
                                  ],
                                  if ((c.propertyPrice.value!.ivaTax ?? 0) >
                                      0) ...[
                                    SizedBox(height: heightSpace(1)),
                                    serviceFeeView(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .propertySingle
                                            .ivaTax,
                                        c.propertyPrice.value!.ivaTax)
                                  ],
                                  if (c.discountObject.value != null) ...[
                                    SizedBox(height: heightSpace(1)),
                                    serviceFeeView(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .bookingDetail
                                            .discountAmount,
                                        "${c.discountObject.value?.discountValue}",
                                        displayCurrency: false,
                                        discount: true),
                                  ],
                                  if (propertyC.data.value!.bookingType ==
                                      "instant") ...[
                                    SizedBox(height: heightSpace(1)),
                                    serviceFeeView(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .bookingDetail
                                            .subtotal,
                                        (c.propertyPrice.value!.totalDiscount ??
                                                c.propertyPrice.value!
                                                    .totalWithDiscount)!
                                            .toStringAsFixed(2)),
                                    SizedBox(height: heightSpace(1)),
                                    serviceFeeView(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .sidenav
                                            .wallet,
                                        "${c.paymentDeductFromWallet.value?.toStringAsFixed(2)}",
                                        discount: true,
                                        walletUnSelect:
                                            !c.isEnableWalletPay.value),
                                  ],
                                  Divider(height: heightSpace(3)),
                                  Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .payment
                                                .total,
                                            size: 2.2),
                                        c.isEnableWalletPay.value
                                            ? CustomText(
                                                "${(c.totalAfterWallet.toStringAsFixed(2))} ${c.propertyPrice.value?.currency ?? "SAR"}",
                                                size: 2.2)
                                            : CustomText(
                                                "${(c.propertyPrice.value!.totalDiscount ?? c.propertyPrice.value!.totalWithDiscount)!.toStringAsFixed(2)} ${c.propertyPrice.value?.currency ?? "SAR"}",
                                                size: 2.2),
                                      ]),
                                ])),
                            if (propertyC.data.value!.bookingType ==
                                "instant") ...[
                              Padding(
                                  padding: EdgeInsets.all(widthSpace(5)),
                                  child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        for (PaymentMethod method
                                            in PaymentHelper.paymentMethods)
                                          paymentMethodUI(method),
                                        // paymentMethodUI(PaymentMethods.tabby,const Image(
                                        //   image: AssetImage(
                                        //     'assets/images/tabby-badge.png',package: 'tabby_flutter_inapp_sdk'),
                                        //   height: 23,
                                        // )),
                                        Divider(height: heightSpace(6)),
                                        InkWell(
                                          onTap: AuthHelper.c.myWallet.isEmpty || (AuthHelper.c.myWallet.first.balance ??0) <=0
                                              ? null
                                              : c.enableWalletPay,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                          child: Container(
                                            padding: const EdgeInsets.all(12),
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                                border: Border.all(
                                                    color: const Color(
                                                        greyBorder))),
                                            child: Row(children: [
                                              SizedBox(width: widthSpace(2)),
                                              SvgPicture.asset(
                                                  "assets/wallet.svg",
                                                  width: widthSpace(6),
                                                  color: Colors.black),
                                              SizedBox(width: widthSpace(4)),
                                              Expanded(
                                                child: CustomText(
                                                    "${Get.find<TranslationHelper>().translations.general.walletPay} (${AuthHelper.c.myWallet.first.balance.toString()}-${Get.find<TranslationHelper>().translations.hostDashboard.sar})",
                                                    color: (AuthHelper
                                                                    .c
                                                                    .myWallet
                                                                    .first
                                                                    .balance ??
                                                                0) <=
                                                            0
                                                        ? const Color(greyText)
                                                        : null,
                                                    size: 2.2,
                                                    weight: FontWeight.w500),
                                              ),
                                              CommonSwitch(
                                                selectorSize: 5,
                                                selectedColor:
                                                    const Color(themeColor),
                                                unSelectedColor: Colors.black26,
                                                isSelected:
                                                    c.isEnableWalletPay.value,
                                              ),
                                            ]),
                                          ),
                                        )
                                      ]))
                            ]
                          ])),
                      SizedBox(height: heightSpace(2)),
                      Divider(height: heightSpace(6)),
                      if (propertyC.data.value?.bookingType == "instant" &&
                          propertyC.data.value?.discount == null &&
                          c.selectedPayment.value != 100) ...[
                        if (Get.find<RemoteConfig>().tamayouzEnabled)
                          Container(
                              padding: const EdgeInsets.all(10),
                              margin: EdgeInsets.only(bottom: heightSpace(2)),
                              height: heightSpace(7),
                              decoration: BoxDecoration(
                                  color: Colors.grey[100]!,
                                  border: const Border(
                                      bottom: BorderSide(
                                          color: Color(greyBorder)))),
                              child: Row(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    discountTab(
                                        'c',
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .propertySingle
                                            .couponCode),
                                    discountTab(
                                        't',
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .propertySingle
                                            .tamayouz),
                                  ])),
                        if (c.discountTab == 'c') ...[
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CustomText(
                                  Get.find<TranslationHelper>()
                                      .translations
                                      .propertySingle
                                      .enterCoupon,
                                  size: 2.4,
                                  weight: FontWeight.w500),
                              if (c.discountObject.value != null)
                                CommonButton(
                                    title: Get.find<TranslationHelper>()
                                        .translations
                                        .payment
                                        .removeCode,
                                    isBorder: true,
                                    buttonThemeColor: const Color(warningColor),
                                    onPressed: c.removeCoupon)
                            ],
                          ),
                          SizedBox(height: heightSpace(1.5)),
                          CustomTextField(
                              controller: c.couponController,
                              isEnabled: c.discountObject.value == null,
                              errorText: c.couponError.value,
                              isRoundedBorder: true,
                              textCapitalization: TextCapitalization.characters,
                              suffix: c.discountObject.value != null
                                  ? const Icon(Icons.check_circle,
                                      color: Color(successColor))
                                  : IconButton(
                                      icon: const Icon(Icons.send),
                                      onPressed: c.onApplyCoupon)),
                        ] else ...[
                          CustomText(trans.signUp.confirmYourPhone,
                              size: 2.4, weight: FontWeight.w500),
                          if (c.discountTab == 't1') ...[
                            SizedBox(height: heightSpace(.5)),
                            CustomText(trans.signUp.enterCodeWeSent,
                                color: const Color(greyText)),
                          ],
                          SizedBox(height: heightSpace(3)),
                          Row(
                            children: [
                              Expanded(
                                  child: c.discountTab == 't'
                                      ? CustomTextField(
                                          controller: AuthHelper.c.phone,
                                          hint: '+966 xxxxxxxxx',
                                          inputType: TextInputType.phone,
                                          errorText: c.couponError.value,
                                          isRoundedBorder: true,
                                        )
                                      : Align(
                                          alignment: Alignment.centerLeft,
                                          child: SizedBox(
                                            width: widthSpace(60),
                                            // height: heightSpace(6),
                                            child: PinFieldAutoFill(
                                                codeLength: 4,
                                                controller:
                                                    AuthHelper.c.controller,
                                                decoration: CirclePinDecoration(
                                                    textStyle: TextStyle(
                                                        fontSize:
                                                            heightSpace(2.0),
                                                        color: Colors.black),
                                                    strokeColorBuilder:
                                                        FixedColorBuilder(Colors
                                                            .black
                                                            .withOpacity(0.3)),
                                                    gapSpace: 15),
                                                currentCode: AuthHelper
                                                    .c.messageOtpCode.value,
                                                onCodeSubmitted: (code) {},
                                                onCodeChanged: (code) {
                                                  AuthHelper.c.messageOtpCode
                                                      .value = code!;
                                                  if (code.length == 4) {
                                                    FocusManager
                                                        .instance.primaryFocus
                                                        ?.unfocus();
                                                  }
                                                }),
                                          ),
                                        )),
                              SizedBox(width: widthSpace(7)),
                              c.isLoading.value
                                  ? const SizedBox(
                                      height: 22,
                                      width: 22,
                                      child: CircularProgressIndicator(
                                          strokeWidth: 1.5))
                                  : c.discountObject.value != null
                                      ? const Icon(Icons.check_circle,
                                          color: Color(successColor))
                                      : GestureDetector(
                                          onTap: c.sendTamayouzOtp,
                                          child: SvgPicture.asset(
                                              'assets/sendText.svg',
                                              width: widthSpace(10.5)),
                                        )
                            ],
                          ),
                        ],
                        if (c.discountObject.value != null)
                          CustomText(
                              c.discountObject.value?.discountType == 'campaign'
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .payment
                                      .campaignCouponApplied
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .payment
                                      .couponApplied,
                              size: 1.9,
                              color: const Color(successColor)),
                        Divider(height: heightSpace(6)),
                      ],
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .propertySingle
                              .cancellations,
                          size: 2.4,
                          weight: FontWeight.w500),
                      SizedBox(height: heightSpace(1.5)),
                      Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Image.asset("assets/icons/calendar.png",
                                width: widthSpace(5)),
                            SizedBox(width: widthSpace(5)),
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                                  .translations
                                                  .hostDashboard
                                                  .toJson()[
                                              propertyC
                                                  .data.value?.cancellation] ??
                                          'Flexible',
                                      color: Colors.tealAccent[700]),
                                  SizedBox(height: heightSpace(1)),
                                  SizedBox(
                                      width: widthSpace(70),
                                      child: CustomText(
                                          Get.find<TranslationHelper>()
                                              .translations
                                              .propertySingle
                                              .cancelUptoPrior,
                                          color: Colors.grey[800])),
                                  SizedBox(height: heightSpace(2)),
                                ])
                          ]),
                    ]),
              ),
      ),
      bottomNavigationBar: Container(
        height: heightSpace(12),
        padding: EdgeInsets.all(widthSpace(4.5)),
        decoration: const BoxDecoration(
            border: Border(top: BorderSide(color: Color(greyBorder)))),
        child: Obx(
          () =>
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              CustomText(
                  Get.find<TranslationHelper>().translations.payment.total,
                  color: Colors.grey[800]),
              CustomText(
                  "${(c.propertyPrice.value!.totalDiscount ?? c.propertyPrice.value!.totalWithDiscount)!.toStringAsFixed(2)} ${c.propertyPrice.value?.currency ?? "SAR"}",
                  weight: FontWeight.w500,
                  size: 2.1),
            ]),
            CommonButton(
              title: propertyC.data.value!.bookingType == "instant"
                  ? Get.find<TranslationHelper>()
                      .translations
                      .propertySingle
                      .instantBook
                  : Get.find<TranslationHelper>()
                      .translations
                      .propertySingle
                      .requestBook,
              isLoading:
                  propertyC.isLoadingCurrentPrice.value || c.isLoading.isTrue,
              onPressed: () {
                noOfGuestEventCalled(propertyC);
                c.onReserve();
              },
            )
          ]),
        ),
      ),
    );
  }

  Widget paymentMethodUI(PaymentMethod method) {
    return PaymentMethodCard(
      method: method,
      onTap: () => c.selectPayment(method.id),
      isSelected: c.selectedPayment.value == method.id,
    );
  }

  discountTab(value, title) {
    return Expanded(
      child: InkWell(
        onTap: () {
          if (!c.discountTab.value.contains(value)) {
            c.discountTab.value = value;
            c.couponError.value = null;
            AuthHelper.c.controller.clear();
            if (kReleaseMode ||
                AuthHelper.c.phone.text != userModel.value?.formatted_phone) {
              AuthHelper.c.phone.text = userModel.value?.formatted_phone ?? '';
            }
          }
        },
        child: Container(
            alignment: Alignment.center,
            decoration: c.discountTab.contains(value)
                ? BoxDecoration(
                    color: Colors.white, boxShadow: ViewsCommon.boxShadow)
                : null,
            child: CustomText(title, size: 1.9, weight: FontWeight.w500)),
      ),
    );
  }

  noOfGuestEventCalled(PropertyDetailController c) {
    WebEngagePlugin.trackEvent('Number of guest Entered', {
      "Number of Adults": "${c.adults}",
      "Number of Children": "${c.children}",
      "User": isHost ? "Host" : "Customer"
    });
  }

  renderImage(image) => Expanded(
      child: Image(image: GlobalHelper.buildNetworkImageProvider(url: image??"",),
          fit: BoxFit.cover));
  dateDisplay(String head, DateTime date) {
    return Column(
      children: [
        CustomText(head, color: const Color(greyText)),
        SizedBox(height: heightSpace(.2)),
        CustomText(
            "${date.day} ${months[Get.locale?.languageCode ?? 'en']![date.month - 1]} ${date.year}",
            size: 2.1),
      ],
    );
  }

  serviceFeeView(title, value,
      {bool displayCurrency = true,
      bool discount = false,
      walletUnSelect = false}) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      Expanded(child:
      title is String
          ? CustomText(title, size: 2.1, color: const Color(greyText))
          : title
      ),
      CustomText(
          displayCurrency
              ? "${discount ? '-' : ''} $value ${c.propertyPrice.value?.currency ?? "SAR"}"
              : "${discount ? "-" : ""}$value",
          size: 2.1,
          color: discount ? const Color(warningColor) : const Color(greyText)),
    ]);
  }

  renderGrey(text1, text2, nights) {
    return Container(
      padding: EdgeInsets.all(widthSpace(5)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withOpacity(0.3),
              spreadRadius: 0.2,
              blurRadius: 3,
              offset: const Offset(2, 2)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CustomText(
                text1,
                size: 2.2,
                weight: FontWeight.w500,
              ),
              CustomText(text2, size: 2, color: const Color(greyText)),
            ],
          ),
          CustomText(nights,
              size: 1.9, color: Colors.grey[400], weight: FontWeight.w500)
        ],
      ),
    );
  }

  extrasComponent(image, text) {
    return Row(children: [
      Image.asset(image, width: widthSpace(5)),
      SizedBox(width: widthSpace(5)),
      CustomText(text, color: Colors.grey[800]),
    ]);
  }
}

class PaymentMethodCard extends StatelessWidget {
  const PaymentMethodCard({
    super.key,
    required this.onTap,
    required this.method,
    required this.isSelected,
  });

  final VoidCallback onTap;
  final bool isSelected;
  final PaymentMethod method;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(5),
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(bottom: 6),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: const Color(greyBorder))),
        child: Row(children: [
          Container(
            width: 21,
            height: 21,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                width: isSelected ? 5.7 : 1,
                color: Colors.black54,
              ),
            ),
          ),
          SizedBox(width: widthSpace(2)),
          PaymentMethodIcon(methodId: method.id),
        ]),
      ),
    );
  }
}
