import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl_phone_field/countries.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import '../helperMethods/remote_config.dart';
import '../helperMethods/translation_helper.dart';
import '../helperMethods/authHelper.dart';

class BankAccount extends StatelessWidget {
  final bool fromListing;
  const BankAccount({Key? key, this.fromListing = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: Text(
              Get.find<TranslationHelper>().translations.sidenav.bankAccount)),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(5), vertical: heightSpace(2)),
        child: PopScope(
          onPopInvoked: (didPop) {
            if (didPop) {
              AuthHelper.c.resetBankFields();
            }
          },
          child: Obx(
            () => Form(
              key: AuthHelper.c.bankFormKey,
              child: Container(
                padding: const EdgeInsets.all(18),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: ViewsCommon.boxShadow,
                    color: Colors.white),
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Align(
                          alignment: Alignment.center,
                          child: CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .wallet
                                  .addBankAccount,
                              size: 1.8,
                              textAlign: TextAlign.justify,
                              color: const Color(greyText))),
                      SizedBox(height: heightSpace(3)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .accountPreference
                              .bankName,
                          size: 2.1),
                      SizedBox(height: heightSpace(2)),
                      CustomTextField(
                        isReadOnly: true,
                        controller: AuthHelper.c.bankName,
                        hint: Get.find<TranslationHelper>()
                            .translations
                            .accountPreference
                            .bankName,
                        isRoundedBorder: true,
                        formatter: [LengthLimitingTextInputFormatter(50)],
                        onClick: () {
                          AuthHelper.c.updatedList.clear();
                          AuthHelper.c.updatedList.addAll(banksList);
                          AuthHelper.c.toggleBankOpen();
                          AuthHelper.c.bankFocus.requestFocus();
                        },
                        validator: (value) => value.isEmpty
                            ? Get.find<TranslationHelper>()
                                .translations
                                .jqueryValidation
                                .required
                            : null,
                      ),
                      SizedBox(height: widthSpace(3)),
                      if (AuthHelper.c.isBankOpen.value) ...[
                        SizedBox(
                            height: heightSpace(20),
                            child: ListView.builder(
                              shrinkWrap: false,
                              itemCount: AuthHelper.c.updatedList.length + 1,
                              itemBuilder: (context, index) {
                                int i = index - 1;
                                if (index == 0) {
                                  return SizedBox(
                                    width: widthSpace(85),
                                    height: heightSpace(5),
                                    child: TextField(
                                      controller: AuthHelper.c.filter,
                                      focusNode: AuthHelper.c.bankFocus,
                                      style: TextStyle(
                                          fontWeight: FontWeight.normal,
                                          fontSize: heightSpace(1.5)),
                                      decoration: InputDecoration(
                                          hintText:
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .header
                                                  .searchHere,
                                          contentPadding: const EdgeInsets.only(
                                              left: 20, right: 20),
                                          prefixIcon: const Icon(Icons.search),
                                          border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              borderSide: BorderSide(
                                                  color: Colors.grey[400]!)),
                                          enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                              borderSide: BorderSide(
                                                  color: Colors.grey[400]!))),
                                      onChanged: (value) {
                                        String val = value.trim();
                                        if (val.isEmpty) {
                                          AuthHelper.c.updatedList.clear();
                                          AuthHelper.c.updatedList
                                              .addAll(banksList);
                                        } else {
                                          AuthHelper.c.updatedList.clear();
                                          for (int i = 0;
                                              i < banksList.length;
                                              i++) {
                                            if (banksList[i]
                                                    .name!
                                                    .toLowerCase()
                                                    .contains(
                                                        val.toLowerCase()) ||
                                                banksList[i]
                                                    .arName!
                                                    .toLowerCase()
                                                    .contains(
                                                        val.toLowerCase())) {
                                              AuthHelper.c.updatedList
                                                  .add(banksList[i]);
                                            }
                                          }
                                        }
                                      },
                                    ),
                                  );
                                } else {
                                  bool isChecked =
                                      AuthHelper.c.selectedBank.value?.name ==
                                          AuthHelper.c.updatedList[i].name;
                                  return InkWell(
                                      onTap: () {
                                        AuthHelper.c.bankName.text = Get
                                                    .locale?.languageCode ==
                                                'ar'
                                            ? AuthHelper.c.updatedList[i].arName
                                            : AuthHelper.c.updatedList[i].name;
                                        // final containsBank = AuthHelper.c.updatedList.where((e) => e.isChecked);
                                        // if (containsBank.isNotEmpty) {
                                        //   for (var bank in banksList) {
                                        //     bank.isChecked = false;
                                        //   }
                                        // }
                                        int bicCodeIndex = AuthHelper
                                            .c.updatedList
                                            .indexWhere((e) =>
                                                e.bicCode ==
                                                AuthHelper
                                                    .c.updatedList[i].bicCode);
                                        // AuthHelper.c.updatedList[bicCodeIndex].isChecked = !AuthHelper.c.updatedList[bicCodeIndex].isChecked;
                                        AuthHelper.c.selectedBank.value =
                                            AuthHelper
                                                .c.updatedList[bicCodeIndex];
                                        AuthHelper.c.toggleBankClosed();
                                      },
                                      child: SizedBox(
                                        width: widthSpace(90),
                                        child: CustomText(
                                                Get.locale?.languageCode == 'ar'
                                                    ? AuthHelper
                                                        .c.updatedList[i].arName
                                                    : AuthHelper
                                                        .c.updatedList[i].name,
                                                textOverflow:
                                                    TextOverflow.ellipsis,
                                                color: isChecked
                                                    ? Colors.black87
                                                    : const Color(greyText),
                                                weight: isChecked
                                                    ? FontWeight.w500
                                                    : null)
                                            .paddingAll(
                                          widthSpace(3),
                                        ),
                                      ));
                                }
                              },
                            ))
                      ],
                      SizedBox(height: heightSpace(2)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .accountPreference
                              .bankHolder,
                          size: 2.1),
                      SizedBox(height: heightSpace(2)),
                      CustomTextField(
                        controller: AuthHelper.c.accTitle,
                        hint: Get.find<TranslationHelper>()
                            .translations
                            .accountPreference
                            .bankHolder,
                        isRoundedBorder: true,
                        formatter: [LengthLimitingTextInputFormatter(50)],
                        validator: (value) => value.isEmpty
                            ? Get.find<TranslationHelper>()
                                .translations
                                .jqueryValidation
                                .required
                            : value.trim().length < 3
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .jqueryValidation
                                    .min5
                                    .replaceAll("5", "3")
                                : null,
                      ),
                      SizedBox(height: heightSpace(2)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .accountPreference
                              .bankAccountNum,
                          size: 2.1),
                      SizedBox(height: heightSpace(2)),
                      CustomTextField(
                        controller: AuthHelper.c.accNumber,
                        inputType: TextInputType.number,
                        hint: Get.find<TranslationHelper>()
                            .translations
                            .accountPreference
                            .bankAccountNum,
                        isRoundedBorder: true,
                        formatter: [
                          LengthLimitingTextInputFormatter(50),
                          FilteringTextInputFormatter.digitsOnly
                        ],
                        validator: (value) => value.isEmpty
                            ? Get.find<TranslationHelper>()
                                .translations
                                .jqueryValidation
                                .required
                            : null,
                      ),
                      SizedBox(height: heightSpace(2)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .accountPreference
                              .accountIban,
                          size: 2.1),
                      SizedBox(height: heightSpace(2)),
                      CustomTextField(
                        controller: AuthHelper.c.accIban,
                        hint: Get.find<TranslationHelper>()
                            .translations
                            .accountPreference
                            .accountIban,
                        isRoundedBorder: true,
                        formatter: [LengthLimitingTextInputFormatter(50)],
                        validator: (value) => value.isEmpty
                            ? Get.find<TranslationHelper>()
                                .translations
                                .jqueryValidation
                                .required
                            : null,
                      ),
                      SizedBox(height: heightSpace(2)),
                      // CustomText(Get.find<TranslationHelper>().translations.accountPreference.swiftCode,size: 2.1),
                      // SizedBox(height: heightSpace(2)),
                      // CustomTextField(
                      //   controller: AuthHelper.c.swiftCode,
                      //   hint: Get.find<TranslationHelper>().translations.accountPreference.swiftCode,
                      //   isRoundedBorder: true,
                      //   formatter: [LengthLimitingTextInputFormatter(20)],
                      //   validator: null,
                      // ),
                      // SizedBox(height: heightSpace(2)),

                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .phoneNumber,
                          size: 2.1),
                      SizedBox(height: heightSpace(2)),
                      Container(
                        // height: heightSpace(6),
                        decoration: BoxDecoration(
                            // border: Border.all(color:Color(AuthHelper.c.phoneError.value?warningColor:greyBorder)),
                            borderRadius: BorderRadius.circular(25)),
                        child: Directionality(
                          textDirection: TextDirection.ltr,
                          child: IntlPhoneField(
                              controller: AuthHelper.c.accPhone,
                              initialCountryCode:
                                  AuthHelper.c.phoneCountry.code,
                              countries: Get.find<RemoteConfig>().fieldCountries.isNotEmpty
                                  ?Get.find<RemoteConfig>().fieldCountries
                                  :countries.where((i)=>i.code=='SA').toList(),
                              autovalidateMode: AutovalidateMode.disabled,
                              onCountryChanged: (value) {
                                AuthHelper.c.phoneCountry = value;
                                AuthHelper.c.isButtonEnabled.value = false;
                                AuthHelper.c.accPhone.clear();
                              },
                              decoration: InputDecoration(
                                  contentPadding: const EdgeInsets.all(15),
                                  border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(25),
                                      borderSide: const BorderSide(
                                          color: Color.fromARGB(
                                              255, 243, 243, 243))),
                                  enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(25),
                                      borderSide: const BorderSide(
                                          color: Color.fromARGB(
                                              255, 243, 243, 243))))),
                        ),
                        // Row(
                        //   crossAxisAlignment: CrossAxisAlignment.stretch,
                        //   children: [
                        //     DropdownButtonHideUnderline(
                        //       child: DropdownButton<CountryCode>(
                        //         menuMaxHeight: heightSpace(40),
                        //           borderRadius: const BorderRadius.horizontal(left:Radius.circular(25)),
                        //           items: countryCodes.map((e)=>DropdownMenuItem<CountryCode>(
                        //             value:e,
                        //             child: Text("+${e.phoneCode} ${Get.locale?.languageCode=='ar'?e.shortName:e.shortName}"),
                        //           )).toList(),
                        //
                        //           onChanged: (value) {
                        //             if(value!=AuthHelper.c.phoneCode.value){
                        //               AuthHelper.c.phoneCode.value = value;
                        //               AuthHelper.c.accPhone.clear();
                        //             }
                        //           },hint:Center(child: CustomText(AuthHelper.c.phoneCode.value!=null?"${AuthHelper.c.phoneCode.value!.shortName} (+${AuthHelper.c.phoneCode.value!.phoneCode})":"SA (+966)")).paddingSymmetric(horizontal: widthSpace(3),
                        //       ),icon: const Icon(Icons.keyboard_arrow_down_sharp)),
                        //     ),
                        //     const VerticalDivider(color: Color(greyBorder),indent:6,endIndent: 6,width: 0),
                        //     Expanded(child: FloatingTextField(
                        //       controller: AuthHelper.c.accPhone,
                        //       labelText: "",
                        //       inputType: TextInputType.phone,
                        //       formatters: [
                        //         LengthLimitingTextInputFormatter(AuthHelper.c.phoneCode.value?.limit??9),
                        //         FilteringTextInputFormatter.digitsOnly
                        //       ],
                        //       onChanged:AuthHelper.c.updateButtonState,
                        //       contentPadding: EdgeInsets.symmetric(vertical: widthSpace(2),horizontal: 10),
                        //       textCenter: true,
                        //       textStyle: TextStyle(fontSize: heightSpace(2.1)),
                        //     )),
                        //   ],
                        // ),
                      ),
                      // if(AuthHelper.c.phoneError.value)
                      //   CustomText(Get.find<TranslationHelper>().translations.jqueryValidation.required,size: 1.55,color: const Color(warningColor)).paddingOnly(right:widthSpace(4),top: 6),
                      SizedBox(height: heightSpace(5)),
                      CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .accountPreference
                              .submitBankDetails,
                          onPressed: () => AuthHelper.c
                              .submitBankAcc(fromListing: fromListing),
                          isLoading: AuthHelper.c.isLoading.value),
                    ]),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
