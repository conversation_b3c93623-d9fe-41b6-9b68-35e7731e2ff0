import 'dart:async';
import 'package:darent/analytics/analytics.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/components/yaqeen_verification_dialog.dart';
import 'package:darent/screens/pdf_reciept_viewer.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';
import 'package:get/get.dart';
import '../helperMethods/authHelper.dart';
import '../helperMethods/search_helper.dart';
import '../helperMethods/translation_helper.dart';
import '../utils/constants.dart'
    show DialogKeyword, propertyLicenseLink, userModel;
import '../utils/routes.dart';

class MyFatoorahScreen extends StatefulWidget {
  final String url, screenName;
  final Function? logEvent;
  final bool isDisplayYaqeen;
  const MyFatoorahScreen({
    Key? key,
    required this.url,
    this.screenName = "Payment Method",
    this.logEvent,
    this.isDisplayYaqeen = true,
  }) : super(key: key);

  @override
  State<MyFatoorahScreen> createState() => _MyFatoorahScreenState();
}

class _MyFatoorahScreenState extends State<MyFatoorahScreen> {
  late final WebViewController? _controller;

  @override
  void initState() {
    final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }
    final WebViewController controller =
        WebViewController.fromPlatformCreationParams(params);
    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(NavigationDelegate(
          onNavigationRequest: (NavigationRequest navigation) {
        if (navigation.url.contains('.pdf')) {
          SearchHelper.c.loadPdf(navigation.url).then((pdf) {
            Get.to(() => PdfReceiptViewer(
                file: pdf,
                screenName: Get.find<TranslationHelper>()
                        .translations
                        .hostDashboard
                        .insurance ??
                    ''));
          });
          return NavigationDecision.prevent;
        } else if (false &&
            widget.screenName ==
                Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .insurance) {
          if (!navigation.url.contains('insurance')) {
            launchUrlString(navigation.url);
            return NavigationDecision.prevent;
          } else {
            return NavigationDecision.navigate;
          }
        } else if (navigation.url == propertyLicenseLink) {
          launchUrlString(navigation.url);
          return NavigationDecision.prevent;
        } else {
          onFinished(navigation.url);
          return NavigationDecision.navigate;
        }
      }))
      ..setBackgroundColor(Colors.white)
      ..loadRequest(Uri.parse(widget.url));
    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }
    _controller = controller;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          leading: IconButton(
              icon: const Icon(Icons.chevron_left),
              onPressed: () {
                Get.back();
              }),
          title: Text(widget.screenName),
        ),
        body: WebViewWidget(
          controller: _controller!,
        ));
  }

  onFinished(String url) {
    if (kDebugMode) {
      print("On Finished = $url");
    }
    if (url
        .contains("payment/callback/1") /* || url.contains("status=paid")*/) {
      if (widget.logEvent != null) {
        widget.logEvent!();
      }
      SearchHelper.c.getBookings(refresh: true);
      Timer(const Duration(seconds: 1), () {
        AuthHelper.c.getCards();
        AuthHelper.c.getTransactions();
      });
      if (url.contains("1/darent/c")) {
        Get.until((route) => Get.isBottomSheetOpen == false);
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>().translations.listing.cardHasAdded!,
            displayTime: 1500);
      } else if (url.contains("1/darent/w")) {
        Get.until((route) => !Get.isDialogOpen! && !Get.isBottomSheetOpen!);
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>()
                .translations
                .bookingDetail
                .depositMessage,
            displayTime: 1500);
      } else {
        print('Successful Payment--');

        // Track payment success event with Analytics Manager
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.paymentCompleted,
          eventAttributes: {
            AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
            AnalyticsAttributes.paymentMethod: 'myfatoorah',
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
          },
        );

        Get.offAllNamed(Routes.home);
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>()
                .translations
                .listing
                .paymentSuccessful!,
            displayTime: 1500);
        SearchHelper.c.changeIndex(2);
        displayYaqeen();
        Get.toNamed(Routes.customerServices);
      }
    } else if (url
        .contains("payment/callback/0") /* || url.contains("status=failed")*/) {
      Get.back();
      ViewsCommon.showSnackbar("paymentFailed".tr,
          displayTime: 1500, keyword: DialogKeyword.warning);
      SearchHelper.c.getBookings(refresh: true);
    }
  }

  displayYaqeen({refresh = false}) {
    if (widget.isDisplayYaqeen) {
      if (widget.screenName == "Payment Method" && Get.isDialogOpen == false) {
        Get.dialog(const YaqeenVerificationDialog());
      }
      SearchHelper.c.getBookings(refresh: true);
    }
  }
}
