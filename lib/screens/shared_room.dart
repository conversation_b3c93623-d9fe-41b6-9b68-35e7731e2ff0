import 'package:darent/components/common_button.dart';
import 'package:darent/components/common_checkbox.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SharedRoom extends StatelessWidget {
  const SharedRoom({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.put(PropertyController());
    return Scaffold(
      body: ListView(children: [
        Container(
          height: heightSpace(20),
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.only(left:widthSpace(viewPadding)),
          color:const Color(themeColor),
          child: const CustomText("Shared room ?",size: 2.7,color: Colors.white,),
        ),
        Padding(padding: EdgeInsets.symmetric(horizontal:widthSpace(viewPadding),vertical: heightSpace(5)),child: Obx(()=>Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const CustomText("Place sharing",size: 2.2),
                SizedBox(height: heightSpace(2.5)),
                CommonCheckBox(onPressed: ()=>c.selectPlaceSharing("owner"),isSelected: c.placeSharing.value=="owner", title: "With the place owner"),
                SizedBox(height: heightSpace(1.5)),
                CommonCheckBox(onPressed: ()=>c.selectPlaceSharing("other"),isSelected: c.placeSharing.value=="other", title: "With other guests"),
                SizedBox(height: heightSpace(5)),

                const CustomText("What’s the shared zones",size: 2.2),
                SizedBox(height: heightSpace(2.5)),
                CommonCheckBox(onPressed: ()=>c.selectSharedZone("kitchen"),isSelected: c.sharedZone.value=="kitchen", title: "Kitchen"),
                SizedBox(height: heightSpace(1.5)),
                CommonCheckBox(onPressed: ()=>c.selectSharedZone("bathroom"),isSelected: c.sharedZone.value=="bathroom", title: "Bathroom"),
                SizedBox(height: heightSpace(1.5)),
                CommonCheckBox(onPressed: ()=>c.selectSharedZone("room"),isSelected: c.sharedZone.value=="room", title: "Living room"),
                SizedBox(height: heightSpace(5)),

                const CustomText("Where will the guest sleep",size: 2.2),
                SizedBox(height: heightSpace(2.5)),
                CommonCheckBox(onPressed: ()=>c.selectGuestSleep("private"),isSelected: c.guestSleep.value=="private", title: "Private room"),
                SizedBox(height: heightSpace(1.5)),
                CommonCheckBox(onPressed: ()=>c.selectGuestSleep("shared"),isSelected: c.guestSleep.value=="shared", title: "Shared room"),

          ]),
        ))
      ]),
      bottomNavigationBar: Container(
        // height:heightSpace(12),
        padding: EdgeInsets.symmetric(horizontal:widthSpace(viewPadding),vertical:widthSpace(3)),
        decoration: const BoxDecoration(
          border: Border(top:BorderSide(color: Color(greyBorder)))
        ),child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            InkWell(onTap:Get.back,child: const CustomText("back",weight: FontWeight.w500,underline: true)),
            CommonButton(title: "Next",horizontalPadding: 7, onPressed: (){
              Get.back();
            })
      ]),
      ),
    );
  }
}
