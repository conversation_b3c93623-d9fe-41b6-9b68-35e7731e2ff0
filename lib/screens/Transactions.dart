import 'package:darent/components/custom_text.dart';
import 'package:darent/components/trasnaction_component.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../helperMethods/authHelper.dart';
import '../helperMethods/translation_helper.dart';

class Transactions extends StatelessWidget {
  const Transactions({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
            leading: IconButton(
                onPressed: Get.back, icon: const Icon(Icons.chevron_left)),
            title: Text(Get.find<TranslationHelper>()
                .translations
                .accountTransaction
                .transaction)),
        body: Obx(
          () => AuthHelper.c.myTransactions.isEmpty
              ? Center(
                  child: CustomText(Get.find<TranslationHelper>()
                      .translations
                      .wallet
                      .noTransactionFound))
              : NotificationListener<ScrollEndNotification>(
                  onNotification: (notification) {
                    final metrics = notification.metrics;
                    if (metrics.atEdge) {
                      bool isTop = metrics.pixels == 0;
                      if (!isTop &&
                          AuthHelper.c.transactionsPage <
                              AuthHelper.c.totalPages) {
                        AuthHelper.c.transactionsPage++;
                        AuthHelper.c.getTransactions();
                      }
                    }
                    return true;
                  },
                  child: ListView.separated(
                      padding: EdgeInsets.all(widthSpace(viewPadding)),
                      itemBuilder: (c, i) => TransactionComponent(i: i),
                      separatorBuilder: (context, index) =>
                          SizedBox(height: heightSpace(2)),
                      itemCount: AuthHelper.c.myTransactions.length),
                ),
        ));
  }
}
