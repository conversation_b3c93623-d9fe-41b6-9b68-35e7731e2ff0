import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/calendar_widget.dart';
import 'package:darent/screens/co-host/permission_review.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/hostDashboard_controller.dart';
import '../../helperMethods/translation_helper.dart';

class CoHostPermission extends StatelessWidget {
  const CoHostPermission({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HostDashboardController>(
        id: "detail",
        builder: (c) => Scaffold(
              resizeToAvoidBottomInset: true,
              appBar: AppBar(
                elevation: 0,
                leading: IconButton(
                    onPressed: Get.back,
                    icon: const Icon(Icons.chevron_left, size: 40)),
              ),
              body: SingleChildScrollView(
                padding: EdgeInsets.symmetric(
                    horizontal: widthSpace(viewPadding),
                    vertical: heightSpace(4)),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .coHost
                            .setPermission,
                        size: 3.2),
                    SizedBox(height: heightSpace(4.5)),
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .coHost
                          .alwaysChange,
                      size: 1.9,
                      color: const Color(greyText),
                    ),
                    SizedBox(height: heightSpace(3)),
                    Obx(() => CalendarWidget(
                        title: c.access[0]["title"],
                        subTitle: c.access[0]["subtitle"],
                        isSelected: true,
                        titleFontSize: 2.0,
                        subTitleFontSize: 1.85,
                        function: () {
                          // if(!c.access[index]["isSelected"])
                          // {
                          //   bool containsTrue = c.access.any((e)=>e['isSelected']);
                          //   if(containsTrue){
                          //     c.access.value = c.access.map((e) {
                          //       if(e['isSelected'] == true)
                          //       {
                          //         e['isSelected'] = false;
                          //       }
                          //       return e;
                          //     }).toList();
                          //   }
                          // }
                          // c.access[index]["isSelected"] = !c.access[index]["isSelected"];
                          // c.access.refresh();
                        })),
                    // ListView.separated(
                    //   physics: const NeverScrollableScrollPhysics(),
                    //     shrinkWrap: true,
                    //     itemCount: c.access.length,
                    //     itemBuilder: (context,index){
                    //   return SizedBox(
                    //     child: Obx(() =>CalendarWidget(
                    //         title:  c.access[index]["title"],
                    //         subTitle: c.access[index]["subtitle"],
                    //         isSelected: c.access[index]["isSelected"],
                    //         titleFontSize: 2.0,
                    //         subTitleFontSize: 1.85,
                    //         function: (){
                    //           if(!c.access[index]["isSelected"])
                    //             {
                    //               bool containsTrue = c.access.any((e)=>e['isSelected']);
                    //             if(containsTrue){
                    //               c.access.value = c.access.map((e) {
                    //                 if(e['isSelected'] == true)
                    //                   {
                    //                     e['isSelected'] = false;
                    //                   }
                    //                 return e;
                    //               }).toList();
                    //             }
                    //             }
                    //           c.access[index]["isSelected"] = !c.access[index]["isSelected"];
                    //           c.access.refresh();
                    //         })),
                    //   );
                    // }, separatorBuilder: (BuildContext context, int index) {
                    //     return SizedBox(height: heightSpace(2));
                    // },),
                  ],
                ),
              ),
              bottomNavigationBar: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  stepper(3),
                  Container(
                    height: heightSpace(10),
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.symmetric(
                        horizontal: widthSpace(viewPadding)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                          onPressed: () => Get.back(),
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingDescription
                              .back,
                          isBorder: true,
                        ),
                        // Obx(()=>
                        CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingBasic
                              .next,
                          horizontalPadding: 7,
                          backgroundBg: Colors.black.withOpacity(0.88),
                          // c.access.any((e)=>e['isSelected'])
                          //     ? Colors.black.withOpacity(0.88)
                          //     : Colors.grey.withOpacity(0.5),
                          onPressed: () {
                            // if(c.access.any((e)=>e['isSelected']))
                            //   {
                            Get.to(() => const PermissionReview());
                            // }
                          },
                        )
                        // )
                      ],
                    ),
                  ),
                ],
              ),
            ));
  }
}
