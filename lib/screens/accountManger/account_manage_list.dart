import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/authHelper.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'addAccountManage.dart';

class AccountManageList extends StatelessWidget {
  const AccountManageList({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomText(
          Get.find<TranslationHelper>().translations.sidenav.accountManager,
          weight: FontWeight.w500,
          size: 2.3,
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Get.to(() => const AddAccountManager());
        },
        label:
            Text(Get.find<TranslationHelper>().translations.sidenav.addManager),
        icon: const Icon(Icons.person_add),
        backgroundColor: const Color(themeColor),
      ),
      body: Obx(
        () => Column(
          children: [
            NotificationListener<ScrollEndNotification>(
              onNotification: (notification) {
                final metrics = notification.metrics;
                if (metrics.atEdge) {
                  bool isTop = metrics.pixels == 0;
                  if (!isTop) {
                    if (AuthHelper.c.managerPage <
                        AuthHelper.c.lastManagerPage) {
                      AuthHelper.c.managerLazyLoader.value = true;
                      AuthHelper.c.managerPage++;
                      AuthHelper.c.getManagerList();
                    }
                  }
                }
                return true;
              },
              child: AuthHelper.c.managers.isEmpty
                  ? Center(
                      child: Padding(
                        padding: EdgeInsets.only(top: heightSpace(30)),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.person_4_sharp,
                              size: 80,
                              color: Colors.grey,
                            ),
                            CustomText(Get.find<TranslationHelper>()
                                .translations
                                .sidenav
                                .noManagerAvailable),
                          ],
                        ),
                      ),
                    )
                  : Expanded(
                      child: ListView.separated(
                        itemBuilder: (context, index) => Padding(
                          padding: const EdgeInsets.all(15.0),
                          child: Container(
                            //height: heightSpace(15),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              color: Colors.white,
                              boxShadow: ViewsCommon.boxShadow,
                            ),
                            padding: const EdgeInsets.only(
                                top: 15, left: 12, right: 12, bottom: 15),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    CustomText(
                                      "${Get.find<TranslationHelper>().translations.login.name} :",
                                      size: 2.2,
                                      weight: FontWeight.w500,
                                    ),
                                    SizedBox(
                                      width: widthSpace(3),
                                    ),
                                    Expanded(
                                        child: CustomText(
                                      AuthHelper.c.managers[index].firstName ??
                                          "",
                                      size: 2.1,
                                    )),
                                    Row(
                                      children: [
                                        InkWell(
                                            onTap: () =>
                                                AuthHelper.c.editManager(index),
                                            child: const Icon(
                                              Icons.edit,
                                              color: Colors.grey,
                                              size: 20,
                                            )),
                                        SizedBox(
                                          width: widthSpace(4),
                                        ),
                                        InkWell(
                                            onTap: () => AuthHelper.c
                                                .deleteManagers(index),
                                            child: const Icon(
                                              Icons.delete,
                                              color: Colors.grey,
                                              size: 20,
                                            )),
                                        SizedBox(
                                          width: widthSpace(4),
                                        ),
                                        Icon(
                                          Icons.remove_red_eye,
                                          color: AuthHelper.c.managers[index]
                                                      .status ==
                                                  "Active"
                                              ? Colors.green
                                              : const Color(greyText),
                                          size: 20,
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                                const Divider(),
                                Row(children: [
                                  const CustomText(
                                    "ID : ",
                                    weight: FontWeight.w500,
                                    size: 2.3,
                                  ),
                                  CustomText(AuthHelper.c.managers[index].id
                                      .toString())
                                ]),
                                Row(children: [
                                  CustomText(
                                    "${Get.find<TranslationHelper>().translations.login.email} : ",
                                    weight: FontWeight.w500,
                                    size: 2.3,
                                  ),
                                  Expanded(
                                      child: CustomText(
                                          AuthHelper.c.managers[index].email ??
                                              ""))
                                ])
                              ],
                            ),
                          ),
                        ),
                        separatorBuilder: (context, index) =>
                            SizedBox(height: heightSpace(0.5)),
                        itemCount: AuthHelper.c.managers.length,
                      ),
                    ),
            ),
            if (AuthHelper.c.managerLazyLoader.value)
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 10.0),
                child: CircularProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }
}
