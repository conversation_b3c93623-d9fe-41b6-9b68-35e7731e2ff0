import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../helperMethods/authHelper.dart';
import '../../helperMethods/translation_helper.dart';

class AddAccountManager extends StatelessWidget {
  final int? id;

  const AddAccountManager({Key? key, this.id}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          leading: InkWell(
              onTap: () {
                AuthHelper.c.clearMangValue();
                Get.back();
              },
              child: const Icon(Icons.arrow_back_ios_outlined)),
          title: const CustomText("Assign User Role",
              size: 2.4, weight: FontWeight.w500),
          backgroundColor: Colors.grey.withOpacity(0.3),
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
              vertical: heightSpace(5), horizontal: widthSpace(8)),
          child: Form(
            key: AuthHelper.c.managerFormKey,
            child: Column(
              children: [
                CustomTextField(
                    hint: Get.find<TranslationHelper>()
                        .translations
                        .signUp
                        .firstName,
                    controller: AuthHelper.c.firstNameMang,
                    validator: (String? val) => val!.isEmpty
                        ? Get.find<TranslationHelper>()
                            .translations
                            .jqueryValidation
                            .required
                        : null,
                    isRoundedBorder: true),
                SizedBox(height: heightSpace(3)),
                CustomTextField(
                    hint: Get.find<TranslationHelper>()
                        .translations
                        .signUp
                        .lastName,
                    controller: AuthHelper.c.lastNameMang,
                    validator: (String? val) => val!.isEmpty
                        ? Get.find<TranslationHelper>()
                            .translations
                            .jqueryValidation
                            .required
                        : null,
                    isRoundedBorder: true),
                SizedBox(height: heightSpace(3)),
                TextFormField(
                    controller: AuthHelper.c.phoneMang,
                    validator: (val) => val!.isEmpty
                        ? Get.find<TranslationHelper>()
                            .translations
                            .jqueryValidation
                            .required
                        : null,
                    keyboardType: TextInputType.phone,
                    inputFormatters: [
                      LengthLimitingTextInputFormatter(9),
                    ],
                    //style: TextStyle(color: Color(greyText)),
                    decoration: InputDecoration(
                        hintText: Get.find<TranslationHelper>()
                            .translations
                            .usersProfile
                            .phone,
                        hintStyle: const TextStyle(fontSize: 15),
                        prefixIcon: Container(
                          padding: const EdgeInsets.only(left: 20),
                          child: const Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CustomText("KSA (+966)"),
                              SizedBox(
                                  height: 23,
                                  child:
                                      VerticalDivider(color: Color(greyBorder)))
                            ],
                          ),
                        ),
                        contentPadding: const EdgeInsets.all(10),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: const BorderSide(
                              color: Color(greyBorder), width: 1),
                        ),
                        focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: const BorderSide(
                                color: Color(themeColor), width: 2)),
                        enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(25),
                            borderSide: const BorderSide(
                                color: Color(greyBorder), width: 1)))),
                SizedBox(height: heightSpace(2)),
                CustomTextField(
                    hint:
                        Get.find<TranslationHelper>().translations.signUp.email,
                    inputType: TextInputType.emailAddress,
                    controller: AuthHelper.c.emailMang,
                    validator: (String? val) => val!.isEmpty
                        ? Get.find<TranslationHelper>()
                            .translations
                            .jqueryValidation
                            .required
                        : null,
                    isRoundedBorder: true),
                if (id == null) ...[
                  SizedBox(height: heightSpace(3)),
                  CustomTextField(
                      hint: Get.find<TranslationHelper>()
                          .translations
                          .login
                          .password,
                      controller: AuthHelper.c.passMang,
                      validator: (val) => val.isEmpty
                          ? Get.find<TranslationHelper>()
                              .translations
                              .jqueryValidation
                              .required
                          : null,
                      isRoundedBorder: true),
                ],
                SizedBox(height: heightSpace(3)),
                Obx(
                  () => DropdownButtonHideUnderline(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(horizontal: 15),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: AuthHelper.c.ticketError.value == null
                              ? const Color(greyBorder)
                              : Colors.redAccent[700]!,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: DropdownButton<String>(
                        value: AuthHelper.c.statusMang.value,
                        hint: CustomText(
                          "Status",
                          size: 1.9,
                          color: Colors.black.withOpacity(0.5),
                        ),
                        items: const [
                          DropdownMenuItem<String>(
                            value: "Active",
                            child: CustomText("Active", size: 2.1),
                          ),
                          DropdownMenuItem<String>(
                            value: "Inactive",
                            child: CustomText("Inactive", size: 2.1),
                          ),
                        ],
                        onChanged: (value) {
                          AuthHelper.c.statusMang.value = value!;
                        },
                      ),
                    ),
                  ),
                ),
                SizedBox(height: heightSpace(5.5)),
                SizedBox(
                    width: double.infinity,
                    child: CommonButton(
                        title: "Save",
                        onPressed: () => AuthHelper.c.submitManagerAcc(id),
                        isLoading: AuthHelper.c.isLoading.value))
              ],
            ),
          ),
        ));
  }
}
