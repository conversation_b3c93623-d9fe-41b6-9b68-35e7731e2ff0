import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class AddProperty7 extends StatelessWidget {
  const AddProperty7({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    PropertyController c = Get.find();
    return Scaffold(
      body: ListView(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(6), vertical: heightSpace(2)),
          children: [
            if (!c.isEdit)
              SizedBox(
                height: heightSpace(15),
                child: Directionality(
                  textDirection: TextDirection.ltr,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Image.asset("assets/icons/darent_logo.png",
                          width: widthSpace(30)),
                      CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .saveExit,
                          fontSize: 2,
                          horizontalPadding: 2,
                          onPressed: () => c.submitName(isExit: true),
                          backgroundBg: Colors.white.withOpacity(0.2),
                          buttonThemeColor: Colors.black),
                    ],
                  ),
                ),
              ),
            SizedBox(
                width: widthSpace(60),
                child: CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .chooseTitleForHouse,
                    size: 2.4,
                    maxlines: 2,
                    weight: FontWeight.w500,
                    textAlign: TextAlign.center)),
            SizedBox(height: heightSpace(2)),
            SizedBox(
                width: widthSpace(85),
                child: CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .setTitleNote,
                    size: 2.2,
                    maxlines: 2,
                    color: const Color(greyText),
                    textAlign: TextAlign.center)),
            SizedBox(height: heightSpace(2)),
            Obx(() => Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextFormField(
                          controller: c.nameAr,
                          maxLines: 4,
                          inputFormatters: [
                            LengthLimitingTextInputFormatter(50)
                          ],
                          onChanged: (text) =>
                              c.characterCountArabic.value = text.length,
                          validator: (val) => val!.trim().isEmpty
                              ? Get.find<TranslationHelper>()
                                  .translations
                                  .jqueryValidation
                                  .required
                              : null,
                          decoration: InputDecoration(
                              hintText: "الوصف بالعربية",
                              hintStyle: const TextStyle(
                                  color: Color(greyText), fontSize: 15),
                              errorText: c.titleErrorAr.value,
                              filled: true,
                              fillColor: Colors.white,
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(20),
                                  borderSide: const BorderSide(
                                      color: Colors.grey, width: 1)),
                              enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(20),
                                  borderSide: const BorderSide(
                                      color: Colors.grey, width: 1)))),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ElevatedButton.icon(
                            onPressed: () {
                              c.translate("en", c.nameAr, c.name,
                                  c.characterCountArabic);
                            },
                            icon: const Icon(Icons.g_translate, size: 18),
                            label: Text(Get.find<TranslationHelper>()
                                    .translations
                                    .listing
                                    .translateToEnglish ??
                                "Translate to English"),
                          ),
                          CustomText('${c.characterCountArabic.value}/50')
                        ],
                      ),
                      SizedBox(height: heightSpace(3)),
                      TextFormField(
                          inputFormatters: [
                            LengthLimitingTextInputFormatter(50)
                          ],
                          controller: c.name,
                          onChanged: (text) =>
                              c.characterCount.value = text.length,
                          maxLines: 4,
                          decoration: InputDecoration(
                              hintText: Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .placeName,
                              hintStyle: const TextStyle(
                                  color: Color(greyText), fontSize: 15),
                              filled: true,
                              counterText: "",
                              errorText: c.titleError.value,
                              fillColor: Colors.white,
                              border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(20),
                                  borderSide: const BorderSide(
                                      color: Colors.grey, width: 1)),
                              enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(20),
                                  borderSide: const BorderSide(
                                      color: Colors.grey, width: 1)))),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ElevatedButton.icon(
                              onPressed: () {
                                c.translate(
                                    "ar", c.name, c.nameAr, c.characterCount);
                              },
                              icon: const Icon(
                                Icons.g_translate,
                                size: 18,
                              ),
                              label: Text(Get.find<TranslationHelper>()
                                      .translations
                                      .listing
                                      .translateToArabic ??
                                  "Translate to Arabic")),
                          CustomText('${c.characterCount.value}/50')
                        ],
                      ),
                    ]))
          ]),
      bottomNavigationBar: c.isEdit
          ? null
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                stepper(2, width: 22),
                Container(
                  margin: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: widthSpace(3)),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                            onPressed: () => Get.back(),
                            title: Get.find<TranslationHelper>()
                                .translations
                                .listingDescription
                                .back,
                            isBorder: true),
                        Obx(() => CommonButton(
                            title: c.isEdit
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .usersProfile
                                    .save
                                : Get.find<TranslationHelper>()
                                    .translations
                                    .listingBasic
                                    .next,
                            isLoading: c.isLoading.isTrue,
                            horizontalPadding: 7,
                            onPressed: c.submitName))
                      ]),
                ),
              ],
            ),
    );
  }
}
