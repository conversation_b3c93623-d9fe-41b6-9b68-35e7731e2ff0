import 'dart:io';

import 'package:darent/components/common_button.dart';
import 'package:darent/components/common_checkbox.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/license_verification.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../myfatoorah_screen.dart';

class PublishListing extends StatelessWidget {
  const PublishListing({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    PropertyController c = Get.find();
    return Scaffold(
      body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(viewPadding), vertical: heightSpace(10)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!c.isEdit)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Image.asset("assets/icons/darent_logo.png",
                        width: widthSpace(30)),
                    if (!Get.find<RemoteConfig>().licenseMandatory &&
                        c.forLicense)
                      GestureDetector(
                          onTap: () => Get.back(result: false),
                          child: Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                color: Colors.grey[200],
                                shape: BoxShape.circle,
                              ),
                              child: const Center(child: Icon(Icons.close))))
                  ],
                ),
              if (!c.forLicense) ...[
                SizedBox(height: heightSpace(5)),
                Center(
                    child: CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .listing
                            .reviewYourListing,
                        size: 2.4,
                        weight: FontWeight.w500)),
                SizedBox(height: heightSpace(2)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .reviewPropertyNote,
                    size: 2.2,
                    maxlines: 2,
                    color: const Color(greyText),
                    textAlign: TextAlign.center),
                SizedBox(height: heightSpace(4)),
                Container(
                    width: double.maxFinite,
                    padding: EdgeInsets.all(widthSpace(viewPadding)),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: ViewsCommon.boxShadow),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: c.coverImage.value?.image.contains("/property") == true
                                ? Image(
                                image: GlobalHelper.buildNetworkImageProvider(url: c.coverImage.value?.image ??"",),
                                width: double.maxFinite,
                                height: heightSpace(25),
                                fit: BoxFit.cover,
                                  )
                                : Image.file(File(c.coverImage.value?.image??""),
                                    width: double.maxFinite,
                                    height: heightSpace(25),
                                    fit: BoxFit.cover)),
                        SizedBox(height: heightSpace(1.5)),
                        CustomText(c.name.text,
                            size: 2.2,
                            maxlines: 2,
                            weight: FontWeight.w500,
                            textAlign: TextAlign.center),
                        SizedBox(height: heightSpace(1.5)),
                        CustomText(
                            ViewsCommon.priceControllers['price']?.text ?? ""),
                        SizedBox(height: heightSpace(1.5)),
                        Row(children: [
                          const Icon(Icons.star, size: 20),
                          SizedBox(width: widthSpace(1)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .hostDashboard
                                  .neW,
                              size: 2.1,
                              maxlines: 2,
                              textAlign: TextAlign.center),
                        ]),
                      ],
                    )),
                SizedBox(height: heightSpace(2)),
                CustomText(
                    Get.find<TranslationHelper>().translations.listing.whatNext,
                    size: 2.4,
                    weight: FontWeight.w500),
                SizedBox(height: heightSpace(2)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .startBySettingCalendar,
                    weight: FontWeight.w500),
                SizedBox(height: heightSpace(1)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .chooseAvailableDateForYourProperty,
                    size: 1.9,
                    color: const Color(greyText)),
                SizedBox(height: heightSpace(1.5)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .adjustYourSetting,
                    weight: FontWeight.w500),
                SizedBox(height: heightSpace(1)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .setHouseRulesCancellationPolicyHowGuestCanBook,
                    size: 1.9,
                    color: const Color(greyText)),
                SizedBox(height: heightSpace(3)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .referralTitle,
                    weight: FontWeight.w500),
                SizedBox(height: heightSpace(.7)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .referralSubtitle,
                    size: 1.9,
                    color: const Color(greyText)),
                SizedBox(height: heightSpace(1)),
                SizedBox(
                    width: widthSpace(50),
                    child: Obx(() => TextFormField(
                        controller: c.referralController,
                        keyboardType: TextInputType.emailAddress,
                        maxLength: 10,
                        decoration: InputDecoration(
                            contentPadding: const EdgeInsets.all(10),
                            errorText: c.referralError.value,
                            errorMaxLines: 2,
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: const BorderSide(
                                    color: Color(greyBorder), width: 1)),
                            focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: const BorderSide(
                                    color: Color(themeColor), width: 2)),
                            enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                                borderSide: const BorderSide(
                                    color: Color(greyBorder), width: 1))))))
              ],
              SizedBox(height: heightSpace(3)),
              Obx(
                () => InkWell(
                  onTap: () {
                    if (!c.documentUploaded.value) {
                      ViewsCommon.showModalBottom(
                          DraggableScrollableSheet(
                              minChildSize: .80,
                              initialChildSize: .80,
                              expand: false,
                              builder: (c, s) => LicenseVerification()),
                          then:(_)=> c.clearLicenseFields);
                    }
                  },
                  child: Row(children: [
                    Expanded(
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .confirmDetailAndPost,
                              weight: FontWeight.w500),
                          SizedBox(height: heightSpace(.7)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .confirmIdentityAndRegistrationWithLocalGovernment,
                              size: 1.9,
                              color: const Color(greyText)),
                          SizedBox(height: heightSpace(.7)),
                          CustomText(
                              c.documentUploaded.value
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .account
                                      .verified
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .hostDashboard
                                      .getVerified,
                              size: 1.9,
                              underline: !c.documentUploaded.value,
                              color: c.documentUploaded.value
                                  ? const Color(successColor)
                                  : null),
                        ])),
                    Icon(
                        c.documentUploaded.value
                            ? Icons.check
                            : Icons.chevron_right,
                        color: c.documentUploaded.value
                            ? const Color(successColor)
                            : null)
                  ]),
                ),
              ),
              if (!c.forLicense) ...[
                SizedBox(height: heightSpace(3)),
                Align(
                  alignment: Get.locale?.languageCode == 'ar'
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: Obx(
                    () => CommonCheckBox(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        onPressed: c.agreeHost,
                        isSelected: c.hostAgreed.value,
                        bottomMargin: 0.0,
                        title: Container(
                          color: Colors.white,
                          child: Row(children: [
                            CustomText(
                                "${Get.find<TranslationHelper>().translations.listing.iAgree} ",
                                color: const Color(greyText)),
                            InkWell(
                                onTap: () {
                                  Get.to(() => MyFatoorahScreen(
                                      url:
                                          "$baseUrl/${Get.locale?.languageCode ?? "en"}/property/hostAgreement?lang=${Get.locale?.languageCode ?? "en"}&mobile=1",
                                      screenName: Get.find<TranslationHelper>()
                                          .translations
                                          .listing
                                          .hostAgreement!));
                                },
                                child: CustomText(
                                    Get.find<TranslationHelper>()
                                        .translations
                                        .listing
                                        .hostAgreement,
                                    color: Colors.blueAccent,
                                    underline: true))
                          ]),
                        )),
                  ),
                )
              ]
            ],
          )),
      bottomNavigationBar: c.forLicense || c.isEdit
          ? null
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                stepper(3, width: 33),
                Container(
                  margin: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: widthSpace(3)),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                            onPressed: () {
                              ViewsCommon.clearPriceControllers();
                              Get.back();
                            },
                            title: Get.find<TranslationHelper>()
                                .translations
                                .listingDescription
                                .back,
                            isBorder: true),
                        Obx(
                          () => CommonButton(
                              title: c.forLicense
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .listingCalendar
                                      .submit
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .listing
                                      .publishYourListing,
                              isLoading: c.isLoading.isTrue,
                              horizontalPadding: 7,
                              onPressed: c.publishListing),
                        )
                      ]),
                ),
              ],
            ),
    );
  }
}
