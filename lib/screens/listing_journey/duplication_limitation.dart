import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/listing_helper/listing_helper.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../helperMethods/globalHelpers.dart';

class DuplicationLimitation extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
          width: double.maxFinite,
          height: heightSpace(9),
          alignment: Alignment.centerLeft,
          decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: Color(greyBorder)))),
          child: IconButton(
              onPressed: () => Navigator.pop(context),
              padding: const EdgeInsets.all(25),
              icon: const Icon(Icons.close))),
      Padding(
          padding: EdgeInsets.all(widthSpace(viewPadding)),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .hostDashboard
                    .finishYourListing,
                size: 2.1,
                weight: FontWeight.w500),
            SizedBox(height: heightSpace(2)),
            if ((ListingHelper.c.inProgressDuplicationPagination?.totalPages ??
                    1) >=
                100)
              Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                IconButton(
                    onPressed: ListingHelper.c.inProgressDuplicationPagination
                                ?.currentPage ==
                            1
                        ? null
                        : () {},
                    iconSize: 21,
                    icon: const Icon(Icons.chevron_left)),
                CustomText(
                    '${ListingHelper.c.inProgressDuplicationPagination?.currentPage}/${ListingHelper.c.inProgressDuplicationPagination?.totalPages}',
                    size: 1.8,
                    color: Colors.black26,
                    weight: FontWeight.w500),
                IconButton(
                    onPressed: ListingHelper.c.inProgressDuplicationPagination
                                ?.currentPage ==
                            ListingHelper
                                .c.inProgressDuplicationPagination?.totalPages
                        ? null
                        : () {},
                    iconSize: 21,
                    icon: const Icon(Icons.chevron_right))
              ]),
            ConstrainedBox(
              constraints: BoxConstraints(maxHeight: heightSpace(45)),
              child: ListView.separated(
                  itemBuilder: _renderItem,
                  separatorBuilder: (c, i) =>
                      SizedBox(height: heightSpace(1.5)),
                  itemCount: ListingHelper.c.inProgressDuplication.length),
            )
          ])),
      Padding(
        padding: EdgeInsets.symmetric(horizontal: widthSpace(viewPadding)),
        child: CustomText(
            Get.find<TranslationHelper>()
                .translations
                .hostDashboard
                .startNewListing,
            size: 2.1,
            weight: FontWeight.w500),
      ),
      _newListingButton(context)
    ]);
  }

  Widget _newListingButton(context) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
        //We will verify the listing helper response and, if the condition is met, proceed to admin management.
        if(ListingHelper.c.isPropertyManageByDarentEnabled.value){
          ListingHelper.goToDarentManagement();
        }
        else{
          ListingHelper.gotoNewListing();
        }
      },
      child: Padding(
        padding: EdgeInsets.all(widthSpace(viewPadding)),
        child:
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          SvgPicture.asset('assets/new_add.svg'),
          SizedBox(width: widthSpace(3)),
          CustomText(Get.find<TranslationHelper>()
              .translations
              .hostListing
              .createListing),
          const Spacer(),
          const Icon(Icons.chevron_right)
        ]),
      ),
    );
  }

  Widget _renderItem(c, i) {
    final item = ListingHelper.c.inProgressDuplication[i];
    return InkWell(
      onTap: () async {
        Navigator.pop(c);
        ListingHelper.completeListing(
            await ListingHelper.c.getDwellingDetail(item.code));
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(4), vertical: widthSpace(7)),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(greyBorder))),
          child: Row(children: [
            SizedBox(
              width: widthSpace(10),
              height: widthSpace(12),
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: GlobalHelper.resolveImageUrl(item.coverPhoto ??"").isNotEmpty
                      ? Image(
                      image: GlobalHelper.buildNetworkImageProvider(
                        url: item.coverPhoto ??"",),
                      )
                      : Container(
                    color: Colors.grey[200],
                    child: const Icon(Icons.home),
                  )
              ),
            ),
            SizedBox(width: widthSpace(4)),
            Expanded(
                child: CustomText(
                    (item.name ?? '').isEmpty
                        ? '${Get.find<TranslationHelper>().translations.hostListing.houseListingStartedOn} ${item.createdAt}'
                        : item.name,
                    weight: FontWeight.w500,
                    maxlines: 2)),
            SizedBox(width: widthSpace(5)),
            Icon(Icons.keyboard_arrow_right)
          ])),
    );
  }
}
