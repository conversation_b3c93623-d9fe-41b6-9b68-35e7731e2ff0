import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../helperMethods/search_helper.dart';

class AddProperty5 extends StatelessWidget {
  const AddProperty5({super.key});

  @override
  Widget build(BuildContext context) {
    final c = Get.find<PropertyController>();
    return Scaffold(
      body:
          ListView(padding: EdgeInsets.all(widthSpace(viewPadding)), children: [
        if (!c.isEdit) ...[
          SizedBox(
            height: heightSpace(18),
            child: Directionality(
              textDirection: TextDirection.ltr,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Image.asset(
                    "assets/icons/darent_logo.png",
                    width: widthSpace(30),
                  ),
                  CommonButton(
                      title: Get.find<TranslationHelper>()
                          .translations
                          .listing
                          .saveExit,
                      fontSize: 2,
                      horizontalPadding: 2,
                      onPressed: () => c.submitAmenities(isExit: true),
                      backgroundBg: Colors.white.withOpacity(0.2),
                      buttonThemeColor: Colors.black),
                ],
              ),
            ),
          ),
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .listing
                  .tellGuestsAboutFeatureInYourSpace,
              size: 2.4,
              maxlines: 2,
              weight: FontWeight.w500),
          SizedBox(height: heightSpace(2)),
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .listing
                  .canAddMoreFeatureAfterAdPosted,
              color: const Color(greyText),
              size: 2.2),
        ], // SizedBox(height: heightSpace(5)),
        Obx(
          () =>
              Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
            Center(
              child: GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount:
                      SearchHelper.c.filters.value?.amenities.length ?? 0,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 1.8,
                      mainAxisSpacing: widthSpace(4),
                      crossAxisSpacing: widthSpace(4)),
                  itemBuilder: (context, index) => InkWell(
                        onTap: () => SearchHelper.c.checkFilter(
                            SearchHelper.c.filters.value?.amenities[index]),
                        child: Container(
                            width: widthSpace(42),
                            height: widthSpace(23),
                            decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.circular(heightSpace(1.5)),
                                border: Border.all(
                                    color: SearchHelper.c.filters.value
                                                ?.amenities[index].isChecked ==
                                            true
                                        ? Colors.black
                                        : const Color(greyText))),
                            child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  GlobalHelper.buildNetworkSvgWidget(
                                    url: "/${SearchHelper.c.filters.value?.amenities[index].iconImage}",
                                    height: widthSpace(7),
                                    myColor: SearchHelper.c.filters.value?.amenities[index].isChecked == true
                                        ? Colors.black
                                        : const Color(greyText),
                                    defaultOption: Image.asset("assets/icons/infant.png",
                                        color: SearchHelper.c.filters.value?.amenities[index].isChecked == true
                                            ? Colors.black
                                            : const Color(greyText)),),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 3, right: 3, top: 5),
                                    child: CustomText(
                                        Get.locale?.languageCode == "ar"
                                            ? SearchHelper.c.filters.value
                                                ?.amenities[index].titleAr
                                            : SearchHelper.c.filters.value
                                                ?.amenities[index].title,
                                        size: 1.75,
                                        textAlign: TextAlign.center,
                                        color: SearchHelper
                                                    .c
                                                    .filters
                                                    .value
                                                    ?.amenities[index]
                                                    .isChecked ==
                                                true
                                            ? Colors.black
                                            : const Color(greyText)),
                                  )
                                ])),
                      )),
            ),
            SizedBox(height: heightSpace(6)),
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .listing
                    .haveAnySafetyItem,
                size: 2.5),
            Center(
              child: GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount:
                      SearchHelper.c.filters.value?.safetyAmenities.length ?? 0,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 1.8,
                      mainAxisSpacing: widthSpace(4),
                      crossAxisSpacing: widthSpace(4)),
                  itemBuilder: (context, index) => InkWell(
                        onTap: () => SearchHelper.c.checkFilter(SearchHelper
                            .c.filters.value?.safetyAmenities[index]),
                        child: Container(
                            width: widthSpace(27),
                            height: widthSpace(25),
                            decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.circular(heightSpace(1.3)),
                                border: Border.all(
                                    color: SearchHelper
                                                .c
                                                .filters
                                                .value
                                                ?.safetyAmenities[index]
                                                .isChecked ==
                                            true
                                        ? Colors.black
                                        : const Color(greyText))),
                            child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  GlobalHelper.buildNetworkSvgWidget(
                                    url:"/${SearchHelper.c.filters.value?.safetyAmenities[index].iconImage}",
                                    height: widthSpace(7),
                                    myColor: SearchHelper.c.filters.value?.safetyAmenities[index].isChecked == true
                                        ? Colors.black
                                        : const Color(greyText),
                                    defaultOption: Image.asset("assets/icons/infant.png",
                                        color: SearchHelper.c.filters.value?.safetyAmenities[index].isChecked == true
                                            ? Colors.black
                                            : const Color(greyText)),),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 3, right: 3, top: 7),
                                    child: CustomText(
                                        Get.locale?.languageCode == "ar"
                                            ? SearchHelper.c.filters.value
                                                ?.safetyAmenities[index].titleAr
                                            : SearchHelper.c.filters.value
                                                ?.safetyAmenities[index].title,
                                        size: 1.6,
                                        textAlign: TextAlign.center,
                                        color: SearchHelper
                                                    .c
                                                    .filters
                                                    .value
                                                    ?.safetyAmenities[index]
                                                    .isChecked ==
                                                true
                                            ? Colors.black
                                            : const Color(greyText)),
                                  )
                                ])),
                      )),
            ),
            SizedBox(height: heightSpace(6)),
            CustomText(
                Get.find<TranslationHelper>().translations.payment.houseRule,
                size: 2.4,
                weight: FontWeight.w500),
            // const SizedBox(height: 5),
            // CustomText("markAllowed".tr,size: 1.8, color: const Color(greyText)),
            Center(
              child: GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: SearchHelper.c.filters.value!.houseRules.length +
                      1 +
                      SearchHelper.c.customHouseRules.length,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 1.8,
                      mainAxisSpacing: widthSpace(4),
                      crossAxisSpacing: widthSpace(4)),
                  itemBuilder: (context, index) {
                    if (index <
                        SearchHelper.c.filters.value!.houseRules.length) {
                      return InkWell(
                        onTap: () => SearchHelper.c.checkFilter(
                            SearchHelper.c.filters.value?.houseRules[index]),
                        child: Container(
                            width: widthSpace(27),
                            height: widthSpace(25),
                            decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.circular(heightSpace(1.3)),
                                border: Border.all(
                                    color: SearchHelper.c.filters.value
                                                ?.houseRules[index].isChecked ==
                                            true
                                        ? Colors.black
                                        : const Color(greyText))),
                            child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  GlobalHelper.buildNetworkSvgWidget(
                                    url:"/${SearchHelper.c.filters.value?.houseRules[index].iconImage}",
                                    myColor: SearchHelper.c.filters.value?.houseRules[index].isChecked ==
                                        true
                                        ? Colors.black
                                        : const Color(greyText),
                                    height: widthSpace(7),
                                    defaultOption: Image.asset("assets/icons/infant.png",
                                        color: SearchHelper.c.filters.value?.houseRules[index].isChecked == true
                                            ? Colors.black
                                            : const Color(greyText)),),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 3, right: 3, top: 7),
                                    child: CustomText(
                                        Get.locale?.languageCode == "ar"
                                            ? SearchHelper.c.filters.value
                                                ?.houseRules[index].titleAr
                                            : SearchHelper.c.filters.value
                                                ?.houseRules[index].title,
                                        size: 1.6,
                                        textAlign: TextAlign.center,
                                        color: SearchHelper
                                                    .c
                                                    .filters
                                                    .value
                                                    ?.houseRules[index]
                                                    .isChecked ==
                                                true
                                            ? Colors.black
                                            : const Color(greyText)),
                                  )
                                ])),
                      );
                    } else if (index <
                        SearchHelper.c.filters.value!.houseRules.length +
                            SearchHelper.c.customHouseRules.length) {
                      int myIndex = index -
                          SearchHelper.c.filters.value!.houseRules.length;
                      return InkWell(
                        onTap: () {
                          SearchHelper.c.removeMyCustomHouseRule(myIndex);
                        },
                        child: Container(
                            width: widthSpace(27),
                            height: widthSpace(25),
                            decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.circular(heightSpace(1.3)),
                                border: Border.all(color: Colors.black)),
                            child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Align(
                                    alignment: Alignment.topRight,
                                    child: IconButton(
                                        padding: const EdgeInsets.only(
                                            bottom: 18, left: 40),
                                        onPressed: () => SearchHelper.c
                                            .removeMyCustomHouseRule(myIndex),
                                        icon: const Icon(Icons.cancel_outlined,
                                            size: 20,
                                            shadows: <Shadow>[
                                              Shadow(
                                                  color: Colors.white,
                                                  blurRadius: 15.0)
                                            ])),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 3, right: 3, top: 2),
                                    child: CustomText(
                                        SearchHelper
                                            .c.customHouseRules[myIndex].title,
                                        size: 1.8,
                                        textAlign: TextAlign.center,
                                        color: Colors.black),
                                  ),
                                ])),
                      );
                    } else {
                      return InkWell(
                        onTap: SearchHelper.c.addMyCustomHouseRule,
                        child: Container(
                            width: widthSpace(27),
                            height: widthSpace(25),
                            decoration: BoxDecoration(
                                borderRadius:
                                    BorderRadius.circular(heightSpace(1.3)),
                                border: Border.all(color: Colors.black)),
                            child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.add,
                                    color: Colors.black,
                                    size: widthSpace(7),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 3, right: 3, top: 7),
                                    child: CustomText(
                                        Get.find<TranslationHelper>()
                                                .translations
                                                .hostDashboard
                                                .addCustomRule ??
                                            "Add More Rule",
                                        size: 1.6,
                                        textAlign: TextAlign.center,
                                        color: Colors.black),
                                  )
                                ])),
                      );
                    }
                  }),
            ),
          ]),
        )
      ]),
      bottomNavigationBar: c.isEdit
          ? null
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                stepper(2, width: 5.5),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: widthSpace(3)),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                            onPressed: Get.back,
                            title: Get.find<TranslationHelper>()
                                .translations
                                .listingDescription
                                .back,
                            isBorder: true),
                        Obx(() => CommonButton(
                            title: c.isEdit
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .usersProfile
                                    .save
                                : Get.find<TranslationHelper>()
                                    .translations
                                    .listingBasic
                                    .next,
                            isLoading: c.isLoading.isTrue,
                            horizontalPadding: 7,
                            onPressed: c.submitAmenities))
                      ]),
                ),
              ],
            ),
    );
  }
}
