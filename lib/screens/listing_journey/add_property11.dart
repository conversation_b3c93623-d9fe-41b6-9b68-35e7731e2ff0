import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class AddProperty11 extends StatelessWidget {
  const AddProperty11({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    PropertyController c = Get.find();
    return Scaffold(
      body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(viewPadding), vertical: heightSpace(10)),
          child: Column(
            children: [
              if (!c.isEdit) ...[
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Image.asset("assets/icons/darent_logo.png",
                          width: widthSpace(30)),
                      CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .saveExit,
                          fontSize: 2,
                          horizontalPadding: 2,
                          onPressed: () => c.submitMinMax(isExit: true),
                          backgroundBg: Colors.white.withOpacity(0.2),
                          buttonThemeColor: Colors.black),
                    ],
                  ),
                ),
                SizedBox(height: heightSpace(5)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listingSidebar
                        .checkinCheckout,
                    size: 2.6,
                    maxlines: 2,
                    weight: FontWeight.w500,
                    textAlign: TextAlign.center),
                // SizedBox(height: heightSpace(2)),
                // const CustomText("You can change it later at any time.", size: 2.2,maxlines: 2, color: Color(greyText),textAlign: TextAlign.center),
                SizedBox(height: heightSpace(4)),
              ],
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .listingBasic
                      .maximumNights,
                  size: 2.5,
                  weight: FontWeight.w500),
              SizedBox(height: heightSpace(2.5)),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                InkWell(
                  onTap: () => c.minusPrice(c.maxNights),
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        border: Border.all(), shape: BoxShape.circle),
                    child: Icon(Icons.remove, size: widthSpace(6)),
                  ),
                ),
                Container(
                    width: widthSpace(60),
                    height: heightSpace(12),
                    padding: EdgeInsets.only(left: widthSpace(5)),
                    decoration: BoxDecoration(
                        border: Border.all(),
                        borderRadius: BorderRadius.circular(heightSpace(3))),
                    child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          SizedBox(width: widthSpace(3)),
                          SizedBox(
                              width: widthSpace(20),
                              child: TextField(
                                  controller: c.maxNights,
                                  textAlign: TextAlign.right,
                                  style: TextStyle(fontSize: heightSpace(4.5)),
                                  decoration: const InputDecoration(
                                      border: InputBorder.none),
                                  keyboardType: TextInputType.number,
                                  inputFormatters: [
                                    LengthLimitingTextInputFormatter(4),
                                    FilteringTextInputFormatter.digitsOnly
                                  ])),
                          SizedBox(width: widthSpace(2)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .listingBasic
                                  .nights,
                              size: 2.8,
                              color: const Color(greyText)),
                        ])),
                InkWell(
                  onTap: () => c.plusPrice(c.maxNights),
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        border: Border.all(), shape: BoxShape.circle),
                    child: Icon(Icons.add, size: widthSpace(6)),
                  ),
                ),
              ]),
              SizedBox(height: heightSpace(2)),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .listingBasic
                      .minimumNights,
                  size: 2.5,
                  weight: FontWeight.w500),
              SizedBox(height: heightSpace(2)),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                InkWell(
                  onTap: () => c.minusPrice(c.minNights),
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        border: Border.all(), shape: BoxShape.circle),
                    child: Icon(Icons.remove, size: widthSpace(6)),
                  ),
                ),
                Container(
                    width: widthSpace(60),
                    height: heightSpace(12),
                    padding: EdgeInsets.only(left: widthSpace(5)),
                    decoration: BoxDecoration(
                        border: Border.all(color: const Color(greyText)),
                        borderRadius: BorderRadius.circular(heightSpace(3))),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        SizedBox(width: widthSpace(3)),
                        SizedBox(
                          width: widthSpace(20),
                          child: TextField(
                            controller: c.minNights,
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              fontSize: heightSpace(4.5),
                            ),
                            decoration: const InputDecoration(
                              border: InputBorder.none,
                            ),
                            keyboardType: TextInputType.number,
                            inputFormatters: [
                              LengthLimitingTextInputFormatter(4),
                              FilteringTextInputFormatter.digitsOnly
                            ],
                          ),
                        ),
                        SizedBox(width: widthSpace(2)),
                        // Obx(() =>
                        //     CustomText("${c.minNights}", size: 4.5)),
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .propertySingle
                                .nights,
                            size: 2.8,
                            color: const Color(greyText)),
                      ],
                    )),
                InkWell(
                  onTap: () => c.plusPrice(c.minNights),
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        border: Border.all(), shape: BoxShape.circle),
                    child: Icon(Icons.add, size: widthSpace(6)),
                  ),
                ),
              ]),
              SizedBox(height: heightSpace(2.5)),
              CustomText(
                  Get.find<TranslationHelper>().translations.header.checkIn,
                  size: 2.3),
              SizedBox(height: heightSpace(1.5)),
              InkWell(
                  onTap: () async {
                    showTimePicker(c.checkIn);
                  },
                  child: Container(
                      width: double.maxFinite,
                      padding: EdgeInsets.all(widthSpace(3)),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(color: const Color(greyBorder)),
                      ),
                      child: Obx(() => CustomText(
                          // "${GlobalHelper.twoNumberFormat(GlobalHelper.to12Hours(c.checkIn.value.hour))}:${GlobalHelper.twoNumberFormat(c.checkIn.value.minute)} ${c.checkIn.value.period.name.toUpperCase()}",
                          "${GlobalHelper.twoNumberFormat(c.checkIn.value.inHours)}:${GlobalHelper.twoNumberFormat(c.checkIn.value.inMinutes % 60)}",
                          textAlign: TextAlign.center)))),
              SizedBox(height: heightSpace(2.5)),
              CustomText(
                  Get.find<TranslationHelper>().translations.header.checkOut,
                  size: 2.3),
              SizedBox(height: heightSpace(1.5)),
              InkWell(
                  onTap: () async {
                    showTimePicker(c.checkOut);
                    // final TimeOfDay? picked_s = await showTimePicker(
                    //     context: context,
                    //     initialTime: c.checkOut.value);
                    //
                    // if (picked_s != null && picked_s != c.checkOut.value ) {
                    //   c.checkOut.value = picked_s;
                    // }
                  },
                  child: Container(
                      width: double.maxFinite,
                      padding: EdgeInsets.symmetric(vertical: widthSpace(3)),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(color: const Color(greyBorder)),
                      ),
                      child: Obx(() => CustomText(
                          // "${GlobalHelper.twoNumberFormat(GlobalHelper.to12Hours(c.checkOut.value.hour))}:${GlobalHelper.twoNumberFormat(c.checkOut.value.minute)} ${c.checkOut.value.period.name.toUpperCase()}",
                          "${GlobalHelper.twoNumberFormat(c.checkOut.value.inHours)}:${GlobalHelper.twoNumberFormat(c.checkOut.value.inMinutes % 60)}",
                          textAlign: TextAlign.center))))
            ],
          )),
      bottomNavigationBar: c.isEdit
          ? null
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                stepper(3, width: 19.8),
                Container(
                  margin: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: widthSpace(3)),
                  child: Obx(
                    () => Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonButton(
                              onPressed: () {
                                Get.back();
                              },
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .listingDescription
                                  .back,
                              isBorder: true),
                          CommonButton(
                              title: c.isEdit
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .usersProfile
                                      .save
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .listingBasic
                                      .next,
                              isLoading: c.isLoading.isTrue,
                              horizontalPadding: 7,
                              onPressed: c.submitMinMax)
                        ]),
                  ),
                ),
              ],
            ),
    );
  }

  showTimePicker(Rx<Duration> time) {
    showCupertinoModalPopup(
        context: Get.context!,
        builder: (context) {
          return Container(
            height: heightSpace(40),
            color: Colors.white,
            child: CupertinoTheme(
              data: const CupertinoThemeData(
                  textTheme: CupertinoTextThemeData(
                pickerTextStyle: TextStyle(
                    fontSize: 14,
                    fontFamily: 'DINNextLTArabic',
                    color: Colors.black87),
              )),
              child: CupertinoTimerPicker(
                  mode: CupertinoTimerPickerMode.hm,
                  initialTimerDuration: time.value,
                  itemExtent: 42,
                  onTimerDurationChanged: (value) => time.value = value),
            ),
          );
        });
  }
}
