import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class ConfirmAddress extends StatelessWidget {
  const ConfirmAddress({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.find<PropertyController>();
    return Scaffold(
      body: Form(
        key: c.locationForm,
        child: ListView(
            padding: EdgeInsets.symmetric(horizontal: widthSpace(5.5)),
            children: [
              SizedBox(height: heightSpace(8)),
              if (!c.isEdit)
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Image.asset("assets/icons/darent_logo.png",
                            width: widthSpace(30)),
                        CommonButton(
                            title: Get.find<TranslationHelper>()
                                .translations
                                .listing
                                .saveExit,
                            fontSize: 2,
                            horizontalPadding: 2,
                            onPressed: () => c.submitLocation(isExit: true),
                            isBorder: true),
                      ]),
                ),
              SizedBox(height: heightSpace(2)),
              CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .listing
                    .confirmYourLocation,
                size: 2.5,
                maxlines: 1,
                weight: FontWeight.w500,
                textAlign: TextAlign.left,
              ),
              SizedBox(height: heightSpace(1)),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .listing
                      .invisibleAddressStepNote,
                  size: 2.1,
                  color: const Color(greyText),
                  maxlines: 2),
              SizedBox(height: heightSpace(5)),
              CustomTextField(
                controller: c.country,
                hint: Get.find<TranslationHelper>()
                    .translations
                    .listingLocation
                    .country,
                isRoundedBorder: true,
                validator: (val) => val.isEmpty
                    ? Get.find<TranslationHelper>()
                        .translations
                        .jqueryValidation
                        .required
                    : null,
              ),
              SizedBox(height: heightSpace(1.5)),
              CustomTextField(
                controller: c.address1,
                hint: Get.find<TranslationHelper>()
                    .translations
                    .listingLocation
                    .addressLine1,
                isRoundedBorder: true,
                formatter: [LengthLimitingTextInputFormatter(250)],
                validator: (val) => val.isEmpty
                    ? Get.find<TranslationHelper>()
                        .translations
                        .jqueryValidation
                        .required
                    : null,
              ),
              SizedBox(height: heightSpace(1.5)),
              CustomTextField(
                  controller: c.address2,
                  hint:
                      "${Get.find<TranslationHelper>().translations.listingLocation.addressLine2} (${Get.find<TranslationHelper>().translations.general.ifAny})",
                  isRoundedBorder: true),
              SizedBox(height: heightSpace(1.5)),
              CustomTextField(
                  controller: c.city,
                  hint: Get.find<TranslationHelper>()
                      .translations
                      .listingLocation
                      .cityTownDistrict,
                  isRoundedBorder: true,
                  validator: (val) => val.isEmpty
                      ? Get.find<TranslationHelper>()
                          .translations
                          .jqueryValidation
                          .required
                      : null),
              SizedBox(height: heightSpace(1.5)),
              CustomTextField(
                  controller: c.state,
                  hint: Get.find<TranslationHelper>()
                      .translations
                      .listingLocation
                      .stateProvince,
                  isRoundedBorder: true,
                  validator: (val) => val.isEmpty
                      ? Get.find<TranslationHelper>()
                          .translations
                          .jqueryValidation
                          .required
                      : null),
              SizedBox(height: heightSpace(1.5)),
              CustomTextField(
                  controller: c.district,
                  hint: Get.find<TranslationHelper>()
                      .translations
                      .listingLocation
                      .district,
                  isRoundedBorder: true,
                  validator: (val) => val.isEmpty
                      ? Get.find<TranslationHelper>()
                          .translations
                          .jqueryValidation
                          .required
                      : null),
              SizedBox(height: heightSpace(1.5)),
              CustomTextField(
                  controller: c.code,
                  hint: Get.find<TranslationHelper>()
                      .translations
                      .listingLocation
                      .zipPostalCode,
                  inputType: TextInputType.number,
                  formatter: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(10)
                  ],
                  validator: (val) => val.isEmpty
                      ? Get.find<TranslationHelper>()
                          .translations
                          .jqueryValidation
                          .required
                      : null,
                  isRoundedBorder: true)
            ]),
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          stepper(1, width: 22),
          Container(
            margin: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(viewPadding), vertical: widthSpace(3)),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonButton(
                      onPressed: () => Get.back(),
                      title: Get.find<TranslationHelper>()
                          .translations
                          .listingDescription
                          .back,
                      isBorder: true),
                  Obx(() => CommonButton(
                      title: c.isEdit
                          ? Get.find<TranslationHelper>()
                              .translations
                              .usersProfile
                              .save
                          : Get.find<TranslationHelper>()
                              .translations
                              .listingBasic
                              .next,
                      isLoading: c.isLoading.isTrue,
                      horizontalPadding: 7,
                      onPressed: c.submitLocation))
                ]),
          ),
        ],
      ),
    );
  }
}
