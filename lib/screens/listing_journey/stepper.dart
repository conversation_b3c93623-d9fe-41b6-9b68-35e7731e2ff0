import 'package:darent/utils/constants.dart' show greyBorder;
import 'package:flutter/material.dart';
import 'package:get/get.dart';

stepper(int step, {double width=0, int total=5}){
  return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        for(int i =2; i<total;i++)...[
          Expanded(child: Container(
            height: 8,
            alignment: Get.locale?.languageCode=="en"?Alignment.topLeft:Alignment.topRight,
            decoration: BoxDecoration(
                color: step >= i ? Colors.black : const Color(greyBorder),
                borderRadius: BorderRadius.circular(10)
            ),
          ),)
        ]
      ]);
}