import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart' show greyText, viewPadding;
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddProperty4 extends StatelessWidget {
  const AddProperty4({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.put(PropertyController());
    return Scaffold(
      body:
          ListView(padding: EdgeInsets.all(widthSpace(viewPadding)), children: [
        if (!c.isEdit)
          SizedBox(
            height: heightSpace(18),
            child: Directionality(
              textDirection: TextDirection.ltr,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Image.asset("assets/icons/darent_logo.png",
                      width: widthSpace(30)),
                  CommonButton(
                      title: Get.find<TranslationHelper>()
                          .translations
                          .listing
                          .saveExit,
                      fontSize: 2,
                      horizontalPadding: 2,
                      onPressed: () => c.submitNoOfRooms(isExit: true),
                      backgroundBg: Colors.white.withOpacity(0.2),
                      buttonThemeColor: Colors.black),
                ],
              ),
            ),
          ),
        CustomText(
            Get.find<TranslationHelper>()
                .translations
                .listing
                .someInformationAboutYourListing,
            size: 2.4,
            maxlines: 2,
            weight: FontWeight.w500),
        SizedBox(height: heightSpace(2)),
        CustomText(
            Get.find<TranslationHelper>()
                .translations
                .listing
                .addMoreDetailLaterSuchBedTypes,
            color: const Color(greyText),
            size: 2.2),
        SizedBox(height: heightSpace(5)),
        Obx(
          () =>
              Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              CustomText(Get.find<TranslationHelper>()
                  .translations
                  .propertySingle
                  .bedroom),
              Row(children: [
                InkWell(
                  onTap: () =>
                      c.minusWithValidation(c.bedroomsCount, minAmount: 0),
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        shape: BoxShape.circle),
                    child:
                        const Icon(Icons.remove, color: Colors.grey, size: 19),
                  ),
                ),
                SizedBox(width: widthSpace(4)),
                CustomText("${c.bedroomsCount.value}", weight: FontWeight.w500),
                SizedBox(width: widthSpace(4)),
                InkWell(
                  onTap: () => c.plus(c.bedroomsCount),
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        shape: BoxShape.circle),
                    child: const Icon(Icons.add, color: Colors.grey, size: 19),
                  ),
                )
              ])
            ]),
            Divider(height: heightSpace(5)),
            if (c.bedroomsCount.value > 0) ...[
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                CustomText(Get.find<TranslationHelper>()
                    .translations
                    .propertySingle
                    .singleBeds),
                Row(children: [
                  InkWell(
                    onTap: () =>
                        c.minusWithValidation(c.singleBedCount, minAmount: 0),
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          shape: BoxShape.circle),
                      child: const Icon(Icons.remove,
                          color: Colors.grey, size: 19),
                    ),
                  ),
                  SizedBox(width: widthSpace(4)),
                  CustomText("${c.singleBedCount.value}",
                      weight: FontWeight.w500),
                  SizedBox(width: widthSpace(4)),
                  InkWell(
                    onTap: () => c.plus(c.singleBedCount),
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          shape: BoxShape.circle),
                      child:
                          const Icon(Icons.add, color: Colors.grey, size: 19),
                    ),
                  )
                ])
              ]),
              SizedBox(height: heightSpace(3)),
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                CustomText(Get.find<TranslationHelper>()
                    .translations
                    .propertySingle
                    .doubleBeds),
                Row(children: [
                  InkWell(
                    onTap: () =>
                        c.minusWithValidation(c.doubleBedCount, minAmount: 0),
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          shape: BoxShape.circle),
                      child: const Icon(Icons.remove,
                          color: Colors.grey, size: 19),
                    ),
                  ),
                  SizedBox(width: widthSpace(4)),
                  CustomText("${c.doubleBedCount.value}",
                      weight: FontWeight.w500),
                  SizedBox(width: widthSpace(4)),
                  InkWell(
                    onTap: () => c.plus(c.doubleBedCount),
                    child: Container(
                      padding: const EdgeInsets.all(5),
                      decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          shape: BoxShape.circle),
                      child:
                          const Icon(Icons.add, color: Colors.grey, size: 19),
                    ),
                  )
                ])
              ]),
              Divider(height: heightSpace(5), color: Colors.black12),
            ],
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              CustomText(Get.find<TranslationHelper>()
                  .translations
                  .propertySingle
                  .bathroom),
              Row(children: [
                InkWell(
                  onTap: c.bathroomMinus,
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        shape: BoxShape.circle),
                    child:
                        const Icon(Icons.remove, color: Colors.grey, size: 19),
                  ),
                ),
                SizedBox(width: widthSpace(4)),
                CustomText(
                    "${c.bathroomsCount.value > 0.5 ? c.bathroomsCount.value.toStringAsFixed(0) : c.bathroomsCount.value}",
                    weight: FontWeight.w500),
                SizedBox(width: widthSpace(4)),
                InkWell(
                  onTap: c.bathroomPlus,
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        shape: BoxShape.circle),
                    child: const Icon(Icons.add, color: Colors.grey, size: 19),
                  ),
                )
              ])
            ]),
          ]),
        )
      ]),
      bottomNavigationBar: c.isEdit
          ? null
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                stepper(1, width: 27.5),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: widthSpace(3)),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                            onPressed: Get.back,
                            title: Get.find<TranslationHelper>()
                                .translations
                                .listingDescription
                                .back,
                            isBorder: true),
                        Obx(() => CommonButton(
                            title: c.isEdit
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .usersProfile
                                    .save
                                : Get.find<TranslationHelper>()
                                    .translations
                                    .listingBasic
                                    .next,
                            isLoading: c.isLoading.isTrue,
                            horizontalPadding: 7,
                            onPressed: c.submitNoOfRooms))
                      ]),
                ),
              ],
            ),
    );
  }
}
