import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/authHelper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/models/listing_prefs_model.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../../components/common_button.dart';
import '../../components/custom_text.dart';
import '../../utils/constants.dart';
import '../../utils/sizeconfig.dart';

class AddProperty13 extends StatelessWidget {
  const AddProperty13({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    PropertyController c = Get.find();
    AuthHelper.c.addPreferredContactTypesFromInitialData(
        listPrefs["listing_preferences"]);
    return Scaffold(
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(10)),
        child: Column(children: [
          SizedBox(
            height: heightSpace(15),
            child: Directionality(
              textDirection: TextDirection.ltr,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Image.asset(
                    "assets/icons/darent_logo.png",
                    width: widthSpace(30),
                  ),
                  CommonButton(
                    title: Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .saveExit,
                    fontSize: 2,
                    horizontalPadding: 2,
                    backgroundBg: Colors.white.withOpacity(0.2),
                    buttonThemeColor: Colors.black,
                    onPressed: () => c.submitPreferredContactType(
                        AuthHelper.c.preferredContactTypeList,
                        isExit: true),
                  ),
                ],
              ),
            ),
          ),
          CustomText(
              Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .yourPrefMethod ??
                  "What is your preferred method of communication?",
              size: 2.4,
              maxlines: 2,
              weight: FontWeight.w500,
              textAlign: TextAlign.center),
          Padding(
              padding: EdgeInsets.symmetric(vertical: heightSpace(5)),
              child: Obx(
                () => Column(children: [
                  for (GeneralModel item
                      in AuthHelper.c.preferredContactTypeList) ...[
                    InkWell(
                      onTap: () =>
                          AuthHelper.c.selectPreferredContactType(item),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 800),
                        curve: Curves.fastOutSlowIn,
                        margin: EdgeInsets.only(bottom: heightSpace(2)),
                        padding: EdgeInsets.symmetric(
                            horizontal: widthSpace(6), vertical: widthSpace(5)),
                        decoration: BoxDecoration(
                            border: item.isChecked!
                                ? Border.all(color: Colors.black)
                                : Border.all(color: const Color(greyBorder)),
                            borderRadius: BorderRadius.circular(8)),
                        child: Row(mainAxisSize: MainAxisSize.max, children: [
                          Expanded(
                              flex: 2,
                              child: Row(
                                children: [
                                  item.image == null
                                      ? Image.asset(
                                          "assets/icons/apartment.png")
                                      : GlobalHelper.buildNetworkSvgWidget(
                                    url:item.image??"",
                                    width: widthSpace(8),
                                    defaultOption: Image.asset(
                                        "assets/icons/apartment.png"),),
                                ],
                              )),
                          Expanded(
                            flex: 3,
                            child: CustomText(
                                Get.locale?.languageCode == "ar"
                                    ? item.nameAr
                                    : item.name,
                                color: item.isChecked!
                                    ? Colors.black
                                    : Colors.grey[700],
                                weight: FontWeight.w500,
                                size: 2.1),
                          ),
                        ]),
                      ),
                    )
                  ]
                ]),
              ))
        ]),
      ),
      bottomNavigationBar: c.isEdit
          ? null
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                stepper(3, width: 26.4),
                Container(
                  margin: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: widthSpace(3)),
                  decoration: const BoxDecoration(
                      border:
                          Border(top: BorderSide(color: Color(greyBorder)))),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                            onPressed: () {
                              c.isLoading.value = false;
                              Get.back();
                            },
                            title: Get.find<TranslationHelper>()
                                .translations
                                .listingDescription
                                .back,
                            isBorder: true),
                        Obx(
                          () => CommonButton(
                              title: c.isEdit
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .usersProfile
                                      .save
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .listingBasic
                                      .next,
                              isLoading: c.isLoading.isTrue,
                              horizontalPadding: 7,
                              onPressed: () => c.submitPreferredContactType(
                                  AuthHelper.c.preferredContactTypeList)),
                        )
                      ]),
                ),
              ],
            ),
    );
  }
}
