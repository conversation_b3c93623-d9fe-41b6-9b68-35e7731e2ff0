import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'step_main.dart';

class AddProperty extends StatelessWidget {
  final fromDwelling;
  const AddProperty({Key? key, this.fromDwelling = true}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(6), vertical: heightSpace(6)),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .listing
                          .isEasyToStart,
                      size: 3.3,
                      weight: FontWeight.w400),
                  CustomText(
                    Get.find<TranslationHelper>().translations.footer.darent,
                    size: 3.3,
                    weight: FontWeight.w500,
                    color: Color(themeColor),
                  ),
                  SizedBox(height: heightSpace(5)),
                  Align(
                      alignment: Alignment.center,
                      child: Image.asset("assets/icons/homeL1.png",
                          width: widthSpace(30))),
                  SizedBox(height: heightSpace(2)),
                  CustomText(
                      "1 ${Get.find<TranslationHelper>().translations.listing.tellAboutYourResidence}",
                      size: 2.3,
                      weight: FontWeight.w500),
                  SizedBox(height: heightSpace(1)),
                  CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .step1ProvideBasicInfo,
                    color: const Color(greyText),
                    size: 1.9,
                    maxlines: 3,
                  ),
                  Divider(height: heightSpace(4)),
                  Align(
                      alignment: Alignment.center,
                      child: Image.asset("assets/icons/homeL2.png",
                          width: widthSpace(30))),
                  SizedBox(height: heightSpace(2)),
                  CustomText(
                      "2 ${Get.find<TranslationHelper>().translations.listing.makeYourHomeStandOut}",
                      size: 2.3,
                      weight: FontWeight.w500),
                  SizedBox(height: heightSpace(1)),
                  CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .step2AddPhotoDescriptionTitle,
                    color: Color(greyText),
                    size: 1.9,
                    maxlines: 3,
                  ),
                  Divider(height: heightSpace(4)),
                  Align(
                      alignment: Alignment.center,
                      child: Image.asset("assets/icons/homeL3.png",
                          width: widthSpace(30))),
                  SizedBox(height: heightSpace(2)),
                  CustomText(
                      "3 ${Get.find<TranslationHelper>().translations.listing.completionAndPublication}",
                      size: 2.3,
                      weight: FontWeight.w500),
                  SizedBox(
                    height: heightSpace(1),
                  ),
                  CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .listing
                          .step3CompletionAndPublication,
                      color: Color(greyText),
                      size: 1.9,
                      maxlines: 3)
                ]),
          ),
        ),
        bottomNavigationBar: Container(
            margin: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(viewPadding), vertical: widthSpace(3)),
            decoration: const BoxDecoration(
                border: Border(top: BorderSide(color: Color(greyBorder)))),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonButton(
                    onPressed: Get.back,
                    title: Get.find<TranslationHelper>()
                        .translations
                        .listingDescription
                        .back,
                    isBorder: true,
                  ),
                  CommonButton(
                      title: Get.find<TranslationHelper>()
                          .translations
                          .listing
                          .start,
                      horizontalPadding: 7,
                      onPressed: () => Get.to(() =>
                          StepMain(stepNo: 1, fromDwelling: fromDwelling)))
                ])));
  }
}
