import 'dart:convert';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:uuid/uuid.dart';
import '../../components/place_provider.dart';
import '../../components/search_component.dart';
import '../../components/common_button.dart';
import '../../components/custom_text.dart';
import 'package:http/http.dart' as http;

class AddProperty3 extends StatelessWidget {
  AddProperty3({super.key});
  @override
  Widget build(BuildContext context) {
    final c = Get.find<PropertyController>();
    return Scaffold(
      body: SafeArea(
        child: PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            c.backFromMap();
            if (didPop) {
              c.backFromMap();
            }
          },
          child: Column(children: [
            Container(
                height: heightSpace(16),
                padding: EdgeInsets.symmetric(horizontal: widthSpace(6)),
                child: Directionality(
                  textDirection: TextDirection.ltr,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Image.asset("assets/icons/darent_logo.png",
                          width: widthSpace(30)),
                      if (!c.isEdit)
                        CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .saveExit,
                          fontSize: 2,
                          horizontalPadding: 2,
                          onPressed: () => Get.until((route) => route.isFirst),
                          backgroundBg: Colors.white.withOpacity(0.2),
                          buttonThemeColor: Colors.black,
                        ),
                    ],
                  ),
                )),
            if (!c.isEdit)
              Padding(
                padding: EdgeInsets.symmetric(horizontal: widthSpace(6)),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .listing
                            .whereDoYouLive,
                        size: 2.5,
                        maxlines: 1,
                        weight: FontWeight.w500,
                        textAlign: TextAlign.left),
                    SizedBox(height: heightSpace(1)),
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .listing
                            .invisibleAddressStepNote,
                        size: 2.1,
                        color: const Color(greyText),
                        maxlines: 2),
                    SizedBox(height: heightSpace(2)),
                  ],
                ),
              ),
            Expanded(
              child: Stack(children: [
                Obx(() => GoogleMap(
                    myLocationButtonEnabled: false,
                    markers: c.marker.toSet(),
                    buildingsEnabled: false,
                    minMaxZoomPreference: const MinMaxZoomPreference(0, 16),
                    onMapCreated: (GoogleMapController controller) {
                      if (!c.mapController.isCompleted) {
                        c.mapController.complete(controller);
                      }
                    },
                    scrollGesturesEnabled: true,
                    onTap: c.onCameraChanged,
                    initialCameraPosition: CameraPosition(
                        bearing: 192.8334901395799,
                        target: c.propertyLocation,
                        tilt: 59.440717697143555,
                        zoom: 16),
                    compassEnabled: false,
                    zoomControlsEnabled: false)),
                InkWell(
                  onTap: () async {
                    final sessionToken = const Uuid().v4();
                    final Suggestion? result = await showSearch<Suggestion?>(
                        context: context,
                        delegate: SearchComponent(sessionToken));
                    if (result != null) {
                      final api =
                          "https://maps.google.com/maps/api/geocode/json?key=$mapKey&address=${result.description}&sensor=false&libraries=establishment&sessiontoken=$sessionToken";
                      final response = await http.get(Uri.parse(api));
                      if (response.statusCode == 200) {
                        final responseResult = jsonDecode(response.body);
                        final location = responseResult['results']
                            .first['geometry']['location'];
                        c.onCameraChanged(
                            LatLng(location['lat'], location['lng']),
                            location: result.mainText);
                      }
                    }
                  },
                  child: Container(
                    width: double.maxFinite,
                    margin: EdgeInsets.all(widthSpace(5)),
                    padding: EdgeInsets.symmetric(
                        vertical: heightSpace(1.5), horizontal: widthSpace(5)),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                            color: const Color.fromARGB(255, 0, 0, 0))),
                    child: Obx(
                      () => Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          const Icon(Icons.location_on, size: 22),
                          SizedBox(width: widthSpace(2)),
                          SizedBox(
                            width: widthSpace(70),
                            child: CustomText(
                              c.propertyAddress.value ?? "",
                              size: 2,
                              color: Colors.black,
                              textAlign: TextAlign.start,
                              maxlines: 2,
                              textOverflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              ]),
            )
          ]),
        ),
      ),
      floatingActionButton: FloatingActionButton(
          onPressed: c.getCurrentLocation,
          child: const Icon(Icons.my_location_sharp)),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          stepper(1, width: 16.5),
          Container(
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(viewPadding), vertical: widthSpace(3)),
            child: Obx(
              () => Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonButton(
                        onPressed: () {
                          c.backFromMap();
                          Get.back();
                        },
                        isLoading: c.isLoading.value,
                        title: Get.find<TranslationHelper>()
                            .translations
                            .listingDescription
                            .back,
                        isBorder: true),
                    CommonButton(
                        title: c.isEdit
                            ? Get.find<TranslationHelper>()
                                .translations
                                .usersProfile
                                .save
                            : Get.find<TranslationHelper>()
                                .translations
                                .listingBasic
                                .next,
                        isLoading: c.marker.isEmpty || c.isLoading.isTrue,
                        horizontalPadding: 7,
                        onPressed: c.nextLocation)
                  ]),
            ),
          ),
        ],
      ),
    );
  }
}
