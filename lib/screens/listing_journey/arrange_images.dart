import 'dart:io' show File;
import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:reorderable_grid_view/reorderable_grid_view.dart';

import '../../helperMethods/remote_config.dart';

class ArrangeImages extends StatelessWidget {
  ArrangeImages({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.put(PropertyController());
    return Obx(
      () => Scaffold(
        body: c.uploading.value
            ? Center(
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                          width: widthSpace(89),
                          child: CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .magicallyArrangeYourPhoto,
                              size: 2.4,
                              weight: FontWeight.w500,
                              textAlign: TextAlign.center)),
                      Image.asset("assets/loader.gif", width: widthSpace(15))
                    ]),
              )
            : Directionality(
                textDirection: TextDirection.ltr,
                child: CustomScrollView(slivers: [
                  if (!c.isEdit) ...[
                    SliverToBoxAdapter(
                      child: Directionality(
                        textDirection: TextDirection.ltr,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Image.asset("assets/icons/darent_logo.png",
                                width: widthSpace(30)),
                            CommonButton(
                                title: Get.find<TranslationHelper>()
                                    .translations
                                    .listing
                                    .saveExit,
                                fontSize: 2,
                                horizontalPadding: 2,
                                onPressed: () => c.sortImages(isExit: true),
                                backgroundBg: Colors.white.withOpacity(0.2),
                                buttonThemeColor: Colors.black),
                          ],
                        ),
                      ).paddingAll(widthSpace(viewPadding)),
                    ),
                    SliverToBoxAdapter(
                        child: CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .listing
                                .successfullyCompletedStepDidYouLikeArrangement,
                            size: 2.4,
                            maxlines: 2,
                            weight: FontWeight.w500,
                            textAlign: TextAlign.center)),
                    SliverToBoxAdapter(child: SizedBox(height: heightSpace(2))),
                    SliverToBoxAdapter(
                      child: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .dragPictureToRearrange,
                          color: const Color(greyText),
                          size: 2.2,
                          textAlign: TextAlign.center),
                    ),
                  ],
                  // SizedBox(height: heightSpace(5)),
                  if (c.isLoading.value) ...[
                    SliverFillRemaining(
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                          Image.asset("assets/loader.gif",
                              width: widthSpace(10), height: widthSpace(10)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .pleaseWait,
                              textAlign: TextAlign.center),
                        ]))
                  ] else ...[
                    if (c.coverImage.value != null)
                      SliverToBoxAdapter(
                        child: Container(
                          width: double.maxFinite,
                          alignment: Alignment.topLeft,
                          height: heightSpace(33),
                          margin: EdgeInsets.only(top: widthSpace(viewPadding)),
                          padding: EdgeInsets.all(widthSpace(viewPadding)),
                          decoration: BoxDecoration(
                              image: DecorationImage(
                                  image: c.coverImage.value!.image
                                              .contains("/property") ==
                                          true
                                      ? GlobalHelper.buildNetworkImageProvider(url: c.coverImage.value?.image ??"",)
                                      : FileImage(
                                          File(c.coverImage.value!.image)),
                                  fit: BoxFit.cover)),
                          child: Container(
                              padding: EdgeInsets.symmetric(
                                  vertical: widthSpace(2),
                                  horizontal: widthSpace(2)),
                              decoration: BoxDecoration(
                                color: Colors.black26.withOpacity(.4),
                                borderRadius: BorderRadius.circular(7),
                              ),
                              child: CustomText(
                                  Get.find<TranslationHelper>()
                                      .translations
                                      .listingDescription
                                      .coverPhoto,
                                  color: Colors.white)),
                        ),
                      ),
                    SliverToBoxAdapter(
                        child: SizedBox(height: widthSpace(viewPadding))),
                    ReorderableSliverGridView.count(
                        crossAxisCount: 2,
                        mainAxisSpacing: widthSpace(3),
                        crossAxisSpacing: widthSpace(3),
                        onReorder: (beforeIndex, afterIndex) {
                          if (beforeIndex != afterIndex) {
                            c.reArrangeList(beforeIndex, afterIndex);
                          }
                        },
                        children: List.generate(c.propertyImages.length, (i) {
                          // int i = index +1;
                          // if((index+1)==c.propertyImages.length){
                          //   return DraggableGridItem(
                          //     child: InkWell(
                          //       onTap:(){
                          //         c.pickImage();
                          //       },
                          //       child: DottedBorder(child: Center(
                          //         child: Icon(Icons.add,size: widthSpace(12)),
                          //       )),
                          //     ),
                          //   );
                          // }
                          return Container(
                              key: Key('$i'),
                              padding: const EdgeInsets.all(6),
                              alignment: Alignment.topRight,
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                    image: c.propertyImages[i].image
                                                .contains("/property") ==
                                            true
                                        ? GlobalHelper.buildNetworkImageProvider(url: c.propertyImages[i].image,)
                                        : FileImage(
                                            File(c.propertyImages[i].image)),
                                    fit: BoxFit.fill),
                              ),
                              child: PopupMenuButton(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  onSelected: (value) {
                                    if (value == 1) {
                                      c.changeCover(i);
                                    } else {
                                      c.removeImage(i);
                                    }
                                  },
                                  itemBuilder: (context) {
                                    return [
                                      PopupMenuItem(
                                          value: 1,
                                          child: Text(
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .listing
                                                  .setCoverPhoto!,
                                              style: const TextStyle(
                                                  fontWeight:
                                                      FontWeight.w500))),
                                      // PopupMenuItem(
                                      //     value: 2,child: Text(translations.utility.delete,style: const TextStyle(fontWeight: FontWeight.w500))),
                                    ];
                                  },
                                  child: Container(
                                      padding: EdgeInsets.all(widthSpace(.5)),
                                      decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(.8),
                                          shape: BoxShape.circle),
                                      child: const Icon(
                                          Icons.more_horiz_rounded,
                                          size: 25))));
                        })),
                    SliverToBoxAdapter(child: SizedBox(height: heightSpace(2)))
                  ]
                ]),
              ),
        bottomNavigationBar: c.isEdit || c.isLoading.value || c.uploading.value
            ? null
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  stepper(2, width: 16.5),
                  Container(
                    padding: EdgeInsets.symmetric(
                        horizontal: widthSpace(viewPadding),
                        vertical: widthSpace(3)),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonButton(
                              onPressed: () {
                                c.isLoading.value = false;
                                Get.back();
                              },
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .listingDescription
                                  .back,
                              isBorder: true),
                          CommonButton(
                            title: Get.find<TranslationHelper>()
                                .translations
                                .listingBasic
                                .next,
                            horizontalPadding: 7,
                            isLoading: c.isLoading.isTrue,
                            onPressed: c.sortImages,
                          )
                        ]),
                  ),
                ],
              ),
      ),
    );
  }
  // PlaceHolderWidget placeHolderWidget(List<DraggableGridItem> list, int i){
  //   return PlaceHolderWidget(
  //       child: Container(
  //           padding: const EdgeInsets.symmetric(horizontal:20),
  //           decoration: BoxDecoration(
  //             color: const Color(greyBorder),
  //             borderRadius: BorderRadius.circular(5),
  //           ),child: const Center(child: CustomText("Place any where you want", weight: FontWeight.w500,textAlign: TextAlign.center))));
  // }
}
