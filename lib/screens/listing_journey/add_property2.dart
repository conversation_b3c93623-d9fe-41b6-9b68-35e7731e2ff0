import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/screens/shared_room.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddProperty2 extends StatelessWidget {
  const AddProperty2({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.put(PropertyController());
    return Scaffold(
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: widthSpace(6)),
          child: ListView(children: [
            if (!c.isEdit)
              SizedBox(
                height: heightSpace(16),
                child: Directionality(
                  textDirection: TextDirection.ltr,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Image.asset(
                        "assets/icons/darent_logo.png",
                        width: widthSpace(30),
                      ),
                      CommonButton(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .listing
                            .saveExit,
                        fontSize: 2,
                        horizontalPadding: 2,
                        onPressed: () => c.submitSpaceType(isExit: true),
                        backgroundBg: Colors.white.withOpacity(0.2),
                        buttonThemeColor: Colors.black,
                      ),
                    ],
                  ),
                ),
              ),
            if (c.spaceTypes.isNotEmpty)
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .listing
                      .typeOfAccommodationAvailableForGuest,
                  size: 2.4,
                  maxlines: 2,
                  weight: FontWeight.w500,
                  textAlign: TextAlign.center),
            Obx(
              () => Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    for (final item in c.spaceTypes) ...[
                      InkWell(
                        onTap: () {
                          if (item.id == 3) {
                            Get.to(() => const SharedRoom());
                          } else {
                            c.selectSpaceType(item);
                          }
                        },
                        child: AnimatedContainer(
                            duration: const Duration(milliseconds: 800),
                            curve: Curves.fastOutSlowIn,
                            margin: EdgeInsets.only(bottom: heightSpace(2)),
                            padding: EdgeInsets.symmetric(
                                horizontal: widthSpace(6),
                                vertical: widthSpace(5)),
                            decoration: BoxDecoration(
                                border: item == c.spaceType.value
                                    ? Border.all(color: Colors.black)
                                    : Border.all(
                                        color: const Color(greyBorder)),
                                borderRadius: BorderRadius.circular(8)),
                            child:
                                Row(mainAxisSize: MainAxisSize.max, children: [
                              Expanded(
                                  flex: 2,
                                  child: Row(
                                    children: [
                                      Image.asset(
                                        "assets/icons/apartment.png",
                                        width: widthSpace(7),
                                      )
                                    ],
                                  )),
                              Expanded(
                                flex: 3,
                                child: CustomText(
                                    Get.locale?.languageCode == "ar"
                                        ? item.nameAr
                                        : item.name,
                                    color: item == c.spaceType.value
                                        ? Colors.black
                                        : Colors.grey[700],
                                    weight: FontWeight.w500,
                                    size: 2.1),
                              ),
                            ])),
                      )
                    ],
                    if (Get.find<RemoteConfig>().enableNoOfApartments) ...[
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: widthSpace(6),
                            vertical: heightSpace(4)),
                        child: CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .listing
                                .numberOfAppartment,
                            size: 2.2,
                            textAlign: TextAlign.center),
                      ),
                      Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: widthSpace(6)),
                        child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              InkWell(
                                onTap: () =>
                                    c.minusWithValidation(c.noOfApartments),
                                child: Container(
                                  padding: EdgeInsets.all(widthSpace(3.5)),
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                          color: const Color(greyText),
                                          width: 2.5),
                                      shape: BoxShape.circle),
                                  child: Icon(Icons.remove,
                                      color: const Color(greyText),
                                      size: widthSpace(4)),
                                ),
                              ),
                              Obx(
                                () => Container(
                                    width: widthSpace(45),
                                    height: heightSpace(12),
                                    padding: EdgeInsets.symmetric(
                                        vertical: heightSpace(1)),
                                    decoration: BoxDecoration(
                                        border: Border.all(),
                                        borderRadius: BorderRadius.circular(
                                            heightSpace(1.5))),
                                    child: Center(
                                        child: CustomText(
                                            "${c.noOfApartments.value}",
                                            size: 7,
                                            weight: FontWeight.w500,
                                            color: const Color(greyText)))),
                              ),
                              InkWell(
                                onTap: () => c.plus(c.noOfApartments),
                                child: Container(
                                  padding: EdgeInsets.all(widthSpace(3.5)),
                                  decoration: BoxDecoration(
                                      border: Border.all(
                                          color: const Color(greyText),
                                          width: 2.5),
                                      shape: BoxShape.circle),
                                  child: Icon(Icons.add,
                                      color: const Color(greyText),
                                      size: widthSpace(4)),
                                ),
                              ),
                            ]),
                      ),
                    ],
                  ]),
            ).paddingSymmetric(vertical: heightSpace(5))
          ]),
        ),
        bottomNavigationBar: c.isEdit
            ? null
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  stepper(1, width: 11),
                  Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: widthSpace(viewPadding),
                          vertical: widthSpace(3)),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CommonButton(
                                onPressed: Get.back,
                                title: Get.find<TranslationHelper>()
                                    .translations
                                    .listingDescription
                                    .back,
                                isBorder: true),
                            Obx(
                              () => CommonButton(
                                  title: c.isEdit
                                      ? Get.find<TranslationHelper>()
                                          .translations
                                          .usersProfile
                                          .save
                                      : Get.find<TranslationHelper>()
                                          .translations
                                          .listingBasic
                                          .next,
                                  horizontalPadding: 7,
                                  isLoading: c.isLoading.isTrue,
                                  onPressed: c.submitSpaceType),
                            )
                          ])),
                ],
              ));
  }
}
