import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddProperty9 extends StatelessWidget {
  const AddProperty9({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.find<PropertyController>();
    return Scaffold(
      body: ListView(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(viewPadding), vertical: heightSpace(5)),
          children: [
            if (!c.isEdit) ...[
              SizedBox(
                height: heightSpace(18),
                child: Directionality(
                  textDirection: TextDirection.ltr,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Image.asset("assets/icons/darent_logo.png",
                          width: widthSpace(30)),
                      CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .saveExit,
                          fontSize: 2,
                          horizontalPadding: 2,
                          onPressed: () => c.submitBasics(isExit: true),
                          backgroundBg: Colors.white.withOpacity(0.2),
                          buttonThemeColor: Colors.black),
                    ],
                  ),
                ),
              ),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .listing
                      .howManyGuest,
                  size: 2.4,
                  maxlines: 2,
                  weight: FontWeight.w500,
                  textAlign: TextAlign.center),
            ],
            SizedBox(height: heightSpace(5)),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                CustomText(
                  Get.find<TranslationHelper>().translations.listing.adults,
                  weight: FontWeight.w500,
                ),
                // const SizedBox(height: 5),
                // CustomText("moreThan13".tr,
                //     size: 1.9, color: const Color(greyText)),
              ]),
              Row(children: [
                InkWell(
                    onTap: () => c.minusWithValidation(c.adultCount),
                    child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            shape: BoxShape.circle),
                        child: const Icon(Icons.remove,
                            color: Colors.grey, size: 21))),
                SizedBox(width: widthSpace(4)),
                Obx(() => CustomText("${c.adultCount.value}",
                    weight: FontWeight.w500)),
                SizedBox(width: widthSpace(4)),
                InkWell(
                  onTap: () => c.plus(c.adultCount),
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        shape: BoxShape.circle),
                    child: const Icon(Icons.add, color: Colors.grey, size: 19),
                  ),
                )
              ])
            ]),
            Divider(height: heightSpace(5)),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
                CustomText(
                  Get.find<TranslationHelper>().translations.listing.children,
                  weight: FontWeight.w500,
                ),
                // const SizedBox(height: 5),
                // CustomText("lessThan13".tr,size: 1.9, color: const Color(greyText)),
              ]),
              Row(children: [
                InkWell(
                  onTap: () => c.minusWithValidation(c.childrenCount),
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        shape: BoxShape.circle),
                    child:
                        const Icon(Icons.remove, color: Colors.grey, size: 19),
                  ),
                ),
                SizedBox(width: widthSpace(4)),
                Obx(() => CustomText("${c.childrenCount.value}",
                    weight: FontWeight.w500)),
                SizedBox(width: widthSpace(4)),
                InkWell(
                  onTap: () => c.plus(c.childrenCount),
                  child: Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        shape: BoxShape.circle),
                    child: const Icon(Icons.add, color: Colors.grey, size: 21),
                  ),
                )
              ])
            ])
          ]),
      bottomNavigationBar: c.isEdit
          ? null
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                stepper(3, width: 6.6),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: widthSpace(3)),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                          onPressed: () => Get.back(),
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listingDescription
                              .back,
                          isBorder: true,
                        ),
                        Obx(() => CommonButton(
                            title: c.isEdit
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .usersProfile
                                    .save
                                : Get.find<TranslationHelper>()
                                    .translations
                                    .listingBasic
                                    .next,
                            isLoading: c.isLoading.isTrue,
                            horizontalPadding: 7,
                            onPressed: c.submitBasics))
                      ]),
                ),
              ],
            ),
    );
  }
}
