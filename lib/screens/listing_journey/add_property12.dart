import 'package:darent/components/common_button.dart';
import 'package:darent/components/common_checkbox.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddProperty12 extends StatelessWidget {
  const AddProperty12({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    PropertyController c = Get.find();
    return Scaffold(
      body: Stack(
        alignment: Alignment.center,
        children: [
          SingleChildScrollView(
              padding: EdgeInsets.symmetric(
                  horizontal: widthSpace(viewPadding),
                  vertical: heightSpace(10)),
              child: Obx(
                () => Column(
                  children: [
                    if (!c.isEdit) ...[
                      Directionality(
                        textDirection: TextDirection.ltr,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Image.asset("assets/icons/darent_logo.png",
                                width: widthSpace(30)),
                            CommonButton(
                                title: Get.find<TranslationHelper>()
                                    .translations
                                    .listing
                                    .saveExit,
                                fontSize: 2,
                                horizontalPadding: 2,
                                onPressed: () => c.submitQuestion(isExit: true),
                                backgroundBg: Colors.white.withOpacity(0.2),
                                buttonThemeColor: Colors.black),
                          ],
                        ),
                      ),
                      SizedBox(height: heightSpace(5)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .chooseGuestOnFirstReservation,
                          size: 2.4,
                          maxlines: 2,
                          weight: FontWeight.w500,
                          textAlign: TextAlign.center),
                      SizedBox(height: heightSpace(2)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .chooseFirstGuestDescription,
                          size: 2.2,
                          maxlines: 2,
                          color: Color(greyText),
                          textAlign: TextAlign.center),
                      SizedBox(height: heightSpace(4)),
                    ],
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .listing
                          .howAreYouHosting,
                      size: 2.4,
                      weight: FontWeight.w500,
                    ),
                    SizedBox(height: heightSpace(3.5)),
                    InkWell(
                      onTap: () => c.selectHosingType("request"),
                      borderRadius: BorderRadius.circular(10),
                      child: Container(
                        padding: EdgeInsets.all(widthSpace(4)),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: const Color(greyBorder))),
                        child: Row(children: [
                          Container(
                            width: widthSpace(5),
                            height: widthSpace(5),
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: Color(
                                        c.hostingType.value == "request"
                                            ? themeColor
                                            : greyBorder),
                                    width: c.hostingType.value == "request"
                                        ? 2
                                        : 1),
                                shape: BoxShape.circle),
                            child: c.hostingType.value == "request"
                                ? Container(
                                    decoration: const BoxDecoration(
                                        color: Color(themeColor),
                                        shape: BoxShape.circle),
                                  )
                                : null,
                          ),
                          SizedBox(width: widthSpace(5)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .reviewRequest,
                              color: Colors.grey[800])
                        ]),
                      ),
                    ),
                    SizedBox(height: heightSpace(1)),
                    InkWell(
                      onTap: () => c.selectHosingType("instant"),
                      borderRadius: BorderRadius.circular(10),
                      child: Container(
                        padding: EdgeInsets.all(widthSpace(4)),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(color: const Color(greyBorder))),
                        child: Row(children: [
                          Container(
                            width: widthSpace(5),
                            height: widthSpace(5),
                            padding: const EdgeInsets.all(2),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: Color(
                                        c.hostingType.value == "instant"
                                            ? themeColor
                                            : greyBorder),
                                    width: c.hostingType.value == "instant"
                                        ? 2
                                        : 1),
                                shape: BoxShape.circle),
                            child: c.hostingType.value == "instant"
                                ? Container(
                                    decoration: const BoxDecoration(
                                        color: Color(themeColor),
                                        shape: BoxShape.circle),
                                  )
                                : null,
                          ),
                          SizedBox(width: widthSpace(5)),
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .bookInstantly,
                              color: Colors.grey[800])
                        ]),
                      ),
                    ),
                    SizedBox(height: heightSpace(3)),
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .listing
                          .haveAnyAtPlace,
                      size: 2.4,
                      weight: FontWeight.w500,
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: heightSpace(3.5)),
                    for (var item in c.haveAny) ...[
                      InkWell(
                        onTap: () {
                          c.checkHaveAny(item);
                        },
                        child: Container(
                          width: double.maxFinite,
                          margin: EdgeInsets.only(bottom: heightSpace(1)),
                          padding: EdgeInsets.all(widthSpace(4)),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border:
                                  Border.all(color: const Color(greyBorder))),
                          child: CommonCheckBox(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              onPressed: () {
                                c.checkHaveAny(item);
                              },
                              isSelected: item['isChecked'],
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .toJson()[item['title']]),
                        ),
                      )
                    ],
                    if (!c.isEdit) ...[
                      SizedBox(height: heightSpace(3.5)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .someImportantThings,
                          weight: FontWeight.w500,
                          size: 2.4),
                      SizedBox(height: heightSpace(2)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .beSureToComplyYourLaws,
                          size: 2.1,
                          color: const Color(greyText),
                          textAlign: TextAlign.center),
                      SizedBox(height: heightSpace(2)),
                      // Align(
                      //   alignment:Alignment.centerLeft,
                      //   child: CommonCheckBox(
                      //       crossAxisAlignment: CrossAxisAlignment.start,
                      //       onPressed: c.agreeHost,
                      //       isSelected: c.hostAgreed.value,
                      //       title: Row(children: [
                      //         const CustomText("I Agree to the ",color:Color(greyText)),
                      //         InkWell(
                      //             onTap:(){
                      //               Get.to(() =>MyFatoorahScreen(
                      //                   url: "$baseUrl/property/hostAgreement?lang=${Get.locale?.languageCode??"en"}&mobile=1",
                      //                   screenName: "Host Agreement"));
                      //             },
                      //             child:const CustomText("the host agreement",color: Colors.blueAccent,underline: true))
                      //       ])
                      //   ),
                      // )
                    ],
                  ],
                ),
              )),
        ],
      ),
      bottomNavigationBar: c.isEdit
          ? null
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                stepper(3, width: 26.4),
                Container(
                  margin: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: widthSpace(3)),
                  decoration: const BoxDecoration(
                      border:
                          Border(top: BorderSide(color: Color(greyBorder)))),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                            onPressed: () {
                              ViewsCommon.clearPriceControllers();
                              Get.back();
                            },
                            title: Get.find<TranslationHelper>()
                                .translations
                                .listingDescription
                                .back,
                            isBorder: true),
                        Obx(
                          () => CommonButton(
                              title: c.isEdit
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .usersProfile
                                      .save
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .listingBasic
                                      .next,
                              isLoading: c.isLoading.isTrue,
                              horizontalPadding: 7,
                              onPressed: c.submitQuestion),
                        )
                      ]),
                ),
              ],
            ),
    );
  }
}
