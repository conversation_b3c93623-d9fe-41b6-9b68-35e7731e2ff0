import 'dart:io' show File;
import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddProperty6 extends StatelessWidget {
  const AddProperty6({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.find<PropertyController>();
    return Obx(
      () => Scaffold(
        body: c.uploading.value
            ? Center(
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                          width: widthSpace(89),
                          child: CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .magicallyArrangeYourPhoto,
                              size: 2.4,
                              weight: FontWeight.w500,
                              textAlign: TextAlign.center)),
                      Image.asset("assets/loader.gif", width: widthSpace(15)),
                      // CustomText('Uploading',color: Color(greyText)),
                    ]),
              )
            : ListView(
                padding: EdgeInsets.all(widthSpace(viewPadding)),
                children: [
                    if (!c.isEdit) ...[
                      SizedBox(
                        height: heightSpace(18),
                        child: Directionality(
                          textDirection: TextDirection.ltr,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Image.asset("assets/icons/darent_logo.png",
                                  width: widthSpace(30)),
                              CommonButton(
                                  title: Get.find<TranslationHelper>()
                                      .translations
                                      .listing
                                      .saveExit,
                                  fontSize: 2,
                                  horizontalPadding: 2,
                                  onPressed: () => c.uploadImages(isExit: true),
                                  backgroundBg: Colors.white.withOpacity(0.2),
                                  buttonThemeColor: Colors.black),
                            ],
                          ),
                        ),
                      ),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .addSomePictureOfHouseOnATree,
                          size: 2.4,
                          maxlines: 2,
                          weight: FontWeight.w500,
                          textAlign: TextAlign.center),
                      SizedBox(height: heightSpace(2)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .atleast5PhotosToGetStarted,
                          color: const Color(greyText),
                          size: 2.2,
                          textAlign: TextAlign.center),
                    ],
                    SizedBox(height: heightSpace(5)),
                    InkWell(
                      onTap: c.propertyImages.isEmpty ? c.pickImage : null,
                      child: SizedBox(
                        height:
                            c.propertyImages.isEmpty ? heightSpace(30) : null,
                        child: DottedBorder(
                            // color: const Color(greyText),
                            // borderType: BorderType.RRect,
                            // radius: const Radius.circular(2),
                            // dashPattern: const [9],
                            // padding: c.propertyImages.isEmpty
                            //     ? EdgeInsets.zero
                            //     : EdgeInsets.all(widthSpace(viewPadding)),
                            child: c.isLoading.value
                                ? Center(
                                    child: Image.asset('assets/loader.gif',
                                        width: widthSpace(10)))
                                : c.propertyImages.isEmpty
                                    ? Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(Icons.perm_media_outlined,
                                                size: widthSpace(10)),
                                            SizedBox(height: heightSpace(1)),
                                            CustomText(
                                                Get.find<TranslationHelper>()
                                                    .translations
                                                    .listing
                                                    .dragYourPhoto,
                                                size: 2.4,
                                                maxlines: 2,
                                                weight: FontWeight.w500),
                                            SizedBox(height: heightSpace(1)),
                                            CustomText(
                                                Get.find<TranslationHelper>()
                                                    .translations
                                                    .listing
                                                    .chooseAtleast5Photo,
                                                color: const Color(greyText),
                                                textAlign: TextAlign.center),
                                          ],
                                        ),
                                      )
                                    : GridView(
                                        physics:
                                            const NeverScrollableScrollPhysics(),
                                        shrinkWrap: true,
                                        // dragPlaceHolder: placeHolderWidget,
                                        gridDelegate:
                                            SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: 2,
                                          crossAxisSpacing: widthSpace(3),
                                          mainAxisSpacing: widthSpace(3),
                                          childAspectRatio: 0.75,
                                        ),
                                        // dragCompletion: (list,beforeIndex,afterIndex) {
                                        //   if (beforeIndex != afterIndex) {
                                        //     c.reArrangeList(beforeIndex, afterIndex);
                                        //   }
                                        // },
                                        children: List.generate(
                                            c.propertyImages.length + 1,
                                            (index) {
                                          if (index ==
                                              c.propertyImages.length) {
                                            return InkWell(
                                              onTap: c.pickImage,
                                              child: DottedBorder(
                                                  child: SizedBox(
                                                width: widthSpace(35),
                                                height: widthSpace(40),
                                                child: Icon(Icons.add,
                                                    size: widthSpace(12)),
                                              )),
                                            );
                                          } else {
                                            return Container(
                                                width: widthSpace(35),
                                                height: widthSpace(40),
                                                clipBehavior: Clip.antiAlias,
                                                alignment: Alignment.topRight,
                                                decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            13),
                                                    image: DecorationImage(
                                                        image: c.propertyImages[index].image.contains("/property") == true
                                                            ? GlobalHelper.buildNetworkImageProvider(url: c.propertyImages[index].image,)
                                                            : FileImage(File(c
                                                                .propertyImages[
                                                                    index]
                                                                .image)),
                                                        fit: BoxFit.cover)),
                                                child: IconButton(
                                                    padding:
                                                        const EdgeInsets.only(
                                                            bottom: 18,
                                                            left: 40),
                                                    onPressed: () =>
                                                        c.removeImage(index),
                                                    icon: const Icon(Icons.cancel_outlined, size: 20, shadows: <Shadow>[Shadow(color: Colors.white, blurRadius: 15.0)])));
                                          }
                                        }),
                                      )),
                      ),
                    ),
                  ]),
        bottomNavigationBar: c.isEdit || c.uploading.value
            ? null
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  stepper(2, width: 11),
                  Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: widthSpace(viewPadding),
                        vertical: widthSpace(3)),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonButton(
                              onPressed: () {
                                c.isLoading.value = false;
                                Get.back();
                              },
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .listingDescription
                                  .back,
                              isBorder: true),
                          CommonButton(
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .save,
                              horizontalPadding: 7,
                              onPressed: c.uploadImages)
                        ]),
                  ),
                ],
              ),
      ),
    );
  }
}
