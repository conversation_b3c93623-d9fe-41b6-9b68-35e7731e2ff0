import 'package:darent/helperMethods/translation_helper.dart';

import '../../components/common_button.dart';
import '../../components/custom_text.dart';
import 'add_property1.dart';
import 'add_property10.dart';
import 'add_property5.dart';
import 'stepper.dart';
import '../../utils/constants.dart';
import '../../utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class StepMain extends StatelessWidget {
  final int stepNo;
  final fromDwelling;
  const StepMain({Key? key, required this.stepNo, this.fromDwelling = true})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
        Container(
          width: double.maxFinite,
          height: heightSpace(50),
          color: Colors.white,
          padding: EdgeInsets.symmetric(vertical: heightSpace(10)),
          child: Image.asset(
            "assets/icons/homeL$stepNo.png",
          ),
        ),
        Padding(
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(viewPadding), vertical: heightSpace(2)),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              SizedBox(
                  width: widthSpace(45),
                  child: CustomText(
                    stepNo == 1
                        ? Get.find<TranslationHelper>()
                            .translations
                            .listing
                            .step1
                        : stepNo == 2
                            ? Get.find<TranslationHelper>()
                                .translations
                                .listing
                                .step2
                            : Get.find<TranslationHelper>()
                                .translations
                                .listing
                                .step3,
                    size: 3.2,
                    weight: FontWeight.w500,
                  )),
              SizedBox(height: heightSpace(3)),
              CustomText(
                  stepNo == 1
                      ? Get.find<TranslationHelper>()
                          .translations
                          .listing
                          .tellAboutYourResidence
                      : stepNo == 2
                          ? Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .makeYourHomeStandOut
                          : Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .completionAndPublication,
                  size: 2.3),
              SizedBox(height: heightSpace(1)),
              CustomText(
                  stepNo == 1
                      ? Get.find<TranslationHelper>()
                          .translations
                          .listing
                          .stepOneDescription
                      : stepNo == 2
                          ? Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .addSomeFeaturesYouHaveInYourListing
                          : Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .step3Description,
                  size: 2,
                  color: const Color(greyText))
            ])),
      ]),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          stepper(stepNo),
          Container(
            height: heightSpace(10),
            alignment: Alignment.centerLeft,
            padding: EdgeInsets.symmetric(horizontal: widthSpace(viewPadding)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonButton(
                  onPressed: () => Get.back(),
                  title: Get.find<TranslationHelper>()
                      .translations
                      .listingDescription
                      .back,
                  isBorder: true,
                ),
                CommonButton(
                    title: Get.find<TranslationHelper>()
                        .translations
                        .listingBasic
                        .next,
                    horizontalPadding: 7,
                    onPressed: () => Get.to(() => stepNo == 1
                            ? AddProperty1(fromDwelling: fromDwelling)
                            : stepNo == 2
                                ? const AddProperty5()
                                : const AddProperty10() //const AddProperty9()
                        )),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
