import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/authHelper.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../components/common_button.dart';
import '../../components/custom_iqama_sheet.dart';
import '../../controllers/property_controller.dart';
import '../../utils/constants.dart';
import '../../utils/sizeconfig.dart';

class AddProperty14 extends StatelessWidget {
  const AddProperty14({super.key});

  @override
  Widget build(BuildContext context) {
    PropertyController c = Get.find();
    return Scaffold(
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(5)),
        child: Column(children: [
          SizedBox(
            height: heightSpace(15),
            child: Directionality(
              textDirection: TextDirection.ltr,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Image.asset(
                    "assets/icons/darent_logo.png",
                    width: widthSpace(30),
                  ),
                  CommonButton(
                    title: Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .saveExit,
                    fontSize: 2,
                    horizontalPadding: 2,
                    backgroundBg: Colors.white.withOpacity(0.2),
                    buttonThemeColor: Colors.black,
                    onPressed: () => c.submitIlmYaqeen(isExit: true),
                  ),
                ],
              ),
            ),
          ),
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .listing
                  .identityVerification,
              size: 2.4,
              maxlines: 2,
              weight: FontWeight.w500,
              textAlign: TextAlign.center),
          Padding(
            padding: EdgeInsets.symmetric(vertical: heightSpace(2)),
            child: Obx(
              () => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: heightSpace(2)),
                    Align(
                        alignment: Alignment.center,
                        child: Image.asset("assets/elm.jpg",
                            height: heightSpace(22))),
                    const SizedBox(height: 5),
                    GestureDetector(
                      onTap: () {
                        if (userModel.value?.yaqeenVerified == false) {
                          {
                            AuthHelper.c.iqamaValue.value = 5;
                            customIqamaSheet(isStep: true);
                          }
                        }
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: widthSpace(3), vertical: widthSpace(2)),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(widthSpace(3)),
                          border: Border.all(color: Colors.grey),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .usersProfile
                                    .ninIqama,
                                size: 2.1,
                                weight: FontWeight.w500),
                            userModel.value?.yaqeenVerified == true
                                ? const Icon(Icons.check_circle,
                                    color: Color(successColor))
                                : const Icon(
                                    Icons.arrow_drop_down,
                                    size: 25,
                                  ),
                          ],
                        ),
                      ),
                    ),
                  ]),
            ),
          )
        ]),
      ),
      bottomNavigationBar: c.isEdit
          ? null
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                stepper(3, width: 26.4),
                Container(
                  margin: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: widthSpace(3)),
                  decoration: const BoxDecoration(
                      border:
                          Border(top: BorderSide(color: Color(greyBorder)))),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                            onPressed: () {
                              Get.back();
                            },
                            title: Get.find<TranslationHelper>()
                                .translations
                                .listingDescription
                                .back,
                            isBorder: true),
                        Obx(
                          () => CommonButton(
                              title: c.isEdit
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .usersProfile
                                      .save
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .listingBasic
                                      .next,
                              isLoading: c.isLoading.isTrue,
                              horizontalPadding: 7,
                              onPressed: c.submitIlmYaqeen),
                        )
                      ]),
                ),
              ],
            ),
    );
  }
}
