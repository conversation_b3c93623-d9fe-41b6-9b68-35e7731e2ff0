import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class AddProperty10 extends StatelessWidget {
  const AddProperty10({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    PropertyController c = Get.find();
    return Scaffold(
      body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(viewPadding), vertical: heightSpace(10)),
          child: Column(
            children: [
              if (!c.isEdit) ...[
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Image.asset("assets/icons/darent_logo.png",
                          width: widthSpace(30)),
                      CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .saveExit,
                          fontSize: 2,
                          horizontalPadding: 2,
                          onPressed: () => c.submitPrice(isExit: true),
                          backgroundBg: Colors.white.withOpacity(0.2),
                          buttonThemeColor: Colors.black),
                    ],
                  ),
                ),
                SizedBox(height: heightSpace(5)),
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .setThePrice,
                    size: 2.4,
                    maxlines: 2,
                    weight: FontWeight.w500,
                    textAlign: TextAlign.center),
                // removed by Asim as per task assigned by Tooba
                // SizedBox(height: heightSpace(2)),
                // CustomText(Get.find<TranslationHelper>().translations.listing.keepInMindPlaceRange, size: 2.2,maxlines: 2, color: Color(greyText),textAlign: TextAlign.center),
                SizedBox(height: heightSpace(3)),
              ],
              for (String priceField in ViewsCommon.priceControllers.keys) ...[
                CustomText(
                    priceField == "price"
                        ? Get.find<TranslationHelper>()
                            .translations
                            .listing
                            .pricePerNight
                        : "${Get.find<TranslationHelper>().translations.listing.priceOn} ${Get.find<TranslationHelper>().translations.general.toJson()[priceField]}",
                    size: 2.5,
                    weight: FontWeight.bold),
                SizedBox(height: heightSpace(2.5)),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () => c.minusPrice(
                            ViewsCommon.priceControllers[priceField]!,
                            minAmount: 5),
                        child: Container(
                          padding: const EdgeInsets.all(5),
                          decoration: BoxDecoration(
                              border: Border.all(), shape: BoxShape.circle),
                          child: Icon(Icons.remove, size: widthSpace(6)),
                        ),
                      ),
                      Container(
                          width: widthSpace(60),
                          height: heightSpace(12),
                          padding: EdgeInsets.only(left: widthSpace(5)),
                          decoration: BoxDecoration(
                              border: Border.all(color: const Color(greyText)),
                              borderRadius:
                                  BorderRadius.circular(heightSpace(3))),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              const SizedBox(),
                              SizedBox(
                                  width: widthSpace(20),
                                  child: TextField(
                                      controller: ViewsCommon
                                          .priceControllers[priceField]!,
                                      textAlign: TextAlign.right,
                                      style:
                                          TextStyle(fontSize: heightSpace(4.5)),
                                      decoration: const InputDecoration(
                                          border: InputBorder.none),
                                      keyboardType: TextInputType.number,
                                      inputFormatters: [
                                        LengthLimitingTextInputFormatter(4),
                                        FilteringTextInputFormatter.digitsOnly
                                      ])),
                              SizedBox(width: widthSpace(2)),
                              const CustomText("SAR",
                                  size: 2.8, color: Color(greyText)),
                            ],
                          )),
                      InkWell(
                          onTap: () => c.plusPrice(
                              ViewsCommon.priceControllers[priceField]!),
                          child: Container(
                            padding: const EdgeInsets.all(5),
                            decoration: BoxDecoration(
                                border: Border.all(), shape: BoxShape.circle),
                            child: Icon(Icons.add, size: widthSpace(6)),
                          )),
                    ]),
                SizedBox(height: heightSpace(3)),
              ],
            ],
          )),
      bottomNavigationBar: c.isEdit
          ? null
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                stepper(3, width: 13.2),
                Container(
                  margin: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: widthSpace(3)),
                  child: Obx(
                    () => Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonButton(
                              onPressed: () {
                                ViewsCommon.clearPriceControllers();
                                Get.back();
                              },
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .listingDescription
                                  .back,
                              isBorder: true),
                          CommonButton(
                              title: c.isEdit
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .usersProfile
                                      .save
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .listingBasic
                                      .next,
                              isLoading: c.isLoading.isTrue,
                              horizontalPadding: 7,
                              onPressed: c.submitPrice)
                        ]),
                  ),
                ),
              ],
            ),
    );
  }
}
