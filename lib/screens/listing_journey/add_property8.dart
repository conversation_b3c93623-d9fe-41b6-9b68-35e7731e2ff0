import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class AddProperty8 extends StatelessWidget {
  const AddProperty8({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    PropertyController c = Get.find();
    return Scaffold(
      appBar: c.isEdit
          ? null
          : AppBar(
              elevation: 0.0,
              automaticallyImplyLeading: false,
              toolbarHeight: 120,
              flexibleSpace: Padding(
                padding: EdgeInsets.only(
                    left: widthSpace(6),
                    right: widthSpace(6),
                    top: heightSpace(3.8)),
                child: <PERSON><PERSON><PERSON><PERSON>(
                  height: heightSpace(48),
                  child: Directionality(
                    textDirection: TextDirection.ltr,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Image.asset(
                          "assets/icons/darent_logo.png",
                          width: widthSpace(30),
                        ),
                        CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .saveExit,
                          fontSize: 2,
                          horizontalPadding: 2,
                          onPressed: () => c.submitDescription(isExit: true),
                          backgroundBg: Colors.white.withOpacity(0.2),
                          buttonThemeColor: Colors.black,
                        ),
                      ],
                    ),
                  ),
                ),
              )),
      body: ListView(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(6), vertical: heightSpace(2)),
          children: [
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .listing
                    .writeDescriptionOfDwelling,
                size: 2.4,
                maxlines: 2,
                weight: FontWeight.w500,
                textAlign: TextAlign.center),
            SizedBox(height: heightSpace(2)),
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .listing
                    .addDescriptionNote,
                size: 2.2,
                maxlines: 2,
                color: Color(greyText),
                textAlign: TextAlign.center),
            SizedBox(height: heightSpace(3)),
            Obx(
              () => Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextFormField(
                        controller: c.summaryAr,
                        maxLines: 4,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(1000)
                        ],
                        onChanged: (text) =>
                            c.desCharCountArabic.value = text.length,
                        validator: (val) => val!.trim().isEmpty
                            ? Get.find<TranslationHelper>()
                                .translations
                                .jqueryValidation
                                .required
                            : null,
                        decoration: InputDecoration(
                            hintText: "الوصف بالعربية",
                            hintStyle: const TextStyle(
                                color: Color(greyText), fontSize: 15),
                            errorText: c.descriptionErrorAr.value,
                            filled: true,
                            fillColor: Colors.white,
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(20),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: 1)),
                            enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(20),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: 1)))),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ElevatedButton.icon(
                            onPressed: () {
                              c.translate("en", c.summaryAr, c.summary,
                                  c.desCharCountArabic);
                            },
                            icon: const Icon(Icons.g_translate, size: 18),
                            label: Text(Get.find<TranslationHelper>()
                                    .translations
                                    .listing
                                    .translateToEnglish ??
                                "Translate to English")),
                        Obx(() =>
                            CustomText('${c.desCharCountArabic.value} /1000'))
                      ],
                    ),
                    SizedBox(height: heightSpace(3)),
                    TextFormField(
                        controller: c.summary,
                        maxLines: 4,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(1000)
                        ],
                        onChanged: (text) => c.desCharCount.value = text.length,
                        decoration: InputDecoration(
                            hintText: Get.find<TranslationHelper>()
                                .translations
                                .photoDetails
                                .description,
                            hintStyle: const TextStyle(
                                color: Color(greyText), fontSize: 15),
                            errorText: c.descriptionError.value,
                            filled: true,
                            fillColor: Colors.white,
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(20),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: 1)),
                            enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(20),
                                borderSide: const BorderSide(
                                    color: Colors.grey, width: 1)))),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ElevatedButton.icon(
                            onPressed: () {
                              c.translate(
                                  "ar", c.summary, c.summaryAr, c.desCharCount);
                            },
                            icon: const Icon(
                              Icons.g_translate,
                              size: 18,
                            ),
                            label: Text(Get.find<TranslationHelper>()
                                    .translations
                                    .listing
                                    .translateToArabic ??
                                "Translate to Arabic")),
                        Obx(() => CustomText('${c.desCharCount.value} /1000'))
                      ],
                    ),
                  ]),
            )
          ]),
      bottomNavigationBar: c.isEdit
          ? null
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                stepper(2, width: 27.5),
                Container(
                  margin: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(viewPadding),
                      vertical: widthSpace(3)),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonButton(
                            onPressed: () => Get.back(),
                            title: Get.find<TranslationHelper>()
                                .translations
                                .listingDescription
                                .back,
                            isBorder: true),
                        Obx(() => CommonButton(
                            title: c.isEdit
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .usersProfile
                                    .save
                                : Get.find<TranslationHelper>()
                                    .translations
                                    .listingBasic
                                    .next,
                            isLoading: c.isLoading.isTrue,
                            horizontalPadding: 7,
                            onPressed: c.submitDescription))
                      ]),
                ),
              ],
            ),
    );
  }
}
