import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/floating_text_field.dart';
import 'package:darent/helperMethods/listing_helper/listing_helper.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/models/filterModel.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl_phone_field/countries.dart';
import 'package:intl_phone_field/intl_phone_field.dart';

class PropertyManagementRegistrationScreen extends StatefulWidget {
  const PropertyManagementRegistrationScreen({super.key});

  @override
  State<PropertyManagementRegistrationScreen> createState() =>
      _PropertyManagementRegistrationScreenState();
}

class _PropertyManagementRegistrationScreenState
    extends State<PropertyManagementRegistrationScreen> {
  @override
  void initState() {
    emailController.text = userModel.value?.email ?? "";
    contactController.text =
        (userModel.value?.phone ?? "").replaceAll("+966", "");
    super.initState();
  }

  // Controllers for form fields
  final TextEditingController nameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController propertyController = TextEditingController();
  final TextEditingController unitController = TextEditingController();
  final TextEditingController districtController = TextEditingController();
  final TextEditingController contactController = TextEditingController();

  // Form key for validation
  final _formKey = GlobalKey<FormState>();

  // Selected city
  InitialCities? selectedCity;

  Country phoneCountry = countries.firstWhere((e) => e.code == 'SA');

  // Common input decoration for text fields
  InputDecoration _inputDecoration(String label) => InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderSide: const BorderSide(color: Color(greyBorder)),
          borderRadius: BorderRadius.circular(10),
        ),
        errorBorder: OutlineInputBorder(
          borderSide: const BorderSide(color: Color(greyBorder)),
          borderRadius: BorderRadius.circular(10),
        ),
      );

  // Common text field widget
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String translationKey,
    required String? Function(String?) validator,
    input = TextInputType.name,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          translationKey,
          color: Colors.black,
          weight: FontWeight.normal,
          size: 2.2,
        ),
        SizedBox(height: heightSpace(1.5)),
        FloatingTextField(
          controller: controller,
          labelText: label,
          fontSize: 2.5,
          inputType: input,
          fontWeight: FontWeight.normal,
          borderType: OutlineInputBorder(
            borderSide: const BorderSide(color: Color(greyBorder)),
            borderRadius: BorderRadius.circular(10),
          ),
          errorBorder: OutlineInputBorder(
            borderSide: const BorderSide(color: Color(greyBorder)),
            borderRadius: BorderRadius.circular(10),
          ),
          validator: validator,
        ),
        SizedBox(height: heightSpace(3)),
      ],
    );
  }

  // City dropdown widget
  Widget _buildCityDropdown() {
    final translation = Get.find<TranslationHelper>().translations;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          translation.property.city,
          color: Colors.black,
          weight: FontWeight.normal,
          size: 2.2,
        ),
        SizedBox(height: heightSpace(1.5)),
        DropdownButtonFormField<InitialCities>(
          decoration: _inputDecoration(translation.property.city),
          value: selectedCity,
          items: SearchHelper.c.filters.value!.cities
              .map((city) => DropdownMenuItem(
                    value: city,
                    child: Text(city.name),
                  ))
              .toList(),
          onChanged: (value) {
            setState(() {
              selectedCity = value;
            });
          },
          validator: (value) =>
              value == null ? translation.jqueryValidation.required : null,
        ),
        SizedBox(height: heightSpace(3)),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final translation = Get.find<TranslationHelper>().translations;

    return Scaffold(
      body: Form(
        key: _formKey,
        child: ListView(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(viewPadding),
            vertical: heightSpace(viewPadding)
          ),
          children: [
            // Header with close button
            SizedBox(
              height: heightSpace(10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    onPressed: Get.back,
                    icon: const Icon(Icons.clear),
                  ),
                ],
              ),
            ),

            // Title and subtitle
            CustomText(
              translation.listing.manageTitle,
              color: Colors.black,
              weight: FontWeight.w500,
              size: 2.5,
            ),
            SizedBox(height: heightSpace(1)),
            CustomText(
              translation.listing.manageSubTitle,
              color: Colors.black,
              weight: FontWeight.normal,
              size: 2.3,
            ),
            SizedBox(height: heightSpace(3)),

            // Form fields
            _buildTextField(
              controller: nameController,
              label: translation.listing.enterFullName ?? "",
              translationKey: translation.listing.fullName ?? "",
              validator: (value) =>
                  value!.isEmpty ? translation.jqueryValidation.required : null,
            ),
            _buildTextField(
              controller: emailController,
              label: translation.account.email,
              translationKey: translation.account.email,
              validator: (value) {
                if (value!.isEmpty) {
                  return translation.jqueryValidation.required;
                }
                if (!value.isEmail) {
                  return translation.jqueryValidation.email;
                }
                return null;
              },
            ),
            _buildTextField(
              controller: propertyController,
              label: translation.listing.propertyTitle ?? "",
              translationKey: translation.listing.propertyTitle ?? "",
              validator: (value) =>
                  value!.isEmpty ? translation.jqueryValidation.required : null,
            ),
            _buildTextField(
              controller: unitController,
              input: TextInputType.number,
              label: translation.listing.noOfUnit ?? "",
              translationKey: translation.listing.noOfUnit ?? "",
              validator: (value) =>
                  value!.isEmpty ? translation.jqueryValidation.required : null,
            ),
            _buildCityDropdown(),
            _buildTextField(
              controller: districtController,
              label: translation.listing.district ?? "",
              translationKey: translation.listing.district ?? "",
              validator: (value) =>
                  value!.isEmpty ? translation.jqueryValidation.required : null,
            ),

            // Contact field
            CustomText(
              translation.listing.contact,
              color: Colors.black,
              weight: FontWeight.normal,
              size: 2.2,
            ),
            SizedBox(height: heightSpace(1.5)),
            IntlPhoneField(
              controller: contactController,
              initialCountryCode: phoneCountry.code,
              onCountryChanged: (value) {
                phoneCountry = value;
                contactController.clear();
              },
              decoration: _inputDecoration(''),
              countries: Get.find<RemoteConfig>().fieldCountries.isNotEmpty
                  ?Get.find<RemoteConfig>().fieldCountries
                  :countries.where((i)=>i.code=='SA').toList(),
              validator: (phone) => phone!.completeNumber.isEmpty
                  ? translation.jqueryValidation.required
                  : null,
            ),
            SizedBox(height: heightSpace(3)),

            // Submit button
            Obx(
              () => CommonButton(
                title: translation.listing.submit,
                backgroundBg: const Color(themeColor),
                horizontalPadding: 7,
                verticalPadding: 4,
                isLoading: ListingHelper.c.isDarStayLoading.value,
                onPressed: () {
                  if (_formKey.currentState!.validate() &&
                      !ListingHelper.c.isDarStayLoading.value &&
                      selectedCity != null) {
                    Map form = {
                      "owner_name": nameController.text,
                      "email": emailController.text,
                      "property_name": propertyController.text,
                      "units": unitController.text,
                      "location": districtController.text,
                      "city_id": selectedCity?.id,
                      "contact_no":
                          "+${phoneCountry.dialCode}${contactController.text.trim()}",
                    };
                    ListingHelper.submitDarStay(formData: form).then((value) {
                      if(value){
                        Get.until((route) => route.isFirst);
                      }
                    });
                  }
                },
              ),
            ),
            SizedBox(height: heightSpace(3)),

            // Info section
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.all(widthSpace(0.5)),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color(0xff36BFFA),
                  ),
                  child: Icon(
                    Icons.question_mark,
                    color: Colors.white,
                    size: widthSpace(6),
                  ),
                ).paddingSymmetric(horizontal: widthSpace(2)),
                SizedBox(
                  width: widthSpace(76),
                  child: CustomText(
                    translation.listing.willContact,
                    color: Colors.black,
                    weight: FontWeight.normal,
                    size: 2.1,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}