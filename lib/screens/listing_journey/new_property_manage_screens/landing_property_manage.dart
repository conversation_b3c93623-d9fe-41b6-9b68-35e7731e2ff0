import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/listing_helper/listing_helper.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/listing_journey/new_property_manage_screens/property_management_registration.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class LandingPageOfPropertyManage extends StatelessWidget {
  const LandingPageOfPropertyManage({super.key});

  @override
  Widget build(BuildContext context) {
    final translation = Get.find<TranslationHelper>().translations.listing;

    return Scaffold(
      body: ListView(
        children: [
          // Header Logo
          SizedBox(
            height: heightSpace(10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Image.asset(
                  "assets/icons/darent_logo.png",
                  width: widthSpace(20),
                ),
              ],
            ),
          ).paddingSymmetric(horizontal: widthSpace(6)),

          // Main Content
          Container(
            padding: EdgeInsets.symmetric(horizontal: widthSpace(8)),
            decoration: BoxDecoration(
              color: const Color(themeColor).withValues(alpha: 0.09),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: heightSpace(4)),
                // Background Image
                Center(
                  child: Image.asset(
                    "assets/icons/darent_back_logo.png",
                    width: widthSpace(65),
                    height: heightSpace(30),
                  ),
                ),
                // Title
                RichText(
                  text: TextSpan(
                    text: translation.landingManageTitle!
                        .replaceAll("listing and start welcoming guests", ""),
                    style: TextStyle(
                      fontSize: heightSpace(3.1),
                      color: Colors.black,
                      fontWeight: FontWeight.w500,
                    ),
                    children: [
                      TextSpan(
                        text: translation.landingManageTitle!
                            .replaceAll("Complete your ", ""),
                        style: TextStyle(
                          fontSize: heightSpace(3.3),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                // Tick Icon
                SizedBox(
                  height: heightSpace(6),
                  child: SvgPicture.asset(
                    "assets/icons/tick.svg",
                    width: widthSpace(45),
                  ),
                ),
                // Subtitle
                CustomText(
                  translation.landingManageSubTitle,
                  color: Colors.black,
                  weight: FontWeight.normal,
                  size: 2.1,
                ),
                // List Property Button
                Padding(
                  padding: EdgeInsets.only(top: heightSpace(1)),
                  child: CommonButton(
                      title: translation.landingListProperty,
                      backgroundBg: const Color(themeColor),
                      horizontalPadding: 7,
                      onPressed: () {
                        // here when moving to normal listing then
                        // 1st set the listing helper variable to false
                        ListingHelper.c.isPropertyManageByDarentEnabled.value = false;
                        // 2nd move to back
                        Get.back();
                        // 3rd going to normal listing
                        ListingHelper.gotoNewListing();
                        // Get.to(() => const AddProperty2())!.then((_) {
                        //   c.spaceType.value = null;
                        // });
                      }),
                ),
                SizedBox(height: heightSpace(5)),
              ],
            ),
          ),

          // Bottom Card
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: heightSpace(3),
              horizontal: widthSpace(6),
            ),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: widthSpace(1),
                vertical: widthSpace(4),
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10),
                color: const Color(themeColor).withValues(alpha: 0.09),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildLinkRow(
                    text: translation.landingDoNotManage ?? "",
                    linkText: translation.landingClick ?? "",
                    onTap: showReviewsSheet,
                    isIcon: true,
                  ),
                  SizedBox(height: heightSpace(1)),
                  _buildLinkRow(
                    text: translation.landingHandleEveryThing ?? "",
                    linkText: translation.landingHandlePropertyManagement ?? "",
                    onTap: () =>
                        Get.to(() => PropertyManagementRegistrationScreen()),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Reusable link row widget
  Widget _buildLinkRow({
    required String text,
    required String linkText,
    required VoidCallback onTap,
    isIcon = false,
  }) {
    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      children: [
        if (isIcon)
          Container(
            padding: EdgeInsets.all(widthSpace(0.5)),
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              color: Color(themeColor),
            ),
            child: Icon(
              Icons.question_mark,
              color: Colors.white,
              size: widthSpace(4),
            ),
          ).paddingSymmetric(horizontal: widthSpace(1)),
        RichText(
          text: TextSpan(
            text: "$text ",
            style: TextStyle(
              fontSize: heightSpace(1.7),
              color: Colors.black,
            ),
            children: [
              TextSpan(
                text: linkText,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  decoration: TextDecoration.underline,
                ),
                recognizer: TapGestureRecognizer()..onTap = onTap,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Modal Bottom Sheet
  void showReviewsSheet() {
    final translation = Get.find<TranslationHelper>().translations.listing;

    ViewsCommon.showModalBottom(
      DraggableScrollableSheet(
        maxChildSize: 0.45,
        initialChildSize: 0.45,
        expand: false,
        builder: (context, scrollController) => SingleChildScrollView(
          padding: EdgeInsets.all(widthSpace(viewPadding)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Sheet Handle
              Container(
                width: widthSpace(10),
                height: heightSpace(1),
                decoration: BoxDecoration(
                  color: const Color(greyBorder),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              SizedBox(height: heightSpace(2.5)),
              // Title
              CustomText(
                translation.landingSheetTitle,
                color: Colors.black,
                weight: FontWeight.normal,
                size: 2.1,
              ),
              SizedBox(height: heightSpace(2.5)),
              // Options
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: widthSpace(viewPadding / 4),
                  vertical: widthSpace(viewPadding / 2),
                ),
                decoration: BoxDecoration(
                  color: const Color(greyBorder).withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(viewPadding * 2),
                ),
                child: Column(
                  children: [
                    _sheetComponent(
                      title: translation.landingHouseKeeping ?? "",
                      icon: "landing2.svg",
                    ),
                    SizedBox(height: heightSpace(2.5)),
                    _sheetComponent(
                      title: translation.landingOnSiteCord ?? "",
                      icon: "landing1.svg",
                    ),
                  ],
                ),
              ),
              SizedBox(height: heightSpace(2.5)),
              // Request Button
              SizedBox(
                width: widthSpace(90),
                child: CommonButton(
                  title: translation.landingHandlePropertyManagement ?? "",
                  backgroundBg: const Color(themeColor),
                  horizontalPadding: 7,
                  verticalPadding: 4,
                  onPressed: () =>
                      Get.to(() => PropertyManagementRegistrationScreen()),
                ),
              ),
              // Skip Button
              TextButton(
                onPressed: Get.back,
                child: CustomText(
                  translation.landingSkip,
                  color: Colors.black,
                  weight: FontWeight.normal,
                  size: 2.1,
                ),
              ),
            ],
          ),
        ),
      ),
      then: (_) {},
    );
  }

  // Reusable sheet component
  Widget _sheetComponent({required String title, required String icon}) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(widthSpace(1.5)),
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Color(themeColor),
          ),
          child: SvgPicture.asset(
            "assets/icons/$icon",
            width: widthSpace(6),
          ),
        ).paddingSymmetric(horizontal: widthSpace(1)),
        CustomText(
          title,
          color: Colors.black,
          weight: FontWeight.bold,
          size: 2.2,
        ),
      ],
    );
  }
}
