import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/models/filterModel.dart';
import 'package:darent/screens/listing_journey/stepper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../helperMethods/search_helper.dart';

class AddProperty1 extends StatelessWidget {
  final fromDwelling;
  const AddProperty1({Key? key, this.fromDwelling = true}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final c = Get.put(PropertyController(fromDwelling: fromDwelling),
        permanent: true);
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: widthSpace(6)),
        child: ListView(children: [
          SizedBox(
            height: heightSpace(15),
            child: Directionality(
              textDirection: TextDirection.ltr,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Image.asset("assets/icons/darent_logo.png",
                      width: widthSpace(30)),
                  CommonButton(
                    title: Get.find<TranslationHelper>()
                        .translations
                        .listing
                        .saveExit,
                    fontSize: 2,
                    horizontalPadding: 2,
                    backgroundBg: Colors.white.withValues(alpha: 0.2),
                    buttonThemeColor: Colors.black,
                    onPressed: () => c.submitPlaceKind(isExit: true),
                  ),
                ],
              ),
            ),
          ),
          CustomText(
              Get.find<TranslationHelper>()
                  .translations
                  .listing
                  .closestDescriptionOfYourHome,
              size: 2.4,
              maxlines: 2,
              weight: FontWeight.w500,
              textAlign: TextAlign.center),
          Padding(
              padding: EdgeInsets.symmetric(vertical: heightSpace(5)),
              child: Obx(
                () => Column(children: [
                  for (PropertyType item
                      in SearchHelper.c.filters.value?.property_type ?? []) ...[
                    InkWell(
                      onTap: () {
                        c.selectPlaceKind(item);
                      },
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 800),
                        curve: Curves.fastOutSlowIn,
                        margin: EdgeInsets.only(bottom: heightSpace(2)),
                        padding: EdgeInsets.symmetric(
                            horizontal: widthSpace(6), vertical: widthSpace(5)),
                        decoration: BoxDecoration(
                            border: item == c.placeKind.value
                                ? Border.all(color: Colors.black)
                                : Border.all(color: const Color(greyBorder)),
                            borderRadius: BorderRadius.circular(8)),
                        child: Row(mainAxisSize: MainAxisSize.max, children: [
                          Expanded(
                              flex: 2,
                              child: Row(
                                children: [
                                  item.image == null
                                      ? Image.asset(
                                          "assets/icons/apartment.png")
                                      : GlobalHelper.buildNetworkSvgWidget(
                                    url:item.image??"",
                                    width: widthSpace(8),
                                    defaultOption: Image.asset(
                                        "assets/icons/apartment.png"),),
                                ],
                              )),
                          Expanded(
                            flex: 3,
                            child: CustomText(
                                Get.locale?.languageCode == "ar"
                                    ? item.nameAr
                                    : item.name,
                                color: item == c.placeKind.value
                                    ? Colors.black
                                    : Colors.grey[700],
                                weight: FontWeight.w500,
                                size: 2.1),
                          ),
                        ]),
                      ),
                    )
                  ]
                ]),
              ))
        ]),
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          stepper(1, width: 5.5),
          Container(
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(viewPadding), vertical: widthSpace(3)),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonButton(
                      onPressed: Get.back,
                      title: Get.find<TranslationHelper>()
                          .translations
                          .listingDescription
                          .back,
                      isBorder: true),
                  Obx(() => CommonButton(
                      title: Get.find<TranslationHelper>()
                          .translations
                          .listingBasic
                          .next,
                      isLoading: c.isLoading.isTrue,
                      horizontalPadding: 7,
                      onPressed: c.submitPlaceKind))
                ]),
          ),
        ],
      ),
    );
  }
}
