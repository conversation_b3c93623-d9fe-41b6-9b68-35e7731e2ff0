import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/screens/authentication/login.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../helperMethods/translation_helper.dart';

class RequiredLogin extends StatelessWidget {
  final subTitle, description;
  final picture;
  const RequiredLogin(
      {required this.subTitle,
      required this.description,
      required this.picture,
      Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.only(left: 25, right: 25),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          SizedBox(
            height: heightSpace(3.5),
          ),
          CustomText(
            subTitle,
            size: 2.9,
            weight: FontWeight.w500,
          ),
          Sized<PERSON>ox(
            height: heightSpace(1),
          ),
          Divider(),
          CustomText(
            description,
            size: 2,
          ),
          SizedBox(
            height: heightSpace(3),
          ),
          CommonButton(
              title: Get.find<TranslationHelper>().translations.login.login,
              onPressed: () {
                Get.to(() => const Login());
              },
              fontSize: 2.5),
          const Spacer(),
          Align(
              alignment: Alignment.center,
              child: SvgPicture.asset(picture, height: heightSpace(18))),
          // const Spacer(),
          // SizedBox(
          //   width: double.infinity,
          //   child: CommonButton(title: "Login", onPressed: (){
          //     Get.to(()=> const Login());
          //   },
          //   fontSize: 2.5),
          // ),
          SizedBox(
            height: heightSpace(20),
          )
        ]),
      ),
    );
    // SizedBox(
    //   height: heightSpace(5.5),
    //   width: widthSpace(3),
    //   child: TextField(
    //     controller: controller,
    //     textAlign: TextAlign.center,
    //     keyboardType: TextInputType.number,
    //     style: TextStyle(fontSize: heightSpace(1.7)),
    //     // controller: controller,
    //     maxLength: 1,
    //     // cursorColor: Theme.of(context).primaryColor,
    //     scrollPadding: EdgeInsets.zero,
    //     decoration: const InputDecoration(
    //       counterText: "",
    //         // hintStyle: TextStyle(color: Colors.black, fontSize: 20.0)
    //     ),
    //     onChanged: (value) {
    //       if (value.length == 1) {
    //         FocusScope.of(context).nextFocus();
    //       }
    //     },
    //   ),
    // );
  }
}
