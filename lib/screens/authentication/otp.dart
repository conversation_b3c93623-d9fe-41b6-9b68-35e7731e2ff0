import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/routes.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sms_autofill/sms_autofill.dart';
import '../../helperMethods/authHelper.dart';
import '../../helperMethods/translation_helper.dart';
import '../../utils/constants.dart';

class OtpVerification extends StatelessWidget {
  final bool isEmailOtp;
  const OtpVerification({required this.isEmailOtp, Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CustomText(
          isEmailOtp ? "Confirm Your Email" : "Confirm Your Number",
          size: 2.3,
          weight: FontWeight.w500,
        ),
        leading: !isEmailOtp
            ? IconButton(
                icon: const Icon(Icons.arrow_back_ios),
                onPressed: () {
                  Navigator.pop(context);
                  AuthHelper.c.timer?.cancel();
                },
              )
            : IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  Get.offAllNamed(Routes.home);
                  AuthHelper.c.timer?.cancel();
                },
              ),
        automaticallyImplyLeading: isEmailOtp ? false : true,
      ),
      body: Center(
        child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Obx(
              () => Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                      alignment: Alignment.center,
                      width: double.maxFinite,
                      height: heightSpace(8),
                      padding: EdgeInsets.only(
                          bottom: heightSpace(1),
                          left: widthSpace(22),
                          right: widthSpace(22)),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(100),
                          border: Border.all(
                              color: Color(AuthHelper.c.otpError.value != null
                                  ? warningColor
                                  : greyBorder))),
                      child: PinFieldAutoFill(
                          codeLength: 4,
                          decoration: UnderlineDecoration(
                            textStyle: TextStyle(
                                fontSize: heightSpace(2.0),
                                color: Colors.black),
                            gapSpace: 28,
                            lineHeight: 1.5,
                            colorBuilder: FixedColorBuilder(
                                Colors.black.withOpacity(0.3)),
                          ),
                          currentCode: AuthHelper.c.messageOtpCode.value,
                          controller: AuthHelper.c.controller,
                          onCodeSubmitted: (code) {},
                          onCodeChanged: (code) {
                            AuthHelper.c.messageOtpCode.value = code!;
                            if (code.length == 4) {
                              FocusManager.instance.primaryFocus?.unfocus();
                            }
                          })),
                  if (AuthHelper.c.otpError.value != null) ...[
                    Padding(
                      padding: const EdgeInsets.only(left: 14.0),
                      child: CustomText(AuthHelper.c.otpError.value,
                          color: const Color(warningColor)),
                    )
                  ],
                  SizedBox(height: heightSpace(5)),
                  RichText(
                    text: TextSpan(
                        text:
                            "Enter the code sent to ${isEmailOtp ? AuthHelper.c.email.text : AuthHelper.c.phone.text}. If you didn't receive the code, please ${AuthHelper.c.secondsRemaining.value == 0 ? '' : 'wait for'} ",
                        style: TextStyle(
                            color: const Color(greyText),
                            fontSize: heightSpace(1.75)),
                        children: <TextSpan>[
                          TextSpan(
                            text: AuthHelper.c.isLoading.value
                                ? 'Please wait'
                                : AuthHelper.c.secondsRemaining.value == 0
                                    ? "Resend Code"
                                    : "${AuthHelper.c.secondsRemaining} sec",
                            style: TextStyle(
                                color: Colors.blue[900],
                                fontWeight: FontWeight.bold),
                            recognizer: AuthHelper.c.isLoading.value
                                ? null
                                : (TapGestureRecognizer()
                                  ..onTap = AuthHelper.c.enableResend.value
                                      ? isEmailOtp
                                          ? AuthHelper.c.sendEmailCode
                                          : AuthHelper.c.sendCode
                                      : null),
                          ),
                          TextSpan(
                              text: "   ",
                              style: TextStyle(color: Colors.blue[900]),
                              recognizer: TapGestureRecognizer()),
                          isEmailOtp
                              ? TextSpan(
                                  text: "Change Email",
                                  style: TextStyle(
                                      color: Colors.blue[900],
                                      fontWeight: FontWeight.bold),
                                  recognizer: TapGestureRecognizer()
                                    ..onTap = AuthHelper.c.gotoChangeEmail,
                                )
                              : TextSpan(
                                  text: "",
                                  style: TextStyle(
                                      color: Colors.blue[900],
                                      fontWeight: FontWeight.bold),
                                  recognizer: TapGestureRecognizer())
                        ]),
                  ),
                  //  CustomText("${translations?.login.codeSent} ${c.phone.text}",color: const Color(greyText)),
                  // Row(
                  //   children: [
                  //     CustomText(translations?.login.notReceiveCode, color: const Color(greyText)),
                  //     InkWell(
                  //         onTap: c.sendCode,
                  //         child: CustomText(" ${translations?.login.resendCode}",color: Colors.blue[900]))
                  //   ]),
                  // SizedBox(height: heightSpace(3.8)),
                  // SizedBox(
                  //     width: double.maxFinite,
                  //     child: Obx(
                  //       () => CommonButton(
                  //           title: translations?.utility.submit,
                  //           isLoading: c.isLoading.isTrue,
                  //           onPressed: c.submitOtp),
                  //     )),
                  // Container(
                  //     alignment: Alignment.center,
                  //     height: heightSpace(7),
                  //     width: double.maxFinite,
                  //     padding: EdgeInsets.only(bottom: heightSpace(1),left:widthSpace(20),right: widthSpace(20),top: heightSpace(1)),
                  //     decoration: BoxDecoration(
                  //         borderRadius: BorderRadius.circular(100),
                  //         border: Border.all(
                  //             color: Color(c.otpError.value != null
                  //                 ? warningColor
                  //                 : greyBorder))),
                  //     child:PinFieldAutoFill(
                  //       codeLength: 4,
                  //     decoration: UnderlineDecoration(
                  //       textStyle: TextStyle(fontSize: heightSpace(2.0), color: Colors.black),
                  //       gapSpace: 28,lineHeight:1.5,
                  //       colorBuilder: FixedColorBuilder(Colors.black.withOpacity(0.3)),
                  //     ),
                  //     currentCode: c.messageOtpCode.value,
                  //     controller: c.controller,
                  //     onCodeSubmitted: (code) {},
                  //     onCodeChanged: (code) {
                  //       c.messageOtpCode.value = code!;
                  //     })),
                  //      TextFormField(
                  //  // controller: _otpController,
                  //   keyboardType: TextInputType.number,
                  //   inputFormatters: [
                  //   LengthLimitingTextInputFormatter(6),
                  //   ],
                  //   onChanged: (value) {
                  //                 c.updateButtonStateOtp(value.length >= 6);},
                  //   textAlign: TextAlign.center, // Center align the entered value
                  //   style: const TextStyle(fontSize: 24), // Increase the hint text size
                  //   decoration: InputDecoration(
                  //      hintText: " -    -    -    -    -    - ",
                  //      hintStyle: const TextStyle(
                  //          color: Color(greyText), fontSize: 22),
                  //     filled: true,
                  //     fillColor: Colors.white,
                  //     contentPadding: const EdgeInsets.all(10),
                  //     border: OutlineInputBorder(
                  //       borderRadius: BorderRadius.circular(25),
                  //       borderSide:const BorderSide(color: Colors.grey, width: 1),
                  //     ),
                  //     enabledBorder: OutlineInputBorder(
                  //       borderRadius: BorderRadius.circular(25),
                  //       borderSide: BorderSide(color: Colors.grey, width: 1),
                  //     ),
                  //     // Increase the hint text size
                  //   ),
                  // ),
                  SizedBox(
                    height: heightSpace(2),
                  ),
                  //   RichText(
                  //  text: TextSpan(
                  //      text: "Enter the code sent to ${c.phone.text}. If you didn't Receive Code ",
                  //      style: TextStyle(
                  //          color: const Color(greyText),
                  //          fontSize: heightSpace(1.75)),
                  //      children: <TextSpan>[
                  //        TextSpan(
                  //            text: "Resend Code",
                  //            style: TextStyle(color: Colors.blue[900]),
                  //            recognizer: TapGestureRecognizer()
                  //     ..onTap = c.sendCode,
                  //            )]
                  //            ),
                  //             ),
                  //             SizedBox(height: heightSpace(4),)
                ],
              ),
            )),
      ),
      bottomNavigationBar: Container(
        height: heightSpace(12),
        padding: const EdgeInsets.only(left: 25, right: 25),
        child:
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          InkWell(
            onTap: () {
              // Get.to(() => const Login());
            },
            child: const CustomText(" ", //"More Options",
                weight: FontWeight.w500,
                underline: true,
                size: 2.1),
          ),
          SizedBox(
              height: heightSpace(6),
              child: Obx(
                () => CommonButton(
                    title: Get.find<TranslationHelper>()
                        .translations
                        .utility
                        .submit,
                    isLoading: AuthHelper.c.isLoading.value,
                    horizontalPadding: 6,
                    onPressed: isEmailOtp
                        ? AuthHelper.c.submitEmailOtp
                        : AuthHelper.c.submitOtp),
              ))
        ]),
      ),
    );
  }
}
