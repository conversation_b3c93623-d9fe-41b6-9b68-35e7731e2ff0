import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl_phone_field/countries.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import '../../helperMethods/authHelper.dart';
import '../../helperMethods/remote_config.dart';
import '../../helperMethods/translation_helper.dart';
import '../../utils/constants.dart';

class LoginSignUp extends StatelessWidget {
  const LoginSignUp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const CustomText(
            "",
            size: 2.3,
            weight: FontWeight.w500,
          ),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/icons/darent_logo.png',
                    width: widthSpace(50),
                  ),
                  SizedBox(
                    height: heightSpace(12),
                  ),
                  Form(
                      key: AuthHelper.c.signUpFormKey,
                      child: Directionality(
                        textDirection: TextDirection.ltr,
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              CustomTextField(
                                  hint: Get.find<TranslationHelper>()
                                      .translations
                                      .signUp
                                      .firstName,
                                  controller: AuthHelper.c.firstName,
                                  textCapitalization: TextCapitalization.words,
                                  validator: (String? val) => val!.isEmpty
                                      ? Get.find<TranslationHelper>()
                                          .translations
                                          .jqueryValidation
                                          .required
                                      : null,
                                  isRoundedBorder: true),
                              SizedBox(height: heightSpace(2)),
                              CustomTextField(
                                  hint: Get.find<TranslationHelper>()
                                      .translations
                                      .signUp
                                      .lastName,
                                  controller: AuthHelper.c.lastName,
                                  textCapitalization: TextCapitalization.words,
                                  validator: (String? val) => val!.isEmpty
                                      ? Get.find<TranslationHelper>()
                                          .translations
                                          .jqueryValidation
                                          .required
                                      : null,
                                  isRoundedBorder: true),
                              SizedBox(height: heightSpace(2)),

                              Directionality(
                                textDirection: TextDirection.ltr,
                                child: IntlPhoneField(
                                    controller: AuthHelper.c.phone,
                                    initialCountryCode:
                                        AuthHelper.c.phoneCountry.code,
                                    autovalidateMode: AutovalidateMode.disabled,
                                    enabled: false,
                                    onCountryChanged: (value) {
                                      AuthHelper.c.phoneCountry = value;
                                      AuthHelper.c.isButtonEnabled.value =
                                          false;
                                      AuthHelper.c.phone.clear();
                                    },
                                    countries: Get.find<RemoteConfig>().fieldCountries.isNotEmpty
                                        ?Get.find<RemoteConfig>().fieldCountries
                                        :countries.where((i)=>i.code=='SA').toList(),
                                    decoration: InputDecoration(
                                        contentPadding:
                                            const EdgeInsets.all(15),
                                        border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(25),
                                            borderSide: const BorderSide(
                                                color: Color.fromARGB(
                                                    255, 243, 243, 243))),
                                        enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(25),
                                            borderSide:
                                                const BorderSide(color: Color.fromARGB(255, 243, 243, 243))))),
                              ),
                              // TextFormField(
                              //     controller: AuthHelper.c.phone,
                              //     validator: (val) => val!.isEmpty
                              //         ? Get.find<TranslationHelper>().translations.jqueryValidation.required
                              //         : null,
                              //     readOnly: true,
                              //     keyboardType: TextInputType.phone,
                              //     inputFormatters: [
                              //       LengthLimitingTextInputFormatter(12),
                              //     ],
                              //     style: const TextStyle(color: Color(greyText)),
                              //     decoration: InputDecoration(
                              //         hintText:
                              //             Get.find<TranslationHelper>().translations.usersProfile.phone,
                              //         hintStyle: const TextStyle(
                              //             color: Color(greyText), fontSize: 15),
                              //         filled: true,
                              //         fillColor: const Color(greyBorder),
                              //         prefixIcon: Container(
                              //           padding:
                              //               const EdgeInsets.only(left: 20),
                              //           child: Row(
                              //             mainAxisSize: MainAxisSize.min,
                              //             children: [
                              //               AuthHelper.c.phoneCode.value != null
                              //                   ? CustomText(
                              //                 "${AuthHelper.c.phoneCode.value!.shortName} (+${AuthHelper.c.phoneCode.value!.phoneCode})",
                              //               )
                              //               : const CustomText(
                              //                 "KSA (+966)",
                              //               ),
                              //               const SizedBox(
                              //                   height: 23,
                              //                   child: VerticalDivider(
                              //                       color: Color(greyBorder)))
                              //             ],
                              //           ),
                              //         ),
                              //         contentPadding: const EdgeInsets.all(10),
                              //         border: OutlineInputBorder(
                              //           borderRadius: BorderRadius.circular(25),
                              //           borderSide: const BorderSide(
                              //               color: Color(greyBorder), width: 1),
                              //         ),
                              //         focusedBorder: OutlineInputBorder(
                              //             borderRadius:
                              //                 BorderRadius.circular(25),
                              //             borderSide: const BorderSide(
                              //                 color: Color(greyBorder),
                              //                 width: 1)),
                              //         enabledBorder: OutlineInputBorder(
                              //             borderRadius:
                              //                 BorderRadius.circular(25),
                              //             borderSide: const BorderSide(
                              //                 color: Color(greyBorder),
                              //                 width: 1)))),

                              SizedBox(height: heightSpace(2)),
                              Obx(() => CustomTextField(
                                    suffixIcon: AuthHelper
                                            .c.isLoadingEmail.value
                                        ? const SizedBox(
                                            height: 15,
                                            width: 15,
                                            child: CircularProgressIndicator(
                                                color: Color(themeColor),
                                                strokeWidth: 1))
                                        // : !c.isEmailExist.value
                                        //     ? const SizedBox(
                                        //         height: 25,
                                        //         width: 20,
                                        //         child: Icon(Icons.check,
                                        //             color: Color(themeColor),
                                        //             size: 20.0),
                                        //       )
                                        //     : c.isEmailExist.value
                                        //         ? const SizedBox(
                                        //             height: 25,
                                        //             width: 20,
                                        //             child: Icon(Icons.cancel,
                                        //                 color: Colors.red,
                                        //                 size: 20.0),
                                        //           )
                                        : null,
                                    focusNode: AuthHelper.c.focusNode,
                                    hint:
                                        "${Get.find<TranslationHelper>().translations.usersProfile.emailAddress} (Optional)",
                                    controller: AuthHelper.c.email,
                                    errorText: AuthHelper.c.isEmailExist.value
                                        ? "Email already Exist..."
                                        : null,
                                    validator: (val) =>
                                        val.isEmpty || GetUtils.isEmail(val)
                                            ? null
                                            : Get.find<TranslationHelper>()
                                                .translations
                                                .jqueryValidation
                                                .email,
                                    inputType: TextInputType.emailAddress,
                                    isRoundedBorder: true,
                                    onFieldSubmitted: (val) {
                                      if (GetUtils.isEmail(val)) {
                                        AuthHelper.c.onSubmitEmailCheck();
                                      }
                                    },
                                  )),
                              SizedBox(height: heightSpace(2)),
                              Obx(
                                () => CustomTextField(
                                    hint: Get.find<TranslationHelper>()
                                        .translations
                                        .signUp
                                        .password,
                                    controller: AuthHelper.c.passwordLogin,
                                    // validator: (val) => val.isEmpty
                                    //     ? Get.find<TranslationHelper>().translations
                                    //         ?.jqueryValidation.required
                                    //     : null,
                                    suffix: IconButton(
                                      onPressed: AuthHelper.c.showHidePassword,
                                      icon: Icon(
                                          AuthHelper.c.isPassVisible.value
                                              ? Icons.visibility_off
                                              : Icons.visibility),
                                    ),
                                    isPassword:
                                        AuthHelper.c.isPassVisible.value,
                                    isRoundedBorder: true),
                              ),

                              // if (c.loginMode.value == "register") ...[
                              // SizedBox(height: heightSpace(2)),
                              // Obx(
                              //   () => CustomTextField(
                              //       hint: Get.find<TranslationHelper>().translations?.login.confirmPassword,
                              //       // validator: (val) => val.isEmpty
                              //       //     ? Get.find<TranslationHelper>().translations
                              //       //         ?.jqueryValidation.required
                              //       //     : null,
                              //       suffix: IconButton(
                              //         onPressed: c.showHideConfirmPassword,
                              //         icon: Icon(c.isConfPassVisible.value
                              //             ? Icons.visibility_off
                              //             : Icons.visibility),
                              //       ),
                              //       isPassword: c.isConfPassVisible.value,
                              //       isRoundedBorder: true),
                              // ),

                              // c.loginMode.value == "register"

                              // TextButton(
                              //     onPressed: Get.back,
                              //     //c.loginMode.value = "email",
                              //     child: RichText(
                              //       text: TextSpan(
                              //         text: "${Get.find<TranslationHelper>().translations?.signUp.alreadyHave}  ",
                              //         style: TextStyle(
                              //           fontSize: heightSpace(2),
                              //           color: Colors.black87,
                              //           // fontWeight:FontWeight.w500,
                              //         ),
                              //         children: <TextSpan>[
                              //           TextSpan(
                              //               text: Get.find<TranslationHelper>().translations?.signUp.login,
                              //               style: TextStyle(
                              //                   fontSize: heightSpace(1.95),
                              //                   color: const Color(themeColor),
                              //                   fontWeight: FontWeight.bold,
                              //                   decoration:
                              //                       TextDecoration.underline)),
                              //         ],
                              //       ),
                              //     ))
                              // : TextButton(
                              //     onPressed: () =>
                              //         c.loginMode.value = "register",
                              //     child: RichText(
                              //       text: TextSpan(
                              //         text: "${Get.find<TranslationHelper>().translations?.login.doNotHaveAnAccount}  ",
                              //         style: TextStyle(
                              //           fontSize: heightSpace(2),
                              //           color: Colors.black87,
                              //           // fontWeight:FontWeight.w500,
                              //         ),
                              //         children: <TextSpan>[
                              //           TextSpan(
                              //               text: Get.find<TranslationHelper>().translations?.login.register,
                              //               style: TextStyle(
                              //                   fontSize: heightSpace(1.95),
                              //                   color:const Color(themeColor),
                              //                   fontWeight: FontWeight.bold,
                              //                   decoration: TextDecoration
                              //                       .underline)),
                              //         ],
                              //       ),
                              //     )),
                              // SizedBox(height: heightSpace(4)),
                            ]),
                      )),
                  SizedBox(
                    height: heightSpace(2),
                  ),
                  SizedBox(
                      width: double.infinity,
                      child: Obx((() => CommonButton(
                          title: Get.find<TranslationHelper>()
                              .translations
                              .utility
                              .submit,
                          isLoading: AuthHelper.c.isLoading.value,
                          onPressed: AuthHelper.c.onSubmitSignUp)))),
                ],
              ),
            ),
          ),
        ));
  }
}
