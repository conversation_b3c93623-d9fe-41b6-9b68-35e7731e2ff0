import 'package:darent/components/common_button.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/countries.dart';
import 'package:get/get.dart';
import '../../components/custom_text.dart';
import '../../helperMethods/authHelper.dart';
import '../../helperMethods/remote_config.dart';
import '../../helperMethods/translation_helper.dart';
import '../../utils/constants.dart';

class Login extends StatelessWidget {
  const Login({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            onPressed: Get.back,
            icon: const Icon(Icons.clear),
            iconSize: 30,
            color: Colors.black.withOpacity(0.7),
          ),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: SingleChildScrollView(
              child: Obx(
                () => Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Image.asset(
                        'assets/icons/darent_logo.png',
                        width: widthSpace(50),
                      ),
                    ),
                    SizedBox(height: heightSpace(10)),
                    Directionality(
                      textDirection: TextDirection.ltr,
                      child: IntlPhoneField(
                          controller: AuthHelper.c.phone,
                          initialCountryCode: AuthHelper.c.phoneCountry.code,
                          onCountryChanged: (value) {
                            AuthHelper.c.phoneCountry = value;
                            AuthHelper.c.isButtonEnabled.value = false;
                            AuthHelper.c.phone.clear();
                          },
                          onChanged: (value) {
                            if(AuthHelper.c.emailError.value != null){
                              AuthHelper.c.emailError.value = null;
                            }
                            AuthHelper.c.isButtonEnabled.value =
                                value.number.length >=AuthHelper.c.phoneCountry.minLength &&
                                    value.number.length <=AuthHelper.c.phoneCountry.maxLength;
                          },
                          countries: Get.find<RemoteConfig>().fieldCountries.isNotEmpty
                              ?Get.find<RemoteConfig>().fieldCountries
                              :countries.where((i)=>i.code=='SA').toList(),
                    )),
                    if (AuthHelper.c.emailError.value != null)
                      CustomText(AuthHelper.c.emailError.value,
                          weight: FontWeight.w500, color: const Color(warningColor)),
                    SizedBox(height: heightSpace(2)),
                    RichText(
                      text: TextSpan(
                          text:
                              "${Get.find<TranslationHelper>().translations.signUp.bySubmittingYourNumber} ",
                          style: TextStyle(
                              color: const Color(greyText),
                              fontSize: heightSpace(1.75)),
                          children: <TextSpan>[
                            TextSpan(
                                text: Get.find<TranslationHelper>()
                                    .translations
                                    .signUp
                                    .termCondition,
                                style: TextStyle(color: Colors.blue[900]),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () => AuthHelper.c.openWebView(
                                      "term_condition",
                                      Get.find<TranslationHelper>()
                                              .translations
                                              .accountMobile
                                              .termsAndConditions ??
                                          'Terms and conditions'))
                          ]),
                    ),
                    SizedBox(height: heightSpace(2)),
                    SizedBox(
                        width: double.maxFinite,
                        child: CommonButton(
                            title: Get.find<TranslationHelper>()
                                .translations
                                .property
                                .continuE,
                            isLoading: AuthHelper.c.isLoading.value,
                            backgroundBg: AuthHelper.c.isButtonEnabled.value
                                ? const Color(themeColor)
                                : Colors.grey.withOpacity(0.5),
                            onPressed: AuthHelper.c.isButtonEnabled.value
                                ? AuthHelper.c.onSubmitLogin
                                : null)),
                    SizedBox(height: heightSpace(2)),
                    // const CustomText(
                    //   "OR",
                    //   color: Color(themeColor),
                    //   weight: FontWeight.w500,
                    //   size: 2.3,
                    // ),
                    // SizedBox(
                    //   height: heightSpace(2),
                    // ),
                    // Obx(
                    //   () => InkWell(
                    //     onTap: AuthHelper.c.isLoadingGoogle.isTrue ? null : AuthHelper.c.googleLogin,
                    //     child: Container(
                    //       padding: EdgeInsets.symmetric(
                    //           horizontal: widthSpace(7), vertical: 10),
                    //       decoration: BoxDecoration(
                    //           border: Border.all(color: const Color(greyBorder)),
                    //           borderRadius: BorderRadius.circular(20)),
                    //       child: Row(mainAxisSize: MainAxisSize.max, children: [
                    //         Expanded(
                    //             flex: 1,
                    //             child: Row(
                    //               children: [
                    //                 Image.asset("assets/icons/google.png",
                    //                     scale: 2),
                    //               ],
                    //             )),
                    //         Expanded(
                    //             flex: 2,
                    //             child: AuthHelper.c.isLoadingGoogle.isTrue
                    //                 ? const Center(
                    //                     child: SizedBox(
                    //                         height: 23,
                    //                         width: 23,
                    //                         child: CircularProgressIndicator()))
                    //                 : CustomText(
                    //                     "${translations?.signUp.loginWith} ${translations?.signUp.google}",
                    //                     size: 2.1,
                    //                     textAlign: TextAlign.start,
                    //                   )),
                    //       ]),
                    //     ),
                    //   ),
                    // ),
                    // SizedBox(height: heightSpace(1)),
                    // if (GetPlatform.isIOS) ...[
                    //   SignInWithAppleButton(
                    //       onPressed: AuthHelper.c.appleSignIn,
                    //       style: SignInWithAppleButtonStyle.whiteOutlined),
                    //   SizedBox(height: heightSpace(1)),
                    // ],
                    // SizedBox(height: heightSpace(1)),
                    // InkWell(
                    //   onTap: () {
                    //     Get.to(() => const LoginEmail());
                    //   },
                    //   child: Container(
                    //     padding: EdgeInsets.symmetric(
                    //         horizontal: widthSpace(7), vertical: 10),
                    //     decoration: BoxDecoration(
                    //         border: Border.all(color: const Color(greyBorder)),
                    //         borderRadius: BorderRadius.circular(20)),
                    //     child: Row(mainAxisSize: MainAxisSize.max, children: [
                    //       Expanded(
                    //           flex: 1,
                    //           child: Row(
                    //             children: [
                    //               Image.asset("assets/icons/inbox.png"),
                    //             ],
                    //           )),
                    //       Expanded(
                    //           flex: 2,
                    //           child: CustomText(
                    //               "${translations?.signUp.loginWith} ${translations?.signUp.email}",
                    //               size: 2.1,
                    //               textAlign: TextAlign.start)),
                    //     ]),
                    //   ),
                    // ),
                  ],
                ),
              ),
            ),
          ),
        ));
  }
}
