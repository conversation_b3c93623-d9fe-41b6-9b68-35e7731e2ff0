import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../helperMethods/authHelper.dart';
import '../../helperMethods/translation_helper.dart';

class ChangeEmail extends StatelessWidget {
  const ChangeEmail({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const CustomText(
            "Change Email",
            size: 2.3,
            weight: FontWeight.w500,
          ),
          leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: const Icon(Icons.clear),
            iconSize: 30,
            color: Colors.black.withOpacity(0.7),
          ),
        ),
        body: Center(
            child: Padding(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/icons/darent_logo.png',
                  width: widthSpace(50),
                ),
                SizedBox(
                  height: heightSpace(10),
                ),
                Form(
                  key: AuthHelper.c.changeEmailKey,
                  child: Directionality(
                      textDirection: TextDirection.ltr,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          CustomTextField(
                              hint: Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .emailAddress,
                              controller: AuthHelper.c.email,
                              validator: (val) => GetUtils.isEmail(val)
                                  ? null
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .jqueryValidation
                                      .email,
                              inputType: TextInputType.emailAddress,
                              isRoundedBorder: true),
                          SizedBox(
                            height: heightSpace(2),
                          ),
                          SizedBox(
                              width: double.infinity,
                              child: Obx((() => CommonButton(
                                  title: Get.find<TranslationHelper>()
                                      .translations
                                      .utility
                                      .submit,
                                  isLoading: AuthHelper.c.isLoading.value,
                                  onPressed:
                                      AuthHelper.c.onSubmitChangeEmail)))),
                        ],
                      )),
                )
              ],
            ),
          ),
        )));
  }
}
