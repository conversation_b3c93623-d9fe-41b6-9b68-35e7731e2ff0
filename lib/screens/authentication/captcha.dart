import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../../utils/constants.dart';

class Captcha extends StatefulWidget{
  Function(JavaScriptMessage) callback;
  Captcha(this.callback);

  @override
  State<StatefulWidget> createState() {
    return CaptchaState();
  }

}
class CaptchaState extends State<Captcha>{
  WebViewController webViewController = WebViewController();
  @override
  initState(){
    webViewController =WebViewController()..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel('Captcha', onMessageReceived: widget.callback)
    ..loadRequest(Uri.parse('$baseUrl/captcha-google-v1'));
    super.initState();
  }


  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: false,
        child: WebViewWidget(
          controller: webViewController,
        )
    );
  }

}