import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/update_helper.dart';
import 'package:darent/screens/authentication/login.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../helperMethods/authHelper.dart';
import '../../helperMethods/translation_helper.dart';

class AccountLogin extends StatelessWidget {
  const AccountLogin({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(widthSpace(viewPadding)),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        CustomText(
          Get.find<TranslationHelper>().translations.footer.profileSubTitle,
          size: 3,
          weight: FontWeight.w500,
        ),
        SizedBox(
          height: heightSpace(1.2),
        ),
        const Divider(),
        CustomText(
          Get.find<TranslationHelper>().translations.footer.profileDescription,
          size: 2.3,
        ),
        SizedBox(height: heightSpace(1)),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            SvgPicture.asset(
              'assets/noLogin.svg',
              height: heightSpace(12),
            ),
          ],
        ),
        SizedBox(height: heightSpace(1)),
        const Divider(),
        SizedBox(height: heightSpace(2)),
        SizedBox(
            height: heightSpace(3.2),
            child: extraComponent("list",
                name: Get.find<TranslationHelper>()
                    .translations
                    .accountMobile
                    .termsAndConditions, function: () {
              AuthHelper.c.openWebView(
                  "term_condition",
                  Get.find<TranslationHelper>()
                      .translations
                      .accountMobile
                      .termsAndConditions);
            })),
        SizedBox(height: heightSpace(0)),
        Divider(height: heightSpace(5)),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Icon(Icons.update),
            SizedBox(width: widthSpace(7.5)),
            Expanded(
                child: CustomText('App Version',
                    size: 2.1,
                    weight: FontWeight.w500,
                    color: Colors.grey[800])),
            CustomText(UpdateHelper.appVersion,
                size: 2.2,
                weight: FontWeight.w500,
                color: const Color(themeColor)),
          ],
        ),
        Divider(height: heightSpace(5)),
        SizedBox(
          width: double.infinity,
          child: CommonButton(
            title: Get.find<TranslationHelper>().translations.login.login,
            onPressed: () {
              Get.to(() => const Login());
            },
          ),
        ),
      ]),
    );
  }

  extraComponent(String image, {required name, Widget? screen, function}) {
    return InkWell(
      onTap: function ??
          () {
            if (screen != null) {
              Get.to(() => screen);
            }
          },
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              "assets/$image.svg",
              width: widthSpace(6),
              color: Colors.black,
            ),
            SizedBox(width: widthSpace(7.5)),
            Expanded(
              child: CustomText(
                name,
                color: Colors.grey[800],
                weight: FontWeight.w500,
                size: 2.1,
              ),
            ),
            SizedBox(width: widthSpace(8)),
            const Icon(Icons.arrow_forward_ios, size: 20)
          ]),
    );
  }
}
