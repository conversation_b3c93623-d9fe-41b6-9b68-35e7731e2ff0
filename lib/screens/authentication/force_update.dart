import 'package:darent/analytics/analytics.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/initial_navigation.dart';
import 'package:darent/helperMethods/update_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:upgrader/upgrader.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../helperMethods/remote_config.dart';

class ForceUpdate extends StatelessWidget {
  const ForceUpdate({super.key});

  @override
  Widget build(BuildContext context) {
    final appMessages = UpdateHelper.packageInfo.determineMessages(context);
    // UpgradeAlert();
    return Scaffold(
      backgroundColor: Colors.grey[200],
      body: Padding(
        padding: EdgeInsets.all(widthSpace(10)),
        child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!Get.find<RemoteConfig>().enableForceUpdate ||
                  kDebugMode ||
                  forceUpdateIgnoreCondition)
                Align(
                  alignment: Alignment.topRight,
                  child: IconButton(
                      onPressed: () => InitialNavigation.checkUserNavigate(
                          fromForceUpdate: true),
                      icon: const Icon(Icons.close)),
                ),
              Center(
                  child: Image.asset('assets/icons/darent_logo.png',
                      width: widthSpace(45))),
              SizedBox(height: heightSpace(10)),
              CustomText(appMessages.message(UpgraderMessage.title),
                  weight: FontWeight.w500, size: 2.1),
              CustomText(UpdateHelper.packageInfo.body(appMessages),
                  size: 1.8, color: Colors.black38, weight: FontWeight.w500),
              SizedBox(height: heightSpace(3)),
              const CustomText(
                  'Get the latest features and improved performance. Download the latest version of the app now.',
                  size: 1.9,
                  color: Color(warningColor),
                  weight: FontWeight.w500),
              GestureDetector(
                onTap: () {
             

                  if (GetPlatform.isAndroid) {
                    launchUrlString(
                        'https://play.google.com/store/apps/details?id=com.darent');
                  } else {
                    launchUrlString(UpdateHelper
                            .packageInfo.versionInfo?.appStoreListingURL ??
                        '');
                  }
                },
                child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 20),
                    margin: EdgeInsets.only(top: heightSpace(2)),
                    decoration: BoxDecoration(
                      color: const Color(themeColor),
                      borderRadius: BorderRadius.circular(9),
                      // border: Border.all()
                    ),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Image.asset(
                              'assets/icons/${GetPlatform.isAndroid}_update.png',
                              height: heightSpace(3)),
                          SizedBox(width: widthSpace(3)),
                          const CustomText('UPDATE',
                              color: Colors.white, weight: FontWeight.bold),
                          const Spacer(),
                          const Icon(Icons.chevron_right, color: Colors.white)
                        ])),
              ),
            ]),
      ),
    );
  }
}
