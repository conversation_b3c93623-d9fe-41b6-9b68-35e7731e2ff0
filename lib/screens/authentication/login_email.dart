import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/screens/authentication/login_SignUp.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../helperMethods/authHelper.dart';
import '../../helperMethods/translation_helper.dart';
import '../../utils/constants.dart';

class LoginEmail extends StatelessWidget {
  const LoginEmail({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Center(
      child: Padding(
        padding: const EdgeInsets.only(left: 20, right: 20, top: 45),
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/icons/darent_logo.png',
                width: widthSpace(50),
              ),
              Sized<PERSON>ox(
                height: heightSpace(12),
              ),
              //fromDetail? c.detailLoginKey:
              Form(
                  key: AuthHelper.c.login<PERSON>orm<PERSON>ey,
                  child: Directionality(
                    textDirection: TextDirection.ltr,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          // if (c.loginMode.value == "register") ...[
                          //   CustomTextField(
                          //       hint: Get.find<TranslationHelper>().translations?.signUp.firstName,
                          //       controller: c.firstName,
                          //       validator: (String? val) =>
                          //           val!.isEmpty ? Get.find<TranslationHelper>().translations?.jqueryValidation.required : null,
                          //       isRoundedBorder: true),
                          //   SizedBox(height: heightSpace(2)),
                          //   CustomTextField(
                          //       hint: Get.find<TranslationHelper>().translations?.signUp.lastName,
                          //       controller: c.lastName,
                          //       validator: (String? val) =>
                          //           val!.isEmpty ? Get.find<TranslationHelper>().translations?.jqueryValidation.required : null,
                          //       isRoundedBorder: true),
                          //   SizedBox(height: heightSpace(2)),
                          //   TextFormField(
                          //       controller: c.phone,
                          //       validator: (val) => val!.isEmpty ? Get.find<TranslationHelper>().translations?.jqueryValidation.required : null,
                          //       keyboardType: TextInputType.phone,
                          //       inputFormatters: [
                          //       LengthLimitingTextInputFormatter(12),
                          //       ],
                          //       decoration: InputDecoration(
                          //           hintText: Get.find<TranslationHelper>().translations?.usersProfile.phone,
                          //           hintStyle: const TextStyle(
                          //               color: Color(greyText), fontSize: 15),
                          //           filled: true,
                          //           fillColor: Colors.white,
                          //           prefixIcon: Container(
                          //             padding: const EdgeInsets.only(left: 20),
                          //             child: Row(
                          //               mainAxisSize: MainAxisSize.min,
                          //               children: const [
                          //                 CustomText("KSA (+966)"),
                          //                 SizedBox(
                          //                     height: 23,
                          //                     child: VerticalDivider(
                          //                         color: Color(greyBorder)))
                          //               ],
                          //             ),
                          //           ),
                          //           contentPadding: const EdgeInsets.all(10),
                          //           border: OutlineInputBorder(
                          //               borderRadius: BorderRadius.circular(25),
                          //               borderSide: const BorderSide(
                          //                   color: Color(greyBorder), width: 1)),
                          //           enabledBorder: OutlineInputBorder(
                          //               borderRadius: BorderRadius.circular(25),
                          //               borderSide: const BorderSide(
                          //                   color: Color(greyBorder), width: 1)))),
                          //   SizedBox(height: heightSpace(2)),
                          // ],
                          CustomTextField(
                              hint: Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .emailAddress,
                              controller: AuthHelper.c.email,
                              validator: (val) => GetUtils.isEmail(val)
                                  ? null
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .jqueryValidation
                                      .email,
                              inputType: TextInputType.emailAddress,
                              isRoundedBorder: true),
                          SizedBox(height: heightSpace(2)),
                          Obx(
                            () => CustomTextField(
                                hint: Get.find<TranslationHelper>()
                                    .translations
                                    .signUp
                                    .password,
                                controller: AuthHelper.c.passwordLogin,
                                validator: (val) => val.isEmpty
                                    ? Get.find<TranslationHelper>()
                                        .translations
                                        .jqueryValidation
                                        .required
                                    : null,
                                suffix: IconButton(
                                  onPressed: AuthHelper.c.showHidePassword,
                                  icon: Icon(AuthHelper.c.isPassVisible.value
                                      ? Icons.visibility_off
                                      : Icons.visibility),
                                ),
                                isPassword: AuthHelper.c.isPassVisible.value,
                                isRoundedBorder: true),
                          ),

                          // if (c.loginMode.value == "register") ...[
                          //   SizedBox(height: heightSpace(2)),
                          //   Obx(
                          //     () => CustomTextField(
                          //         hint: Get.find<TranslationHelper>().translations?.login.confirmPassword,
                          //         controller: c.confirmPasswordLogin,
                          //         validator: (val) =>
                          //             val.isEmpty ? Get.find<TranslationHelper>().translations?.jqueryValidation.required : null,
                          //         suffix: IconButton(
                          //           onPressed: c.showHideConfirmPassword,
                          //           icon: Icon(c.isConfPassVisible.value
                          //               ? Icons.visibility_off
                          //               : Icons.visibility),
                          //         ),
                          //         isPassword: c.isConfPassVisible.value,
                          //         isRoundedBorder: true),
                          //   ),
                          // ],

                          // c.loginMode.value == "register"

                          // TextButton(
                          //     onPressed: () => Get.to(()=> const LoginSignUp()),
                          //         //c.loginMode.value = "email",
                          //     child: RichText(
                          //       text: TextSpan(
                          //         text: "${Get.find<TranslationHelper>().translations?.signUp.alreadyHave}  ",
                          //         style: TextStyle(
                          //           fontSize: heightSpace(2),
                          //           color: Colors.black87,
                          //           // fontWeight:FontWeight.w500,
                          //         ),
                          //         children: <TextSpan>[
                          //           TextSpan(
                          //               text: Get.find<TranslationHelper>().translations?.signUp.login,
                          //               style: TextStyle(
                          //                   fontSize: heightSpace(1.95),
                          //                   color:
                          //                       const Color(themeColor),
                          //                   fontWeight: FontWeight.bold,
                          //                   decoration: TextDecoration
                          //                       .underline)),
                          //         ],
                          //       ),
                          //     ))
                          // :
                          TextButton(
                              onPressed: () =>
                                  Get.to(() => const LoginSignUp()),
                              child: RichText(
                                text: TextSpan(
                                  text:
                                      "${Get.find<TranslationHelper>().translations?.login.doNotHaveAnAccount}  ",
                                  style: TextStyle(
                                    fontSize: heightSpace(2),
                                    color: Colors.black87,
                                    // fontWeight:FontWeight.w500,
                                  ),
                                  children: <TextSpan>[
                                    TextSpan(
                                        text: Get.find<TranslationHelper>()
                                            .translations
                                            .login
                                            .register,
                                        style: TextStyle(
                                            fontSize: heightSpace(1.95),
                                            color: const Color(themeColor),
                                            fontWeight: FontWeight.bold,
                                            decoration:
                                                TextDecoration.underline)),
                                  ],
                                ),
                              )),
                          // SizedBox(height: heightSpace(4)),
                        ]),
                  )),
              SizedBox(
                height: heightSpace(2),
              ),
              SizedBox(
                  width: double.infinity,
                  child: Obx((() => CommonButton(
                      title: Get.find<TranslationHelper>()
                          .translations
                          .utility
                          .submit,
                      isLoading: AuthHelper.c.isLoading.value,
                      onPressed: AuthHelper.c.onSubmitLoginEmail)))),
            ],
          ),
        ),
      ),
    ));
  }
}
