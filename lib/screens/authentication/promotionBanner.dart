import 'package:carousel_slider/carousel_slider.dart';
import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/routes.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PromotionBanner extends StatelessWidget {
  PromotionBanner({super.key});
  CarouselSliderController controller = CarouselSliderController();
  @override
  Widget build(BuildContext context) {
    // SplashController c = Get.find();
    final splashStep =0.obs;
    return Scaffold(
      body:  SizedBox(
          width: widthSpace(100),
          height: heightSpace(100),
          child: Stack(
            alignment: Alignment.center,
            children: [
              CarouselSlider(
                  carouselController: controller,
                  items: [
                    Image.asset("assets/banner1.jpg",fit:BoxFit.fill),
                    Image.asset("assets/banner2.jpg",fit:BoxFit.fill),
                    Image.asset("assets/banner3.jpg",fit:BoxFit.fill)
                  ], options: CarouselOptions(
                  viewportFraction: 1,
                  height: heightSpace(100),
                onPageChanged: (index, reason) {
                    splashStep.value = index;
                },
              )),
              Positioned(
                 bottom: 0,
                width: widthSpace(99),
                height: heightSpace(35),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: widthSpace(10)),
                  decoration: BoxDecoration(
                      color: Colors.white.withOpacity(.9),
                      borderRadius: const BorderRadius.vertical(top:Radius.circular(16))
                  ),
                  child: Obx(()=>Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CustomText(
                            'banner${splashStep.value+1}Main'.tr,size: 2.2,weight: FontWeight.w500),
                        SizedBox(height: heightSpace(3)),
                        CustomText(
                          'banner${splashStep.value+1}Sub'.tr,
                          color: Colors.black54,textAlign: TextAlign.center),
                        SizedBox(height: heightSpace(3)),
                        CommonButton(title:splashStep.value<2?'next'.tr:"start_now".tr,
                          fontSize: 2,
                          horizontalPadding: 5,
                          onPressed: (){
                            if(splashStep.value < 2){
                              controller.animateToPage(splashStep.value+1);
                            } else {
                              navigate();
                            }
                          } ,
                          backgroundBg: const Color(themeColor),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Positioned(
                  top:40,left:20,
                  child: CommonButton(
                      onPressed: navigate,
                      title: 'skip'.tr,
                      backgroundBg: Colors.black.withOpacity(.4))),
            ],
          ),
        ),
    );
  }
  navigate(){
    GlobalHelper.storageBox.write("notFirstTime", true);
    Get.offAllNamed(Routes.home);
  }
}
