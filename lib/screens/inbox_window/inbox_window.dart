import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/controllers/chat_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/screens/new_host/guest_details.dart';
import 'package:darent/screens/property_single/property_details.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' show DateFormat;

import '../../helperMethods/translation_helper.dart';
import '../../utils/routes.dart';

class InboxWindow extends StatelessWidget {
  const InboxWindow({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    ChatController c = Get.find();
    return PopScope(
      onPopInvoked: (pop) {
        if (pop) {
          c.messageC.clear();
        }
      },
      child: Scaffold(
        backgroundColor: Colors.grey[100],
        body: Obx(() => c.areChatsLoading.isTrue
            ? const Center(child: CircularProgressIndicator())
            : NotificationListener<ScrollEndNotification>(
                onNotification: (notification) {
                  final metrics = notification.metrics;
                  if (metrics.atEdge) {
                    bool isTop = metrics.pixels == 0;
                    if (c.isLazyLoading.isFalse && isTop && c.hasMoreMessage) {
                      c.getChats(c.selectedChatHead!.id!, paginate: true);
                    }
                  }
                  return true;
                },
                child: Directionality(
                  textDirection: TextDirection.ltr,
                  child: CustomScrollView(
                      controller: c.scrollController,
                      slivers: [
                        SliverAppBar(
                          actions: [
                            InkWell(
                              onTap: () {
                                c.messageC.clear();
                                Get.back();
                              },
                              child: Row(children: [
                                CustomText(
                                    c.selectedChatHead?.converser?.firstName ??
                                        ""),
                                SizedBox(width: widthSpace(2)),
                                const Icon(Icons.chevron_right)
                              ]).paddingAll(widthSpace(3)),
                            ),
                          ],
                          automaticallyImplyLeading: false,
                          //leading: IconButton(onPressed: (){},icon: const Icon(Icons.more_horiz_rounded)),
                          pinned: true,
                          floating: false,
                          bottom: PreferredSize(
                              preferredSize: Size.fromHeight(heightSpace(11)),
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: widthSpace(3.7),
                                    vertical: widthSpace(3)),
                                // margin: EdgeInsets.only(bottom: widthSpace(5)),
                                decoration: const BoxDecoration(
                                    color: Colors.white,
                                    border: Border.symmetric(
                                        horizontal: BorderSide(
                                            color: Color(greyBorder)))),
                                child: Row(children: [
                                  Expanded(
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          CustomText(
                                              c.selectedChatHead?.property
                                                      ?.name ??
                                                  "",
                                              size: 2.3,
                                              underline: true,
                                              weight: FontWeight.w500),
                                          if (c.selectedChatHead?.details !=
                                              null)
                                            CustomText(
                                              '${DateFormat(DateFormat.ABBR_MONTH_DAY).format(c.selectedChatHead!.details!.startDate!)} - ${DateFormat(DateFormat.ABBR_MONTH_DAY).format(c.selectedChatHead!.details!.endDate!)}',
                                              size: 1.9,
                                              color: Colors.black54,
                                            )
                                        ]),
                                  ),
                                  if (c.booking != null)
                                    CommonButton(
                                        title: Get.find<TranslationHelper>()
                                            .translations
                                            .hostDashboard
                                            .details,
                                        fontSize: 2.0,
                                        minimumSize: Size(0, heightSpace(6)),
                                        onPressed: () {
                                          Get.dialog(
                                              GuestDetails(data: c.booking!));
                                        })
                                ]),
                              )),
                        ),
                        if (c.isLazyLoading.value)
                          SliverToBoxAdapter(
                              child: Container(
                                  width: 20,
                                  padding: const EdgeInsets.all(15),
                                  alignment: Alignment.center,
                                  child: const CircularProgressIndicator(
                                      strokeWidth: 2))),
                        SliverPadding(
                            padding: EdgeInsets.symmetric(
                                horizontal: widthSpace(viewPadding)),
                            sliver: SliverList.separated(
                                itemBuilder: (context, index) {
                                  String parentKey =
                                      c.messages.keys.toList()[index];
                                  return Column(children: [
                                    CustomText(
                                        formDateFormatCservice.format(
                                            formDateFormat.parse(parentKey)),
                                        weight: FontWeight.w500),
                                    SizedBox(height: heightSpace(2)),
                                    for (var timeItem
                                        in c.messages[parentKey].keys) ...[
                                      for (var item in c.messages[parentKey]
                                          [timeItem]) ...[
                                        if (item['sender'] != null)
                                          Row(
                                              mainAxisAlignment: item['sender']
                                                          ['id'] ==
                                                      userModel.value?.id
                                                  ? MainAxisAlignment.end
                                                  : MainAxisAlignment.start,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                if (item['sender']['id'] !=
                                                    userModel.value?.id) ...[
                                                  profilePhoto(item['sender']
                                                      ['profile_image']),
                                                  SizedBox(
                                                      width: widthSpace(4)),
                                                ],
                                                Expanded(
                                                  child: Column(
                                                      crossAxisAlignment: item[
                                                                      'sender']
                                                                  ['id'] ==
                                                              userModel
                                                                  .value?.id
                                                          ? CrossAxisAlignment
                                                              .end
                                                          : CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        SizedBox(
                                                            height:
                                                                heightSpace(1)),
                                                        for (var messageItem
                                                            in item[
                                                                'messages']) ...[
                                                          messageItem['type'] ==
                                                                  "media"
                                                              ? ClipRRect(
                                                                  borderRadius:
                                                                      BorderRadius
                                                                          .circular(
                                                                              10),
                                                                  child: Image(
                                                                      image: GlobalHelper.buildNetworkImageProvider(url: messageItem['message'].split(";").first ??''),
                                                                      width:widthSpace(70),
                                                                      height:heightSpace(30),
                                                                      fit: BoxFit.cover,
                                                                      ),
                                                                )
                                                              : Container(
                                                                  padding:
                                                                      const EdgeInsets.all(
                                                                          10),
                                                                  decoration: BoxDecoration(
                                                                      color: item['sender']['id'] == userModel.value?.id
                                                                          ? Colors.blueGrey[
                                                                              300]
                                                                          : Colors
                                                                              .white,
                                                                      borderRadius:
                                                                          BorderRadius.circular(
                                                                              10)),
                                                                  child: CustomText(
                                                                      messageItem[
                                                                          'message'],
                                                                      color: item['sender']['id'] == userModel.value?.id
                                                                          ? Colors.white
                                                                          : null)),
                                                          SizedBox(
                                                              height:
                                                                  heightSpace(
                                                                      1)),
                                                        ],
                                                        CustomText(
                                                            timeItem.substring(
                                                                0, 5),
                                                            size: 1.6,
                                                            color: Colors.grey),
                                                      ]),
                                                ),
                                                if (item['sender']['id'] ==
                                                    userModel.value?.id) ...[
                                                  SizedBox(
                                                      width: widthSpace(4)),
                                                  profilePhoto(item['sender']
                                                      ['profile_image']),
                                                ]
                                              ]),
                                        SizedBox(height: heightSpace(4)),
                                        for (var messageItem
                                            in item['messages'])
                                          if (messageItem['type'] == "prompt")
                                            GestureDetector(
                                              onTap: () {
                                                if (c.selectedChatHead?.property
                                                        ?.slug !=
                                                    null) {
                                                  if (Get.previousRoute
                                                      .contains(Routes
                                                          .propertySingle)) {
                                                    Get.back();
                                                  } else {
                                                    Get.to(() =>
                                                        PropertyDetailScreen(
                                                            slug: c
                                                                .selectedChatHead!
                                                                .property!
                                                                .slug!));
                                                    // Get.toNamed('/${Routes.propertySingle}/${c.selectedChatHead!.property!.slug!}');
                                                  }
                                                }
                                              },
                                              child: Container(
                                                  padding:
                                                      const EdgeInsets.all(10),
                                                  margin: EdgeInsets.only(
                                                      bottom: heightSpace(4)),
                                                  decoration: BoxDecoration(
                                                      color: Colors.white,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              15)),
                                                  child: Row(children: [
                                                    CircleAvatar(
                                                      backgroundImage:
                                                          const AssetImage(
                                                              "assets/icons/d_logo.png"),
                                                      radius: widthSpace(5),
                                                    ),
                                                    SizedBox(
                                                        width: widthSpace(3)),
                                                    Expanded(
                                                        child: messageItem[
                                                                        'message']
                                                                    .split(";")
                                                                    .length >
                                                                1
                                                            ? Text.rich(
                                                                TextSpan(
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          heightSpace(
                                                                              2.0),
                                                                      color: Colors
                                                                          .black87),
                                                                  children: [
                                                                    TextSpan(
                                                                        text:
                                                                            "${messageItem['message'].split(";")[0]} "),
                                                                    TextSpan(
                                                                      text: messageItem[
                                                                              'message']
                                                                          .split(
                                                                              ";")[1],
                                                                      style: const TextStyle(
                                                                          fontWeight: FontWeight
                                                                              .w500,
                                                                          decoration:
                                                                              TextDecoration.underline),
                                                                    )
                                                                  ],
                                                                ),
                                                              )
                                                            : CustomText(
                                                                messageItem[
                                                                    'message'],
                                                                color: Colors
                                                                    .black87)),
                                                  ])),
                                            )
                                      ]
                                    ]
                                  ]);
                                },
                                separatorBuilder: (context, index) =>
                                    SizedBox(height: heightSpace(2)),
                                itemCount: c.messages.keys.length))
                      ]),
                ),
              )),
        bottomNavigationBar: Padding(
            padding: EdgeInsets.only(
                bottom:
                    MediaQuery.of(context).viewInsets.bottom + widthSpace(6),
                left: widthSpace(6),
                right: widthSpace(6)),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(width: heightSpace(2)),
                  Expanded(
                      child: Obx(
                    () => CustomTextField(
                        hint: Get.find<TranslationHelper>()
                            .translations
                            .accountMobile
                            .enterYourMsgHere,
                        controller: c.messageC,
                        textCapitalization: TextCapitalization.sentences,
                        isEnabled: !c.isBtnLoading.value,
                        filledColor: Colors.white,
                        maxLength: 1000,
                        suffix: Obx(
                          () => Row(mainAxisSize: MainAxisSize.min, children: [
                            // IconButton(
                            //   splashRadius: 1,
                            //   padding: EdgeInsets.zero,
                            //   icon: const Icon(Icons.emoji_emotions_outlined),
                            //   onPressed: (){},
                            // ),
                            // IconButton(
                            //   splashRadius: 1,
                            //   color: const Color(greyText),
                            //   icon: const Icon(Icons.attach_file_outlined),
                            //   onPressed: (){},
                            // ),
                            c.isBtnLoading.value
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2))
                                : SizedBox(
                                    height: 38,
                                    width: 48,
                                    child: FloatingActionButton(
                                      onPressed: c.sendMessage,
                                      child: SvgPicture.asset(
                                          "assets/icons/send.svg",
                                          width: 14,
                                          height: 14),
                                    ),
                                  ),
                          ]),
                        ),
                        isRoundedBorder: true),
                  )),
                  // SizedBox(width: heightSpace(2)),
                  // Obx(()=>
                  // c.isBtnLoading.value
                  //     ?const SizedBox(width: 20,height: 20,child: CircularProgressIndicator(strokeWidth: 2))
                  //     :SizedBox(
                  //     height: 48,width: 48,
                  //     child: FloatingActionButton(
                  //       onPressed:c.sendMessage,
                  //       child: Image.asset(
                  //         "assets/icons/send.png",
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  // SizedBox(width: heightSpace(2)),
                ])),
      ),
    );
  }

  profilePhoto(photo) {
    return CircleAvatar(
        // borderRadius: BorderRadius.circular(100),child:
        backgroundColor: Colors.grey[400],
        child:
        GlobalHelper.resolveImageUrl(photo ??"").isNotEmpty
            ? Image(
            image: GlobalHelper.buildNetworkImageProvider(
                url: photo ??'',
            ),
            width: widthSpace(12),
            height: widthSpace(12),)
            : const Icon(Icons.person, color: Colors.black)
    );
  }
}
