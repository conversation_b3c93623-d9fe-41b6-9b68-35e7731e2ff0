import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/wishlist_helper.dart';
import 'package:darent/models/homeProperty.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../helperMethods/search_helper.dart';
import '../../helperMethods/translation_helper.dart';
import '../../utils/constants.dart';
import '../../utils/sizeconfig.dart';

class AddWishlistListGroup extends StatelessWidget {
  final HomeProperty? property;
  final int? groupIndex;
  const AddWishlistListGroup({super.key, this.property, this.groupIndex});
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Dialog(
          insetPadding: EdgeInsets.symmetric(
              horizontal: widthSpace(4), vertical: heightSpace(31)),
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          child: Column(children: [
            Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CustomText(
                      groupIndex != null
                          ? Get.find<TranslationHelper>()
                              .translations
                              .footer
                              .setting
                          : Get.find<TranslationHelper>()
                              .translations
                              .wishlist
                              .nameThisWishlist,
                      size: 2.4,
                      weight: FontWeight.w500,
                      textAlign: TextAlign.center),
                  if (groupIndex != null)
                    InkWell(
                        onTap: () {
                          WishlistHelper.deleteWishlist(groupIndex);
                        },
                        child: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .utility
                              .delete,
                          size: 2.2,
                          weight: FontWeight.w500,
                          underline: true,
                        )),
                ],
              ),
            ),
            SizedBox(height: heightSpace(2)),
            Padding(
              padding: const EdgeInsets.only(left: 12, right: 12),
              child: Container(
                padding: EdgeInsets.all(widthSpace(3)),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.black54, width: 2.0),
                ),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .settings
                              .name,
                          color: const Color(greyText),
                          size: 1.7),
                      TextField(
                        controller: SearchHelper.c.wishlistNameController,
                        decoration:
                            const InputDecoration.collapsed(hintText: ''),
                        inputFormatters: [LengthLimitingTextInputFormatter(50)],
                        onChanged: (value) {
                          SearchHelper.c.isButtonDisabled.value = value.isEmpty;
                          SearchHelper.c.updateCharacterCount();
                        },
                      ),
                    ]),
              ),
            ),
            SizedBox(height: heightSpace(1)),
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.only(left: 12.5),
                child: Obx(()=>CustomText(
                    "${SearchHelper.c.characterCount.value}/50 ${Get.find<TranslationHelper>().translations.wishlist.characterMaximum}",
                    size: 1.6,
                    color: Colors.black.withOpacity(0.5),
                  ),
                ),
              ),
            ),
            SizedBox(height: heightSpace(4)),
            Divider(color: Colors.grey.withOpacity(0.5), thickness: 2),
            Padding(
              padding: const EdgeInsets.only(left: 12, right: 12, bottom: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  InkWell(
                      onTap: () {
                        Get.back();
                        SearchHelper.c.characterCount.value = 0;
                      },
                      child: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .search
                              .cancel,
                          size: 2.2,
                          weight: FontWeight.w500,
                          underline: true)),
                  Obx(() => CommonButton(
                      title: groupIndex != null
                          ? Get.find<TranslationHelper>()
                              .translations
                              .usersProfile
                              .save
                          : Get.find<TranslationHelper>()
                              .translations
                              .wishlist
                              .create,
                      isDisabled: SearchHelper.c.isButtonDisabled.value,
                      isLoading: SearchHelper.c.isBtnLoading.value,
                      onPressed: () {
                        SearchHelper.c.characterCount.value = 0;
                        if (property != null) {
                          WishlistHelper.addWishlistGroup(property!);
                        } else {
                          WishlistHelper.changeWishlistName(groupIndex);
                        }
                      }))
                ],
              ),
            )
          ])),
    );
  }
}
