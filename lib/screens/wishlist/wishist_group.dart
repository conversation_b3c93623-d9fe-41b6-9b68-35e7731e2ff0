import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/helperMethods/wishlist_helper.dart';
import 'package:darent/screens/wishlist/wishlist.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import '../../helperMethods/globalHelpers.dart';
import '../../helperMethods/search_helper.dart';
import '../../helperMethods/translation_helper.dart';

class WishlistGroup extends StatelessWidget {
  const WishlistGroup({super.key});

  @override
  Widget build(BuildContext context) {
    final trans = Get.find<TranslationHelper>();
    return Obx(() => SearchHelper.c.dataLoading.value
        ? shimmersLoading()
        : SearchHelper.c.wishlistGroups.isEmpty
        ?
    Center(
      child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
        Image.asset('assets/empty_wishlist.png',width: widthSpace(45)),
        SizedBox(height: heightSpace(4)),
        InkWell(
          onTap: ()=>SearchHelper.c.changeIndex(0),
            child: CustomText(trans.translations.usersDashboard.emptyWishlistTitle,size: 2.2,weight: FontWeight.w500,underline: true))
      ]),
    )
            : NotificationListener<ScrollEndNotification>(
                onNotification: (notification) {
                  final metrics = notification.metrics;
                  if (metrics.atEdge) {
                    bool isTop = metrics.pixels == 0;
                    if (!isTop &&
                        WishlistHelper.wishlistGroupPage <
                            WishlistHelper.wishlistGroupTotal) {
                      WishlistHelper.wishlistGroupPage++;
                      WishlistHelper.getWishlistGroup();
                    }
                  }
                  return true;
                },
                child: RefreshIndicator(
                  onRefresh: () async {
                    WishlistHelper.getWishlistGroup(refresh: true);
                  },
                  child: CustomScrollView(
                    slivers: [
                      SliverPadding(
                        padding: EdgeInsets.all(widthSpace(viewPadding)),
                        sliver: SliverList(
                            delegate: SliverChildBuilderDelegate(
                                (context, index) {
                          final int itemIndex = index ~/ 2;
                          if (index.isOdd) {
                            // Only add spacer if the previous item was not empty and there's a next item
                            if (itemIndex >=
                                    SearchHelper.c.wishlistGroups.length ||
                                SearchHelper.c.wishlistGroups[itemIndex]
                                    .wishlistProperties!.isEmpty ||
                                (itemIndex + 1 <
                                        SearchHelper.c.wishlistGroups.length &&
                                    SearchHelper.c.wishlistGroups[itemIndex + 1]
                                        .wishlistProperties!.isEmpty)) {
                              return const SizedBox(); // Skip spacer if current or next item is empty
                            }
                            return SizedBox(height: heightSpace(3));
                          }
                          if (SearchHelper.c.wishlistGroups[itemIndex]
                              .wishlistProperties!.isEmpty) {
                            return const SizedBox();
                          }
                          return InkWell(
                            onTap: () =>
                                Get.to(() => Wishlist(groupIndex: itemIndex)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  height: heightSpace(25),
                                  width: double.maxFinite,
                                  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(
                                    borderRadius:
                                        BorderRadius.circular(heightSpace(1.6)),
                                  ),
                                  child: SearchHelper
                                              .c
                                              .wishlistGroups[itemIndex]
                                              .wishlistProperties!
                                              .length ==
                                          1
                                      ? renderImage(
                                          "${SearchHelper.c.wishlistGroups[itemIndex].wishlistProperties![0].coverPhoto}")
                                      : Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.stretch,
                                          children: [
                                            Expanded(
                                                child: renderImage(
                                                    "${SearchHelper.c.wishlistGroups[itemIndex].wishlistProperties![0].coverPhoto}")),
                                            SearchHelper
                                                        .c
                                                        .wishlistGroups[
                                                            itemIndex]
                                                        .wishlistProperties!
                                                        .length >=
                                                    3
                                                ? Expanded(
                                                    child: Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .stretch,
                                                      children: [
                                                        Expanded(
                                                            child: renderImage(
                                                                "${SearchHelper.c.wishlistGroups[itemIndex].wishlistProperties![1].coverPhoto}")),
                                                        Expanded(
                                                            child: renderImage(
                                                                "${SearchHelper.c.wishlistGroups[itemIndex].wishlistProperties![2].coverPhoto}")),
                                                      ],
                                                    ),
                                                  )
                                                : Expanded(
                                                    child: renderImage(
                                                        "${SearchHelper.c.wishlistGroups[itemIndex].wishlistProperties![1].coverPhoto}")),
                                          ],
                                        ),
                                ),
                                SizedBox(height: heightSpace(1)),
                                CustomText(
                                  SearchHelper
                                          .c.wishlistGroups[itemIndex].name ??
                                      "",
                                  size: 2.2,
                                  weight: FontWeight.w500,
                                ),
                              ],
                            ),
                          );
                        },
                                childCount:
                                    SearchHelper.c.wishlistGroups.length * 2 -
                                        1)),
                      ),
                      if (SearchHelper.c.lazyLoader.value)
                        //WishlistHelper.wishlistGroupPage<WishlistHelper.wishlistGroupTotal
                        SliverToBoxAdapter(
                            child: Container(
                                padding: EdgeInsets.all(widthSpace(4)),
                                alignment: Alignment.center,
                                child: const CircularProgressIndicator()))
                    ],
                  ),
                ),
              ));
  }

  renderImage(String url) {
    return Image(
      image: GlobalHelper.buildNetworkImageProvider(url: url),
      fit: BoxFit.cover,
    );
  }

  shimmersLoading() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Column(
        children: [
          Container(
            height: heightSpace(25),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(heightSpace(1.6)),
            ),
          ),
          SizedBox(height: heightSpace(1)),
          Container(height: heightSpace(2), color: Colors.white),
        ],
      ),
    ).paddingAll(widthSpace(viewPadding));
  }
}
