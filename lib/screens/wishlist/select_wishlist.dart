import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/helperMethods/wishlist_helper.dart';
import 'package:darent/models/homeProperty.dart';
import 'package:darent/screens/wishlist/add_wishlist_group.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../helperMethods/search_helper.dart';
import '../../helperMethods/translation_helper.dart';

class SelectWishlist extends StatelessWidget {
  final HomeProperty item;
  const SelectWishlist({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return BottomSheet(
        onClosing: () {},
        builder: (context) {
          return SizedBox(
            height: heightSpace(90),
            child: Column(children: [
              AppBar(
                leading: IconButton(
                    onPressed: Get.back, icon: const Icon(Icons.close)),
                title: Text(Get.find<TranslationHelper>()
                    .translations
                    .wishlist
                    .yourWishlists),
                centerTitle: false,
              ),
              SizedBox(height: heightSpace(2)),
              ListTile(
                  onTap: () {
                    Get.back();
                    WishlistHelper.clearForm();
                    Get.dialog(AddWishlistListGroup(property: item));
                  },
                  title: CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .wishlist
                          .createNewWishlist,
                      weight: FontWeight.w500),
                  leading: Container(
                    width: widthSpace(20),
                    height: widthSpace(20),
                    decoration: BoxDecoration(
                        border: Border.all(color: const Color(greyBorder)),
                        borderRadius: BorderRadius.circular(9)),
                    child: const Icon(Icons.add),
                  )),
              Expanded(
                child: ListView.separated(
                    padding: EdgeInsets.symmetric(vertical: heightSpace(2)),
                    itemBuilder: (context, index) {
                      return ListTile(
                          onTap: () {
                            Get.close(1);
                            WishlistHelper.toggleWishlist(item, index);
                          },
                          title: CustomText(
                              SearchHelper.c.wishlistGroups[index].name,
                              weight: FontWeight.w500),
                          leading: ClipRRect(
                            borderRadius: BorderRadius.circular(9),
                            child: Image(
                              image: GlobalHelper.buildNetworkImageProvider(url: SearchHelper.c.wishlistGroups[index].wishlistProperties?.first.coverPhoto ??"",),
                              width: widthSpace(20),
                              height: widthSpace(20),
                              fit: BoxFit.cover,
                            ),
                          ));
                    },
                    separatorBuilder: (context, index) =>
                        SizedBox(height: heightSpace(2)),
                    itemCount: SearchHelper.c.wishlistGroups.length),
              )
            ]),
          );
        });
  }
}
