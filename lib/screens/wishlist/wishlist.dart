import 'package:darent/components/custom_text.dart';
import 'package:darent/components/product_item.dart';
import 'package:darent/controllers/property_detail_controller.dart';
import 'package:darent/controllers/wishlistController.dart';
import 'package:darent/helperMethods/wishlist_helper.dart';
import 'package:darent/screens/property_single/property_details.dart';
import 'package:darent/screens/wishlist/shareWishlist.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../../components/products_shimmer.dart';
import '../../helperMethods/translation_helper.dart';
import 'add_wishlist_group.dart';

class Wishlist extends StatelessWidget {
  final groupIndex;
  const Wishlist({Key? key, required this.groupIndex}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final data = WishlistHelper.c.wishlistGroups[groupIndex];

    WishlistController wishlistC = Get.put(
        WishlistController(data.id ?? 0));
    return Obx(
      () => Scaffold(
        appBar: AppBar(
          leading: IconButton(
              icon: const Icon(Icons.chevron_left), onPressed: Get.back),
          title: CustomText(
              data.name ?? "Wishlist",
              size: 2.7,
              weight: FontWeight.w500),
          actions: [
            InkWell(
                onTap: () {
                  Get.dialog(ShareWishList(
                      code: data.code ?? ''));
                },
                child: Icon(Icons.ios_share,
                    size: 25, color: Colors.black.withOpacity(0.6))),
            SizedBox(width: widthSpace(2)),
            InkWell(
                onTap: () {
                  WishlistHelper.clearForm(
                      name: data?.name);
                  Get.dialog(AddWishlistListGroup(groupIndex: groupIndex));
                },
                child: Icon(Icons.more_horiz,
                    size: 30, color: Colors.black.withOpacity(0.6))),
            SizedBox(width: widthSpace(6))
          ],
        ),
        body: wishlistC.loader.value
            ? const ProductsShimmer().paddingAll(widthSpace(viewPadding))
            : wishlistC.wishlist.isEmpty
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .usersDashboard
                                .emptyWishlistTitle,
                            size: 2.5,
                            weight: FontWeight.w500),
                        SizedBox(height: heightSpace(2)),
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .usersDashboard
                                .emptyWishlistNote,
                            color: const Color(greyText))
                      ]).paddingAll(widthSpace(viewPadding))
                : NotificationListener<ScrollEndNotification>(
                    onNotification: (notification) {
                      final metrics = notification.metrics;
                      if (metrics.atEdge) {
                        bool isTop = metrics.pixels == 0;
                        if (!isTop && wishlistC.page < wishlistC.totalPages) {
                          wishlistC.page++;
                          wishlistC.getData();
                        }
                      }
                      return true;
                    },
                    child: RefreshIndicator(
                      onRefresh: () async {
                        wishlistC.getData(refresh: true);
                      },
                      child: wishlistC.listView.value
                          ? CustomScrollView(
                              slivers: [
                                SliverPadding(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: widthSpace(viewPadding)),
                                  sliver: SliverList(
                                      delegate: SliverChildBuilderDelegate(
                                    (context, index) {
                                      final int itemIndex = index ~/ 2;
                                      if (index.isOdd) {
                                        return SizedBox(
                                            height: heightSpace(3.5));
                                      } else {
                                        return ProductItem(
                                          data: wishlistC.wishlist[itemIndex],
                                          wishlistGroupIndex: groupIndex,
                                          wishlistLoader: WishlistHelper
                                              .c.wishlistLoading.value,
                                          translations:
                                              Get.find<TranslationHelper>()
                                                  .translations,
                                          onTap: wishlistC.getData,
                                        );
                                      }
                                    },
                                    childCount:
                                        wishlistC.wishlist.length * 2 - 1,
                                  )),
                                ),
                                // SliverToBoxAdapter(child: CustomText("${wishlistC.page} ${wishlistC.totalPages}")),
                                if (wishlistC.page < wishlistC.totalPages)
                                  SliverToBoxAdapter(
                                      child: Container(
                                          padding:
                                              EdgeInsets.all(widthSpace(4)),
                                          alignment: Alignment.center,
                                          child:
                                              const CircularProgressIndicator()))
                              ],
                            )
                          : GoogleMap(
                              initialCameraPosition: CameraPosition(
                                  target: WishlistHelper.c.mapCenter, zoom: 10),
                              // markers: wishlistC.markers.value,
                              myLocationButtonEnabled: false,
                              zoomControlsEnabled: false,
                              onMapCreated: wishlistC.initializeMap,
                              onTap: (argument) =>
                                  wishlistC.customInfoWindow.hideInfoWindow!(),
                              // onCameraMove: (position) => wishlistC.customInfoWindow.onCameraMove!()
                            ),
                    ),
                  ),
        // floatingActionButton:wishlistC.wishlist.isEmpty?null
        //     :FloatingActionButton(onPressed: wishlistC.toggleView,backgroundColor: Colors.white,child: SvgPicture.asset("assets/icons/show-${wishlistC.listView.value?"map":"list"}.svg",width: widthSpace(7))),
      ),
    );
  }
}
