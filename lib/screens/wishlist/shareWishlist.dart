import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/wishlist_helper.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../helperMethods/translation_helper.dart';
import '../../utils/constants.dart';
import '../../utils/sizeconfig.dart';

class ShareWishList extends StatelessWidget {
  final String code;
  const ShareWishList({super.key, required this.code});
  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(
          horizontal: widthSpace(4), vertical: heightSpace(30)),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Column(
        children: [
          Container(
            height: heightSpace(7.5),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(10.0),
                  topRight: Radius.circular(10.0),
                ),
                boxShadow: ViewsCommon.boxShadow),
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  InkWell(
                      onTap: Get.back,
                      child: const Icon(
                        Icons.clear,
                        size: 20,
                      )),
                  CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .reservation
                        .howDoYouShare,
                    size: 2.2,
                    weight: FontWeight.w500,
                  ),
                  SizedBox(
                    width: widthSpace(4),
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: heightSpace(2)),
          InkWell(
            onTap: () => ViewsCommon.share(
                '$baseUrl/${Get.locale?.languageCode ?? 'en'}/wishlists/$code',
                title: 'Wishlist',
                itemId: code),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(
                      10), // Adjust the radius value as needed
                  border: Border.all()),
              child: Row(
                children: [
                  const Icon(Icons.share),
                  SizedBox(width: widthSpace(5)),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .reservation
                            .sendALink,
                        size: 1.9,
                        weight: FontWeight.bold,
                      ),
                      SizedBox(height: heightSpace(0.5)),
                      CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .reservation
                            .anyoneWithTheLink,
                        size: 1.6,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          InkWell(
            onTap: () => WishlistHelper.invite(code),
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 10),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(
                      10), // Adjust the radius value as needed
                  border: Border.all()),
              child: Row(
                children: [
                  const Icon(Icons.share),
                  SizedBox(
                    width: widthSpace(5),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .reservation
                            .inviteOthers,
                        size: 1.9,
                        weight: FontWeight.bold,
                      ),
                      SizedBox(height: heightSpace(0.5)),
                      CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .reservation
                            .onceYouShare,
                        size: 1.6,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          SizedBox(
            height: heightSpace(2),
          ),
        ],
      ),
    );
  }
}
