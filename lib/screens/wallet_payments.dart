import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/trasnaction_component.dart';
import 'package:darent/screens/bank.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../helperMethods/payment_helper.dart';
import '../helperMethods/translation_helper.dart';
import '../helperMethods/authHelper.dart';

class WalletPayments extends StatelessWidget {
  const WalletPayments({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(
      () => SafeArea(
        child: Scaffold(
            // wallet removed
            //   appBar: AppBar(
            //     automaticallyImplyLeading: false,
            //     leading: IconButton(onPressed: Get.back,icon:const Icon(Icons.chevron_left)),
            //   ),
            body: NestedScrollView(
          floatHeaderSlivers: true,
          headerSliverBuilder: (context, isScrolled) => [
            // wallet removed
            SliverAppBar(
              pinned: true,
              automaticallyImplyLeading: false,
              leading: isScrolled
                  ? IconButton(
                      onPressed: Get.back, icon: const Icon(Icons.chevron_left))
                  : null,
              centerTitle: true,
              title: isScrolled
                  ? Text(Get.find<TranslationHelper>()
                      .translations
                      .wallet
                      .myWallet)
                  : null,
              flexibleSpace: FlexibleSpaceBar(
                background: walletHeader(),
              ),
              expandedHeight: heightSpace(33),
            ),
          ],
          body: SingleChildScrollView(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              if (PaymentHelper.paymentGateway != 'hyperpay') ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    CustomText(
                        Get.find<TranslationHelper>().translations.wallet.cards,
                        size: 3.1,
                        weight: FontWeight.w500),
                    SizedBox(width: widthSpace(2)),
                    IconButton(
                        onPressed: AuthHelper.c.getCards,
                        icon: const Icon(Icons.refresh_sharp)),
                    const Spacer(),
                    TextButton(
                        onPressed: AuthHelper.c.showAddCardSheet,
                        child: Row(
                          children: [
                            CustomText(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .hostDashboard
                                    .add,
                                size: 1.8,
                                weight: FontWeight.w500),
                            SizedBox(width: widthSpace(1)),
                            const Icon(
                              Icons.add,
                              color: Colors.black,
                              size: 18,
                            ),
                          ],
                        ))
                  ],
                ),
                CustomText(
                    AuthHelper.c.myCards.isNotEmpty
                        ? Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .savedCards
                        : Get.find<TranslationHelper>()
                            .translations
                            .wallet
                            .noSavedCard,
                    color: const Color(greyText),
                    size: 2.1,
                    weight: FontWeight.w500),
                if (AuthHelper.c.myCards.isNotEmpty) ...[
                  ListView.separated(
                      shrinkWrap: true,
                      primary: false,
                      padding: EdgeInsets.only(top: heightSpace(1)),
                      itemBuilder: (context, index) {
                        return InkWell(
                            onTap: () => AuthHelper.c.changeDefaultCard(
                                AuthHelper.c.myCards[index].id),
                            child: Row(children: [
                              SvgPicture.asset("assets/cvv-card.svg",
                                  height: 40, width: 80),
                              SizedBox(width: widthSpace(5)),
                              Expanded(
                                  child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                    CustomText(
                                      "**** **** **** ${AuthHelper.c.myCards[index].cardNumber[12]}${AuthHelper.c.myCards[index].cardNumber[13]}${AuthHelper.c.myCards[index].cardNumber[14]}${AuthHelper.c.myCards[index].cardNumber[15]}",
                                      color: Colors.grey[700],
                                    ),
                                    Radio(
                                        value: AuthHelper.c.myCards[index].id,
                                        groupValue:
                                            AuthHelper.c.defaultCardId.value,
                                        onChanged:
                                            AuthHelper.c.changeDefaultCard),
                                    InkWell(
                                        onTap: () => AuthHelper.c.deleteCard(
                                            AuthHelper.c.myCards[index].id),
                                        child: const Icon(Icons.clear))
                                  ]))
                            ]));
                      },
                      separatorBuilder: (context, index) =>
                          SizedBox(height: heightSpace(1)),
                      itemCount: AuthHelper.c.myCards.length),
                  SizedBox(height: heightSpace(1))
                ],
                Divider(height: heightSpace(6))
              ],

              CustomText(
                  Get.find<TranslationHelper>().translations.wallet.payoutInfo,
                  size: 3.1,
                  weight: FontWeight.w500),
              SizedBox(height: heightSpace(1)),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .wallet
                      .atleastOnePayoutMethod,
                  color: const Color(greyText),
                  size: 2.1,
                  weight: FontWeight.w500),
              SizedBox(height: heightSpace(2)),
              CommonButton(
                  title: Get.find<TranslationHelper>()
                      .translations
                      .wallet
                      .setupPayout,
                  horizontalPadding: 7,
                  onPressed: () => Get.to(() => const BankAccount())),
              SizedBox(height: heightSpace(4)),
              Container(
                  padding: EdgeInsets.all(widthSpace(5)),
                  decoration: BoxDecoration(
                      border: Border.all(color: const Color(greyBorder)),
                      borderRadius: BorderRadius.circular(20)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .wallet
                                    .latestTransaction,
                                size: 2.1),
                            IconButton(
                                onPressed: () =>
                                    AuthHelper.c.getTransactions(refresh: true),
                                icon: const Icon(Icons.refresh_sharp)),
                          ]),
                      if (AuthHelper.c.myTransactions.isNotEmpty)
                        InkWell(
                            onTap: AuthHelper.c.gotoTransactions,
                            child: CustomText(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .propertySingle
                                    .viewAll,
                                size: 2.1,
                                underline: true)),
                      AuthHelper.c.myTransactions.isEmpty
                          ? CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .wallet
                                  .noTransactionFound,
                              color: Colors.grey)
                          : ListView.separated(
                              padding: EdgeInsets.only(top: heightSpace(3.5)),
                              shrinkWrap: true,
                              primary: false,
                              itemBuilder: (context, index) {
                                return TransactionComponent(i: index);
                              },
                              separatorBuilder: (context, index) =>
                                  SizedBox(height: heightSpace(2)),
                              itemCount: AuthHelper.c.myTransactions.length > 5
                                  ? 5
                                  : AuthHelper.c.myTransactions.length)
                    ],
                  )),
              SizedBox(height: heightSpace(5)),
              Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Image.asset("assets/icons/wallet.png",
                          width: widthSpace(8)),
                      SizedBox(
                          width: widthSpace(14.5),
                          child: const Icon(
                            Icons.check,
                            color: Colors.green,
                            size: 16,
                          )),
                    ],
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .wallet
                              .makePaymentThroughDarent,
                          size: 2.2,
                          color: Colors.green,
                          weight: FontWeight.w500,
                        ),
                        SizedBox(height: heightSpace(1)),
                        SizedBox(
                          width: widthSpace(70),
                          child: CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .wallet
                                .alwaysPayAndCommunicateThroughDarent,
                            size: 2,
                            color: const Color(greyText),
                            weight: FontWeight.w500,
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
              // Row(children: [
              //   Image.asset("assets/icons/wallet.png", width: widthSpace(8)),
              //   SizedBox(width: widthSpace(4)),
              //   CustomText(
              //     "makePaymentsDarent".tr,
              //     size: 2.1,
              //     color: Colors.green,
              //     weight: FontWeight.w500,
              //   ),

              // ]),
              // Row(
              //     crossAxisAlignment: CrossAxisAlignment.start,
              //     mainAxisAlignment: MainAxisAlignment.start,
              //     children: [
              //       SizedBox(
              //           width: widthSpace(12.5),
              //           child: const Icon(
              //             Icons.check,
              //             color: Colors.green,
              //             size: 16,
              //           )),
              //       // SizedBox(width: widthSpace(4)),
              //       Expanded(
              //           child: CustomText(
              //         "payTermsService".tr,
              //         size: 2.1,
              //         color: const Color(greyText),
              //         weight: FontWeight.w500,
              //       )),
              //     ]),
            ]).paddingSymmetric(
                    horizontal: widthSpace(viewPadding),
                    vertical: heightSpace(3)),
          ),
        )),
      ),
    );
  }

  walletHeader() {
    return Container(
      padding: EdgeInsets.all(widthSpace(5.5)),
      decoration: const BoxDecoration(
        color: Color(themeColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              InkWell(
                  onTap: Get.back,
                  child: const CircleAvatar(
                      backgroundColor: Colors.white,
                      maxRadius: 18,
                      child: Icon(
                        Icons.arrow_back,
                        color: Colors.black,
                        size: 18,
                      ))),
            ],
          ),
          SizedBox(height: heightSpace(3)),
          CustomText(
            Get.find<TranslationHelper>().translations.wallet.walletCredit,
            size: 2.5,
            weight: FontWeight.w500,
            color: Colors.white,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomText(
                "${Get.find<TranslationHelper>().translations.hostDashboard.sar} ",
                size: 3.5,
                weight: FontWeight.w500,
                color: Colors.white,
              ),
              CustomText(
                AuthHelper.c.myWallet.first.balance.toString(),
                size: 4.0,
                weight: FontWeight.w500,
                color: Colors.white,
              ),
            ],
          ),
          SizedBox(height: heightSpace(3)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              if (PaymentHelper.paymentGateway != 'hyperpay')
                SizedBox(
                    width: widthSpace(40),
                    child: TextButton(
                      onPressed: AuthHelper.c.showAddCardSheet,
                      style: TextButton.styleFrom(
                          minimumSize: null,
                          backgroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide.none)),
                      child: Text(
                          Get.find<TranslationHelper>()
                              .translations
                              .wallet
                              .addCard,
                          style: TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.normal,
                              fontSize: heightSpace(2.1))),
                    )),
              SizedBox(
                  width: widthSpace(40),
                  child: TextButton(
                    onPressed: AuthHelper.c.showPaymentMethodSelectionSheet,
                    style: TextButton.styleFrom(
                        minimumSize: null,
                        backgroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide.none)),
                    child: Text(
                        Get.find<TranslationHelper>()
                            .translations
                            .bookingDetail
                            .walletDeposit,
                        style: TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.normal,
                            fontSize: heightSpace(2.1))),
                  )),
            ],
          ),
        ],
      ),
    );
  }
}
