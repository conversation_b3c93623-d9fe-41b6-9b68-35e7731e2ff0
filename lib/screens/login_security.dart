import 'package:darent/components/custom_iqama_sheet.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../helperMethods/translation_helper.dart';
import '../helperMethods/authHelper.dart';

class LoginSecurity extends StatelessWidget {
  const LoginSecurity({super.key});

  @override
  Widget build(BuildContext context) {
    AuthHelper.c.addPreferredContactTypesFromInitialData(
        listPrefs["listing_preferences"]);

    return Scaffold(
      appBar: AppBar(
          leading: IconButton(
              onPressed: Get.back, icon: const Icon(Icons.chevron_left)),
          title: Text(Get.find<TranslationHelper>()
              .translations
              .usersProfile
              .loginSecurity)),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: widthSpace(viewPadding)),
        child: Obx(
          () => Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            SizedBox(height: heightSpace(2)),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              CustomText(
                  userModel.value?.userVerification?.email == "yes"
                      ? Get.find<TranslationHelper>()
                          .translations
                          .usersProfile
                          .verifiedEmail
                      : Get.find<TranslationHelper>()
                          .translations
                          .usersProfile
                          .unVerifiedEmail,
                  color: const Color(greyText),
                  size: 2.1,
                  weight: FontWeight.w500),
              Align(
                alignment: Alignment.centerRight,
                child: InkWell(
                    onTap: () => AuthHelper.c
                        .openChangeEmailSheet(), //AuthHelper.c.toggleEmail,
                    child: CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .edit,
                        size: 1.9,
                        underline: true,
                        weight: FontWeight.w500)),
              ),
            ]),
            const SizedBox(height: 5),
            CustomTextField(isEnabled: false, controller: AuthHelper.c.email),
            SizedBox(height: heightSpace(3)),
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
              CustomText(
                  userModel.value?.userVerification?.phone == "yes"
                      ? "verifiedPhoneNumber".tr
                      : "Unverified phone number",
                  color: const Color(greyText),
                  size: 2.1,
                  weight: FontWeight.w500),
              // Obx(()=>InkWell(
              //     onTap: c.togglePhone,
              //     child: CustomText(c.phoneEnabled.value? "save".tr : "edit".tr,
              //         size: 2.1,
              //         color: Colors.grey[800],
              //         weight: FontWeight.w500,
              //         underline: true),
              //   ),
              // )
            ]),
            const SizedBox(height: 5),
            Obx(() => CustomTextField(
                isEnabled: AuthHelper.c.phoneEnabled.value,
                controller: AuthHelper.c.phone,
                inputType: TextInputType.number)),
            SizedBox(height: heightSpace(3)),
            // SizedBox(height: heightSpace(3)),
            // CustomText("otherVerifications".tr,
            //     size: 2.1, weight: FontWeight.w500),
            // SizedBox(height: heightSpace(2.5)),
            // Row(children: [
            //   Image.asset("assets/icons/google.png", scale: 1.1),
            //   SizedBox(width: widthSpace(6)),
            //   // Expanded(
            //   //   child: Row(
            //   //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //   //       crossAxisAlignment: CrossAxisAlignment.start,
            //   //       children: [
            //   //         Column(
            //   //             crossAxisAlignment: CrossAxisAlignment.start,
            //   //             children: [
            //   //               CustomText("loginWithGoogle".tr,
            //   //                   color: Color(greyText),
            //   //                   size: 2.0,
            //   //                   weight: FontWeight.w500),
            //   //               const CustomText("<EMAIL>",size: 2.1, weight: FontWeight.w500),
            //   //             ]),
            //   //         CustomText("edit".tr,
            //   //             size: 2.1,
            //   //             color: Colors.grey[800],
            //   //             weight: FontWeight.w500,
            //   //             underline: true)
            //   //       ]),
            //   // )
            // ]),
            // SizedBox(height: heightSpace(3.5)),
            // Row(children: [
            //   Image.asset("assets/icons/nic.png", scale: 1.1),
            //   SizedBox(width: widthSpace(6)),
            //   Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            //     CustomText("loginWithNic".tr,
            //         color: const Color(greyText), size: 2.0, weight: FontWeight.w500),
            //     InkWell(
            //         onTap: () {},
            //         child: CustomText("signIn".tr,
            //             size: 2.1, underline: true, weight: FontWeight.w500)),
            //   ])
            // ]),
            // Divider(height: heightSpace(5)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .usersProfile
                        .ninIqama,
                    size: 2.1,
                    weight: FontWeight.w500),
                userModel.value?.yaqeenVerified == true
                    ? const Icon(Icons.check_circle, color: Color(successColor))
                    : IconButton(
                        onPressed: () {
                          AuthHelper.c.iqamaValue.value = 5;
                          customIqamaSheet();
                        },
                        icon: const Icon(
                          Icons.arrow_drop_down,
                        ),
                        iconSize: 25,
                      )
              ],
            ),
            const Divider(),
            SizedBox(height: heightSpace(3)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .usersProfile
                        .prefsTitle,
                    size: 2.1,
                    weight: FontWeight.w500),
                userModel.value?.contactMethodAdded == true
                    ? const IconButton(
                        onPressed: customCommunicationSheet,
                        icon: Icon(Icons.check_circle,
                            color: Color(successColor)),
                        iconSize: 25,
                      )
                    : const IconButton(
                        onPressed: customCommunicationSheet,
                        icon: Icon(
                          Icons.arrow_drop_down,
                        ),
                        iconSize: 25,
                      )
              ],
            ),
            const Divider(),
            // if (c.verificationCode.value) ...[
            //   ViewsCommon.showModalBottom(Column(children: [
            //     SizedBox(height: heightSpace(3)),
            //     Container(
            //       width: double.maxFinite,
            //       padding: EdgeInsets.only(
            //           bottom: heightSpace(2), top: heightSpace(1.7)),
            //       decoration: const BoxDecoration(
            //         border: Border(
            //           bottom: BorderSide(
            //             color: Color.fromARGB(255, 230, 230, 230),
            //           ),
            //         ),
            //       ),
            //       child: Row(
            //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //         children: [
            //           CustomText(
            //             c.iqamaValue.value == 0
            //                 ? c.dobHijriDate.value == null
            //                     ? translation.usersProfile.birthDate
            //                     : "${c.dobHijriDate.value!.year}-${GlobalHelper.twoNumberFormat(c.dobHijriDate.value!.month)}-${GlobalHelper.twoNumberFormat(c.dobHijriDate.value!.day)}"
            //                 : c.dobVerification.value == null
            //                     ? translation.usersProfile.birthDate
            //                     : formDateFormat
            //                         .format(c.dobVerification.value!),
            //             size: 2.1,
            //             color: c.dobVerification.value == null
            //                 ? const Color(greyText)
            //                 : null,
            //             weight: FontWeight.w500,
            //           ),
            //           InkWell(
            //             onTap: c.selectDobVerification,
            //             child: const Icon(Icons.calendar_month_outlined),
            //           ),
            //         ],
            //       ),
            //     ),
            //     SizedBox(height: heightSpace(2.5)),
            //     CustomTextField(
            //       controller: c.iqma,
            //       maxLength: 10,
            //       hint: translation.usersProfile.ninIqama,
            //       inputType: TextInputType.number,
            //     ),
            //     SizedBox(height: heightSpace(2.5)),
            //     Row(
            //       children: [
            //         Row(
            //           children: [
            //             Radio<int>(
            //               value: 0,
            //               groupValue: c.iqamaValue.value,
            //               onChanged: c.updateIqamaValue,
            //             ),
            //             const CustomText("Hijri"),
            //           ],
            //         ),
            //         Row(
            //           children: [
            //             Radio<int>(
            //               value: 1,
            //               groupValue: c.iqamaValue.value,
            //               onChanged: c.updateIqamaValue,
            //             ),
            //             const CustomText("Gregorian"),
            //           ],
            //         ),
            //       ],
            //     ),
            //     SizedBox(height: heightSpace(2.5)),
            //     Align(
            //         alignment: Alignment.bottomRight,
            //         child: CommonButton(
            //             title: translation.usersProfile.save,
            //             onPressed: c.submitIqma,
            //             isLoading: c.isLoading.isTrue,
            //             horizontalPadding: 8))
            //   ]))
            // ]

            // Row(crossAxisAlignment: CrossAxisAlignment.end,
            //   children: [
            //   Image.asset("assets/icons/account_info.png", scale: 1.2),
            //   SizedBox(width: widthSpace(6)),
            //   SizedBox(
            //       width: widthSpace(50),
            //       child: Obx(()=>CustomText(userModel.value?.document==null?"uploadIdPassport".tr:"You document has been uploaded.",
            //             color: const Color(greyText),
            //             size: 2.0,
            //             weight: FontWeight.w500),
            //       )),
            // ]),
          ]),
        ),
      ),
    );
  }
}
