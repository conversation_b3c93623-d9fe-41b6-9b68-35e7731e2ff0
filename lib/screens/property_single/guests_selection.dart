import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_detail_controller.dart';
import 'package:darent/models/translationModel.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';

class GuestsSelection extends StatelessWidget{
  final TranslationModel translations;
  final PropertyDetailController c;
  const GuestsSelection({required this.c,required this.translations,super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(widthSpace(5)),
      margin: EdgeInsets.only(bottom: heightSpace(2.5)),
      decoration: BoxDecoration(
          border:Border.all(color: const Color(greyBorder)),
          borderRadius: BorderRadius.circular(22)),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: c.toggleGuests,
              child: Row(
                  mainAxisAlignment:MainAxisAlignment.spaceBetween,
                  children: [
                    CustomText(translations.propertySingle.guests,
                        size: 2.2,
                        weight: FontWeight.w500),
                    CustomText(
                        "${c.adults} ${translations.listing.adults}, ${c.children} ${c.children.value <= 1 ? translations.listing.child : translations.listing.children}",
                        size: 2.2),
                  ]),
            ),
            SizedBox(height: heightSpace(4)),
            Row(
                mainAxisAlignment:MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                      crossAxisAlignment:CrossAxisAlignment.start,
                      children: [
                        CustomText(translations.listing.adults,weight: FontWeight.w500),
                        const SizedBox(height: 5),
                        CustomText(translations.header.moreThan13Y,
                            size: 1.9,
                            color: const Color(greyText)),
                      ]),
                  Row(children: [
                    InkWell(
                      onTap: () => c.plusMinusAdults("-"),
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),shape: BoxShape.circle),
                        child: const Icon(Icons.remove,color: Colors.grey, size: 21),
                      ),
                    ),
                    SizedBox(width: widthSpace(4)),
                    CustomText(c.adults.toString(),
                        weight: FontWeight.w500),
                    SizedBox(width: widthSpace(4)),
                    InkWell(
                      onTap: () => c.plusMinusAdults("+"),
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),shape: BoxShape.circle),
                        child: const Icon(Icons.add,color: Colors.grey, size: 21),
                      ),
                    )
                  ])
                ]),
            Divider(height: heightSpace(5)),
            Row(
                mainAxisAlignment:MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                      crossAxisAlignment:CrossAxisAlignment.start,
                      children: [
                        CustomText(translations.listing.children,weight: FontWeight.w500),
                        const SizedBox(height: 5),
                        CustomText(translations.header.lessThan13Y,size: 1.9,color: const Color(greyText)),
                      ]),
                  Row(children: [
                    InkWell(
                      onTap: () =>c.plusMinusChildren("-"),
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey),
                            shape: BoxShape.circle),
                        child: const Icon(Icons.remove,color: Colors.grey, size: 21),
                      ),
                    ),
                    SizedBox(width: widthSpace(4)),
                    CustomText(c.children.toString(),weight: FontWeight.w500),
                    SizedBox(width: widthSpace(4)),
                    InkWell(
                      onTap: () => c.plusMinusChildren("+"),
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(border: Border.all(color: Colors.grey),shape: BoxShape.circle),
                        child: const Icon(Icons.add,color: Colors.grey, size: 21)))])]),
            // if(c.guestError.value!=null)...[
            //   Divider(height: heightSpace(5)),
            //   Row(children: [
            //     const Icon(Icons.warning_rounded,color: Color(themeColor)),
            //     SizedBox(width: widthSpace(2)),
            //     CustomText(c.guestError.value,size: 1.8,weight: FontWeight.w500,color: Colors.black54)
            //   ]),
            // ],
          ]),
    );
  }}