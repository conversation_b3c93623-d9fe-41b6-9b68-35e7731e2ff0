import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/property_detail_controller.dart';
import '../../helperMethods/remote_config.dart';
import '../../helperMethods/translation_helper.dart';

class HostMessage extends StatelessWidget {
  const HostMessage({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    PropertyDetailController propertyC = Get.find();
    return Scaffold(
      body: SingleChildScrollView(
        padding: EdgeInsets.all(widthSpace(6)),
        child: Obx(
          () => Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: heightSpace(3)),
                Align(
                    alignment: Alignment.centerLeft,
                    child: InkWell(
                        onTap: Get.back,
                        child: const Icon(
                          Icons.arrow_back,
                          size: 28,
                        ))),
                SizedBox(
                  height: heightSpace(4),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                          CustomText(propertyC.data.value?.propertyTitle,
                              size: 2,
                              maxlines: 1,
                              textOverflow: TextOverflow.ellipsis,
                              weight: FontWeight.w500),
                          SizedBox(
                            height: heightSpace(0.3),
                          ),
                          if (propertyC
                                  .data.value?.propertyAddress!.addressLine1 !=
                              null) ...[
                            CustomText(
                                propertyC.data.value?.propertyAddress
                                        ?.addressLine1 ??
                                    "",
                                size: 1.9,
                                maxlines: 2,
                                textOverflow: TextOverflow.ellipsis,
                                color: Colors.grey,
                                weight: FontWeight.w500)
                          ],
                        ])),
                    SizedBox(
                      width: widthSpace(5),
                    ),
                    Container(
                        height: heightSpace(9),
                        width: widthSpace(22),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8)),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image(
                              image: GlobalHelper.buildNetworkImageProvider(url: propertyC.data.value?.coverPhoto ??"",),
                              fit: BoxFit.fill,
                          ),
                        ))
                    // if(c.userModel.value!=null)c.userModel.value!.profile_src!.contains("svg")
                    //     ?SvgPicture.network(c.userModel.value !.profile_src!,
                    //     height: widthSpace(15),
                    //     width: widthSpace(15))
                    //     :ClipOval(
                    //     child: Image.network(
                    //       c.userModel.value!.profile_src!,
                    //       height: widthSpace(15),
                    //       width: widthSpace(15),
                    //           fit: BoxFit.fitWidth,

                    //       errorBuilder: (context, error, stackTrace) {
                    //         return Icon(Icons.person, size: widthSpace(10));
                    //       },
                    //     ),
                    //   ),
                  ],
                ),
                Divider(height: heightSpace(6)),

                InkWell(
                    onTap: () =>
                        propertyC.selectReserveDates(fromBooking: true),
                    child: renderGrey(
                        "Date",
                        "${propertyC.startDate.value.day} ${months[Get.locale?.languageCode ?? 'en']![propertyC.startDate.value.month - 1]} ${propertyC.startDate.value.year % 100} -"
                            "${propertyC.endDate.value.day} ${months[Get.locale?.languageCode ?? 'en']![propertyC.endDate.value.month - 1]} ${propertyC.endDate.value.year % 100} )")),
                SizedBox(height: heightSpace(2)),
                SizedBox(height: heightSpace(2)),
                // propertyC.isGuestsOpen.isTrue
                //             ? GuestsSelection(c: propertyC, translations: Get.find<TranslationHelper>().translations)
                //             : InkWell(
                //                 onTap: propertyC.toggleGuests,
                //                 child: renderGrey(Get.find<TranslationHelper>().translations.listing.guests,
                //                     "${propertyC.adults} ${Get.find<TranslationHelper>().translations.listing.adults}, ${propertyC.children} ${propertyC.children <= 1 ? Get.find<TranslationHelper>().translations.listing.child : Get.find<TranslationHelper>().translations.listing.children}")),
                //
                //                 SizedBox(height: heightSpace(3)),
                Form(
                  key: propertyC.msgKey,
                  child: CustomTextField(
                      controller: propertyC.messageHost,
                      hint: Get.find<TranslationHelper>()
                          .translations
                          .contactHost
                          .typeHere,
                      validator: (value) => value.trim().isEmpty
                          ? Get.find<TranslationHelper>()
                              .translations
                              .jqueryValidation
                              .required
                          : null,
                      maxlines: 4,
                      isRoundedBorder: true),
                )
              ]),
        ),
      ),
      bottomNavigationBar: Container(
        height: heightSpace(12),
        margin:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(4), vertical: heightSpace(1)),
        decoration: const BoxDecoration(
            border: Border(top: BorderSide(color: Color(greyBorder)))),
        child: Column(
          children: [
            SizedBox(height: heightSpace(1)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .propertySingle
                        .chatWithTheHost,
                    size: 1.7,
                    color: Colors.black54),
                Obx(
                  () => CommonButton(
                      title: propertyC.isUnAvailable.value
                          ? Get.find<TranslationHelper>()
                              .translations
                              .contactHost
                              .checkAvailability
                          : Get.find<TranslationHelper>()
                              .translations
                              .propertySingle
                              .chatWithTheHost,
                      isLoading: propertyC.isBtnLoading.value,
                      isBorder: propertyC.isUnAvailable.value,
                      onPressed: propertyC.isUnAvailable.value
                          ? propertyC.selectReserveDates
                          : () {
                              if (propertyC.msgKey.currentState?.validate() ??
                                  false) {
                                GlobalHelper.removeFocus();
                                Get.close(2);
                                propertyC.onContactSeller();
                              }
                            }),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

renderGrey(text1, text2) {
  return Container(
    padding: EdgeInsets.all(widthSpace(5)),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(15),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.3),
          spreadRadius: 0.2,
          blurRadius: 3,
          offset: const Offset(2, 2), // changes the position of the shadow
        ),
      ],
    ),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        CustomText(text1, size: 2.2, weight: FontWeight.w500),
        CustomText(text2, size: 2, color: Colors.black.withOpacity(0.5)),
      ],
    ),
  );
}
