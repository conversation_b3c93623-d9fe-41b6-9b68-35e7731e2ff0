import 'package:carousel_slider/carousel_slider.dart';
import 'package:darent/controllers/property_detail_controller.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../components/views_common.dart';
import '../../helperMethods/globalHelpers.dart';

class PropertyPictures extends StatelessWidget {
  PropertyPictures({Key? key}) : super(key: key);

  final CarouselSliderController controller = CarouselSliderController();
  @override
  Widget build(BuildContext context) {
    PropertyDetailController c = Get.find();

    // _controller = PageController(viewportFraction: 0.9, initialPage: 0);
    List<Widget> photoWidgets = [];
    for(int i=0; i<c.data.value!.propertyPhotos!.length; i++){
      // if(currentIndex>0 && currentIndex==i){
      //   continue;
      // }
      // if(i%3==0){
      //   currentIndex = i+1;
        photoWidgets.add(Container(
            height: heightSpace(35),
            color: Colors.grey[100],
            margin: EdgeInsets.symmetric(vertical: widthSpace(2.5)),
            child: Image(
                image: GlobalHelper.buildNetworkImageProvider(url: c.data.value?.propertyPhotos?[i].photo ??"",),
                )));
      // }
      // else{
      //   photoWidgets.add(SizedBox(
      //     height: heightSpace(35),
      //     child: Row(crossAxisAlignment: CrossAxisAlignment.stretch,children: [
      //       Expanded(child: Container(
      //     color: Colors.grey[100],child: Image.network("$baseUrl/${c.data.value!.propertyPhotos![i].photo}"))),
      //       SizedBox(width: widthSpace(2.5)),
      //       if((currentIndex+1)<=c.data.value!.propertyPhotos!.length)
      //         Expanded(child: Container(color: Colors.grey[100],child: Image.network("$baseUrl/${c.data.value!.propertyPhotos![currentIndex].photo}")))
      //     ]),
      //   ));
      // }
    }
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(icon:const Icon(Icons.chevron_left),onPressed: Get.back),
        title: const Text('Pictures'),
      ),
      body: SizedBox(
        width: widthSpace(100),
        height: heightSpace(100),
        child: Stack(
          alignment: Alignment.center,
          children: [
            CarouselSlider(
                carouselController: controller,
                items: photoWidgets,
                options: CarouselOptions(
              viewportFraction: 1,
              height: heightSpace(100),
              onPageChanged: (index, reason) {
                c.detailImageStep.value = index;
              },
            )),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: 18.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(icon: Icon(Icons.chevron_left,size: heightSpace(7),color: Colors.white,shadows: ViewsCommon.boxShadow),onPressed: (){
                    if(c.detailImageStep.value != 0){
                      controller.animateToPage(c.detailImageStep.value-1);
                    }
                  }),
                  IconButton(icon: Icon(Icons.chevron_right,size: heightSpace(7),color: Colors.white,shadows: ViewsCommon.boxShadow),onPressed: (){
                    if(c.detailImageStep.value != photoWidgets.length-1){
                      controller.animateToPage(c.detailImageStep.value+1);
                    }
                  }),
                ],
              ),
            ),
          ],
        ),
      ),
      // ListView(padding: EdgeInsets.only(top:widthSpace(2.5)),children: photoWidgets),
    );
  }
}
