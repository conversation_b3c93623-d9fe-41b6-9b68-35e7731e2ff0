import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/screens/property_single/hostMessage.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../controllers/property_detail_controller.dart';
import '../../helperMethods/translation_helper.dart';

class ContactHost extends StatelessWidget {
  const ContactHost({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    PropertyDetailController pc = Get.find();
    return Scaffold(
      body: SingleChildScrollView(
        padding: EdgeInsets.all(widthSpace(6)),
        child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: heightSpace(3)),
              Align(
                  alignment: Alignment.centerLeft,
                  child: InkWell(
                      onTap: Get.back,
                      child: const Icon(
                        Icons.arrow_back,
                        size: 28,
                      ))),
              SizedBox(height: heightSpace(4)),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(
                            "${Get.find<TranslationHelper>().translations.contactHost.contact} ${pc.data.value?.hostName}",
                            size: 2.2,
                            weight: FontWeight.w500),
                        CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .contactHost
                                .typicallyResponseInHour,
                            color: Colors.black54,
                            size: 1.7),
                      ],
                    ),
                  ),
                  SizedBox(width: widthSpace(5)),
                  if (userModel.value != null)
                    userModel.value!.profile_src!.contains("svg")
                        ?  GlobalHelper.buildNetworkSvgWidget(
                      url: userModel.value?.profile_src ??"",
                      width: widthSpace(14),
                      height: widthSpace(14),
                      defaultOption: Icon(Icons.person,
                        size: widthSpace(14),
                        ),
                    )
                        : ClipOval(
                            child: GlobalHelper.resolveImageUrl(userModel.value?.profile_src ??"").isNotEmpty
                                ? Image(
                              image: GlobalHelper.buildNetworkImageProvider(
                                  url: userModel.value?.profile_src ??"",),
                              height: widthSpace(14),
                              width: widthSpace(14),
                              fit: BoxFit.fitWidth,
                            )
                                : Icon(Icons.person, size: widthSpace(10)),
                          ),
                ],
              ),
              Divider(height: heightSpace(6)),
              // const  CustomText(
              //       "Most travellers ask about",
              //       size: 2.2,
              //       weight: FontWeight.w500),
              //      SizedBox(height: heightSpace(3),),

              CustomText(
                  "${Get.find<TranslationHelper>().translations.reservation.gettingThere} ",
                  size: 1.9,
                  weight: FontWeight.w500),
              SizedBox(height: heightSpace(.7)),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .contactHost
                      .checkinCheckoutBetween
                      .replaceAll(':checkintime', pc.data.value?.checkin ?? '')
                      .replaceAll(
                          ':checkouttime', pc.data.value?.checkout ?? ''),
                  size: 1.7,
                  color: Colors.black54),
              //      SizedBox(height: heightSpace(3),),
              //       CustomText(
              //       "House details and rules ",
              //       size: 1.9,
              //       weight: FontWeight.w500),
              //        SizedBox(height: heightSpace(.7)),
              //       CustomText(
              //           "No smoking",
              //           size: 1.7,
              //           color: Colors.black54
              //      ),
              //      SizedBox(height: heightSpace(3),),
              //       CustomText(
              //           "Price and availability",
              //           size: 1.9,
              //           weight: FontWeight.w500),
              // SizedBox(height: heightSpace(.7)),
              //       CustomText(
              //       ".  Get a 5% discount on stays longer than a month",
              //           size: 1.7,
              //           color: Colors.black54
              //      ),
              //      CustomText(
              //       ".  Full refund within limited period ",
              //          size: 1.7,
              //          color: Colors.black54
              //      ),
              // Divider(height: heightSpace(5)),
              //      Row( mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //        children: [
              //          Obx(()=>RichText(
              //              text: TextSpan(
              //                text: "This home is Available : ",
              //                style: TextStyle(
              //                  fontSize: heightSpace(1.6),
              //                  color: Colors.black54,
              //                  // fontWeight:FontWeight.w500,
              //                ),
              //                children: <TextSpan>[
              //                  TextSpan(
              //                      text: "${pc.formatter1.format(pc.startDate.value)} - ${pc.formatter1.format(pc.endDate.value)} ",
              //                      style: TextStyle(
              //                          color:const Color(themeColor),
              //                          decoration: TextDecoration.underline),recognizer: TapGestureRecognizer()..onTap=(){
              //                    pc.selectReserveDates(fromBooking:true);
              //                  }),
              //                ],
              //              ),
              //            ),
              //          ),
              //        ],
              //      ),
              // SizedBox(height:heightSpace(5)),
              // pc.data.value == null
              //              ? Image.asset('assets/loader.gif')
              //              : CommonButton(
              //                isBorder: true,
              //                fontSize: 1.7,
              //                backgroundBg: Colors.white,
              //                  horizontalPadding: 5,
              //                  title: pc.isUnAvailable.value
              //                      ? "Check Availability"
              //                      : Get.find<TranslationHelper>().translations.propertySingle.reserve,
              //                  isLoading: c.isLoading.value,
              //                  onPressed: pc.isUnAvailable.value
              //                      ? pc.selectReserveDates
              //                      : pc.gotoBooking)
            ]),
      ),
      bottomNavigationBar: Container(
        height: heightSpace(12),
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(4), vertical: heightSpace(1)),
        decoration: const BoxDecoration(
            border: Border(top: BorderSide(color: Color(greyBorder)))),
        child: Column(
          children: [
            SizedBox(height: heightSpace(1)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomText(
                    Get.find<TranslationHelper>()
                        .translations
                        .contactHost
                        .stillHaveQuestion,
                    size: 1.7,
                    color: Colors.black54),
                CommonButton(
                    title: Get.find<TranslationHelper>()
                        .translations
                        .propertySingle
                        .chatWithTheHost,
                    onPressed: () => Get.to(() => HostMessage())),
                // CommonButton(
                //             fontSize: 2.4,
                //             title: translation.propertySingle.chatWithTheHost,
                //             isLoading: pc.isBtnLoading.value,
                //             onPressed: pc.onContactSeller),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
