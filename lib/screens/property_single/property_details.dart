import 'package:carousel_slider/carousel_slider.dart';
import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/payment_method_icon.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/property_detail_controller.dart';
import 'package:darent/helperMethods/payment_helper.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/models/productDetailModel.dart';
import 'package:darent/models/property_reviews.dart';
import 'package:darent/screens/property_single/property_pictures.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/avg_rating_by_five.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:map_launcher/map_launcher.dart' as custom_launcher;
import 'package:shimmer/shimmer.dart';
import 'package:tabby_flutter_inapp_sdk/tabby_flutter_inapp_sdk.dart';
import '../../helperMethods/globalHelpers.dart';
import '../../helperMethods/search_helper.dart';
import '../../helperMethods/translation_helper.dart';
import '../../models/homeProperty.dart';
import '../../utils/routes.dart';
import 'package:darent/core/utility.dart';

class PropertyDetailScreen extends StatefulWidget {
  final String slug;
  final bool scrollToMap;
  final HomeProperty? data;

  const PropertyDetailScreen({
    super.key,
    required this.slug,
    this.scrollToMap=false,
    this.data,
  });

  @override
  State<PropertyDetailScreen> createState() => _PropertyDetailScreenState();
}

class _PropertyDetailScreenState extends State<PropertyDetailScreen> {

  late final PropertyDetailController c;

  @override
  void initState() {
    super.initState();
    c = Get.isRegistered<PropertyDetailController>()
        ? (Get.find<PropertyDetailController>()
          ..slug = widget.slug
          ..getData())
        : Get.put(PropertyDetailController(slug: widget.slug,scrollToMap:widget.scrollToMap));
  }

  @override
  Widget build(BuildContext context) {
    List previousRoutes = [Routes.home, '/Dashboard', '/Wishlist'];
    return PopScope(
      canPop: previousRoutes.contains(Get.previousRoute),
      onPopInvokedWithResult: (didPope, _) {
        if (!didPope) backFunction();
      },
      child: Get.find<TranslationHelper>().translateKeywords.isEmpty
          ? Scaffold(body: loader)
          : Obx(
              () => Scaffold(
                appBar: c.message.value != null ? AppBar() : null,
                body: c.message.value != null
                    ? Center(
                        child:
                            Column(mainAxisSize: MainAxisSize.min, children: [
                          CustomText(c.message.value,
                              textAlign: TextAlign.center),
                          TextButton.icon(
                            icon: const Icon(Icons.keyboard_return_rounded),
                            label: Text(Get.find<TranslationHelper>()
                                    .translations
                                    .propertySingle
                                    .browse ??
                                ''),
                            onPressed: () {
                              if (Get.previousRoute.isEmpty ||
                                  // Get.previousRoute == Routes.home ||
                                  Get.previousRoute == Routes.splash) {
                                Get.offAllNamed(Routes.home);
                              } else {
                                Get.until((r) => r.isFirst);
                                SearchHelper.c.changeIndex(0);
                              }
                            },
                          )
                        ]).paddingSymmetric(horizontal: widthSpace(5)),
                      )
                    // : c.data.value == null
                    //     ? const Center(child: CircularProgressIndicator())
                    : ListView(
                    controller: c.scrollController,
                    children: [
                        SizedBox(
                          height: heightSpace(40),
                          width: double.maxFinite,
                          child: Stack(
                            children: [
                              c.data.value == null
                                  ? FadeInImage.assetNetwork(
                                      placeholder: 'assets/default-image.png',
                                      image: GlobalHelper.resolveImageUrl(
                                          widget.data?.photo ?? ''),
                                      imageErrorBuilder: (c, e, s) =>
                                          Image.asset(
                                              'assets/default-image.png'),
                                      width: double.maxFinite,
                                      fit: BoxFit.fill,
                                    )
                                  : c.data.value!.propertyPhotos!.isEmpty
                                      ? Image.asset('assets/default-image.png',
                                          height: heightSpace(40))
                                      : CarouselSlider.builder(
                                          itemBuilder:
                                              (context, index, realIndex) {
                                            return InkWell(
                                              onTap: () {
                                                c.detailImageStep.value = 0;
                                                Get.to(
                                                    () => PropertyPictures());
                                              },
                                              child: FadeInImage.assetNetwork(
                                                placeholder:
                                                    'assets/default-image.png',
                                                image:
                                                    "${c.data.value?.propertyPhotos![index].photo?.contains('http') == true ? '' : Get.find<RemoteConfig>().imagesBaseUrl}${c.data.value?.propertyPhotos![index].photo}",
                                                imageErrorBuilder:
                                                    (ctx, e, s) => Image.asset(
                                                        "assets/default-image.png",
                                                        width: double.maxFinite,
                                                        fit: BoxFit.cover),
                                                width: double.maxFinite,
                                                fit: BoxFit.fill,
                                              ),
                                            );
                                          },
                                          options: CarouselOptions(
                                              height: heightSpace(40),
                                              viewportFraction: 1,
                                              onPageChanged:
                                                  c.onCarouselChanged),
                                          itemCount: c.data.value
                                                  ?.propertyPhotos?.length ??
                                              0),
                              Padding(
                                padding:
                                    EdgeInsets.all(widthSpace(viewPadding)),
                                child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      InkWell(
                                        onTap: backFunction,
                                        child: Container(
                                          padding:
                                              EdgeInsets.all(widthSpace(2.5)),
                                          decoration: const BoxDecoration(
                                              color: Colors.white,
                                              shape: BoxShape.circle),
                                          child: const Icon(Icons.chevron_left),
                                        ),
                                      ),
                                      Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Column(children: [
                                            if (c.data.value?.hostId !=
                                                userModel.value?.id)
                                              InkWell(
                                                onTap: c.addToWishlist,
                                                child: Container(
                                                  padding: EdgeInsets.all(
                                                      widthSpace(2.5)),
                                                  decoration:
                                                      const BoxDecoration(
                                                          color: Colors.white,
                                                          shape:
                                                              BoxShape.circle),
                                                  child: Icon(
                                                      c.data.value?.wishlist ==
                                                              true
                                                          ? Icons
                                                              .favorite_rounded
                                                          : Icons
                                                              .favorite_border_rounded,
                                                      size: widthSpace(6),
                                                      color: c.data.value
                                                                  ?.wishlist ==
                                                              true
                                                          ? const Color(
                                                              0xffff2e2e)
                                                          : null),
                                                ),
                                              ),
                                            SizedBox(height: heightSpace(1)),
                                            InkWell(
                                              onTap:
                                                  //ViewsCommon.share('${baseUrl}/properties/${c.data.value!.id}'),
                                                  c.share,
                                              child: Container(
                                                padding: EdgeInsets.all(
                                                    widthSpace(2.5)),
                                                decoration: const BoxDecoration(
                                                    color: Colors.white,
                                                    shape: BoxShape.circle),
                                                child: Icon(
                                                    Icons.share_outlined,
                                                    size: widthSpace(6)),
                                              ),
                                            ),
                                          ]),
                                          if (c.data.value != null)
                                            Container(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 14,
                                                        vertical: 4.5),
                                                decoration: BoxDecoration(
                                                    color: Colors.black
                                                        .withOpacity(.4),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6)),
                                                child: CustomText(
                                                    "${c.currentPhoto.value}/${c.data.value!.propertyPhotos!.length}",
                                                    size: 1.8,
                                                    color: Colors.white))
                                        ],
                                      )
                                    ]),
                              ),
                            ],
                          ),
                        ),
                        c.data.value != null
                            ? Padding(
                                padding:
                                    EdgeInsets.all(widthSpace(viewPadding)),
                                child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Expanded(
                                            child: CustomText(
                                                c.data.value?.propertyTitle,
                                                size: 2.4,
                                                weight: FontWeight.w500),
                                          ),
                                          if (c.data.value?.discount != null)
                                            Container(
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: widthSpace(5),
                                                  vertical: 5),
                                              decoration: BoxDecoration(
                                                  color:
                                                      const Color(themeColor),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          12)),
                                              child: CustomText(
                                                  "${c.data.value!.discount} %",
                                                  color: Colors.white,
                                                  weight: FontWeight.w500),
                                            )
                                        ],
                                      ),
                                      // SizedBox(height: heightSpace(1)),
                                      // CustomText(
                                      //     c.data.value?.propertyAddress?.addressLine1 ??
                                      //         "",
                                      //     color: Colors.black54),
                                      // if (c.data.value?.propertyAddress!.addressLine2 !=null) ...[
                                      //   CustomText(
                                      //       c.data.value?.propertyAddress?.addressLine2,
                                      //       color: Colors.black54),
                                      // ],
                                      SizedBox(height: heightSpace(1)),
                                      CustomText(
                                          "${c.data.value?.bedrooms} ${Get.find<TranslationHelper>().translations.propertySingle.bedroom}"
                                          // ", ${c.data.value?.adultGuest ?? "0"} ${Get.find<TranslationHelper>().translations.listing.adults}, ${c.data.value?.childrenGuest ?? "0"} ${Get.find<TranslationHelper>().translations.listing.children}"
                                          ", ${c.data.value?.beds} ${Get.find<TranslationHelper>().translations.propertySingle.bed}, ${c.data.value?.bathrooms} ${Get.find<TranslationHelper>().translations.propertySingle.bathroom}, "
                                              "${(c.data.value?.adultGuest??1)+(c.data.value?.childrenGuest??0)} ${Get.find<TranslationHelper>().translations.listing.guests}",
                                          color: Colors.black54),
                                      SizedBox(height: heightSpace(1.5)),
                                      Directionality(
                                        textDirection: TextDirection.ltr,
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            PropertyRating(
                                              avgRating: c
                                                      .data
                                                      .value
                                                      ?.reviewAverage
                                                      ?.avgTotal ??
                                                  0,
                                              rootDirectionalityContext:
                                                  context,
                                              isNewProperty: c.data.value?.isNewLable ?? false,
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                CustomText(
                                                    c.data.value
                                                            ?.spaceTypeLang ??
                                                        "",
                                                    size: 2.1,
                                                    color:
                                                        Colors.tealAccent[700],
                                                    weight: FontWeight.w500),
                                                if (c.data.value
                                                        ?.propertyCode !=
                                                    null) ...[
                                                  SizedBox(
                                                      height:
                                                          (heightSpace(.5))),
                                                  Directionality(
                                                    textDirection: Get.locale
                                                                ?.languageCode ==
                                                            'ar'
                                                        ? TextDirection.rtl
                                                        : TextDirection.ltr,
                                                    child: RichText(
                                                      text: TextSpan(
                                                        text:
                                                            "${Get.find<TranslationHelper>().translations.home.unitCode}: ",
                                                        style: TextStyle(
                                                          fontSize:
                                                              heightSpace(1.8),
                                                          color: Colors.black87,
                                                          // fontWeight:FontWeight.w500,
                                                        ),
                                                        children: <TextSpan>[
                                                          TextSpan(
                                                              text: c.data.value
                                                                  ?.propertyCode,
                                                              style: TextStyle(
                                                                  fontSize:
                                                                      heightSpace(
                                                                          2),
                                                                  color: const Color(
                                                                      themeColor),
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500)),
                                                        ],
                                                      ),
                                                    ),
                                                  )
                                                ],
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                      if (c.data.value?.licenseNo != null) ...[
                                        Divider(height: heightSpace(3)),
                                        Row(
                                          children: [
                                            Icon(Icons.check_circle,
                                                color: const Color(themeColor),
                                                size: widthSpace(7)),
                                            SizedBox(width: widthSpace(1)),
                                            CustomText(
                                                '${Get.find<TranslationHelper>().translations.listing.licenseNumber}: ',
                                                weight: FontWeight.w500,
                                                size: 1.9),
                                            CustomText(c.data.value?.licenseNo,
                                                weight: FontWeight.bold,
                                                size: 1.9),
                                          ],
                                        ),
                                      ],
                                      SizedBox(height: heightSpace(1.5)),
                                      const SupportedPayments(),
                                      if (!c.isUnAvailable.value) ...[
                                        TabbyPresentationSnippet(
                                          price: c.data.value?.discount != null
                                              ? c.data.value?.discountedAmount
                                                      .toString() ??
                                                  "0.0"
                                              : c.data.value?.propertyPrice
                                                      ?.price
                                                      ?.toStringAsFixed(2) ??
                                                  "0.0",
                                          currency: Currency.sar,
                                          lang: Get.locale!.languageCode == "en"
                                              ? Lang.en
                                              : Lang.ar,
                                        ),
                                      ],
                                      Divider(height: heightSpace(6)),
                                      Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Row(children: [
                                              ClipRRect(
                                                borderRadius:
                                                    BorderRadius.circular(24),
                                                child: (c.data.value!.hostProfile ?? "").contains(".svg")
                                                    ? GlobalHelper.buildNetworkSvgWidget(
                                                  url: c.data.value?.hostProfile??"",
                                                  width: 48,
                                                  height: 48,
                                                  defaultOption: const Icon(Icons.person,
                                                    size:48,
                                                  ),)
                                                    : GlobalHelper.resolveImageUrl( c.data.value?.hostProfile ??"").isNotEmpty
                                                    ? Image(
                                                        image: GlobalHelper.buildNetworkImageProvider(
                                                          url: c.data.value?.hostProfile??"",),
                                                        width: 48,
                                                        height: 48,
                                                        fit: BoxFit.fill,
                                                        )
                                                    : Container(
                                                    width:
                                                    widthSpace(12),
                                                    height:
                                                    widthSpace(11),
                                                    decoration: BoxDecoration(
                                                        color: Colors
                                                            .grey[200],
                                                        shape: BoxShape
                                                            .circle),
                                                    child: const Icon(
                                                        Icons.person,
                                                        size: 22)),
                                              ),
                                              // c.data.value?.hostProfile != null
                                              //     ? c.data.value!.hostProfile!.contains("svg")
                                              //         ? SvgPicture.network(
                                              //             c.data.value!.hostProfile!,
                                              //             width: widthSpace(11))
                                              //         : Image.network(
                                              //             c.data.value!.hostProfile!,
                                              //             width: widthSpace(11),
                                              //             errorBuilder: (context, error,
                                              //                 stackTrace) {
                                              //               return Container(
                                              //                   width: widthSpace(12),
                                              //                   height: widthSpace(11),
                                              //                   decoration:
                                              //                       BoxDecoration(
                                              //                           color: Colors
                                              //                               .grey[200],
                                              //                           shape: BoxShape
                                              //                               .circle),
                                              //                   child: const Icon(
                                              //                       Icons.person,
                                              //                       size: 22));
                                              //             },
                                              //           )
                                              //     : Container(
                                              //         width: widthSpace(12),
                                              //         height: widthSpace(11),
                                              //         decoration: BoxDecoration(
                                              //             color: Colors.grey[200],
                                              //             shape: BoxShape.circle),
                                              //         child: const Icon(Icons.person,
                                              //             size: 22)),
                                              SizedBox(width: widthSpace(5)),
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  CustomText(
                                                      c.data.value?.hostName,
                                                      size: 2.4,
                                                      weight: FontWeight.w500),
                                                  const SizedBox(height: 2),
                                                  CustomText(
                                                      "${Get.find<TranslationHelper>().translations.propertySingle.joinedIn} ${months[Get.locale?.languageCode ?? 'en']![c.data.value!.hostJoiningDate!.month - 1]} ${c.data.value!.hostJoiningDate!.year}",
                                                      color:
                                                          const Color(greyText),
                                                      size: 1.7)
                                                ],
                                              ),
                                            ]),
                                            // if (userModel.value?.id !=
                                            //     c.data.value?.hostId && !isHost)
                                            //       CommonButton(
                                            //       title: Get.find<TranslationHelper>().translations.propertySingle.chatWithTheHost,
                                            //       onPressed:(){
                                            //         c.onContactSeller(chatHeadId:c.data.value?.chatHeadId!=null);
                                            //       })
                                          ]),
                                      Divider(height: heightSpace(6)),
                                      CustomText(
                                          Get.find<TranslationHelper>()
                                              .translations
                                              .propertySingle
                                              .overveiw,
                                          size: 2.4,
                                          weight: FontWeight.w500),
                                      SizedBox(height: heightSpace(1)),
                                      CustomText(
                                          (Get.locale?.languageCode == 'ar'
                                                  ? c
                                                      .data
                                                      .value
                                                      ?.propertyDescription
                                                      ?.summaryAr
                                                  : c
                                                      .data
                                                      .value
                                                      ?.propertyDescription
                                                      ?.summary) ??
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .home
                                                  .noDescription,
                                          color: Colors.black54),
                                      Divider(height: heightSpace(6)),
                                      CustomText(
                                          Get.find<TranslationHelper>()
                                              .translations
                                              .propertySingle
                                              .amenity,
                                          size: 2.4,
                                          weight: FontWeight.w500),
                                      SizedBox(height: heightSpace(2.5)),
                                      if (c.data.value?.amenities != null) ...[
                                        Wrap(
                                            runSpacing: heightSpace(2),
                                            children: c.data.value!.amenities!
                                                .map<Widget>((item) => SizedBox(
                                                    width: widthSpace(40),
                                                    child: extrasComponent(
                                                        item.icon_image,
                                                        Get.locale?.languageCode ==
                                                                "ar"
                                                            ? item.titleAr
                                                            : item.title)))
                                                .toList())
                                      ],
                                      if (c.data.value!.safetyAmenities!
                                          .isNotEmpty) ...[
                                        Divider(height: heightSpace(6)),
                                        CustomText(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .propertySingle
                                                .safetyFeature,
                                            size: 2.4,
                                            weight: FontWeight.w500),
                                        SizedBox(height: heightSpace(2.5)),
                                        for (Amenity item
                                            in c.data.value!.safetyAmenities
                                                as List<Amenity>) ...[
                                          extrasComponent(
                                            item.icon_image,
                                            Get.locale?.languageCode == "ar"
                                                ? item.titleAr
                                                : item.title,
                                          ),
                                          SizedBox(height: heightSpace(2)),
                                        ],
                                      ],
                                      const Divider(),
                                      CustomText(
                                          Get.find<TranslationHelper>()
                                              .translations
                                              .propertySingle
                                              .accommodationRules,
                                          size: 2.4,
                                          weight: FontWeight.w500),
                                      SizedBox(height: heightSpace(2.5)),
                                      SizedBox(
                                        width: widthSpace(50),
                                        child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                  child: extrasComponent(
                                                      "assets/icons/clock.png",
                                                      Get.find<
                                                              TranslationHelper>()
                                                          .translations
                                                          .propertySingle
                                                          .checkIn)),
                                              CustomText(
                                                  c.data.value!.checkin!
                                                          .isNotEmpty
                                                      ? c.data.value!.checkin
                                                      : "12:00 pm",
                                                  color: Colors.tealAccent[700],
                                                  size: 2.1,
                                                  weight: FontWeight.w500),
                                            ]),
                                      ),
                                      SizedBox(height: heightSpace(2)),
                                      SizedBox(
                                        width: widthSpace(50),
                                        child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Expanded(
                                                  child: extrasComponent(
                                                      "assets/icons/clock.png",
                                                      Get.find<
                                                              TranslationHelper>()
                                                          .translations
                                                          .propertySingle
                                                          .checkOut)),
                                              CustomText(
                                                  c.data.value!.checkout!
                                                          .isNotEmpty
                                                      ? c.data.value?.checkout
                                                      : "02:00 pm",
                                                  color: Colors.tealAccent[700],
                                                  size: 2.1,
                                                  weight: FontWeight.w500),
                                            ]),
                                      ),
                                      SizedBox(height: heightSpace(2)),
                                      Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Image.asset(
                                                "assets/icons/infant.png",
                                                scale: 1.5,
                                                color: Colors.grey[500]),
                                            SizedBox(width: widthSpace(5)),
                                            SizedBox(
                                                width: widthSpace(75),
                                                child: CustomText(
                                                    Get.find<
                                                            TranslationHelper>()
                                                        .translations
                                                        .propertySingle
                                                        .suitableForChildren,
                                                    color: Colors.grey[800])),
                                          ]),
                                      Divider(height: heightSpace(5)),
                                      if (c.data.value!.houseruleAmenities!
                                          .isNotEmpty) ...[
                                        CustomText(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .payment
                                                .houseRule,
                                            size: 2.4,
                                            weight: FontWeight.w500),
                                        SizedBox(height: heightSpace(2.5)),
                                        for (Amenity item
                                            in c.data.value!.houseruleAmenities
                                                as List<Amenity>) ...[
                                          extrasComponent(
                                            item.icon_image,
                                            Get.locale?.languageCode == "ar"
                                                ? item.titleAr
                                                : item.title,
                                          ),
                                          SizedBox(height: heightSpace(2)),
                                        ],
                                        Divider(height: heightSpace(5)),
                                      ],
                                      Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            CustomText("cancellations".tr,
                                                size: 2.4,
                                                weight: FontWeight.w500),
                                            SizedBox(height: heightSpace(1.5)),
                                            Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Image.asset(
                                                      "assets/icons/calendar.png",
                                                      width: widthSpace(5)),
                                                  SizedBox(
                                                      width: widthSpace(5)),
                                                  Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        CustomText(
                                                            Get.find<TranslationHelper>()
                                                                        .translations
                                                                        .hostDashboard
                                                                        .toJson()[
                                                                    c.data.value
                                                                        ?.cancellation] ??
                                                                'Flexible',
                                                            color: Colors
                                                                    .tealAccent[
                                                                700]),
                                                        SizedBox(
                                                            height:
                                                                heightSpace(1)),
                                                        SizedBox(
                                                            width:
                                                                widthSpace(55),
                                                            child: CustomText(
                                                                '${Get.find<TranslationHelper>().translations.hostDashboard.toJson()['${c.data.value?.cancellation}_d']}',
                                                                color:
                                                                    Colors.grey[
                                                                        800])),
                                                      ])
                                                ])
                                          ]),
                                      if (c.data.value?.propertyAddress
                                              ?.latitude !=
                                          null) ...[
                                        Divider(height: heightSpace(6)),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            renderHead(
                                                Get.find<TranslationHelper>()
                                                    .translations
                                                    .propertySingle
                                                    .locationOnMap),
                                            CommonButton(
                                              title:
                                                  Get.find<TranslationHelper>()
                                                      .translations
                                                      .footer
                                                      .getDirections,
                                              backgroundBg:
                                                  const Color(themeColor),
                                              horizontalPadding: 7,
                                              onPressed: () => showApproximateSheet(
                                                  c.data.value?.propertyAddress!
                                                              .nearbyCoordinates !=
                                                          null
                                                      ? c
                                                              .data
                                                              .value
                                                              ?.propertyAddress!
                                                              .nearbyCoordinates!
                                                              .latitude ??
                                                          0.0
                                                      : 0.0,
                                                  c.data.value?.propertyAddress!
                                                              .nearbyCoordinates !=
                                                          null
                                                      ? c
                                                              .data
                                                              .value
                                                              ?.propertyAddress!
                                                              .nearbyCoordinates!
                                                              .longitude ??
                                                          0.0
                                                      : 0.0,
                                                  c.data.value!.name ??
                                                      "Destination"),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: heightSpace(2)),
                                        SizedBox(
                                            height: heightSpace(40),
                                            child: GoogleMap(
                                                key: c.mapKey,
                                                zoomControlsEnabled: false,
                                                buildingsEnabled: false,
                                                myLocationButtonEnabled: false,
                                                initialCameraPosition: CameraPosition(
                                                    target: LatLng(
                                                        c
                                                                .data
                                                                .value
                                                                ?.propertyAddress!
                                                                .latitude ??
                                                            0.0,
                                                        c
                                                                .data
                                                                .value
                                                                ?.propertyAddress
                                                                ?.longitude ??
                                                            0.0),
                                                    zoom: 15),
                                                markers: {
                                                  Marker(
                                                      markerId: MarkerId(
                                                          c.data.value!.name ??
                                                              "0"),
                                                      position: LatLng(
                                                          c
                                                                  .data
                                                                  .value
                                                                  ?.propertyAddress!
                                                                  .latitude ??
                                                              0.0,
                                                          c
                                                                  .data
                                                                  .value
                                                                  ?.propertyAddress
                                                                  ?.longitude ??
                                                              0.0),
                                                      icon: c.markerImage)
                                                }))
                                      ],
                                      if (c.data.value?.reviewAverage !=
                                          null) ...[
                                        Divider(height: heightSpace(6)),
                                        Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              renderHead(
                                                  Get.find<TranslationHelper>()
                                                      .translations
                                                      .sidenav
                                                      .reviews),
                                              Row(children: [
                                                Icon(Icons.star_rounded,
                                                    color:
                                                        const Color(themeColor),
                                                    size: widthSpace(7)),
                                                SizedBox(width: widthSpace(1)),
                                                CustomText(
                                                    " ${AvgRatingByFive(c.data.value?.reviewAverage?.avgTotal ?? 0.0)}",
                                                    size: 2.2,
                                                    weight: FontWeight.w500)
                                              ])
                                            ]),
                                        SizedBox(height: heightSpace(1)),
                                        CustomText(
                                            "${c.data.value?.reviewAverage!.totalReviews ?? "0.0"}",
                                            color: Colors.black54),
                                        SizedBox(height: heightSpace(2)),
                                        ...ratingBar(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .propertySingle
                                                .cleanliness,
                                            c.data.value!.reviewAverage?.cleanliness ??0.0,
                                            "/clean.svg"),
                                        const Divider(),
                                        ...ratingBar(
                                            Get.find<TranslationHelper>()
                                                .translations.hostDashboard.accuracy,
                                            c.data.value!.reviewAverage?.accuracy ??0.0,
                                            "/accuracy.svg"),
                                        const Divider(),
                                        ...ratingBar(
                                            Get.find<TranslationHelper>()
                                                .translations.hostDashboard.communication,
                                            c.data.value!.reviewAverage?.communication ??0.0,
                                            "/communication.svg"),

                                        const Divider(),
                                        ...ratingBar(Get.find<TranslationHelper>().translations.propertySingle.location, c.data.value!.reviewAverage?.location??0.0,"/location.svg"),
                                        // const Divider(),
                                        // ...ratingBar(Get.find<TranslationHelper>().translations.propertySingle.darentService, c.data.value!.reviewAverage?.darentService??0.0,"/app-icon.png"),
                                        // const Divider(),
                                        // ...ratingBar(Get.find<TranslationHelper>().translations.propertySingle.darentRecomended, c.data.value!.reviewAverage?.darentRecomended??0.0,Icons.thumb_up_alt_outlined),

                                        SizedBox(height: heightSpace(2)),
                                        ListView.separated(
                                          shrinkWrap: true,
                                          itemCount:
                                              c.reviews.length.clamp(0, 2),
                                          physics:
                                              const NeverScrollableScrollPhysics(),
                                          itemBuilder: (context, index) {
                                            var item = c.reviews[index];
                                            return reviewItem(item);
                                          },
                                          separatorBuilder:
                                              (BuildContext context,
                                                  int index) {
                                            return SizedBox(
                                                height: heightSpace(3));
                                          },
                                        ),
                                        if (c.reviews.length
                                            .isGreaterThan(1)) ...[
                                          GestureDetector(
                                              onTap: () {
                                                c.clearFiltersAndGetData(
                                                    redirectToSheet: true);
                                              },
                                              child: CustomText(
                                                  Get.find<TranslationHelper>()
                                                      .translations
                                                      .withdraw
                                                      .showMore,
                                                  underline: true)),
                                        ],
                                      ]
                                    ]))
                            : Center(
                                child: const CircularProgressIndicator()
                                    .marginOnly(top: heightSpace(10)))
                      ]),
                bottomNavigationBar: c.data.value?.hostId == userModel.value?.id?null:Container(
                  // height: heightSpace(14),
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(4), vertical: heightSpace(1)),
                  decoration: const BoxDecoration(
                      border:
                          Border(top: BorderSide(color: Color(greyBorder)))),
                  child: c.data.value == null
                      ? Shimmer(
                          gradient: ViewsCommon.shimmerGradient,
                          child: Column(
                             mainAxisSize: MainAxisSize.min,

                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                textShimmer(width: widthSpace(35)),
                                const SizedBox(height: 5),
                                textShimmer(width: widthSpace(55)),
                                const SizedBox(height: 5),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    textShimmer(width: widthSpace(55)),
                                    textShimmer(
                                        height: widthSpace(10),
                                        width: widthSpace(30)),
                                  ],
                                )
                              ]),
                        )
                      : Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                  if (c.isLoadingCurrentPrice.value)
                                    const SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                            strokeWidth: 2))
                                  else ...[
                                    Text.rich(
                                      TextSpan(
                                        children: [
                                          TextSpan(
                                              text:
                                                  "${(c.data.value?.propertyPrice?.perNightPrice??c.data.value!.propertyPrice!.price)!.toStringAsFixed(2)} ${c.data.value?.propertyPrice?.currencyCode ?? "SAR"}",
                                              style: TextStyle(
                                                  fontSize: heightSpace(1.8),
                                                  fontWeight: FontWeight.w500)),
                                          TextSpan(
                                            text:
                                                " / ${Get.find<TranslationHelper>().translations.propertySingle.night ?? "night"}",
                                            style: TextStyle(
                                                color: const Color(greyText),
                                                fontSize: heightSpace(1.6)),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Row(children: [
                                      if (false &&
                                          c.data.value?.discount != null)
                                        Text.rich(
                                          TextSpan(
                                            children: [
                                              TextSpan(
                                                  text:
                                                      "${c.data.value?.propertyPrice?.price?.toStringAsFixed(2) ?? 0.0} ${c.data.value?.propertyPrice?.currencyCode ?? "SAR"}",
                                                  style: TextStyle(
                                                      fontSize:
                                                          heightSpace(1.5),
                                                      color: const Color(
                                                          greyText))),
                                              TextSpan(
                                                text:
                                                    " ${c.data.value?.discountedAmount?.toStringAsFixed(2) ?? 0.0} ${c.data.value?.propertyPrice?.currencyCode ?? "SAR"}",
                                                style: TextStyle(
                                                    fontSize: heightSpace(1.9)),
                                              ),
                                            ],
                                          ),
                                        )
                                      else
                                        CustomText(
                                            '${Get.find<TranslationHelper>().translations.search.totalOf} (${c.endDate.value.difference(c.startDate.value).inDays} ${Get.find<TranslationHelper>().translations?.search.nights}) ${c.data.value?.propertyPrice?.price?.toStringAsFixed(2)} ${c.data.value?.propertyPrice?.currencyCode ?? "SAR"}',
                                            color: const Color(greyText),
                                            size: 1.6,
                                            weight: FontWeight.w500,
                                            underline: true),

                                      //   CustomText(
                                      //       "${c.data.value?.propertyPrice?.price?.toStringAsFixed(2)} ${c.data.value?.propertyPrice?.currencyCode ?? "SAR"}",
                                      //       size: 1.9,color:Colors.grey),
                                      // CustomText(" (${c.endDate.value.difference(c.startDate.value).inDays} ${Get.find<TranslationHelper>().translations.propertySingle.nights??"night"})",
                                      //     color: Colors.grey,size: 1.6),
                                    ]),
                                  ],
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      c.isUnAvailable.value
                                          ? Expanded(
                                              child: CustomText(
                                                  Get.find<TranslationHelper>()
                                                      .translations
                                                      .propertySingle
                                                      .dateNotAvailable))
                                          : CommonButton(
                                              title:
                                                  " ${formDateFormatCservice.format(c.startDate.value)} - ${formDateFormatCservice.format(c.endDate.value)} ",
                                              onPressed: c.data.value == null
                                                  ? null
                                                  : c.selectReserveDates,
                                              horizontalPadding: 2,
                                              backgroundBg: Colors.white,
                                              isBorder: true,
                                              fontSize: 1.6),
                                      c.data.value == null
                                          ? Image.asset('assets/loader.gif')
                                          : CommonButton(
                                              horizontalPadding: 5,
                                              title: c.isUnAvailable.value
                                                  ? Get.find<TranslationHelper>().translations.contactHost.checkAvailability
                                                  : c.data.value!.bookingType == "instant"
                                                  ? Get.find<TranslationHelper>().translations.propertySingle.instantBook
                                                  : Get.find<TranslationHelper>().translations.propertySingle.requestBook,
                                              isLoading: c.isLoadingCurrentPrice.value,
                                              fontSize: 1.6,
                                              onPressed: c.isUnAvailable.value
                                                  ? c.selectReserveDates
                                                  : c.gotoBooking)
                                    ],
                                  ),

  // InkWell(
  //                                     onTap: () {
  //                                       var propertyId =
  //                                           c.data.value?.id.toString() ?? '';
  //                                       var propertyCode =
  //                                           c.data.value?.propertyCode ?? '';
  //                                       var propertyName =
  //                                           c.data.value?.name ?? '';
  //                                       var checkIn = c.startDate.value
  //                                           .toString()
  //                                           .split(' ')[0];
  //                                       var checkOut = c.endDate.value
  //                                           .toString()
  //                                           .split(' ')[0];
  //                                       var msg = Utility
  //                                           .generateReservationWhatsAppMessage(
  //                                               propertyId: propertyId,
  //                                               propertyCode: propertyCode,
  //                                               propertyName: propertyName,
  //                                               checkIn: checkIn,
  //                                               checkOut: checkOut);
  //                                       Utility.launchWhatsApp(
  //                                           phoneNumber: '+966920033870',
  //                                           message: msg);
  //                                     },
  //                                     child:   Container(
  //                                     padding: EdgeInsets.symmetric(
  //                                         vertical: 5, horizontal: 8),
  //                                     alignment: Alignment.center,
  //                                     child: RichText(
  //                                       text: TextSpan(
  //                                         style: TextStyle(
  //                                           // fontSize: widthSpace(2.1),
  //                                           color: Colors.black,
  //                                         ),
  //                                         children: [
  //                                           TextSpan(
  //                                             text: "whatsapp_contact".tr,
  //                                           ),
  //                                           TextSpan(
  //                                             text: "whatsapp".tr,
  //                                             style: const TextStyle(
  //                                               fontWeight: FontWeight.bold,
  //                                             ),
  //                                           ),
  //                                           const TextSpan(text: '!'),
  //                                         ],
  //                                       ),
  //                                     ),
  //                                   ))
                                 

                                ]),
                ),
              ),
            ),
    );
  }

  Widget textShimmer({double height = 17, double width = 50}) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(5)),
    );
  }

  showApproximateSheet(double lat, double long, String name) {
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        maxChildSize: .60,
        initialChildSize: .60,
        expand: false,
        builder: (context, scrollController) {
          return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Align(
                    alignment: Alignment.topRight,
                    child: InkWell(
                        onTap: Get.back, child: const Icon(Icons.clear))),
                const Spacer(),
                Align(
                    alignment: Alignment.center,
                    child: InkWell(
                        onTap: Get.back,
                        child: Icon(
                          Icons.info_outline,
                          color: const Color(themeColor),
                          size: heightSpace(11),
                        ))),
                const Spacer(),
                Align(
                  alignment: Alignment.center,
                  child: SizedBox(
                      width: widthSpace(75),
                      child: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .footer
                              .alert,
                          size: 4.0,
                          weight: FontWeight.bold,
                          textAlign: TextAlign.center,
                          color: Colors.black)),
                ),
                const Spacer(),
                Align(
                  alignment: Alignment.center,
                  child: SizedBox(
                      width: widthSpace(75),
                      child: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .footer
                              .approximateLocation,
                          color: Colors.grey[800])),
                ),
                const Spacer(),
                Container(
                    height: heightSpace(9),
                    padding: EdgeInsets.symmetric(
                        horizontal: widthSpace(5), vertical: heightSpace(1.5)),
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Obx(
                        () => CommonButton(
                            title: Get.find<TranslationHelper>()
                                .translations
                                .general
                                .confirm,
                            borderRadius: 5,
                            isLoading: c.isLoading.value,
                            minimumSize: Size.fromWidth(widthSpace(85)),
                            onPressed: () {
                              Get.back();
                              openMapChooserSheet(lat, long, name);
                            }),
                      ),
                    ))
              ]).paddingAll(widthSpace(viewPadding));
        }));
  }
  backFunction() {
    if (Get.previousRoute.isEmpty ||
        Get.previousRoute == Routes.promotion ||
        Get.previousRoute == Routes.splash || Get.previousRoute.contains(Routes.properties)) {
      Get.offAllNamed(Routes.home);
    } else {
      Get.back();
    }
  }

  openMapChooserSheet(double latitude, double longitude, String name) async {
    final availableMaps = await custom_launcher.MapLauncher.installedMaps;
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        maxChildSize: .40,
        initialChildSize: .40,
        expand: false,
        builder: (context, scrollController) {
          return Padding(
              padding: EdgeInsets.all(widthSpace(viewPadding)),
              child: Column(
                children: availableMaps.map((map) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: InkWell(
                      onTap: () {
                        Get.back();
                        map.showMarker(
                          coords: custom_launcher.Coords(latitude, longitude),
                          title: name,
                        );
                      },
                      child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            map.icon.contains("svg")
                                ? ClipOval(
                                    child: SvgPicture.asset(map.icon,
                                        height: widthSpace(10),
                                        width: widthSpace(10)),
                                  )
                                : ClipOval(
                                    child: GlobalHelper.resolveImageUrl(map.icon).isNotEmpty
                                        ? Image(
                                      image: GlobalHelper.buildNetworkImageProvider(url: map.icon,),
                                      height: widthSpace(10),
                                      width: widthSpace(10),
                                      fit: BoxFit.fitWidth,
                                    )
                                        : Icon(Icons.map_outlined,
                                        size: widthSpace(10)),
                                  ),
                            SizedBox(width: widthSpace(5.5)),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(map.mapName,
                                      size: 2.1, weight: FontWeight.w500),
                                  SizedBox(
                                      height: heightSpace(
                                          0.3)), //Divider(height: heightSpace(4)),
                                ],
                              ),
                            ),
                            SizedBox(width: widthSpace(5)),
                            const Icon(Icons.arrow_forward_ios, size: 20)
                          ]),
                    ),
                  );
                }).toList(),
              ));
        }));
  }

  ratingBar(title, double value, icon, {isLast = false}) {
    return [
      Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
        icon is IconData
            ? Icon(icon, size: heightSpace(2))
            : icon.contains('png')
                ? Image.asset('assets/icons$icon',
                    height: heightSpace(2),
                    fit: BoxFit.fill,
                    color: Colors.black)
                : SvgPicture.asset("assets/icons$icon",
                    height: heightSpace(2),
                    width: 10,
                    fit: BoxFit.fill,
                    color: Colors.black),
        SizedBox(
          width: widthSpace(5),
        ),
        CustomText(title, size: 1.9, color: Colors.black87),
        const Spacer(),
        // Container(
        //   height: heightSpace(1.3),
        //   width: widthSpace(45),
        //   alignment: Alignment.centerLeft,
        //   decoration: BoxDecoration(
        //       color: const Color(greyBorder),
        //       borderRadius: BorderRadius.circular(10)),
        //   child: Container(
        //       width: ratingWidth,
        //       decoration: BoxDecoration(
        //           color: const Color(themeColor),
        //           borderRadius: BorderRadius.circular(10))),
        // ),
        SizedBox(width: widthSpace(4)),
        renderHead(value.toString())
      ]),
    ];
  }

  reviewItem(PropertyReviews item) {
    String? joinedDate;
    if (item.createdAt != null) {
      joinedDate = item.createdAt!;
    }
    return Container(
      padding: EdgeInsets.all(widthSpace(viewPadding / 2)),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15.0),
          border: Border.all(color: Colors.black, width: 0.5)),
      child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            item.reviewerProfileImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(widthSpace(6)),
                    child: item.reviewerProfileImage!.contains("svg")
                        ? GlobalHelper.buildNetworkSvgWidget(
                      url:"/${item.reviewerProfileImage}",
                      width: widthSpace(12),
                      height: widthSpace(12),
                      defaultOption: const Icon(Icons.person,
                        size:48,),)
                        : Image(
                        image: GlobalHelper.buildNetworkImageProvider(
                            url: item.reviewerProfileImage??"",),
                            width: widthSpace(12),
                            height: widthSpace(12),
                            fit: BoxFit.fill,
                          ))
                : Container(
                    width: widthSpace(12),
                    height: widthSpace(12),
                    decoration: BoxDecoration(
                        color: Colors.grey[200], shape: BoxShape.circle),
                    child: Icon(Icons.person, size: widthSpace(8))),
            SizedBox(width: widthSpace(5)),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(
                      "${item.reviewerFirstName ?? ""} ${item.reviewerLastName ?? ""}",
                      size: 2.3,
                      maxlines: 2,
                      textOverflow: TextOverflow.ellipsis),
                  SizedBox(height: heightSpace(1)),
                  CustomText(joinedDate,
                      color: const Color(greyText), size: 1.7),
                  // <<<<<<< HEAD
                  //                 SizedBox(height: heightSpace(1)),
                  // // <<<<<<< HEAD
                  // //                 CustomText(joinedDate, color: const Color(greyText), size: 1.7)
                  // //               ]
                  // //             ],
                  // //           ),
                  // //         ]),
                  // //         SizedBox(height: heightSpace(2)),
                  // //         renderRatingBar(item.rating??0.0),
                  // //         CustomText(item.message??"No message given",color: Colors.black54),
                  // //         SizedBox(height: heightSpace(4)),
                  // //       ],
                  // // =======
                  // =======
                  // >>>>>>> ddd743fb45badd162de03c0948d4483a57df3d2c
                  renderRatingBar(item.rating ?? 0.0),
                  SizedBox(
                      width: widthSpace(40),
                      child: CustomText(item.message ?? "No message given",
                          color: Colors.black54)),
                ],
              ),
            ),
            SizedBox(width: widthSpace(5)),
            if (joinedDate != null) ...[
              SizedBox(height: heightSpace(1)),
              CustomText(joinedDate, color: const Color(greyText), size: 1.7)
            ]
          ]),
    );
  }

  renderRatingBar(rating) {
    return RatingBar(
      ignoreGestures: true,
      initialRating: rating,
      minRating: 0,
      direction: Axis.horizontal,
      allowHalfRating: true,
      unratedColor: Colors.grey.withOpacity(.5),
      itemCount: 5,
      itemSize: widthSpace(4),
      itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
      ratingWidget: RatingWidget(
        full: const Icon(
          Icons.star,
          color: Colors.black,
        ),
        half: const Icon(
          Icons.star_half,
          color: Colors.black,
        ),
        empty: const Icon(
          Icons.star_border,
          color: Colors.black,
        ),
      ),
      onRatingUpdate: (rating) {
        debugPrint(rating.toString());
      },
    );
  }

  renderHead(text) => CustomText(
        text,
        size: 2.4,
        weight: FontWeight.bold,
        maxlines: 3,
      );

  extrasComponent(image, text) {
    return Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          image == null || image.contains("assets")
              ? SvgPicture.asset("assets/icons/booking.svg",
                  width: widthSpace(5))
              : GlobalHelper.buildNetworkSvgWidget(
            url: "/$image" ,
            width: widthSpace(5),
            height: image == "listing-extinguisher.svg"
                ? widthSpace(8)
                : null,
            defaultOption: Image.asset("assets/icons/bookings.png",
                height: heightSpace(3)),),
          SizedBox(width: widthSpace(5)),
          Expanded(
              child: CustomText(text, color: Colors.grey[800], maxlines: 2)),
        ]);
  }

  final Widget loader = const Center(child: CircularProgressIndicator());
}

class PropertyRating extends StatelessWidget {
  const PropertyRating({
    super.key,
    required this.avgRating,
    required this.rootDirectionalityContext,
     this.isNewProperty = false,
  });

  final num avgRating;
  final bool isNewProperty;
  final BuildContext rootDirectionalityContext;

  @override
  Widget build(BuildContext context) {
    return
      isNewProperty? Row(
        children: [
          const Icon(
            Icons.star_rounded,
            size: 17,
            // color: Colors.white,
          ),
          const SizedBox(width: 3,),
          CustomText(
            'new'.tr,
            size: 1.5,
            // color: Colors.white,
          ),
        ],
      ):

      Container(
      padding: EdgeInsets.symmetric(
        vertical: widthSpace(1.5),
        horizontal: widthSpace(4.0),
      ),
      decoration: BoxDecoration(
        color: const Color(themeColor),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        crossAxisAlignment:
            Directionality.of(rootDirectionalityContext) == TextDirection.ltr
                ? CrossAxisAlignment.center
                : CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.star_rounded,
            size: 17,
            color: Colors.white,
          ),
          CustomText(
            " ${AvgRatingByFive(avgRating)}",
            size: 2.1,
            color: Colors.white,
          ),
        ],
      ),
    );
  }
}

class SupportedPayments extends StatelessWidget {
  const SupportedPayments({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<PropertyDetailController>();

    if (!controller.isOnGuestMode()) return const SizedBox.shrink();

    final property = controller.data.value;

    if (!(property?.isInstantBooking() ?? false)) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      width: MediaQuery.sizeOf(context).width,
      child: Wrap(
        alignment: WrapAlignment.spaceBetween,
        children: [
          ...PaymentHelper.paymentMethods.map(
            (e) => PaymentMethodIcon(
              methodId: e.id,
              size: Size(widthSpace(14), widthSpace(14)),
            ),
          ),
        ],
      ),
    );
  }
}
