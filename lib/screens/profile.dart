import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/screens/account_info.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../helperMethods/remote_config.dart';
import '../helperMethods/translation_helper.dart';

class Profile extends StatelessWidget {
  const Profile({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          leading: IconButton(
              onPressed: Get.back, icon: const Icon(Icons.chevron_left)),
          title: CustomText(
              Get.find<TranslationHelper>().translations.usersProfile.profile,
              size: 2.4,
              weight: FontWeight.w500),
          actions: [
            SizedBox(
              width: widthSpace(20),
              child: IconButton(
                  onPressed: () => Get.to(() => const AccountInfo()),
                  icon: CustomText("edit".tr,
                      size: 2.2,
                      textOverflow: TextOverflow.visible,
                      underline: true)),
            ),
          ]),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(5)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              //height: heightSpace(14),
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(
                    10), // Adjust the radius value as needed
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3), // Shadow color
                    spreadRadius: 0.1, // Spread radius
                    blurRadius: 4, // Blur radius
                    offset:
                        const Offset(1, 2), // Offset in the x and y directions
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                          color: Colors.grey[200],
                          border: Border.all(color: Colors.black, width: 1),
                          shape: BoxShape.circle),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(45),
                        child: (userModel.value?.profile_image ?? "")
                                .contains(".svg")
                            ? GlobalHelper.buildNetworkSvgWidget(
                          url:userModel.value?.profile_image??"",
                          defaultOption: Image.asset(
                              "assets/default-image.png",
                              fit: BoxFit.fill),)
                            : GlobalHelper.resolveImageUrl(userModel.value?.profile_image ??"").isNotEmpty
                            ? Image(
                            image: GlobalHelper.buildNetworkImageProvider(
                                url: userModel.value?.profile_image ??"",),
                             width: 90,
                             height: 90,
                             fit: BoxFit.fill,
                        )
                            : Padding(
                          padding: const EdgeInsets.all(30),
                          child: Icon(Icons.person,
                              size: widthSpace(14)),
                        ),
                      )),
                  SizedBox(height: heightSpace(1)),
                  CustomText(
                      "${userModel.value?.first_name} ${userModel.value?.last_name}",
                      size: 2.4,
                      weight: FontWeight.w500),
                  SizedBox(height: heightSpace(0.5)),
                  CustomText(
                      Get.find<TranslationHelper>().translations.search.guest),
                ],
              ),
            ),

            SizedBox(height: heightSpace(4.5)),
            CustomText(
                userModel.value?.userVerification?.phone == "yes"
                    ? "${userModel.value!.first_name} ${Get.find<TranslationHelper>().translations.usersProfile.verified}"
                    : "${userModel.value!.first_name} is not Verified",
                // "isVerified".trArgs([userModel.value!.first_name]):'isNotVerified'.tr.trArgs([userModel.value!.first_name]),
                size: 2.4,
                weight: FontWeight.w500),
            SizedBox(height: heightSpace(2)),
            Row(children: [
              Icon(
                userModel.value?.userVerification?.phone == "yes"
                    ? Icons.check
                    : Icons.close,
                size: 22,
                color: userModel.value?.userVerification?.phone == "yes"
                    ? const Color(themeColor)
                    : const Color(warningColor),
              ),
              SizedBox(width: widthSpace(5)),
              CustomText("verifiedPhoneNumber".tr, color: Colors.grey[800]),
            ]),

            SizedBox(height: heightSpace(1.5)),
            Row(children: [
              Icon(
                userModel.value?.userVerification?.email == "yes"
                    ? Icons.check
                    : Icons.close,
                size: 22,
                color: userModel.value?.userVerification?.email == "yes"
                    ? const Color(themeColor)
                    : const Color(warningColor),
              ),
              SizedBox(width: widthSpace(5)),
              CustomText("verifieEmail".tr, color: Colors.grey[800]),
            ]),
            SizedBox(height: heightSpace(1.5)),
            Row(children: [
              Icon(
                  userModel.value?.dateofbirth != null
                      ? Icons.check
                      : Icons.close,
                  size: 22,
                  color: userModel.value?.dateofbirth != null
                      ? const Color(themeColor)
                      : const Color(warningColor)),
              SizedBox(width: widthSpace(5)),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .dateOfBirth,
                  color: Colors.grey[800]),
            ]),
            SizedBox(height: heightSpace(1.5)),
            Row(children: [
              Icon(
                  userModel.value?.userVerification?.identity == "yes"
                      ? Icons.check
                      : Icons.close,
                  size: 22,
                  color: userModel.value?.userVerification?.identity == "yes"
                      ? const Color(themeColor)
                      : const Color(warningColor)),
              SizedBox(width: widthSpace(5)),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .identity,
                  color: Colors.grey[800]),
            ]),

            SizedBox(height: heightSpace(1.5)),
            Row(children: [
              Icon(
                  userModel.value?.yaqeenVerified == true
                      ? Icons.check
                      : Icons.close,
                  size: 22,
                  color: userModel.value?.yaqeenVerified == true
                      ? const Color(themeColor)
                      : const Color(warningColor)),
              SizedBox(width: widthSpace(5)),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .ninIqama,
                  color: Colors.grey[800]),
            ]),

            // SizedBox(height: heightSpace(1.5)),
            // Row(children: [
            //   if(joiningDate!=null)CustomText(
            //       " ${"joined".tr} ${joiningDate.day} ${months[joiningDate.month - 1]} ${joiningDate.year}",
            //       color: Colors.grey[800]),
            //   SizedBox(width: widthSpace(1)),
            //   Icon(Icons.star_rounded, color: Colors.tealAccent[700], size: 17),
            //   SizedBox(width: widthSpace(1)),
            //   CustomText("${userModel.value?.avgRating??0}/5", size: 2.2, color: Colors.tealAccent[700]),
            // ]),
            // Divider(height: heightSpace(5)),
            // CustomText("aboutMe".tr, size: 2.4, weight: FontWeight.w500),
            // SizedBox(height: heightSpace(2)),
            // CustomText(userModel.value?.about ?? "", color: Colors.grey[800]),
            // if(c.myReviews.isNotEmpty)...[
            //   SizedBox(height: heightSpace(3),),
            //    Obx(
            //      ()=> Row(
            //        mainAxisAlignment: MainAxisAlignment.center,
            //        children: [
            //        InkWell(
            //      child: Container(
            //        padding: EdgeInsets.symmetric(
            //            horizontal: widthSpace(9), vertical: widthSpace(2)),
            //        decoration: BoxDecoration(
            //            color: c.reviewTab.value == "reviews" ? const Color(lightBg) : null,
            //            borderRadius: BorderRadius.circular(50)),
            //        child:  CustomText(
            //            "Reviews",
            //            size: 2.1,
            //            weight: FontWeight.w500,
            //            color:c.reviewTab.value == "reviews" ? null : const Color(greyText)),
            //      ),
            //      onTap: () {
            //        c.setReviewsTab("reviews");
            //      }
            //      ),
            //      InkWell(
            //      child: Container(
            //        padding: EdgeInsets.symmetric(
            //            horizontal: widthSpace(9), vertical: widthSpace(2)),
            //        decoration: BoxDecoration(

            //            color: c.reviewTab.value == "yourReviews" ? const Color(lightBg) : null,
            //            borderRadius: BorderRadius.circular(50)),
            //        child:  CustomText(
            //            "Your Reviews",
            //            size: 2.1,
            //            weight: FontWeight.w500,
            //            color:
            //               c.reviewTab.value == "yourReviews" ? null : const Color(greyText)),
            //      ),
            //      onTap: () {
            //        c.setReviewsTab("yourReviews");
            //      }
            //      )]),
            //    ),
            //   Obx(() =>c.reviewTab.value == "reviews" ?
            //     c.myReviews.isEmpty ?
            //     Center(child:Column( mainAxisAlignment: MainAxisAlignment.center,
            //   children: [
            //     SizedBox(height: heightSpace(5),),
            //     Image.asset('assets/notFound.png', height: 25),
            //     const CustomText("Your Reviews is Empty"),
            //   ],
            // )) :ListView.separated(
            //      padding: EdgeInsets.only(top: heightSpace(5)),
            //      physics: const NeverScrollableScrollPhysics(),
            //      shrinkWrap: true,
            //      itemBuilder: (context, index) {
            //        DateTime createdAt = DateTime.parse(c.myReviews[index].createdAt);
            //        return Row(
            //            crossAxisAlignment: CrossAxisAlignment.start,
            //            children: [
            //              Container(
            //                  padding: const EdgeInsets.all(7),
            //                  decoration: BoxDecoration(
            //                      color: Colors.grey[200], shape: BoxShape.circle),
            //                  child: const Icon(Icons.person)),
            //              SizedBox(width: widthSpace(4)),
            //              Expanded(
            //                child: Column(crossAxisAlignment: CrossAxisAlignment.start,children: [
            //                  Row(
            //                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //                      children: [
            //                        CustomText(c.myReviews[index].senderName,weight: FontWeight.w500),
            //                        CustomText(
            //                          "${createdAt.day} ${months[createdAt.month-1]} ${createdAt.year}",
            //                          size: 1.8,
            //                        ),
            //                      ]),
            //                  SizedBox(height: heightSpace(1)),
            //                  Row( mainAxisAlignment: MainAxisAlignment.spaceBetween,
            //                    children: [
            //                      Row(children: [
            //                        Icon(Icons.star_rounded,
            //                            color: Colors.tealAccent[700], size: 16),
            //                        SizedBox(width: widthSpace(1)),
            //                        CustomText("${c.myReviews[index].rating}/5",
            //                            size: 2.1, color: Colors.tealAccent[700]),
            //                      ]),
            //                      InkWell(
            //                            onTap:c.myReviews[index].rated == true?null:() {
            //                              Get.dialog(
            //                                RatingDialog(
            //                                  image: c.ratingImage,
            //                                    pickImageOnTap: c.pickImage,
            //                                    ratingController: c.ratingController,
            //                                    onConfirmed: () =>  c.submitRatingHost(index),
            //                                    onRatingUpdate:(double) {c.rating = double;}
            //                                    )
            //                              ).then((value){
            //                                c.rating = 0;
            //                              });},
            //                                                 child:
            //                                                  CustomText(
            //                                                     //"Rate Your Guest",
            //                                                     c.myReviews[index].rated == true ? "Rated" : "Rate Your Guest",
            //                                                     size: 1.9,
            //                                                     color: const Color(successColor),
            //                                                     underline: true),

            //                                                     ),
            //                    ],
            //                  ),
            //                  SizedBox(height: heightSpace(1)),
            //                  CustomText(
            //                      c.myReviews[index].message??"",
            //                      size: 1.9,
            //                      color: const Color(greyText)),
            //                ]),
            //              )
            //            ]);
            //      },
            //      separatorBuilder: (context, index) =>SizedBox(height: heightSpace(5)),
            //      itemCount: c.myReviews.length)
            //       :c.myReviewsByYou.isEmpty
            //       ? Center(child:Column( mainAxisAlignment: MainAxisAlignment.center,
            //   children: [
            //     SizedBox(height: heightSpace(5),),
            //     Image.asset('assets/notFound.png', height: 25),
            //     const CustomText("Your Reviews is Empty"),
            //   ],
            // ))
            //       :ListView.separated(
            //       padding: EdgeInsets.only(top: heightSpace(4)),
            //       physics: const NeverScrollableScrollPhysics(),
            //       shrinkWrap: true,
            //       itemBuilder: (context, index) {
            //         return Card(
            //           semanticContainer: true,
            //           elevation: 5,
            //           clipBehavior: Clip.hardEdge,
            //           margin: const EdgeInsets.only(),
            //           shape: const RoundedRectangleBorder(borderRadius:BorderRadius.all(Radius.circular(15))),
            //                       child: Padding(
            //                         padding: const EdgeInsets.all(12),
            //                         child: Row(
            //                             mainAxisAlignment:MainAxisAlignment.spaceBetween,
            //                             children: [
            //                               Container(
            //                                       height: heightSpace(9),
            //                                       width: widthSpace(20),
            //                                       alignment: Alignment.topRight,
            //                                       decoration: BoxDecoration(
            //                                           borderRadius: BorderRadius.circular(heightSpace(1.5)),
            //                                           image: DecorationImage(
            //                                               image: NetworkImage("$baseUrl/${c.myReviewsByYou[index].propertyPhoto}"),
            //                                               fit: BoxFit.fill))),

            //           SizedBox(width: widthSpace(2)),
            //            Expanded(
            //              child: Column(

            //              crossAxisAlignment: CrossAxisAlignment.start,
            //               children: [
            //                 Row(
            //                mainAxisAlignment:MainAxisAlignment.spaceBetween,
            //                children: [
            //                  SizedBox(
            //                   width: widthSpace(40),
            //                   child: CustomText(c.myReviewsByYou[index].propertyName ?? "", size: 1.9, weight: FontWeight.w500,textOverflow: TextOverflow.ellipsis)),
            //                   Row(
            //              children: [
            //                const Icon(Icons.star_rounded, size: 20, color: Colors.green),
            //                CustomText("${c.myReviewsByYou[index].rating}/5", size: 2.2, color: Colors.green, weight: FontWeight.w500,)
            //              ],),
            //              ]),
            //                        SizedBox(height: heightSpace(0.6),),
            //                        CustomText(c.myReviewsByYou[index].message ?? "No Message",size: 1.9, color: Colors.black.withOpacity(0.6), maxlines: 1,textOverflow: TextOverflow.ellipsis),
            //                                                          SizedBox(height: heightSpace(0.6),),

            //                          Row(mainAxisAlignment: MainAxisAlignment.end,
            //                          crossAxisAlignment: CrossAxisAlignment.center,
            //                           children: [
            //                                     Container(
            //                                     height: 20,
            //                                     width: 20,
            //                                     decoration: BoxDecoration(
            //                                         borderRadius:BorderRadius.circular(100),
            //                                         color:Colors.grey.withOpacity(0.3)),
            //                                     child: Icon(
            //                                       Icons.person,
            //                                       color:Colors.black.withOpacity(0.7),
            //                                       size: 14,
            //                                     )),
            //                                     SizedBox(width: widthSpace(1),),
            //                                      CustomText(c.myReviewsByYou[index].senderName, size: 2, weight: FontWeight.w500,)

            //                                                        ],)

            //                            ],),
            //            )
            //            ]),
            //                       ),
            //                     );

            //      },
            //      separatorBuilder: (context, index) =>SizedBox(height: heightSpace(3)),
            //      itemCount: c.myReviewsByYou.length)
            //      )
          ],
          //]
        ),
      ),
    );
  }
}
