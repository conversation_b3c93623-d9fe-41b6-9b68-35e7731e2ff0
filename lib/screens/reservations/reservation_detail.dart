import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/mapChooser.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/components/warning_dialog.dart';
import 'package:darent/controllers/reservation_detail_controller.dart';
import 'package:darent/helperMethods/authHelper.dart';
import 'package:darent/helperMethods/chat_helper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/screens/customer_services.dart';
import 'package:darent/screens/problem/report_problem.dart';
import 'package:darent/screens/property_single/property_details.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:webengage_flutter/webengage_flutter.dart';

import '../../helperMethods/search_helper.dart';
import '../../helperMethods/translation_helper.dart';
import '../../models/productDetailModel.dart';
import '../../utils/routes.dart';

class ReservationDetail extends StatelessWidget {
  final String code;
  final bool forReview;
  ReservationDetail({super.key, required this.code, this.forReview = false});
  @override
  Widget build(BuildContext context) {
    ReservationDetailController c =
        Get.put(ReservationDetailController(code, forReview));
    bool isMasked = SearchHelper.c.reservationTabController.index == 2 ||
        SearchHelper.c.reservationTabController.index == 3;
    return Obx(
      () => Scaffold(
        body: Column(
          children: [
            Container(
              width: double.maxFinite,
              height: heightSpace(43), // Match the expanded height you had
              padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 10),
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: c.data.value == null
                      ? const AssetImage("assets/default-image.png")
                      : GlobalHelper.buildNetworkImageProvider(url: c.data.value?.photos?.first??''),
                  fit: BoxFit.cover,
                  colorFilter: ColorFilter.mode(
                      Colors.black.withOpacity(.4), BlendMode.darken),
                ),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: Get.back,
                        child: const CircleAvatar(
                          backgroundColor: Colors.white,
                          maxRadius: 18,
                          child: Icon(
                            Icons.close,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      if (c.data.value != null)
                        InkWell(
                          onTap: () {
                            ViewsCommon.share(
                              '$baseUrl/properties/${c.data.value!.slug}',
                              title: 'Property',
                              itemId: '${c.data.value?.id}',
                            );
                          },
                          child: const CircleAvatar(
                            backgroundColor: Colors.white,
                            maxRadius: 18,
                            child: Icon(
                              Icons.file_upload_outlined,
                              color: Colors.black,
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: heightSpace(2)),
                  if (c.data.value != null)
                    CustomText(
                      "${Get.find<TranslationHelper>().translations.reservation.youAreAllSetFor} ${c.data.value?.city ?? ''}",
                      size: 2.7,
                      color: Colors.white,
                      weight: FontWeight.w500,
                    ),
                ],
              ),
            ),
            Expanded(
              child: NestedScrollView(
                floatHeaderSlivers: true,
                headerSliverBuilder: (context, isScrolled) => [
                  // SliverAppBar(
                  //   // floating: true,
                  //   pinned: true,
                  //   automaticallyImplyLeading: false,
                  //   leading: isScrolled
                  //       ? IconButton(
                  //           onPressed: Get.back, icon: const Icon(Icons.chevron_left))
                  //       : null,
                  //   centerTitle: true,
                  //   title: isScrolled
                  //       ? Text(
                  //           "${Get.find<TranslationHelper>().translations.reservation.reservationConfirmNumber}: $code")
                  //       : null,
                  //   flexibleSpace: FlexibleSpaceBar(
                  //     background: Container(
                  //         width: double.maxFinite,
                  //         height: double.maxFinite,
                  //         padding: const EdgeInsets.symmetric(
                  //             vertical: 40, horizontal: 10),
                  //         decoration: BoxDecoration(
                  //           image: DecorationImage(
                  //               image: c.data.value == null
                  //                   ? const AssetImage("assets/default-image.png")
                  //                   : NetworkImage(
                  //                           '${Get.find<RemoteConfig>().imagesBaseUrl}${c.data.value?.photos?.first}')
                  //                       as ImageProvider,
                  //               fit: BoxFit.cover,
                  //               colorFilter: ColorFilter.mode(
                  //                   Colors.black.withOpacity(.4), BlendMode.darken)),
                  //         ),
                  //         child: Column(
                  //           children: [
                  //             Row(
                  //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  //                 children: [
                  //                   InkWell(
                  //                       onTap: Get.back,
                  //                       child: const CircleAvatar(
                  //                           backgroundColor: Colors.white,
                  //                           maxRadius: 18,
                  //                           child: Icon(
                  //                             Icons.close,
                  //                             color: Colors.black,
                  //                           ))),
                  //                   if (c.data.value != null)
                  //                     InkWell(
                  //                         onTap: () {
                  //                           ViewsCommon.share(
                  //                               '$baseUrl/properties/${c.data.value!.slug}',
                  //                               title: 'Property',
                  //                               itemId: '${c.data.value?.id}');
                  //                         },
                  //                         child: const CircleAvatar(
                  //                             backgroundColor: Colors.white,
                  //                             maxRadius: 18,
                  //                             child: Icon(Icons.file_upload_outlined,
                  //                                 color: Colors.black)))
                  //                 ]),
                  //             SizedBox(height: heightSpace(2)),
                  //             if (c.data.value != null)
                  //               CustomText(
                  //                   "${Get.find<TranslationHelper>().translations.reservation.youAreAllSetFor} ${c.data.value?.city ?? ''}",
                  //                   size: 2.7,
                  //                   color: Colors.white,
                  //                   weight: FontWeight.w500)
                  //           ],
                  //         )),
                  //   ),
                  //   expandedHeight: heightSpace(43),
                  // ),
                ],
                body: c.data.value == null
                    ? Center(child: Image.asset('assets/loader.gif', scale: 1.2))
                    : SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Column(children: [
                              SizedBox(
                                height: heightSpace(10),
                                child: Row(children: [
                                  Expanded(
                                    child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          CustomText(
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .header
                                                  .checkIn,
                                              size: 1.85,
                                              weight: FontWeight.w500),
                                          SizedBox(height: heightSpace(.3)),
                                          CustomText(
                                              DateFormat.MMMEd().format(
                                                  c.data.value?.startDate ??
                                                      DateTime.now()),
                                              weight: FontWeight.w500),
                                          CustomText(c.data.value?.checkinTime ?? '',
                                              color: const Color(greyText),
                                              size: 1.85),
                                        ]),
                                  ),
                                  VerticalDivider(
                                      width: widthSpace(8), indent: 5, endIndent: 5),
                                  Expanded(
                                    child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          CustomText(
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .header
                                                  .checkOut,
                                              size: 1.85,
                                              weight: FontWeight.w500),
                                          SizedBox(height: heightSpace(.3)),
                                          CustomText(
                                              DateFormat.MMMEd().format(
                                                  c.data.value?.endDate ??
                                                      DateTime.now()),
                                              weight: FontWeight.w500),
                                          CustomText(c.data.value?.checkoutTime ?? '',
                                              color: const Color(greyText),
                                              size: 1.85),
                                        ]),
                                  ),
                                ]),
                              ),
                              if (!isMasked) ...[
                                Divider(height: heightSpace(3.5)),
                                linkedWidgets(
                                    Icons.location_on_outlined,
                                    Get.find<TranslationHelper>()
                                        .translations
                                        .reservation
                                        .gettingThere,
                                    c.data.value?.status == 'Accepted'
                                        ? '${Get.find<TranslationHelper>().translations.hostDashboard.address}: ${c.data.value?.propertyAddress}'
                                        : Get.find<TranslationHelper>()
                                            .translations
                                            .reservation
                                            .youWillSeeAddress,
                                    c.data.value?.status == 'Accepted'
                                        ? () {
                                            showMapChooser(
                                                c.data.value?.latitude ?? 0.0,
                                                c.data.value?.longitude ?? 0.0,
                                                c.data.value?.propertyAddress ??
                                                    'Destination');
                                          }
                                        : null),
                              ],
                              Divider(height: heightSpace(3.5)),
                              linkedWidgets(
                                  Icons.menu_book_rounded,
                                  Get.find<TranslationHelper>()
                                      .translations
                                      .listingSidebar
                                      .thingsToKnow,
                                  Get.find<TranslationHelper>()
                                      .translations
                                      .reservation
                                      .instructionsAndHouseRules, () {
                                ViewsCommon.showModalBottom(DraggableScrollableSheet(
                                    maxChildSize: .7,
                                    initialChildSize: .7,
                                    expand: false,
                                    builder: (context, scrollController) {
                                      return SingleChildScrollView(
                                        child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              AppBar(
                                                // toolbarHeight: heightSpace(6),
                                                elevation: 1,
                                                leading: InkWell(
                                                    onTap: Get.back,
                                                    child: const Icon(Icons.clear)),
                                                title: Text(
                                                    Get.find<TranslationHelper>()
                                                        .translations
                                                        .listingSidebar
                                                        .thingsToKnow),
                                              ),
                                              Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    CustomText(
                                                        Get.find<TranslationHelper>()
                                                            .translations
                                                            .payment
                                                            .houseRule,
                                                        size: 2.3,
                                                        weight: FontWeight.w500),
                                                    SizedBox(height: heightSpace(1)),
                                                    CustomText(Get.find<
                                                            TranslationHelper>()
                                                        .translations
                                                        .reservation
                                                        .youWillBeStayingInSomeone),
                                                    CustomText(
                                                            Get.find<
                                                                    TranslationHelper>()
                                                                .translations
                                                                .propertySingle
                                                                .checkInOut,
                                                            size: 2.15,
                                                            weight: FontWeight.w500)
                                                        .paddingSymmetric(
                                                            vertical:
                                                                heightSpace(3.5)),
                                                    iconWidget(Icons.access_time,
                                                        "${Get.find<TranslationHelper>().translations.reservation.checkInAfter} ${c.data.value?.checkinTime}"),
                                                    Divider(height: heightSpace(3)),
                                                    iconWidget(Icons.access_time,
                                                        "${Get.find<TranslationHelper>().translations.reservation.checkOutBefore} ${c.data.value?.checkoutTime}"),
                                                    CustomText(
                                                            Get.find<
                                                                    TranslationHelper>()
                                                                .translations
                                                                .reservation
                                                                .duringYourStay,
                                                            size: 2.1,
                                                            weight: FontWeight.w500)
                                                        .paddingSymmetric(
                                                            vertical:
                                                                heightSpace(3.5)),
                                                    for (final Amenity item
                                                        in c.data.value?.houseRules ??
                                                            []) ...[
                                                      Row(children: [
                                                        item.icon_image == null ||
                                                                item.icon_image!
                                                                    .contains(
                                                                        "assets")
                                                            ? SvgPicture.asset(
                                                                "assets/icons/booking.svg",
                                                                width: widthSpace(5))
                                                            : GlobalHelper.buildNetworkSvgWidget(
                                                          url: item.icon_image ??"",
                                                          width: widthSpace(5),
                                                          height: item.icon_image ==
                                                              "listing-extinguisher.svg"
                                                              ? widthSpace(8)
                                                              : null,
                                                          defaultOption: Image.asset(
                                                              "assets/icons/bookings.png",
                                                              height:
                                                              heightSpace(
                                                                  3)),
                                                        ),
                                                        SizedBox(
                                                            width: widthSpace(2)),
                                                        CustomText(
                                                            Get.locale?.languageCode ==
                                                                    'ar'
                                                                ? item.titleAr
                                                                : item.title,
                                                            color: Colors.black54)
                                                      ]),
                                                      SizedBox(height: heightSpace(2))
                                                    ]
                                                  ]).paddingAll(
                                                  widthSpace(viewPadding)),
                                            ]),
                                      );
                                    }));
                              }),
                              if (c.data.value?.status == "Accepted") ...[
                                Divider(height: heightSpace(3.5)),
                                linkedWidgets(
                                    Icons.messenger_outline,
                                    Get.find<TranslationHelper>()
                                        .translations
                                        .reservation
                                        .messageYourHost,
                                    c.data.value?.userName, () {
                                  ChatHelper.gotoChats(c.data.value?.chatHeadId ?? 0,
                                      webEngageData: {
                                        'Host Name': c.data.value?.userName,
                                        'Checkin Date': c.data.value?.checkinTime,
                                        'Checkout Date': c.data.value?.checkoutTime,
                                        'Unit Code': c.data.value?.unitCode,
                                        'Cost Per Night': c.data.value?.perNight,
                                        'Category Name':
                                            c.data.value?.propertyType ?? '',
                                        'User': isHost ? 'Host' : 'Customer'
                                      });
                                }),
                              ],
                              Divider(height: heightSpace(3.5)),
                              linkedWidgets(
                                  Icons.house_outlined,
                                  Get.find<TranslationHelper>()
                                      .translations
                                      .reservation
                                      .yourPlace,
                                  c.data.value?.propertyName ?? '', () {
                                // Get.toNamed('/${Routes.propertySingle}/${c.data.value?.slug??''}');
                                Get.to(() =>
                                    PropertyDetailScreen(slug: c.data.value!.slug!));
                              }),
                            ]).paddingAll(widthSpace(viewPadding)),
                            const Divider(thickness: 5),
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .hostDashboard
                                          .reservationDetail,
                                      weight: FontWeight.w500),
                                  SizedBox(height: heightSpace(1)),
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .reservation
                                          .reservationNumber,
                                      size: 1.8,
                                      color: const Color(greyText)),
                                  Row(children: [
                                    CustomText(c.data.value?.bookingCode ?? ''),
                                    IconButton(
                                        onPressed: () {
                                          Clipboard.setData(ClipboardData(
                                                  text: c.data.value?.bookingCode ??
                                                      ''))
                                              .then((_) {
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(SnackBar(
                                                    content: Text(
                                                        Get.find<TranslationHelper>()
                                                            .translations
                                                            .reservation
                                                            .copied)));
                                          });
                                        },
                                        icon: const Icon(Icons.copy))
                                  ]),
                                  const Divider(),
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .home
                                          .unitCode,
                                      size: 1.8,
                                      color: const Color(greyText)),
                                  SizedBox(height: heightSpace(.5)),
                                  CustomText(c.data.value?.unitCode ?? '',
                                      weight: FontWeight.w500),
                                ]).paddingSymmetric(
                                horizontal: widthSpace(viewPadding),
                                vertical: heightSpace(1)),
                            const Divider(thickness: 5),
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .reservation
                                          .termsPolicies,
                                      weight: FontWeight.w500),
                                  SizedBox(height: heightSpace(1)),
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .reservation
                                          .cancelRefundPolicy,
                                      size: 1.8,
                                      color: const Color(greyText)),
                                  SizedBox(height: heightSpace(.5)),
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .hostDashboard
                                          .toJson()[c.data.value?.cancelPolicy],
                                      weight: FontWeight.w500),
                                ]).paddingSymmetric(
                                horizontal: widthSpace(viewPadding),
                                vertical: heightSpace(1)),
                            const Divider(thickness: 5),
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .reservation
                                          .checkInInfo,
                                      weight: FontWeight.w500),
                                  SizedBox(height: heightSpace(1)),
                                  renderGoogleMap(
                                      c.data.value?.latitude ?? 0.0,
                                      c.data.value?.longitude ?? 0.0,isAccepted: c.data.value?.status=='Accepted')
                                ]).paddingSymmetric(
                                horizontal: widthSpace(viewPadding),
                                vertical: heightSpace(1.5)),
                            const Divider(thickness: 5),
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .reservation
                                          .hostInformation,
                                      weight: FontWeight.w500),
                                  SizedBox(height: heightSpace(1)),
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .reservation
                                          .hostName,
                                      size: 1.8,
                                      color: const Color(greyText)),
                                  CustomText(c.data.value?.userName),
                                  if (c.data.value?.status == "Accepted") ...[
                                    Divider(height: heightSpace(3)),
                                    InkWell(
                                      onTap: () => SearchHelper.c.showContactHost(
                                          c.data.value?.chatHeadId ?? 0,
                                          c.data.value?.hostNumber,
                                          c.data.value?.status == 'Accepted',
                                          {
                                            'Host Name': c.data.value?.userName,
                                            'Checkin Date': c.data.value?.checkinTime,
                                            'Checkout Date':
                                                c.data.value?.checkoutTime,
                                            'Unit Code': c.data.value?.unitCode,
                                            'Cost Per Night':
                                                c.data.value?.perNight ?? 0,
                                            'Category Name':
                                                c.data.value?.propertyType ?? '',
                                            'User': isHost ? 'Host' : 'Customer'
                                          },
                                          isMasked: isMasked),
                                      child: Row(children: [
                                        const Icon(Icons.phone_outlined, size: 22),
                                        SizedBox(width: widthSpace(2)),
                                        CustomText(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .propertySingle
                                                .chatWithTheHost,
                                            size: 1.9,
                                            weight: FontWeight.bold),
                                        const Spacer(),
                                        const Icon(Icons.chevron_right, size: 22)
                                      ]).paddingSymmetric(vertical: widthSpace(1.6)),
                                    )
                                  ],
                                ]).paddingSymmetric(
                                horizontal: widthSpace(viewPadding),
                                vertical: heightSpace(1)),
                            const Divider(thickness: 5),
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .reservation
                                          .reservationSummary,
                                      weight: FontWeight.w500),
                                  SizedBox(height: heightSpace(2)),
                                  priceWidget(
                                      Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      CustomText("${c.data.value?.totalNights} ${Get.find<TranslationHelper>().translations.listingBasic.nights}"),
                                      const CustomText(" x "),
                                      CustomText("${c.data.value?.perNight} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}"),
                                    ],
                                    ),
                                      // '${c.data.value?.totalNights} ${Get.find<TranslationHelper>().translations.listingBasic.nights} x ${c.data.value?.perNight} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}',
                                      '${(c.data.value?.subTotal ?? 0.0).toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}'),
                                  if (c.data.value!.serviceFee! > 0) ...[
                                    SizedBox(height: heightSpace(1)),
                                    priceWidget(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .propertySingle
                                            .serviceFee,
                                        '${c.data.value!.serviceFee!.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}'),
                                  ],
                                  if (c.data.value!.ivaTax! > 0) ...[
                                    SizedBox(height: heightSpace(1)),
                                    priceWidget(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .propertySingle
                                            .ivaTax,
                                        '${c.data.value!.ivaTax!.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}')
                                  ],
                                  if (c.data.value!.securityFee! > 0) ...[
                                    SizedBox(height: heightSpace(1)),
                                    priceWidget(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .propertySingle
                                            .securityFee,
                                        '${c.data.value!.securityFee!.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}')
                                  ],
                                  if (c.data.value!.cleaningFee! > 0) ...[
                                    SizedBox(height: heightSpace(1)),
                                    priceWidget(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .propertySingle
                                            .cleaningFee,
                                        '${c.data.value!.cleaningFee} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}')
                                  ],
                                  if (c.data.value!.guestFee! > 0) ...[
                                    SizedBox(height: heightSpace(1)),
                                    priceWidget(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .propertySingle
                                            .additionalGuestFee,
                                        '${c.data.value!.guestFee} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}')
                                  ],
                                  if (c.data.value?.discountType != 'no_discount') ...[
                                    SizedBox(height: heightSpace(1)),
                                    priceWidget(
                                        Get.find<TranslationHelper>().translations.propertySingle.toJson()[c.data.value?.discountType],
                                        '${c.data.value!.youSaved} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}',color: const Color(warningColor))
                                  ],
                                  const Divider(),
                                  if (c.data.value!.totalDiscount! > 0) ...[
                                    SizedBox(height: heightSpace(1)),
                                    priceWidget(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .hostReservation
                                            .darentDiscount,
                                        '${c.data.value!.totalDiscount} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}',color: const Color(warningColor))
                                  ],
                                  if (c.data.value?.status == "Accepted") ...[
                                    SizedBox(height: heightSpace(1)),
                                    priceWidget(
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .bookingDetail
                                            .subtotal,
                                        '${c.data.value?.total ?? 0.0} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}'),
                                    SizedBox(height: heightSpace(1)),
                                    Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          CustomText(
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .sidenav
                                                  .wallet,
                                              color: const Color(greyText)),
                                          CustomText(
                                              '-${c.data.value!.walletDeduction} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}',
                                              strikeThrough:
                                                  c.data.value!.walletDeduction == 0,
                                              color: const Color(warningColor),
                                              weight: FontWeight.w500),
                                        ]),
                                  ],
                                  SizedBox(height: heightSpace(1)),
                                  Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .hostDashboard
                                                .total,
                                            color: const Color(greyText)),
                                        CustomText(
                                            '${c.totalAfterWallet.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}',
                                            size: 2.1,
                                            weight: FontWeight.w500),
                                      ]),
                                  if (c.data.value?.status == "Accepted") ...[
                                    const Divider(),
                                    linkedWidget(
                                        Icons.receipt_long,
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .reservation
                                            .viewReservationSummary,
                                        () => SearchHelper.c.viewReceipt(code),
                                        isLoading: SearchHelper.c.lazyLoader.value),
                                  ],
                                  if (SearchHelper.c.reservationTabController.index == 2) ...[
                                    const Divider(),
                                    c.data.value!.rated!
                                        ? Row(children: [
                                            const Icon(Icons.star_border_rounded,
                                                size: 22),
                                            SizedBox(width: widthSpace(2)),
                                            CustomText(
                                                Get.find<TranslationHelper>()
                                                    .translations
                                                    .reservation
                                                    .rated,
                                                size: 1.9,
                                                weight: FontWeight.bold),
                                            const Spacer(),
                                            const Icon(Icons.check_circle,
                                                size: 22, color: Color(successColor))
                                          ]).paddingSymmetric(
                                            vertical: widthSpace(1.6))
                                        : linkedWidget(
                                            Icons.star_border_rounded,
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .reservation
                                                .rateYourStay, () {
                                            SearchHelper.c.clearRatingFields();
                                            SearchHelper.c.showRatingSheet(c.data);
                                          }),
                                  ],
                                ]).paddingSymmetric(
                                horizontal: widthSpace(viewPadding),
                                vertical: heightSpace(1)),
                            const Divider(thickness: 5),
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .reservation
                                          .paymentInformation,
                                      weight: FontWeight.w500, ),
                                  if (c.data.value?.paymentType != null) ...[
                                    SizedBox(height: heightSpace(2)),
                                    Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          CustomText(
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .utility
                                                  .paymentMethod,
                                              color: const Color(greyText)),
                                          CustomText(c.data.value!.paymentType,
                                              weight: FontWeight.w500),
                                        ]),
                                  ],
                                  SizedBox(height: heightSpace(1)),
                                  Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        CustomText(
                                            c.data.value?.status == "Accepted"
                                                ? Get.find<TranslationHelper>()
                                                    .translations
                                                    .reservation
                                                    .paidFull
                                                : Get.find<TranslationHelper>()
                                                    .translations
                                                    .tripsActive
                                                    .unpaid,
                                            color: const Color(greyText)),
                                        CustomText(
                                            '${c.data.value?.total ?? 0.0} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}',
                                            weight: FontWeight.w500),
                                      ]),
                                ]).paddingSymmetric(
                                horizontal: widthSpace(viewPadding),
                                vertical: heightSpace(1)),
                            const Divider(thickness: 5),
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .reservation
                                          .getSupportAnytime,
                                      size: 2.3,
                                      weight: FontWeight.w500),
                                  CustomText(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .reservation
                                          .ifYouNeedHelp,
                                      size: 1.85),
                                  SizedBox(height: heightSpace(2)),
                                  InkWell(
                                      child: Row(children: [
                                        SvgPicture.asset("assets/service.svg",
                                            width: widthSpace(6),
                                            color: Colors.black),
                                        SizedBox(width: widthSpace(3)),
                                        CustomText(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .accountMobile
                                                .customerServices,
                                            size: 2.1,
                                            weight: FontWeight.w500),
                                        const Spacer(),
                                        const Icon(Icons.chevron_right)
                                      ]).paddingSymmetric(vertical: heightSpace(1.5)),
                                      onTap: () =>
                                          Get.to(() => const CustomerServices())),
                                  InkWell(
                                      onTap: () {
                                        AuthHelper.c.bookingId.text =
                                            c.data.value?.bookingCode ?? "";
                                        Get.to(() => const ReportProblem(
                                            notReservation: false));
                                      },
                                      child: Row(children: [
                                        const Icon(Icons.report_gmailerrorred),
                                        SizedBox(width: widthSpace(3)),
                                        CustomText(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .reservation
                                                .report,
                                            size: 2.1,
                                            weight: FontWeight.w500),
                                        const Spacer(),
                                        const Icon(Icons.chevron_right)
                                      ]).paddingSymmetric(
                                          vertical: heightSpace(1.5))),
                                  if (c.allowCancel &&
                                      c.data.value?.status != 'Cancelled' &&
                                      c.data.value?.status != 'Declined' &&
                                      SearchHelper.c.reservationTabController.index == 0)
                                    CommonButton(
                                        title: Get.find<TranslationHelper>()
                                            .translations
                                            .reservation
                                            .cancelReservation,
                                        isLoading: SearchHelper.c.isBtnLoading.value,
                                        onPressed: () {
                                          Get.dialog(WarningDialog(
                                              title: Get.find<TranslationHelper>()
                                                  .translations
                                                  .general
                                                  .beforeCancelSeeCancelPolicy,
                                              description:
                                                  Get.find<TranslationHelper>()
                                                      .translations
                                                      .general
                                                      .hostCancelPolicyDetermine,
                                              largeText: true,
                                              onConfirmed: () {
                                                SearchHelper.c
                                                    .onCancelReservation(
                                                        c.data.value?.id)
                                                    .then((val) {
                                                  if (val == true) {
                                                    c.data.value?.status =
                                                        'Cancelled';
                                                    c.data.refresh();
                                                    Map<String, dynamic> myMap = {
                                                      'Reservation number':
                                                          "${c.data.value?.bookingCode}",
                                                      'Cost per night':
                                                          "${c.data.value?.perNight}",
                                                      'Status':
                                                          'Cancelled by the Guest',
                                                      'Cancellation Policy':
                                                          "${c.data.value?.cancelPolicy}",
                                                      'User': isHost
                                                          ? 'Host'
                                                          : 'Customer',
                                                      'Name':
                                                          "${c.data.value?.propertyName}",
                                                      'Checkin Date':
                                                          c.data.value?.checkinTime,
                                                      'Checkout Date':
                                                          c.data.value?.checkoutTime,
                                                      'Unit Code':
                                                          c.data.value?.unitCode ??
                                                              '',
                                                      'Category Name':
                                                          "${c.data.value?.propertyType}",
                                                    };
                                                    if (kDebugMode) {
                                                      print(
                                                          "Cancel Booking reservation details $myMap ");
                                                    }
                                                    WebEngagePlugin.trackEvent(
                                                        'Cancel Booking', myMap);
                                                  }
                                                });
                                              }));
                                        })
                                ]).paddingSymmetric(
                                horizontal: widthSpace(viewPadding),
                                vertical: heightSpace(1)),
                          ],
                        ),
                      ),
              ),
            ),
          ],
        ),
        bottomNavigationBar: c.data.value?.propertyStatus == true &&
                (c.data.value?.status == "Processing" ||
                    c.data.value?.status == "Unpaid")
            ? Container(
                margin: EdgeInsets.symmetric(
                    horizontal: widthSpace(5), vertical: 20),
                child: CommonButton(
                    title: Get.find<TranslationHelper>()
                        .translations
                        .payment
                        .makePayment,
                    isLoading: SearchHelper.c.isBtnLoading.value,
                    onPressed: () {
                      SearchHelper.c.unSelectWalletPay();
                      SearchHelper.c.continuePayment(c, c.getData);
                    }),
              )
            : null,
      ),
    );
  }
  bool mapFullScreen = false;
  Widget renderGoogleMap(latitude,longitude,{bool isAccepted=true}){
    return Stack(
      children: [
        Container(
            height: heightSpace(mapFullScreen?100:35),
            clipBehavior: Clip.antiAlias,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12)),
            child: GoogleMap(
                mapType: MapType.normal,
                zoomControlsEnabled: isAccepted,
                zoomGesturesEnabled: mapFullScreen,
                buildingsEnabled: false,
                myLocationButtonEnabled: false,
                initialCameraPosition: CameraPosition(
                    target: LatLng(latitude ?? 0.0,longitude ?? 0.0),
                    zoom: 15))),
        if(isAccepted)Positioned(
          top: 16,
          right: 16,
          child: FloatingActionButton(
            mini: true,
            backgroundColor: Colors.white,
            onPressed: () {
              if(Get.isDialogOpen==false){
                mapFullScreen = true;
                Get.dialog(renderGoogleMap(latitude, longitude)).then((_){
                  mapFullScreen = false;
                });
              }else{
                Get.back();
              }
            },
            child: Icon(
              mapFullScreen?Icons.fullscreen_exit:Icons.fullscreen,
              color: Colors.grey[600],
            ),
            // backgroundColor: Colors.black54,
          ),
        )
      ],
    );
  }
  priceWidget(title, String value, {Color color = const Color(greyText)}) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      if(title is String)...[
        CustomText(title)
      ] else...[
        title ??const SizedBox.shrink()
      ],
      CustomText(value, weight: FontWeight.w500,color: color),
    ]);
  }

  linkedWidget(icon, name, function, {bool isLoading = false}) {
    return InkWell(
      onTap: function,
      child: Row(children: [
        Icon(icon, size: 22),
        SizedBox(width: widthSpace(2)),
        CustomText(
            isLoading
                ? Get.find<TranslationHelper>().translations.listing.pleaseWait
                : name,
            size: 1.9,
            weight: FontWeight.bold),
        const Spacer(),
        isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(strokeWidth: 2))
            : Icon(Icons.chevron_right, size: 22)
      ]).paddingSymmetric(vertical: widthSpace(1.6)),
    );
  }

  iconWidget(icon, title) {
    return Row(children: [
      const Icon(Icons.access_time, color: Colors.black54),
      SizedBox(width: widthSpace(2)),
      CustomText(title, color: Colors.black54)
    ]);
  }

  linkedWidgets(icon, title, subTitle, [onTap]) {
    return InkWell(
      onTap: onTap,
      child: Row(children: [
        Icon(icon, size: widthSpace(7.5)),
        SizedBox(width: widthSpace(2.7)),
        Expanded(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            CustomText(title, weight: FontWeight.w500),
            CustomText(subTitle, size: 1.9, color: const Color(greyText)),
          ]),
        )
      ]),
    );
  }
}
