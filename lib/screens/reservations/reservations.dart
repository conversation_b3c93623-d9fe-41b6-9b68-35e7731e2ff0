import 'package:darent/components/custom_text.dart';
import 'package:darent/components/mapChooser.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/components/yaqeen_verification_dialog.dart';
import 'package:darent/controllers/dashboard_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:darent/models/reservationModel.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/routes.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';

import '../../helperMethods/translation_helper.dart';

class Reservations extends StatelessWidget {
  const Reservations({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final trans = Get.find<TranslationHelper>().translations;
    return GetBuilder<DashboardController>(
        id: "bookings",
        builder: (c) => NestedScrollView(
                headerSliverBuilder: (ctx, isScrolled) => [
                      SliverToBoxAdapter(
                        child: Container(
                          height: heightSpace(5.3),
                          padding: const EdgeInsets.all(3),
                          decoration: BoxDecoration(
                              color: Colors.grey[200],
                              borderRadius: BorderRadius.circular(7)),
                          alignment: Alignment.center,
                          child: TabBar(
                              controller: c.reservationTabController,
                              isScrollable: true,
                              indicator: BoxDecoration(
                                  color: Colors.white,
                                  boxShadow: ViewsCommon.boxShadow,
                                  borderRadius: BorderRadius.circular(6.5)),
                              automaticIndicatorColorAdjustment: true,
                              tabs: [
                                Tab(
                                    text: trans.reservation.upComing),
                                Tab(
                                    text: trans.reservation.ongoing),
                                Tab(
                                    text: trans.reservation.history),
                                Tab(
                                    text: trans.hostDashboard.canceled ?? "Canceled"),
                                Tab(
                                    text: trans.reservation.expired!)
                              ]),
                        ),
                      ),
                    ],
                // Container(
                //   padding: const EdgeInsets.all(3),
                //   decoration: BoxDecoration(
                //       color: Colors.grey[200],
                //       borderRadius: BorderRadius.circular(10)
                //   ),
                //   child: SingleChildScrollView(
                //     scrollDirection: Axis.horizontal,
                //     child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                //       InkWell(
                //           child: Container(
                //             padding: EdgeInsets.symmetric(
                //                 horizontal: widthSpace(6), vertical: widthSpace(2.2)),
                //             decoration: BoxDecoration(
                //                 color: c.bookingTab == "coming"
                //                     ?  Colors.white
                //                     : null,
                //                      boxShadow:  c.bookingTab == "coming"?[
                //                        BoxShadow(
                //                          color: Colors.grey.withOpacity(0.5),
                //                          spreadRadius: 0.05,
                //                          blurRadius: 2,
                //                          offset: const Offset(0,0))]:null,
                //                 borderRadius: BorderRadius.circular(7)),
                //             child: CustomText(
                //                 translation.reservation.upComing,
                //                 size: 2,
                //                 weight: FontWeight.w500,
                //                 color: c.bookingTab == "coming"
                //                     ? null
                //                     : const Color(greyText)),
                //           ),
                //           onTap: () {
                //             c.setBookingTab("coming");
                //           }),
                //           InkWell(
                //               child: Padding(
                //           padding: const EdgeInsets.only(right: 14),
                //           child: Container(
                //                   padding: EdgeInsets.symmetric(
                //                       horizontal: widthSpace(4), vertical: widthSpace(2.2)),
                //                   decoration: BoxDecoration(
                //                       color: c.bookingTab == "onGoing"
                //                           ?  Colors.white
                //                           : null,
                //                       boxShadow:  [
                //                                  c.bookingTab == "onGoing"? BoxShadow(
                //                                     color: Colors.grey.withOpacity(0.5),
                //                                     spreadRadius: 0.05,
                //                                     blurRadius: 2,
                //                                     offset: const Offset(0,0), // changes position of shadow
                //                                   ) :  BoxShadow(
                //                                     color: Colors.grey.withOpacity(0.1),
                //                                     spreadRadius: 0.0,
                //                                     blurRadius: 0.0,
                //                                     offset: const Offset(0,0), // changes position of shadow
                //                                   )
                //                                 ],
                //                   borderRadius: BorderRadius.circular(6)
                //                       ),
                //                   child: CustomText(
                //                       translation.reservation.ongoing,
                //                       weight: FontWeight.w500,
                //                       color: c.bookingTab == "onGoing"
                //                           ? null
                //                           : const Color(greyText)),
                //                 ),
                //               ),
                //               onTap: () {
                //                 c.setBookingTab("onGoing");
                //               }),
                //       InkWell(
                //           child: Padding(
                //             padding: const EdgeInsets.only(right: 14),
                //             child: Container(
                //                   padding: EdgeInsets.symmetric(
                //                       horizontal: widthSpace(4), vertical: widthSpace(2.2)),
                //                   decoration: BoxDecoration(
                //                       color: c.bookingTab == "history"
                //                           ?  Colors.white
                //                           : null,
                //                       boxShadow:  [
                //                                  c.bookingTab == "history"? BoxShadow(
                //                                     color: Colors.grey.withOpacity(0.5),
                //                                     spreadRadius: 0.05,
                //                                     blurRadius: 2,
                //                                     offset: Offset(0,0), // changes position of shadow
                //                                   ) :  BoxShadow(
                //                                     color: Colors.grey.withOpacity(0.1),
                //                                     spreadRadius: 0.0,
                //                                     blurRadius: 0.0,
                //                                     offset: Offset(0,0), // changes position of shadow
                //                                   )
                //                                 ],
                //                   borderRadius: BorderRadius.circular(6)
                //                   ),
                //               child:
                //               CustomText(
                //                   translation.reservation.history,
                //                   size: 1.9,
                //                   weight: FontWeight.w500,
                //                   color: c.bookingTab == "history"
                //                       ? null
                //                       : const Color(greyText)),
                //             ),
                //           ),
                //           onTap: () {
                //             c.setBookingTab("history");
                //           }),
                //            InkWell(
                //           child: Padding(
                //           padding: const EdgeInsets.only(right: 14),
                //           child: Container(
                //                   padding: EdgeInsets.symmetric(
                //                       horizontal: widthSpace(4), vertical: widthSpace(2.2)),
                //                   decoration: BoxDecoration(
                //                       color:  c.bookingTab == "Cancelled"
                //                           ?  Colors.white
                //                           : null,
                //                       boxShadow:  [
                //                                   c.bookingTab == "Cancelled"? BoxShadow(
                //                                     color: Colors.grey.withOpacity(0.5),
                //                                     spreadRadius: 0.05,
                //                                     blurRadius: 2,
                //                                     offset: const Offset(0,0), // changes position of shadow
                //                                   ) :  BoxShadow(
                //                                     color: Colors.grey.withOpacity(0.1),
                //                                     spreadRadius: 0.0,
                //                                     blurRadius: 0.0,
                //                                     offset: Offset(0,0), // changes position of shadow
                //                                   )
                //                                 ],
                //                   borderRadius: BorderRadius.circular(6)
                //                   ),
                //               child: CustomText(
                //                   translation.hostDashboard.canceled,
                //                   size: 1.9,
                //                   weight: FontWeight.w500,
                //                   color: c.bookingTab == "Cancelled"
                //                       ? null
                //                       : const Color(greyText)),
                //             ),
                //           ),
                //           onTap: () {
                //             c.setBookingTab("Cancelled");
                //           }),
                //           // InkWell(
                //           // child: Padding(
                //           // padding: const EdgeInsets.only(right: 14),
                //           // child: Container(
                //           //         padding: EdgeInsets.symmetric(
                //           //             horizontal: widthSpace(4), vertical: widthSpace(2.2)),
                //           //         decoration: BoxDecoration(
                //           //             color:  c.bookingTab == "Expired"
                //           //                 ?  Colors.white
                //           //                 : null,
                //           //             boxShadow:  [
                //           //                         c.bookingTab == "Expired"? BoxShadow(
                //           //                           color: Colors.grey.withOpacity(0.5),
                //           //                           spreadRadius: 0.05,
                //           //                           blurRadius: 2,
                //           //                           offset: Offset(0,0), // changes position of shadow
                //           //                         ) :  BoxShadow(
                //           //                           color: Colors.grey.withOpacity(0.1),
                //           //                           spreadRadius: 0.0,
                //           //                           blurRadius: 0.0,
                //           //                           offset: Offset(0,0), // changes position of shadow
                //           //                         )
                //           //                       ],
                //           //         borderRadius: BorderRadius.circular(6)
                //           //         ),
                //           //     child: CustomText(
                //           //         // translation.reservation.history,
                //           //         "Expired",
                //           //         size: 1.9,
                //           //         weight: FontWeight.w500,
                //           //         color: c.bookingTab == "Expired"
                //           //             ? null
                //           //             : const Color(greyText)),
                //           //   ),
                //           // ),
                //           // onTap: () {
                //           //   c.setBookingTab("Expired");
                //           // })
                //     ]),
                //   ),
                // ),
                body: c.userDataLoading
                    ? shimmers()
                    : NotificationListener<ScrollEndNotification>(
                  onNotification: (notification) {
                    final metrics = notification.metrics;
                    if (metrics.atEdge) {
                      bool isTop = metrics.pixels == 0;
                      if (!isTop) {
                        int index = c.reservationTabController.index;
                        if(index==0){
                          if(c.upcomingPage < c.upcomingLastPage){
                            c.upcomingPage++;
                            c.getBookings();
                          }
                        }else if(index==1){
                          if(c.ongoingPage < c.ongoingLastPage){
                            c.ongoingPage++;
                            c.getOnGoing();
                          }
                        }else if(index==2){
                          if(c.historyPage < c.historyLastPage){
                            c.historyPage++;
                            c.getBookingsHistory();
                          }
                        }else if(index==3){
                          if(c.cancelledPage < c.cancelledLastPage){
                            c.cancelledPage++;
                            c.getCancelledBookings();
                          }
                        }else if(index==4){
                          if(c.expiredPage < c.expiredLastPage){
                            c.expiredPage++;
                            c.getExpiredBookings();
                          }
                        }
                      }
                    }
                    return true;
                  },
                      child: TabBarView(controller: c.reservationTabController, children: [
                          Tab(
                              child: renderListing(c.reservationComing, c.getBookings,trans.reservation.noUpcomingBookings,c.upcomingPage,c.upcomingLastPage)),
                          Tab(
                              child: renderListing(c.onGoing, c.getOnGoing,trans.reservation.noOngoingBookings,c.ongoingPage,c.ongoingLastPage)),
                          Tab(
                              child: renderListing(c.reservationHistory, c.getBookingsHistory,trans.reservation.noPastBookings,c.historyPage,c.historyLastPage)),
                          Tab(
                              child: renderListing(c.cancelledReservation, c.getCancelledBookings,trans.reservation.noCancelledBookings,c.cancelledPage,c.cancelledLastPage)),
                          Tab(
                              child: renderListing(c.expiredReservation, c.getExpiredBookings, trans.reservation.noExpiredBookings,c.expiredPage,c.expiredLastPage))
                        ]),
                    ))
            .paddingAll(widthSpace(viewPadding)));
  }

  renderListing(List<ReservationModel> data, refresh,String? emptyMsg,int currentPage,int totalPages) {
    return RefreshIndicator(
        onRefresh: () async {
          refresh(refresh: true);
        },
        child: data.isEmpty
            ? emptyView(emptyMsg??'')
            : ListView.separated(
                padding: EdgeInsets.only(top: heightSpace(4)),
                itemBuilder: (context, index) {
                  if(index==data.length){
                    return const Center(child: CircularProgressIndicator()).paddingAll(10);
                  }
                  bool isMasked = SearchHelper.c.reservationTabController.index == 2 || SearchHelper.c.reservationTabController.index == 3;
                  DateTime start = formDateFormat.parse(data[index].startDate!);
                  DateTime end = formDateFormat.parse(data[index].endDate!);
                  bool isAccepted = data[index].status == 'Accepted';
                  return InkWell(
                    onTap: () => checkAndMoveToScreen(
                        () => Get.toNamed('${Routes.reservationDetail}/${data[index].bookingCode}'),
                        isAccepted: isAccepted),
                    child: Container(
                        clipBehavior: Clip.antiAlias,
                        decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(color: const Color(greyBorder)),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: ViewsCommon.boxShadow),
                        child: Column(children: [
                          Container(
                              height: heightSpace(18),
                              padding: EdgeInsets.all(widthSpace(2)),
                              alignment: Alignment.topLeft,
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                    image: GlobalHelper.buildNetworkImageProvider(url: data[index].coverPhoto??''),
                                    fit: BoxFit.cover),
                              ),
                              child: SearchHelper.c.reservationTabController.index == 0
                                  ? Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius:
                                              BorderRadius.circular(3.5)),
                                      child: CustomText(
                                          data[index].upcomingDays == 0
                                              ? Get.find<TranslationHelper>().translations.hostDashboard.today ??"Today"
                                              : data[index].upcomingDays == 1
                                                  ? Get.find<TranslationHelper>().translations.reservation.inDay
                                                  : Get.find<TranslationHelper>().translations.reservation.inDays
                                                      .replaceAll(':date',data[index].upcomingDays.toString()),
                                          size: 1.75,
                                          weight: FontWeight.w500),
                                    )
                                  : null),
                          Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CustomText(data[index].city ?? 'City',
                                    size: 2.3, weight: FontWeight.w500),
                                CustomText(data[index].propertyName, size: 1.7),
                                const Divider(),
                                SizedBox(
                                  height: heightSpace(10),
                                  child: Row(children: [
                                    Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          CustomText(
                                              "${months[Get.locale?.languageCode ?? 'en']![start.month - 1]}\n${GlobalHelper.twoNumberFormat(start.day)}-${GlobalHelper.twoNumberFormat(end.day)}"),
                                          CustomText('${start.year}',
                                              size: 1.8),
                                        ]),
                                    VerticalDivider(width: widthSpace(6)),
                                    Expanded(
                                        child: CustomText(
                                            isMasked &&
                                                    (data[index].address ?? '')
                                                            .length >
                                                        5
                                                ? '${data[index].address!.substring(0, 4)}...'
                                                : data[index].address,
                                            size: 1.9))
                                  ]),
                                ),
                                if (!isMasked) ...[
                                  const Divider(),
                                  InkWell(
                                    onTap: () => checkAndMoveToScreen(
                                      () {
                                        if (isAccepted) {
                                          showMapChooser(
                                              double.parse(data[index].latitude ??'0.0'),
                                              double.parse(data[index].longitude ??'0.0'),data[index].address ??"Destination");
                                        }
                                      },
                                    ),
                                    child: Row(children: [
                                      const Icon(Icons.location_on_outlined,
                                          size: 22),
                                      SizedBox(width: widthSpace(2)),
                                      Expanded(
                                          child: CustomText(
                                              isAccepted
                                                  ? Get.find<TranslationHelper>().translations.reservation.propertyLocation
                                                  : Get.find<TranslationHelper>().translations.reservation.youWillSeeAddress,
                                              size: 1.9,
                                              weight: FontWeight.bold)),
                                      const Icon(Icons.chevron_right, size: 22)
                                    ]).paddingSymmetric(vertical: widthSpace(1.6)),
                                  ),
                                ],
                                if (SearchHelper.c.reservationTabController.index != 3) ...[
                                  const Divider(),
                                  InkWell(
                                    onTap: () => checkAndMoveToScreen(
                                      () => SearchHelper.c.showContactHost(
                                          data[index].chatHeadId ?? 0,
                                          data[index].phone,
                                          isAccepted,
                                          {
                                            'Host Name': data[index].userName,
                                            'Checkin Date': data[index].startDate,
                                            'Checkout Date': data[index].endDate,
                                            'Unit Code': data[index].unitCode ?? '',
                                            'Cost Per Night': data[index].perNight ?? 0,
                                            'Category Name': data[index].propertyType ?? '',
                                            'User': isHost ? 'Host' : 'Customer'
                                          },
                                          isMasked: isMasked),
                                    ),
                                    child: Row(children: [
                                      const Icon(Icons.phone_outlined,
                                          size: 22),
                                      SizedBox(width: widthSpace(2)),
                                      CustomText(
                                          Get.find<TranslationHelper>().translations.propertySingle.chatWithTheHost ??"Chat With Host",
                                          size: 1.9,
                                          weight: FontWeight.bold),
                                      const Spacer(),
                                      const Icon(Icons.chevron_right, size: 22)
                                    ]).paddingSymmetric(vertical: widthSpace(1.6)),
                                  )
                                ]
                              ]).paddingAll(widthSpace(4))
                        ])),
                  );
                },
                separatorBuilder: (c, i) => SizedBox(height: heightSpace(2.5)),
                itemCount: currentPage<totalPages?data.length+1:data.length));
  }

  checkAndMoveToScreen(function, {bool isAccepted = false}) {
    print("checkAndMoveToScreen");
    if (
    // userModel.value!.showYaqeenReservation // &&
        !userModel.value!.yaqeenVerified
        // kReleaseMode &&
        // isAccepted
    ) {
      Get.dialog(const YaqeenVerificationDialog());
    } else {
      function();
    }
  }

  Shimmer shimmers() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        margin: EdgeInsets.only(top: heightSpace(4)),
        clipBehavior: Clip.antiAlias,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: heightSpace(18),
              padding: EdgeInsets.all(widthSpace(2)),
              color: Colors.white,
            ),
            Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: heightSpace(1)),
                      Container(
                        width: double.infinity,
                        height: 18.0,
                        color: Colors.white,
                      ),
                      SizedBox(height: heightSpace(0.7)),
                      Container(
                        width: double.infinity,
                        height: 18.0,
                        color: Colors.white,
                      ),
                      SizedBox(height: heightSpace(.7)),
                      Container(
                        width: double.infinity,
                        height: 18.0,
                        color: Colors.white,
                      ),
                      SizedBox(height: heightSpace(0.2)),
                    ])),
            Container(
              padding: EdgeInsets.symmetric(vertical: widthSpace(3)),
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                  border: Border(top: BorderSide(color: Colors.white))),
              child: Container(
                width: widthSpace(80),
                height: 18.0,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  emptyView(final String name) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.calendar_today_outlined,
          size: 100,
          color: Colors.grey.withOpacity(0.6),
        ),
        CustomText(name),
      ],
    );
  }
}
