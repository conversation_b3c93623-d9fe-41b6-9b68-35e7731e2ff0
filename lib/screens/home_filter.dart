import 'dart:async';

import 'package:darent/components/common_button.dart';
import 'package:darent/components/common_checkbox.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/components/host/host_listtile.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import '../helperMethods/remote_config.dart';
import '../helperMethods/translation_helper.dart';

class HomeFilter extends StatefulWidget {
  final dynamic data;

  const HomeFilter({super.key, required this.data});

  @override
  State<HomeFilter> createState() => _HomeFilterState();
}

class _HomeFilterState extends State<HomeFilter> {
  final ScrollController _scrollController = ScrollController();
  final GlobalKey _priceRangeKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // Setup listeners once during initialization
    _setupListeners();
    // Scroll to Price Range section after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToPriceRange();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    SearchHelper.c.minController.removeListener(_minListener);
    SearchHelper.c.maxController.removeListener(_maxListener);
    super.dispose();
  }

  void _setupListeners() {
    SearchHelper.c.minController.addListener(_minListener);
    SearchHelper.c.maxController.addListener(_maxListener);
  }

  void _minListener() {
    final val = SearchHelper.c.minController.text;
    if (val.isEmpty || val == '0') return;
    final value = double.tryParse(val.replaceAll('+', ''));
    if (value != null && value <= SearchHelper.c.priceRange.value.end) {
      if (value <= SearchHelper.maxPrice) {
        SearchHelper.c.priceRange.value =
            SfRangeValues(value, SearchHelper.c.priceRange.value.end);
      } else {
        SearchHelper.c.minController.text = '${SearchHelper.maxPrice}+';
      }
    }
  }

  void _maxListener() {
    final val = SearchHelper.c.maxController.text;
    if (val.isEmpty) return;
    final value = double.tryParse(val.replaceAll('+', ''));
    if (value != null && value >= SearchHelper.c.priceRange.value.start) {
      if (value <= SearchHelper.maxPrice) {
        SearchHelper.c.priceRange.value =
            SfRangeValues(SearchHelper.c.priceRange.value.start, value);
      } else {
        SearchHelper.c.maxController.text = '${SearchHelper.maxPrice}+';
      }
    }
  }

  void _scrollToPriceRange() {
    final renderBox =
    _priceRangeKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final position = renderBox.localToGlobal(Offset.zero).dy;
      _scrollController.animateTo(
        position - 100, // Adjust for app bar
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final translation = Get.find<TranslationHelper>().translations;
    final remoteConfig = Get.find<RemoteConfig>();

    return Scaffold(
      body: ListView(
        controller: _scrollController,
        children: [
          _buildHeaderSection(translation),
          SizedBox(height: heightSpace(4)),
          _buildFilterSections(translation, remoteConfig),
        ],
      ),
      bottomNavigationBar: _buildBottomBar(translation),
    );
  }

  Widget _buildHeaderSection(translation) {
    final controller = SearchHelper.c;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: widthSpace(viewPadding)),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(3, 7),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: heightSpace(3)),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                height: heightSpace(5.6),
                width: heightSpace(5.8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100),
                  color: Colors.white,
                  boxShadow: ViewsCommon.boxShadow,
                ),
                child: Center(
                  child: IconButton(
                    onPressed: Get.back,
                    icon: const Icon(Icons.close),
                  ),
                ),
              ),
              CustomText(
                translation.filter.filter,
                size: 2.6,
                weight: FontWeight.w500,
              ),
              SizedBox(width: widthSpace(10)),
            ],
          ),
          SizedBox(height: heightSpace(2.5)),
          Obx(() => controller.filters.value != null &&
              controller.filters.value!.recommendedAmenities.isNotEmpty
              ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _renderHead(translation.filter.recommendedProperties ?? ''),
              SizedBox(height: heightSpace(2)),
              SizedBox(
                width: double.maxFinite,
                child: Wrap(
                  direction: Axis.horizontal,
                  alignment: WrapAlignment.spaceBetween,
                  runSpacing: heightSpace(1),
                  children: List.generate(
                    controller.filters.value!.recommendedAmenities.length,
                        (i) {
                      final amenity =
                      controller.filters.value!.recommendedAmenities[i];
                      final selected = amenity.isChecked;
                      return Column(
                        children: [
                          InkWell(
                            onTap: () => controller.checkFilter(
                                amenity, isRecommended: true),
                            child: Container(
                              width: widthSpace(42),
                              height: widthSpace(20),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                color: selected
                                    ? const Color(themeColor)
                                    .withOpacity(0.07)
                                    : null,
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: selected
                                      ? Colors.black
                                      : const Color(greyBorder),
                                ),
                              ),
                              child: GlobalHelper.buildNetworkSvgWidget(
                          url: "/${amenity.iconImage}",
                                height: widthSpace(7),
                          myColor: Colors.black,
                          defaultOption: Image.asset(
                          'assets/icons/bookings.png',
                          height: widthSpace(7),
                            color: Colors.black,
                          ),)
                            ),
                          ),
                          SizedBox(height: heightSpace(1)),
                          CustomText(
                            Get.locale?.languageCode == 'ar'
                                ? amenity.titleAr
                                : amenity.title,
                            weight: FontWeight.w500,
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
              Divider(height: heightSpace(4)),
            ],
          )
              : const SizedBox.shrink()),
          _renderHead(translation.filter.propertyTypes),
          SizedBox(height: heightSpace(2)),
          Obx(() => Wrap(
            spacing: widthSpace(3),
            runSpacing: widthSpace(2),
            direction: Axis.horizontal,
            children: List.generate(
              controller.filters.value?.property_type.length ?? 0,
                  (index) {
                final property =
                controller.filters.value!.property_type[index];
                final selected =
                    controller.selectedPropertyType.value == property.id;
                return InkWell(
                  onTap: () => controller.checkPropertyType(property.id),
                  child: Container(
                    width: widthSpace(27),
                    padding: EdgeInsets.symmetric(vertical: widthSpace(2)),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: selected
                          ? const Color(themeColor).withOpacity(0.07)
                          : null,
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: selected
                            ? Colors.black
                            : const Color(greyBorder),
                      ),
                    ),
                    child: CustomText(
                      Get.locale?.languageCode == 'ar'
                          ? property.nameAr
                          : property.name,
                      size: 1.9,
                      weight: FontWeight.w500,
                    ),
                  ),
                );
              },
            ),
          )),
          SizedBox(height: heightSpace(2)),
        ],
      ),
    );
  }

  Widget _buildFilterSections(translation, remoteConfig) {
    final controller = SearchHelper.c;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: widthSpace(viewPadding)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(translation.listing.guests),
          SizedBox(height: heightSpace(2)),
          Obx(() => SizedBox(
            height: heightSpace(4),
            child: GridView.count(
              scrollDirection: Axis.horizontal,
              crossAxisCount: 1,
              mainAxisSpacing: widthSpace(2),
              childAspectRatio: 0.67,
              children: noOfCounts
                  .map((item) => _countContainer(
                item,
                controller.bedsNo.value == item,
                    () {
                  controller.bedsNo.value = item;
                  SearchHelper.filterCleared = false;
                  SearchHelper.c.getResultCount();
                },
              ))
                  .toList(),
            ),
          )),
          Divider(height: heightSpace(4)),
          _renderHead(translation.search.priceRange, key: _priceRangeKey),
          SizedBox(height: heightSpace(2)),
          Obx(() => SfRangeSliderTheme(
            data: SfRangeSliderThemeData(
              tooltipBackgroundColor:
              const Color(themeColor).withOpacity(0.8),
              activeTrackHeight: 10,
              inactiveTrackHeight: 8,
            ),
            child: SfRangeSlider(
              min: controller.filters.value?.min_price ?? 0,
              max: controller.filters.value?.max_price ?? 5000,
              values: controller.priceRange.value,
              inactiveColor: Colors.grey[200],
              stepSize: 1,
              activeColor: const Color(themeColor).withOpacity(0.5),
              showLabels: true,
              enableTooltip: true,
              endThumbIcon: const DecoratedBox(
                decoration: BoxDecoration(
                  color: Color(themeColor),
                  shape: BoxShape.circle,
                ),
              ),
              startThumbIcon: const DecoratedBox(
                decoration: BoxDecoration(
                  color: Color(themeColor),
                  shape: BoxShape.circle,
                ),
              ),
              labelFormatterCallback: (dynamic value, String formattedText) {
                return value >= SearchHelper.maxPrice
                    ? '${SearchHelper.maxPrice}+ SAR'
                    : '${value.toStringAsFixed(1)} SAR';
              },
              onChanged: (SfRangeValues newValues) {
                controller.priceRange.value = newValues;
              },
              onChangeEnd: (SfRangeValues newValues) {
                controller.minController.text =
                newValues.start >= SearchHelper.maxPrice
                    ? '${SearchHelper.maxPrice}+'
                    : newValues.start.toStringAsFixed(1);
                controller.maxController.text =
                newValues.end >= SearchHelper.maxPrice
                    ? '${SearchHelper.maxPrice}+'
                    : newValues.end.toStringAsFixed(1);
                SearchHelper.filterCleared = false;
                SearchHelper.c.getResultCount();
              },
            ),
          )),
          SizedBox(height: heightSpace(4.5)),
          Row(
            children: [
              _priceRangeField(
                translation.search.minimum,
                controller.minController,
                    (val) {
                  final min = double.tryParse(val.replaceAll('+', ''));
                  final max = double.tryParse(
                      controller.maxController.text.replaceAll('+', ''));
                  if (min != null && max != null) {
                    controller.rangeError.value =
                    min > max ? translation.filter.minError : null;
                  }
                },
              ),
              SizedBox(width: widthSpace(3)),
              _priceRangeField(
                translation.search.maximum,
                controller.maxController,
                    (val) {
                  final max = double.tryParse(val.replaceAll('+', ''));
                  final min = double.tryParse(
                      controller.minController.text.replaceAll('+', ''));
                  if (min != null && max != null) {
                    controller.rangeError.value =
                    min > max ? translation.filter.maxError : null;
                  }
                },
              ),
            ],
          ),
          Obx(() => controller.rangeError.value != null
              ? Padding(
            padding: EdgeInsets.only(top: heightSpace(1)),
            child: CustomText(
              controller.rangeError.value!,
              color: const Color(warningColor),
              size: 1.8,
            ),
          )
              : const SizedBox.shrink()),
          Divider(height: heightSpace(4)),
          CustomText(translation.filter.searchProperty),
          SizedBox(height: heightSpace(3)),
          Obx(()=>HostListTile(
              title: translation.property.city,
              subtitle: Get.locale?.languageCode == 'en'
                  ? controller.selectedCity.value?.name
                  : controller.selectedCity.value?.nameAr ??
                  translation.filter.nothingSelected,
              screen: _showCountries,
              borderColor: const Color(greyBorder),
            ),
          ),
          Obx(() => controller.districts.isNotEmpty
              ? HostListTile(
            title: translation.listingLocation.district,
            subtitle: controller.selectedDistricts.isEmpty
                ? translation.filter.nothingSelected
                : Get.locale?.languageCode == 'en'
                ? controller.selectedDistricts
                .map((e) => e?.name)
                .join(',')
                : controller.selectedDistricts
                .map((e) => e?.nameAr)
                .join(','),
            screen: _showDistricts,
            borderColor: const Color(greyBorder),
          )
              : const SizedBox.shrink()),
          CustomTextField(
            hint: translation.home.unitCode,
            controller: controller.unitCode,
            textCapitalization: TextCapitalization.characters,
            onChanged: (val){
              //here will check if controller.totalAvailablePages.value is null then simply assign value -1
              //so that button of show result enable
              if(controller.totalAvailablePages.value == null){
                controller.totalAvailablePages.value = -1;
              }
              //and in case value is -1 then it means we have assign fake value to enable button but now text
              //field is empty so again changing it to null so that button disabled
              else if(controller.totalAvailablePages.value == -1 && val.trim().isEmpty){
                controller.totalAvailablePages.value = null;
              }
            },
          ),
          Divider(height: heightSpace(4)),
          CustomText(
            translation.search.roomsAndBeds,
            size: 2.1,
            weight: FontWeight.w500,
          ),
          SizedBox(height: heightSpace(3)),
          CustomText(translation.search.bedrooms),
          SizedBox(height: heightSpace(2)),
          SizedBox(
            height: heightSpace(4),
            child: Obx(()=>GridView.count(
                scrollDirection: Axis.horizontal,
                crossAxisCount: 1,
                mainAxisSpacing: widthSpace(2),
                childAspectRatio: 0.67,
                children: List.generate(
                  noOfCounts.length,
                      (index) => _countContainer(
                    noOfCounts[index],
                    controller.bedRoomsNo.value == noOfCounts[index],
                        () {
                      controller.bedRoomsNo.value = noOfCounts[index];
                      SearchHelper.filterCleared = false;
                      SearchHelper.c.getResultCount();
                    },
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: heightSpace(3)),
          CustomText(translation.search.bathrooms),
          SizedBox(height: heightSpace(2)),
          SizedBox(
            height: heightSpace(4),
            child: Obx(()=>GridView.count(
                scrollDirection: Axis.horizontal,
                crossAxisCount: 1,
                mainAxisSpacing: widthSpace(2),
                childAspectRatio: 0.67,
                children: noOfCounts
                    .map((item) => _countContainer(
                  item,
                  controller.bathroomsNo.value == item,
                      () {
                    controller.bathroomsNo.value = item;
                    SearchHelper.filterCleared = false;
                    SearchHelper.c.getResultCount();
                  },
                ))
                    .toList(),
              ),
            ),
          ),
          SizedBox(height: heightSpace(5.8)),
          _renderHead(translation.search.amenities),
          SizedBox(height: heightSpace(1)),
          CustomText(translation.hostDashboard.essential),
          Obx(() => GridView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(top: heightSpace(1)),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 4,
            ),
            children: List.generate(
              controller.filters.value?.amenities.length ?? 0,
                  (index) {
                final amenity = controller.filters.value!.amenities[index];
                return CommonCheckBox(
                  onPressed: () => controller.checkFilter(amenity),
                  isSelected: amenity.isChecked,
                  title: Expanded(
                    child: CustomText(
                      Get.locale?.languageCode == 'ar'
                          ? amenity.titleAr
                          : amenity.title,
                    ),
                  ),
                );
              },
            ),
          )),
          SizedBox(height: heightSpace(5.8)),
          CustomText(
            translation.search.safetyItems,
            size: 2.1,
          ),
          SizedBox(height: heightSpace(0.5)),
          Obx(() => GridView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(top: heightSpace(1)),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 4,
            ),
            children: List.generate(
              controller.filters.value?.safetyAmenities.length ?? 0,
                  (index) {
                final safety = controller.filters.value!.safetyAmenities[index];
                return CommonCheckBox(
                  onPressed: () => controller.checkFilter(safety),
                  isSelected: safety.isChecked,
                  title: Get.locale?.languageCode == 'ar'
                      ? safety.titleAr
                      : safety.title,
                );
              },
            ),
          )),
          SizedBox(height: heightSpace(5.8)),
          CustomText(
            translation.payment.houseRule,
            size: 2.1,
          ),
          SizedBox(height: heightSpace(0.5)),
          Obx(() => GridView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(top: heightSpace(1)),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 4,
            ),
            children: List.generate(
              controller.filters.value?.houseRules.length ?? 0,
                  (index) {
                final rule = controller.filters.value!.houseRules[index];
                return CommonCheckBox(
                  onPressed: () => controller.checkFilter(rule),
                  isSelected: rule.isChecked,
                  title: Get.locale?.languageCode == 'ar'
                      ? rule.titleAr
                      : rule.title,
                );
              },
            ),
          )),
          if (kDebugMode) ...[
            SizedBox(height: heightSpace(5.8)),
            CustomText(
              translation.listingBook.bookingType,
              size: 2.1,
            ),
            SizedBox(height: heightSpace(3)),
            Obx(() => Row(
              children: [
                Expanded(
                  child: CommonCheckBox(
                    onPressed: () => controller.selectBookingType('requestToBook'),
                    isSelected: controller.selectedBookingType.value == 'requestToBook',
                    title: translation.propertySingle.requestBook,
                  ),
                ),
                Expanded(
                  child: CommonCheckBox(
                    onPressed: () => controller.selectBookingType('instantBook'),
                    isSelected: controller.selectedBookingType.value == 'instantBook',
                    title: translation.propertySingle.instantBook,
                  ),
                ),
              ],
            )),
          ],
          SizedBox(height: heightSpace(2)),
        ],
      ),
    );
  }

  Widget _buildBottomBar(translation) {
    final controller = SearchHelper.c;
    return Container(
      margin: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      padding: EdgeInsets.symmetric(
        horizontal: widthSpace(viewPadding),
        vertical: widthSpace(3),
      ),
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(color: Color(greyBorder))),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () => SearchHelper.clearFilter(applyFilter: true),
            child: CustomText(
              translation.search.clearValues,
              weight: FontWeight.w500,
              underline: true,
            ),
          ),
          Obx(() => CommonButton(
            title: controller.showResultsText.value??translation.filter.showResults,
            horizontalPadding: 7,
            isLoading: controller.dataLoading.value,
            isDisabled: controller.rangeError.value != null || controller.totalAvailablePages.value == 0 || controller.totalAvailablePages.value==null,
            onPressed: SearchHelper.applyFilter,
          )),
        ],
      ),
    );
  }

  void _showCountries() {
    ViewsCommon.showModalBottom(BottomSheet(
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(widthSpace(viewPadding)),
          height: heightSpace(85),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: Obx(() => ListView.builder(
                  itemCount: SearchHelper.c.cities.length,
                  itemBuilder: (context, i) {
                    final city = SearchHelper.c.cities[i];
                    return CheckboxListTile(
                      value: city.isChecked,
                      onChanged: (e) {
                        final containsTrue =
                        SearchHelper.c.cities.any((e) => e.isChecked);
                        if (containsTrue) {
                          for (var city in SearchHelper.c.cities) {
                            city.isChecked = false;
                          }
                        }
                        city.isChecked = !city.isChecked;
                        SearchHelper.c.selectedCity.value = city;
                        SearchHelper.c.cities.refresh();
                      },
                      title: Text(
                        Get.locale?.languageCode == 'en'
                            ? city.name
                            : city.nameAr,
                      ),
                    );
                  },
                )),
              ),
              CommonButton(
                title: Get.find<TranslationHelper>().translations.signUp.submit,
                onPressed: () {
                  SearchHelper.c.selectedDistricts.value = [];
                  SearchHelper.c.districts.clear();
                  for (var item in SearchHelper.c.cities) {
                    if (item.isChecked) {
                      SearchHelper.c.selectedCity.value = item;
                      for (var e in item.districts) {
                        e.isChecked = false;
                        SearchHelper.c.districts.add(e);
                      }
                    }
                  }
                  SearchHelper.c.districts.refresh();
                  SearchHelper.c.getResultCount();
                  Get.back();
                },
              ),
            ],
          ),
        );
      },
      onClosing: () {},
    ));
  }

  void _showDistricts() {
    ViewsCommon.showModalBottom(BottomSheet(
      builder: (BuildContext context) {
        return Container(
          padding: EdgeInsets.all(widthSpace(viewPadding)),
          height: heightSpace(85),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: Obx(() => ListView.builder(
                  itemCount: SearchHelper.c.districts.length,
                  itemBuilder: (context, i) {
                    final district = SearchHelper.c.districts[i];
                    return CheckboxListTile(
                      value: district.isChecked,
                      onChanged: (e) {
                        district.isChecked = !district.isChecked;
                        SearchHelper.c.districts.refresh();
                      },
                      title: Text(
                        Get.locale?.languageCode == 'en'
                            ? district.name
                            : district.nameAr,
                      ),
                    );
                  },
                )),
              ),
              CommonButton(
                title: Get.find<TranslationHelper>().translations.signUp.submit,
                onPressed: () {
                  SearchHelper.c.selectedDistricts.clear();
                  final containsTrue =
                  SearchHelper.c.districts.any((model) => model.isChecked);
                  if (containsTrue) {
                    SearchHelper.c.selectedDistricts.value = SearchHelper.c.districts
                        .where((e) => e.isChecked)
                        .toList();
                    SearchHelper.c.selectedDistricts.refresh();
                  }
                  SearchHelper.c.getResultCount();
                  Get.back();
                  SearchHelper.filterCleared = false;
                },
              ),
            ],
          ),
        );
      },
      onClosing: () {},
    ));
  }

  Widget _renderHead(String text, {Key? key}) {
    return CustomText(
      text,
      key: key,
      size: 2.3,
      weight: FontWeight.w500,
    );
  }

  Widget _priceRangeField(
      String value, TextEditingController controller, Function(String) onChanged) {
    Timer? debounceTimer; // Timer for debouncing

    return Expanded(
        child: Container(
          padding: EdgeInsets.all(widthSpace(3)),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.black54),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                value,
                color: const Color(greyText),
                size: 1.9,
              ),
              TextField(
                controller: controller,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9+]+')),
                ],
                decoration: const InputDecoration(
                  hintText: '0',
                  suffixText: 'SAR',
                  border: InputBorder.none,
                ),
                onChanged: (val) {
                  onChanged(val);

                  // Cancel any existing timer
                  debounceTimer?.cancel();

                  // Only proceed if the input is not empty
                  if (val.isNotEmpty) {
                    // Start a new timer
                    debounceTimer = Timer(const Duration(milliseconds: 500), () {
                      // Disable button immediately
                      SearchHelper.c.totalAvailablePages.value = null;
                      // Call API after 500ms delay if text hasn't changed again
                      SearchHelper.c.getResultCount();
                    });
                  }
                },
              ),
            ],
          ),
        )
    );
  }

  Widget _countContainer(dynamic value, bool isChecked, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: isChecked ? const Color(themeColor) : null,
          border: isChecked ? null : Border.all(width: 0.6),
          borderRadius: BorderRadius.circular(50),
        ),
        child: Center(
          child: CustomText(
            value == '0'
                ? Get.find<TranslationHelper>().translations.filter.any
                : value == '8'
                ? '+8'
                : value,
            color: isChecked ? Colors.white : Colors.black,
          ),
        ),
      ),
    );
  }
}
