import 'dart:ui' as ui;

import 'package:darent/components/custom_review_sheet.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/components/warning_dialog.dart';
import 'package:darent/controllers/checkout_controller.dart';
import 'package:darent/helperMethods/chat_helper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/analytics/analytics.dart';
import 'package:darent/helperMethods/wishlist_helper.dart';
import 'package:darent/models/homeProperty.dart';
import 'package:darent/models/productDetailModel.dart';
import 'package:darent/screens/property_single/contactHost.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:webengage_flutter/webengage_flutter.dart';
import '../helperMethods/search_helper.dart';
import '../helperMethods/translation_helper.dart';
import '../models/property_reviews.dart';
import 'package:darent/screens/authentication/login.dart';
import 'package:darent/screens/booking.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../models/propertyCheckoutPrice.dart';
import '../utils/api_constants.dart';
import '../utils/routes.dart';

class PropertyDetailController extends GetxController {
  String slug;
  final bool scrollToMap;
  PropertyDetailController({required this.slug,this.scrollToMap=false});
  late BitmapDescriptor markerImage;

  final data = Rxn<PropertyDetail>();

  final detailImageStep = 0.obs;
  final reviews = RxList<PropertyReviews>();
  String? reviewsNextPage;
  final reviewsLoading = false.obs;

  final message = RxnString();
  DateRangePickerController dateRangeController = DateRangePickerController();
  final startDate = Rx<DateTime>(DateTime.now());
  final endDate = Rx<DateTime>(DateTime.now());
  final isUnAvailable = false.obs, isGuestsOpen = false.obs;
  final formatter1 = DateFormat("dd/MMM/yy");
  final adults = 1.obs;
  final children = 0.obs;
  final isLoading = false.obs,
      isBtnLoading = false.obs,
      isLoadingCurrentPrice = false.obs;
  int nights = 1;
  final currentPhoto = 1.obs;
  final GlobalKey<FormState> msgKey = GlobalKey<FormState>();
  TextEditingController messageHost = TextEditingController();

  //for Map scrolling
  ScrollController scrollController = ScrollController();
  final GlobalKey mapKey = GlobalKey();

  bool isOnGuestMode() => !GlobalHelper.storageBox.hasData('user');

  addToWishlist() {
    if (isUser) {
      WishlistHelper.checkWishlist(HomeProperty.fromDetail(data.value));
    } else {
      Get.to(() => const Login());
    }
  }

  share() {
    if (data.value?.coverPhoto != null ||
        data.value!.propertyPhotos!.first.photo!.isNotEmpty) {
      ViewsCommon.share(
          '$baseUrl/${Get.locale?.languageCode ?? 'en'}/properties/$slug',
          title: 'Property',
          itemId: '${data.value?.id}');

      // Track property share event with Analytics Manager
      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.propertyShare,
        eventAttributes: {
          AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
          AnalyticsAttributes.propertyId: data.value?.id?.toString(),
          AnalyticsAttributes.propertyType: data.value?.propertyTypeName,
          AnalyticsAttributes.city: data.value?.propertyAddress?.city,
          AnalyticsAttributes.sessionTimestamp:
              DateTime.now().toIso8601String(),
          'share_method': 'native_share',
        },
      );

      // Snapkit().share(SnapchatMediaType.PHOTO,
      //   image: NetworkImage("$baseUrl/${data.value?.coverPhoto??data.value!.propertyPhotos!.first.photo!}"),
      //   caption: data.value?.propertyTitle,
      //   attachmentUrl:"$baseUrl/${data.value!.slug}",
      // );
    }
  }

  onCarouselChanged(int index, reason) {
    currentPhoto.value = index + 1;
  }

  // selectReserveDates({bool fromBooking = false}) {
  //   Get.dialog(Dialog(
  //       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
  //       child: Container(
  //         height: heightSpace(85),
  //         width: widthSpace(90),
  //         padding: EdgeInsets.all(widthSpace(3)),
  //         child: Column(
  //             mainAxisSize: MainAxisSize.min,
  //             crossAxisAlignment: CrossAxisAlignment.stretch,
  //             children: [
  //               CustomText("selectTheDate".tr,
  //                   size: 2.7, weight: FontWeight.w500),
  //               Divider(
  //                 height: heightSpace(6),
  //                 color: const Color(themeColor),
  //               ),
  //               Expanded(
  //                 child: SfDateRangePicker(
  //                     controller: dateRangeController,
  //                     enablePastDates: false,
  //                     selectionMode: DateRangePickerSelectionMode.range,
  //                     enableMultiView: true,
  //                     navigationDirection:
  //                         DateRangePickerNavigationDirection.vertical,
  //                     showActionButtons: true,
  //                     viewSpacing: 0,
  //                     initialSelectedRange:
  //                         PickerDateRange(startDate.value, endDate.value),
  //                     startRangeSelectionColor: Colors.transparent,
  //                     endRangeSelectionColor: Colors.transparent,
  //                     cellBuilder: (context, cellDetails) {
  //                       bool unAvailable = false, isRangeSelector = false;
  //                       if (data.value!.unavailabeDates != null) {
  //                         unAvailable = data.value!.unavailabeDates!
  //                             .contains(cellDetails.date);
  //                       }
  //                       if (cellDetails.date.isBefore(DateTime.now())) {
  //                         unAvailable = true;
  //                       }
  //                       if (!cellDetails.date.isBefore(DateTime.now())) {
  //                         if (dateRangeController.selectedRange?.startDate !=null && dateRangeController.selectedRange!.startDate!.isAtSameMomentAs(cellDetails.date)) {
  //                           isRangeSelector = true;
  //                         } else if (dateRangeController.selectedRange?.endDate !=null) {
  //                           isRangeSelector = dateRangeController
  //                               .selectedRange!.endDate!
  //                               .isAtSameMomentAs(cellDetails.date);
  //                         }
  //                       }
  //                       return Container(
  //                           alignment: Alignment.center,
  //                           decoration: isRangeSelector
  //                               ? const ShapeDecoration(
  //                                   color: Color(themeColor),
  //                                   shape: CircleBorder())
  //                               : null,
  //                           child: CustomText(cellDetails.date.day.toString(),
  //                               color: unAvailable
  //                                   ? Colors.grey[500]
  //                                   : isRangeSelector
  //                                       ? Colors.white
  //                                       : null,
  //                               size: 1.9,
  //                               strikeThrough: unAvailable));
  //                     },
  //                     selectableDayPredicate: (date) {
  //                       if (data.value!.unavailabeDates != null) {
  //                         return !(data.value!.unavailabeDates!.contains(date));
  //                       }
  //                       return true;
  //                     },
  //                     onSubmit: (result) async {
  //                       try{
  //                         if (result != null) {
  //                           result as PickerDateRange;
  //                           if (result.startDate != null && result.endDate != null) {
  //                             if (fromBooking) {
  //                               if (await getDatePrices(result.startDate!, result.endDate!)) {
  //                                 nights = result.endDate!.difference(result.startDate!).inDays;
  //                                 startDate.value = result.startDate!;
  //                                 endDate.value = result.endDate!;
  //                                 Get.back();
  //                               }
  //                             } else {
  //                               nights = result.endDate!.difference(result.startDate!).inDays;
  //                               startDate.value = result.startDate!;
  //                               endDate.value = result.endDate!;
  //                               Get.back();
  //                               isUnAvailable.value = areDatesUnavailable();
  //                             }
  //                           }
  //                         }
  //                       }catch(e){
  //                         Get.back();
  //                       }
  //                     },
  //                     onCancel: () => Get.back()),
  //               )
  //             ]),
  //       )));
  // }
  selectReserveDates({bool fromBooking = false}) async {
    if (!isLoadingCurrentPrice.value) {
      //every time we will re-fetch the property details
      //via the API to ensure the calendar reflects the most up-to-date availability,
      //including changes made by the admin, host, or other bookings.
      await getData(isForRefresh: true);
      Get.dialog(Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        insetPadding: EdgeInsets.zero,
        child: Container(
          height: heightSpace(85),
          width: widthSpace(95),
          padding: EdgeInsets.all(widthSpace(3)),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              CustomText("selectTheDate".tr,
                  size: 2.7, weight: FontWeight.w500),
              Divider(
                height: heightSpace(6),
                color: const Color(themeColor),
              ),
              Expanded(
                child: Directionality(
                  textDirection: ui.TextDirection.ltr,
                  child: SfDateRangePicker(
                    controller: dateRangeController,
                    selectionMode: DateRangePickerSelectionMode.range,
                    enableMultiView: true,
                    navigationDirection:
                        DateRangePickerNavigationDirection.vertical,
                    showActionButtons: true,
                    viewSpacing: 0,
                    startRangeSelectionColor: Colors.transparent,
                    endRangeSelectionColor: Colors.transparent,
                    initialSelectedRange:
                        PickerDateRange(startDate.value, endDate.value),
                    cellBuilder: (context, cellDetails) {
                      // bool isMonth = dateRangeController.view==DateRangePickerView.month;
                      bool unAvailable = false, isRangeSelector = false;
                      if (data.value!.unavailabeDates != null) {
                        unAvailable = data.value!.unavailabeDates!
                            .contains(cellDetails.date);
                      }
                      // altering condition so that it can display today's option
                      if (cellDetails.date.isBefore(
                          DateTime.now().subtract(const Duration(days: 1)))) {
                        unAvailable = true;
                      }
                      if (dateRangeController.selectedRange?.startDate !=
                              null &&
                          dateRangeController.selectedRange!.startDate!
                              .isAtSameMomentAs(cellDetails.date)) {
                        isRangeSelector = true;
                      } else if (dateRangeController.selectedRange?.endDate !=
                          null) {
                        isRangeSelector = dateRangeController
                            .selectedRange!.endDate!
                            .isAtSameMomentAs(cellDetails.date);
                      }
                      return Container(
                        alignment: Alignment.center,
                        decoration: isRangeSelector
                            ? const ShapeDecoration(
                                color: Color(themeColor),
                                shape: CircleBorder(),
                              )
                            : null,
                        child: CustomText(
                          '${dateRangeController.view == DateRangePickerView.month ? cellDetails.date.day : dateRangeController.view == DateRangePickerView.year ? DateFormat.MMM().format(cellDetails.date) : cellDetails.date.year}',
                          color: unAvailable
                              ? Colors.grey[500]
                              : isRangeSelector
                                  ? Colors.white
                                  : null,
                          size: 1.9,
                          strikeThrough: unAvailable,
                        ),
                      );
                    },
                    selectableDayPredicate: (date) {
                      if (data.value!.unavailabeDates != null) {
                        return !(data.value!.unavailabeDates!.contains(date));
                      }
                      return true;
                    },
                    confirmText:
                        Get.find<TranslationHelper>().translations.filter.apply,
                    cancelText: Get.find<TranslationHelper>()
                        .translations
                        .filter
                        .cancel,
                    onSubmit: (result) async {
                      try {
                        if (result != null) {
                          isLoadingCurrentPrice.value = true;
                          Get.back();
                          result as PickerDateRange;
                          DateTime startDateTemp =
                              result.startDate ?? DateTime.now();
                          DateTime? endDateTemp = result.endDate;
                          if (result.endDate == null ||
                              endDateTemp!.isAtSameMomentAs(startDateTemp)) {
                            endDateTemp =
                                startDateTemp.add(const Duration(days: 1));
                          }
                          bool available =
                              await getDatePrices(startDateTemp, endDateTemp);
                          if (!fromBooking || (fromBooking && available)) {
                            nights =
                                endDateTemp.difference(startDateTemp).inDays;
                            startDate.value = startDateTemp;
                            endDate.value = endDateTemp;
                            isUnAvailable.value = areDatesUnavailable();
                            bool isActive =
                                Get.isRegistered<CheckoutController>();
                            if (isActive) {
                              initializeController();
                              CheckoutController c = Get.find();
                              if (c.discountObject.value != null) {
                                await c.onApplyCoupon();
                                if (c.couponError.value != null) {
                                  debugPrint(
                                      "getting coupon code while coupon error object is not null");
                                  c.removeCoupon();
                                  c.couponError.value = null;
                                }
                              }
                            }
                          }
                          WebEngagePlugin.trackEvent(
                              'Arrival Date & Departure Date', {
                            "Date":
                                "${DateFormat('dd-MM-yyyy').format(startDate.value)} - ${DateFormat('dd-MM-yyyy').format(endDate.value)}",
                            "User": isHost ? "Host" : "Customer"
                          });
                          isLoadingCurrentPrice.value = false;
                        }
                      } catch (e) {
                        Get.back();
                        isLoadingCurrentPrice.value = false;
                      }
                    },
                    onCancel: Get.back,
                    minDate: DateTime.now(),
                    maxDate: DateTime.now().add(const Duration(days: 365)),
                  ),
                ),
              ),
            ],
          ),
        ),
      )).then((_) {
        dateRangeController.view = DateRangePickerView.month;
      });
    }
  }

  toggleGuests() {
    isGuestsOpen.toggle();
  }

  plusMinusAdults(sign) {
    if (sign == "+") {
      if (adults.value == data.value!.adultGuest!) {
        guestError.value = Get.find<TranslationHelper>()
            .translations
            .propertySingle
            .adultMaxLimit!;
        children.refresh();
      } else {
        adults.value++;
        guestError.value = null;
      }
    } else if (adults > 1) {
      adults.value--;
      guestError.value = null;
    }
  }

  final guestError = RxnString();
  plusMinusChildren(sign) {
    if (sign == "+") {
      if (data.value?.childrenGuest == 0) {
        guestError.value = Get.find<TranslationHelper>()
            .translations
            .propertySingle
            .childrenNotAllowed!;
        children.refresh();
      } else if (children.value == data.value!.childrenGuest) {
        guestError.value = Get.find<TranslationHelper>()
            .translations
            .propertySingle
            .childMaxLimit!;
        children.refresh();
      } else {
        children.value++;
        guestError.value = null;
      }
    } else if (children > 1) {
      children.value--;
      guestError.value = null;
    }
  }

  gotoBooking() async {
      // if(userModel.value?.userVerification?.email == "no"){
      //   await accC.sendEmailCode();
      //   Get.to(()=> const OtpVerification(isEmailOtp: true ));
      //   return;
      // }

    //local condition commenting for now
      // else
      // if (nights < (data.value?.minNights ?? 1)) {
      //   Get.dialog(WarningDialog(
      //       title: 'warning'.tr,
      //       description: 'minWarn'.trArgs([data.value!.minNights.toString()])));
      // } else if (nights > (data.value?.maxNights ?? 1)) {
      //   Get.dialog(WarningDialog(
      //       title: 'warning'.tr,
      //       description: 'maxWarn'.trArgs([data.value!.maxNights.toString()])));
      // } else {
      //   //removing check condition for now
      // }
      try{

        // Check date prices and proceed
        if (await getDatePrices()) {
          isLoadingCurrentPrice.value = true;
          Map form = {
            'propertyId': data.value?.id,
            'checkin': formatter.format(startDate.value),
            'checkout': formatter.format(endDate.value),
            'number_of_guests': adults.value + children.value,
            'booking_source': GetPlatform.isAndroid ? 'android' : 'ios',
          };

          ApiServices.postApi(ApiConstants.guestPropertyView, body: form, isAuth: true,allowDialog: false);

          if(isUser){
            final res = await ApiServices.postApi('v1/reserveProperty', body: form, isAuth: true);
            if (res.status) {

              final c = Get.find<CheckoutController>();
              c.bookingId = res.data['booking_id'];
              Get.to(() => Booking());
              placeReservationStartedEventCalled();
            }
          }else{
            showSignSignup();
          }
        }
        isLoadingCurrentPrice.value = false;
      }catch(e) {
        debugPrint(e.toString());
        isLoadingCurrentPrice.value = false;
      }
  }

  AnalyticsEventItem getEventItem() {
    int i = SearchHelper.c.data.indexWhere((item) => item.id == data.value?.id);
    return AnalyticsEventItem(
        itemId: data.value?.id.toString(),
        affiliation: '',
        discount: data.value?.discount,
        index: i + 1,
        price: (c.propertyPrice.value?.propertyPrice ??
                data.value?.propertyPrice?.price)! /
            1000000,
        quantity: c.propertyPrice.value?.dateWithPrice.length ?? 0,
        currency: data.value?.propertyPrice?.currencyCode,
        parameters: {
          'item_type': '${data.value?.propertyTypeName}',
          'item_city_name': '${data.value?.propertyAddress?.city}',
          'item_host_id': '${data.value?.hostId}',
          'total_price': c.propertyPrice.value?.totalNightPriceAfterDiscount
                  ?.toStringAsFixed(2) ??
              'Any'
        });
  }

  placeReservationStartedEventCalled() {
    // Track booking started event with Firebase Analytics (existing)
    analytics.logBeginCheckout(parameters: {
      "value": c.propertyPrice.value?.totalNightPriceAfterDiscount ??
          data.value?.propertyPrice?.price ??
          0,
      "currency": c.propertyPrice.value?.currency ?? 'SAR'
    }, items: [
      getEventItem()
    ]);

    // Track booking started event with WebEngage (existing)
    Map<String, dynamic> myMap = {
      "Cost Per Night": "${data.value?.propertyPrice?.price ?? 0.0}",
      "Date":
          "${formDateFormatCservice.format(startDate.value)} - ${formDateFormatCservice.format(endDate.value)}",
      "Number of Adults": adults.value.toString(),
      "Number of Children": children.value.toString(),
      "Service fees": data.value!.propertyPrice?.serviceFee.toString(),
      "Total": c.propertyPrice.value!.totalWithDiscount!.toStringAsFixed(2),
      "Name": data.value!.propertyCode,
      "Unit Code": data.value!.propertyCode,
    };
    if (isHost) {
      myMap['User Customer'] = "Host";
    } else {
      myMap['User Host'] = "Customer";
    }
    WebEngagePlugin.trackEvent('Place Reservation Started', myMap);

    // Track booking started event with Analytics Manager
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.startBooking,
      eventAttributes: {
        AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
        AnalyticsAttributes.propertyId: data.value?.id?.toString(),
        AnalyticsAttributes.bookingStage: 'initiated',
        AnalyticsAttributes.sessionTimestamp: DateTime.now().toIso8601String(),
        AnalyticsAttributes.price: data.value?.propertyPrice?.price?.toString(),
        AnalyticsAttributes.city: data.value?.propertyAddress?.city,
        AnalyticsAttributes.deviceLanguage: Get.locale?.languageCode ?? 'en',
      },
    );
  }

  getDatePrices([start, end]) async {
    Map formData = {
      "property_id": data.value!.id,
      "checkin": formDateFormatCservice.format(start ?? startDate.value),
      "checkout": formDateFormatCservice.format(end ?? endDate.value),
      "guest_count": adults.value + children.value
    };
    ResponseModel response =
        await ApiServices.postApi("v1/properties/get-price", body: formData);
    if (response.status) {
      if (response.data['status'] != null) {
        String message = response.data['status'] == "nights become max"
            ? 'maxWarn'.trArgs(["${response.data['max_nights']}"])
            : response.data['status'] == "nights become min"
                ? 'minWarn'.trArgs(["${response.data['min_nights']}"])
                : response.data['status'];
        ViewsCommon.showSnackbar(message,
            keyword: DialogKeyword.warning, displayTime: 2000);
        isUnAvailable.value = true;
        return !isUnAvailable.value;
      }
      c.propertyPrice.value = PropertyCheckoutPrice.fromJson(response.data);
      data.value?.propertyPrice?.perNightPrice =
          c.propertyPrice.value?.propertyPrice ?? 0;

      data.value?.discountedAmount =
          c.propertyPrice.value?.totalNightPriceAfterDiscount;
      data.value?.propertyPrice?.price = c.propertyPrice.value?.totalNightPrice;
      c.unSelectWallet();
    }
    return true;
  }

  showSignSignup() {
    Get.to(() => const Login());
  }

  onContactSeller({chatHeadId = true}) async {
    if (isUser) {
      if (chatHeadId) {
        isBtnLoading.value = true;
        int? chatHeadId = await ChatHelper.contactHost(data.value?.id ?? 0,
            startDate.value, endDate.value, adults.value, children.value,
            chatHeadId: data.value?.chatHeadId,
            inquiryMessage: messageHost.text);
        messageHost.clear();
        if (chatHeadId != null) {
          data.value?.chatHeadId = chatHeadId;
        }
        isBtnLoading.value = false;
      } else {
        Get.to(() => const ContactHost());
      }
    } else {
      showSignSignup();
    }
  }

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  setTimeGuest({bool isForRefresh = false}) {
    if(!isForRefresh){
      startDate.value = SearchHelper.c.checkin;
      endDate.value = SearchHelper.c.checkout;
    }
    dateRangeController.selectedRange =
        PickerDateRange(startDate.value, endDate.value);
    adults.value = SearchHelper.c.adults ?? 1;
    if ((SearchHelper.c.children ?? 0) > data.value!.childrenGuest!) {
      children.value = data.value!.childrenGuest!;
    } else {
      children.value = (SearchHelper.c.children ?? 0);
    }
  }

  placeSelectEventCalled() async {
    await WebEngagePlugin.trackEvent('Place Selected', {
      "Name": data.value?.propertyTitle,
      "Unit Code": data.value?.propertyCode,
      "Cost Per Night": "${data.value?.propertyPrice?.price ?? 0.0}",
      "Category Name": data.value?.spaceTypeName ?? '',
      "User": isHost ? "Host" : "Customer"
    });
  }

  String getSlugFromCurrentRoute() {
    Uri uri = Uri.parse(Get.currentRoute);
    return uri.pathSegments.last;
  }

  getData({bool isForRefresh = false}) async {
    if (slug.isEmpty) {
      slug = getSlugFromCurrentRoute();
    }
    ResponseModel response = await ApiServices.postApi(
        "${ApiConstants.propertySingle}/$slug?lang=${Get.locale?.languageCode ?? "en"}",
        isAuth: true,
        allowDialog: false);
    if (response.status) {
      markerImage = await getMarkerIcon();
      data.value = PropertyDetail.fromJson(response.data);
      setTimeGuest(isForRefresh: isForRefresh);
      message.value = null;
      if(isForRefresh){
        // To avoid unnecessary API calls—such as for reviews and pricing—during refresh,
        // we prevent multiple event triggers.
        return;
      }
      placeSelectEventCalled();
      getReviews(data.value?.id ?? 0);
      isLoadingCurrentPrice.value = true;
      if (data.value?.hostId != userModel.value?.id) {
        initializeController(callEvent: true);
      }
      int? index = data.value?.propertyPhotos
          ?.indexWhere((index) => index.coverPhoto == 1);
      if (index != -1) {
        var removedPhoto = data.value?.propertyPhotos?.removeAt(index!);
        data.value?.propertyPhotos?.insert(0, removedPhoto!);
      }
      isUnAvailable.value = areDatesUnavailable();
      if(scrollToMap)Future.delayed(Duration(milliseconds: 500),(){
        GlobalHelper.scrollToTarget(scrollController, mapKey);
      });
    } else {
      message.value = Get.find<TranslationHelper>()
              .translations
              .propertySingle
              .weApologizeItSeemsThatTheProperty ??
          '';
    }
  }

  propertyViewEvent() {
    if (Get.previousRoute == Routes.home) {
      analytics.logSelectItem(items: [getEventItem()]);
    }
    analytics.logViewItem(parameters: {
      "value": c.propertyPrice.value?.totalNightPriceAfterDiscount ??
          data.value?.propertyPrice?.price ??
          0,
      "currency": c.propertyPrice.value?.currency ?? 'SAR'
    }, items: [
      getEventItem()
    ]);

    // Track property viewed event with Analytics Manager (general event)
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.viewPropertyDetails,
      eventAttributes: {
        AnalyticsAttributes.userId: userModel.value?.id?.toString(),
        AnalyticsAttributes.propertyId: data.value?.id?.toString(),
        AnalyticsAttributes.propertyType: data.value?.propertyTypeName,
        AnalyticsAttributes.city: data.value?.propertyAddress?.city,
        AnalyticsAttributes.priceRange:
            data.value?.propertyPrice?.price?.toString(),
        AnalyticsAttributes.timestamp: DateTime.now().toIso8601String(),
      },
    );

   
  }

  late CheckoutController c;

  initializeController({callEvent = false}) async {
    c = Get.put(CheckoutController(this));
    await getDatePrices().then((value) {
      if (value) {
        // double calculatedPrice = double.parse(
        //     (c.propertyPrice.value!.totalDiscount ??
        //             c.propertyPrice.value!.totalWithDiscount)!
        //         .toStringAsFixed(2));
        // data.value?.propertyPrice?.price = calculatedPrice;
        // data.refresh();
      }
      isLoadingCurrentPrice.value = false;
      if (callEvent) {
        propertyViewEvent();
      }
    });
  }

  TextEditingController reviewSearchC = TextEditingController();
  FocusNode reviewSearchFocus = FocusNode();
  final isReviewSortingOpen = RxBool(false);
  String selectedSortingType = "recent";
  clearFiltersAndGetData(
      {bool getAPIData = false, bool redirectToSheet = false}) {
    //clearing search field and sorting
    reviewSearchC.clear();
    selectedSortingType = "recent";
    if (redirectToSheet) {
      showReviewsSheet();
    }
    if (getAPIData) {
      reviews.clear();
      reviews.refresh();
      reviewsNextPage = null;
      reviewsLoading.value ? null : getReviews(data.value?.id ?? 0);
    }
  }

  List sortByList = [
    {"title_en": "Most recent", "title_ar": "الأحدث", "slug": "recent"},
    {
      "title_en": "Highest Rated First",
      "title_ar": "الأعلى تقييمًا أولاً",
      "slug": "rated-desc"
    },
    {
      "title_en": "Lowest Rated First",
      "title_ar": "أدنى تصنيف أولا",
      "slug": "rated-asc"
    },
  ];
  setSortingType(value) {
    selectedSortingType = value;
    update();
  }

  toggleReviewSortingOpen() {
    isReviewSortingOpen.value = !isReviewSortingOpen.value;
    update();
  }

  getReviews(id, {bool isForFilerReviews = false}) async {
    // String? url = reviewsNextPage??"v2/guest/properties/$id/reviews";
    String? myUrl = isForFilerReviews
        ? "v1/searchReviews"
        : reviewsNextPage ?? "v1/searchReviews";
    Map formData = {
      "propertyId": id,
      "perPage": "2",
      "reviewer": "guest",
      'filterBy': selectedSortingType,
    };
    if (reviewSearchC.text.isNotEmpty && isForFilerReviews) {
      formData['searchText'] = reviewSearchC.text;
    }
    bool isPaginate = reviewsNextPage != null;
    if (isPaginate || isForFilerReviews) {
      reviewsLoading.value = true;
    }
    try {
      final response = await ApiServices.postApi(myUrl,
          fullUrl: isPaginate && !isForFilerReviews, body: formData);
      // final response = await ApiServices.getApi(url,fullUrl: isPaginate,showDialog:false);
      if (response.status) {
        reviewsNextPage = response.data['reviews']['next_page_url'];
        if (isPaginate) {
          reviews.addAll(response.data['reviews']['data']
              .map<PropertyReviews>((item) => PropertyReviews.fromJson(item)));
        } else {
          reviews.value = response.data['reviews']['data']
              .map<PropertyReviews>((item) => PropertyReviews.fromJson(item))
              .toList();
        }
      }
      reviewsLoading.value = false;
    } catch (e) {
      reviewsLoading.value = false;
    }
    reviews.refresh();
  }

  Future<Uint8List> getBytesFromAsset(String path, int width) async {
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: width);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  getMarkerIcon() async {
    return BitmapDescriptor.bytes(
        await getBytesFromAsset("assets/icons/home_marker.png", 70));
  }

  bool areDatesUnavailable() {
    for (DateTime date in data.value!.unavailabeDates!) {
      // if(kDebugMode){
      //   print("Checking unavailable date: $date");
      //   print("Desired booking period: ${startDate.value} to ${endDate.value}, ${date.isAtSameMomentAs(startDate.value)}, ${date.isAtSameMomentAs(endDate.value)},");
      // }
      if (date.isAtSameMomentAs(startDate.value)) {
        // if(kDebugMode){
        // print("Date $date is within the booking period. Booking not possible.");
        // }
        return true;

      }
    }
    // if(kDebugMode) {
    // print("No conflicts found. Booking is possible.");
    // }
    // removing this condition as it is giving us issues
    //   if(GlobalHelper.isBetweenDates(date, startDate.value, endDate.value)){
    //     return true;
    //   }
    // }
    return false;
  }

  @override
  void onClose() {
    Get.delete<CheckoutController>();
    super.onClose();
  }
}
