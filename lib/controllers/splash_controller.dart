import 'dart:async';
import 'package:darent/helperMethods/deepLink_helper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:darent/helperMethods/update_helper.dart';
import 'package:darent/screens/authentication/promotionBanner.dart';
import 'package:darent/screens/property_single/property_details.dart';
import 'package:darent/utils/routes.dart';
import 'package:get/get.dart';

import '../screens/authentication/force_update.dart';

class SplashController extends GetxController {
  final isLoading = false.obs;
  final splashStep = 0.obs;

  @override
  void onInit() {
    super.onInit();
    //clearing badge number
    SearchHelper.c.setApplicationBadgeCount(iResetToNull: true);
    updateRoute();
  }

  updateRoute() {
    DeepLinkHelper();
    Future.delayed(const Duration(milliseconds: 4500), () async {
      // AccountController c = Get.find();
      await DeepLinkHelper.retrieveDynamicLink();
      if (!Get.currentRoute.contains(Routes.propertySingle)) {
        if (DeepLinkHelper.deepLink?.pathSegments
                .contains(Routes.propertySingle) ==
            true) {
          // Get.to(()=>DeepLinkHelper.deepLink);
          Get.to(() => PropertyDetailScreen(
              slug: DeepLinkHelper.deepLink!.pathSegments.last));
        } else {
          if (!GlobalHelper.storageBox.hasData('notFirstTime')) {
            Get.to(() => PromotionBanner());
          } else if (UpdateHelper.updateApp) {
            Get.to(() => ForceUpdate());
          } else {
            checkUserNavigate();
          }
        }
      }
    });
    // checkUpdate().then((canUpdate){
    //   if(!canUpdate){

    //   }
    // });
  }

  checkUserNavigate() {
    //c.userModel.value?.userVerification?.email == "no"
    // if(storageBox.hasData('user')){
    //   final lang = storageBox.read('user')['lang']??'en';
    //   Get.updateLocale(Locale(lang));
    //   ApiServices.postApi("v1/set-language",body: {"lang":lang},isAuth:true);
    // }
    if (GlobalHelper.storageBox.read('isHost') ?? false) {
      if(!Get.currentRoute.contains(Routes.hostHome)){
        Get.offAllNamed(Routes.hostHome);
      }
    } else {
      if (false) {
        // Get.to(() => const OtpVerification(isEmailOtp: true));
        // c.secondsRemaining.value = 60;
        // c.enableResend.value = false;
        // c.startResendTimer();
      } else {
        Get.offAllNamed(Routes.home);
      }
    }
  }
  // Future<bool> checkUpdate()async{
  //   if(baseUrl.contains(".dev")){
  //     return false;
  //   }
  //   try{
  //     final newVersionPlus = NewVersionPlus();
  //     final status = await newVersionPlus.getVersionStatus();
  //     if(status!=null && status.canUpdate){
  //       newVersionPlus.showUpdateDialog(
  //         context: Get.context!,
  //         versionStatus: status,
  //         launchModeVersion: LaunchModeVersion.external,
  //         dialogTitle: 'Update App!',
  //         dialogText: "New version of this app is available on store. Update now.", allowDismissal: false,
  //         updateButtonText: 'Update Now',
  //         dismissButtonText: 'Ignore',
  //       );
  //       return status.canUpdate;
  //     }
  //     return false;
  //   }catch(e){
  //     print(e);
  //     return false;
  //   }
  // }
}
