import 'package:darent/analytics/analytics.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../helperMethods/translation_helper.dart';

class HostCalendarController extends GetxController {
  final int id;
  HostCalendarController({required this.id});
  DateRangePickerController calendarC = DateRangePickerController();
  DateTime current = DateTime(DateTime.now().year, DateTime.now().month);
  DateTime lastDate = DateTime.now();
  bool isLoading = true;
  final isBtnLoading = false.obs;
  int perNight = 0;
  Map<String, dynamic> specialDays = {};
  Map<String, dynamic> customPrices = {};
  List<ReservationsModel> reservations = [];

  final monthView = true.obs;
  bool monthViewD = true;

  final calendarStart = DateTime.now().obs;
  final calendarEnd = DateTime.now().obs;
  TextEditingController customPrice = TextEditingController();
  TextEditingController customDiscount = TextEditingController();
  final availability = true.obs;
  final discountType = 'calendar'.obs;

  submitAvailability() async {
    List<String> between = [];
    for (int i = 0;
        i <= calendarEnd.value.difference(calendarStart.value).inDays;
        i++) {
      final tempDate = calendarStart.value.add(Duration(days: i));
      final reservation = reservations.firstWhereOrNull((item) =>
          GlobalHelper.isBetweenDates(tempDate, item.start, item.end));
      if (reservation == null) {
        between.add(formDateFormat.format(tempDate));
      }
    }
    if (between.isEmpty) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.listing.datesReserve!,
          keyword: DialogKeyword.warning);
      return;
    }
    isBtnLoading.value = true;
    Map form = {
      'available': availability.value ? 1 : 0,
      'dates': between,
      'type': discountType.value
    };
    if (discountType.value == 'calendar') {
      form['price'] = customPrice.text.isEmpty ? 0 : customPrice.text;
    }
    if (customDiscount.text.trim().isNotEmpty) {
      form['discount'] = customDiscount.text;
    }
    final response = await ApiServices.postApi(
        "v1/properties/$id/calendar/save",
        body: form,
        isAuth: true);
    if (response.status) {
      // Track host calendar updated event with Analytics Manager
      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.hostCalendarUpdated,
        eventAttributes: {
          AnalyticsAttributes.hostId: userModel.value?.id?.toString(),
          AnalyticsAttributes.propertyId: id.toString(),
          AnalyticsAttributes.sessionTimestamp:
              DateTime.now().toIso8601String(),
          'availability_status':
              availability.value ? 'available' : 'unavailable',
          'dates_count': between.length,
          'discount_type': discountType.value,
          'has_custom_price': customPrice.text.isNotEmpty,
          'has_discount': customDiscount.text.trim().isNotEmpty,
        },
      );

      getData();
    }
    Get.back();
    isBtnLoading.value = false;
  }

  // submitYear(String sign){
  //   if(sign=="-"){
  //     current = current.subtract(const Duration(days: 365));
  //   }else{
  //     current = current.add(const Duration(days: 365));
  //   }
  //   getData();
  // }
  // minusMonth(){
  //   current = DateTime(current.year,current.month-1);
  //   getData();
  // }
  // plusMonth(){
  //   current = DateTime(current.year,current.month+1);
  //   getData();
  // }
  submitMonthView() {
    if (monthViewD != monthView.value) {
      monthViewD = monthView.value;
      // current = now;
      getData();
      Get.back();
    }
  }

  @override
  void onInit() {
    getData();
    calendarC.addPropertyChangedListener((value) {
      if (value == 'displayDate' && calendarC.displayDate != null) {
        if (DateTime(calendarC.displayDate!.year, calendarC.displayDate!.month)
                .isAfter(current) ??
            false) {
          current = DateTime(
              calendarC.displayDate!.year, calendarC.displayDate!.month);
          getData(paginate: true);
        }
      }
    });
    super.onInit();
  }

  String comboQString = '';
  getString() {
    int last = current.month;
    // if(paginate){
    //   last+=6;
    // }
    comboQString = '';
    if (current.year == DateTime.now().year) {
      for (int m = DateTime.now().month; m <= current.month; m++) {
        comboQString +=
            "combos[]=${current.year}-${GlobalHelper.twoNumberFormat(m)}&";
      }
    } else {
      for (int y = DateTime.now().year; y <= current.year; y++) {
        int firstM = y == DateTime.now().year ? DateTime.now().month : 1;
        for (int m = firstM;
            m <= (y == current.year ? current.month : 12);
            m++) {
          comboQString +=
              "combos[]=${GlobalHelper.twoNumberFormat(y)}-${GlobalHelper.twoNumberFormat(m)}&";
        }
      }
    }
    return comboQString;
    // for(int m = current.month;m<=last; m++){
    //   current = DateTime(current.year,m);
    //   comboQString+="combos[]=${current.year}-${GlobalHelper.twoNumberFormat(current.month)}&";
    // }
  }

  getData({paginate = false}) async {
    isLoading = true;
    // String comboString = '';
    // if(!paginate){
    //   current = now;
    // }
    String url = "v1/properties/$id/calendar?";
    if (!paginate &&
        current.isAfter(DateTime(DateTime.now().year, DateTime.now().month))) {
      url += getString();
    } else {
      url +=
          'combos[]=${current.year}-${GlobalHelper.twoNumberFormat(current.month)}';
    }
    print('calendar url: $url');
    // if(monthViewD){
    //   url+="combos[]=${current.year}-${GlobalHelper.twoNumberFormat(current.month)}";
    // }else{
    //   url+="year=${current.year}";
    // }
    update(['calendar']);
    final response = await ApiServices.getApi(url);
    if (response.status) {
      perNight = response.data['price_obj']['per_night'];
      specialDays = response.data['price_obj']['special_prices'];
      if (paginate) {
        if (response.data['price_obj']['custom_prices'] is Map) {
          customPrices.addAll(response.data['price_obj']['custom_prices']);
        }
        reservations.addAll(response.data['reservations']
            .map<ReservationsModel>(
                (item) => ReservationsModel.fromJson(item)));
      } else {
        customPrices = response.data['price_obj']['custom_prices'] is Map
            ? response.data['price_obj']['custom_prices']
            : {};
        reservations = response.data['reservations']
            .map<ReservationsModel>((item) => ReservationsModel.fromJson(item))
            .toList();
      }
      if (reservations.isNotEmpty) {
        lastDate = reservations.first.end;
        lastDate = reservations.first.start;
        for (var reservation in reservations) {
          if (reservation.end.isAfter(lastDate)) {
            lastDate = reservation.end;
          }
          // if(reservation.start.isBefore(firstDate)){
          //   firstDate = reservation.start;
          // }
        }
      } else {
        lastDate = DateTime(current.year, current.month + 1, 0);
      }
    }
    isLoading = false;
    update(['calendar']);
  }
}

class ReservationsModel {
  int id;
  String code;
  String guestName;
  String guestImage;
  String guests;
  DateTime start;
  DateTime end;
  double total;
  ReservationsModel(this.id, this.code, this.guestName, this.guestImage,
      this.guests, this.start, this.end, this.total);
  factory ReservationsModel.fromJson(Map json) {
    return ReservationsModel(
        json['id'],
        json['code'],
        json['guest_name'],
        json['guest_image'],
        json['guests'],
        formDateFormat.parse(json['start']),
        formDateFormat.parse(json['end']),
        json['total'] is int ? json['total'].toDouble() : (json['total'] ?? 0));
  }
}
