import 'dart:async';
import 'dart:convert';
import 'dart:ffi';
import 'dart:io';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:custom_info_window/custom_info_window.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/components/yaqeen_verification_dialog.dart';
import 'package:darent/controllers/bookings_controller.dart';
import 'package:darent/controllers/chat_controller.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/controllers/reservation_detail_controller.dart';
import 'package:darent/controllers/splash_controller.dart';
import 'package:darent/helperMethods/authHelper.dart';
import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/product_item_info.dart';
import 'package:darent/components/warning_dialog.dart';
import 'package:darent/helperMethods/chat_helper.dart';
import 'package:darent/helperMethods/deepLink_helper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/analytics/analytics.dart';
import 'package:darent/helperMethods/payment_helper.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/helperMethods/wishlist_helper.dart';
import 'package:darent/models/listing_prefs_model.dart';
import 'package:darent/models/receiptModel.dart';
import 'package:darent/models/filterModel.dart';
import 'package:darent/models/homeProperty.dart';
import 'package:darent/models/inobxModel.dart';
import 'package:darent/models/promoDataModel.dart';
import 'package:darent/models/reservationHistory.dart';
import 'package:darent/models/reservationModel.dart';
import 'package:darent/models/reservation_detail_model.dart';
import 'package:darent/models/wishlist/wishlist_group_model.dart';
import 'package:darent/screens/myfatoorah_screen.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/routes.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:label_marker/label_marker.dart';
import 'package:moyasar/moyasar.dart';
import 'package:myfatoorah_flutter/myfatoorah_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';
import 'package:tabby_flutter_inapp_sdk/tabby_flutter_inapp_sdk.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webengage_flutter/webengage_flutter.dart';
import '../components/common_switch.dart';
import '../components/custom_textfield.dart';
import '../helperMethods/listing_helper/listing_helper.dart';
import '../helperMethods/remote_config.dart';
import '../models/attibutes.dart';
import 'package:http/http.dart' as http;

import '../models/host/dwelling_detail.dart';
import '../models/propertyCheckoutPrice.dart';
import '../screens/pdf_reciept_viewer.dart';
import '../utils/api_constants.dart';

class DashboardController extends FullLifeCycleController
    with GetTickerProviderStateMixin {
  int index = 0;
  CarouselSliderController sliderBtn = CarouselSliderController();
  final bannerIndex = RxnString();
  RxList banners = [].obs;
  bool isLoading = true, userDataLoading = false;
  final isButtonDisabled = true.obs, dataLoading = false.obs;
  final lazyLoader = false.obs;
  final isBtnLoading = false.obs;

  TextEditingController wishlistNameController = TextEditingController();
  var characterCount = 0.obs;

  void updateCharacterCount() {
    characterCount.value = wishlistNameController.value.text.length;
  }

  int dataPage = 1, dataSize = 8, dataTotalPages = 1;
  final data = RxList<HomeProperty>();
  String? locationText;
  final searcText = RxnString();

  final discountType = RxnString();
  TextEditingController couponController = TextEditingController();
  final couponError = RxnString();
  final discountObject = Rxn<PromoDataModel>();

  final listView = true.obs;
  LatLng mapCenter = const LatLng(21.4858, 39.1925);
  Set<Marker> markers = {};
  Map? selectedProperty;
  final showResultsText = RxnString();
  final totalAvailablePages = RxnInt();
  CustomInfoWindowController customInfoWindow = CustomInfoWindowController();

  ScrollController scrollController = ScrollController();

  TextEditingController ratingController = TextEditingController();
  TextEditingController ratingMessage = TextEditingController();
  TextEditingController ratingMessageDarent = TextEditingController();

  final ratingImages = <String>[].obs;
  double rating = 0.0;
  final ratingCleanliness = RxnString(),
      ratingLocation = RxnString(),
      ratingCommunication = RxnString(),
      ratingStates = RxnString(),
      ratingService = RxnString(),
      ratingRecommend = RxnString(),
      ratingStep = 1.obs;

  // String bookingTab = "coming";
  bool isCalenderView = false;
  late TabController reservationTabController;
  List<ReservationModel> reservationComing = [];
  final reservationHistory = RxList<ReservationModel>();
  List<ReservationModel> cancelledReservation = [];
  List<ReservationModel> expiredReservation = [];
  List<ReservationModel> onGoing = [];

  // Map<String, dynamic> userMap={};

  // assignUserValues(){
  //   userMap={
  //     "First Name" : userModel.value!.first_name,
  //     "Last Name" : userModel.value!.last_name,
  //     "Gender" : userModel.value!.gender,
  //     "Email" : userModel.value!.email,
  //     "Phone Number" : userModel.value!.phone,
  //     "Current Reservations": onGoing.length,
  //     "Total Price" : "0",
  //     "Past Reservations" : reservationHistory.length,
  //     "Cancelled Reservations" : cancelledReservation.length,
  //     "User" : isHost ? "Host" : "Customer",
  //   };
  // }

// receipt
  //List<ReceiptModel> receiptData = [];
  final receiptData = Rxn<ReceiptModel>();
  List datesPrices = [];

  int upcomingPage = 1;
  int upcomingLastPage = 1;

  int historyPage = 1;
  int historyLastPage = 1;

  int cancelledPage = 1;
  int cancelledLastPage = 1;

  int expiredPage = 1;
  int expiredLastPage = 1;

  int ongoingPage = 1;
  int ongoingLastPage = 1;
  bool reservationLazyLoader = false;

  final DateRangePickerController rangeController = DateRangePickerController();

  String inboxTab = "inbox";

  final filters = Rxn<FilterModel>();
  final selectedPropertyType = RxnInt();
  int? backupPropertyType;
  final districts = RxList<District>();
  late List cityData = [];
  final cities = RxList<CityBasedDistricModel>();
  final selectedCity = Rxn<CityBasedDistricModel>();
  final selectedDistricts = RxList<District?>();
  TextEditingController unitCode = TextEditingController();
  final selectedPlaceType = RxnString();
  final priceRange = const SfRangeValues(0.0, 999999.0).obs;
  final TextEditingController minController = TextEditingController();
  final TextEditingController maxController = TextEditingController();
  final rangeError = RxnString();

  final bedRoomsNo = "0".obs;
  final bedsNo = "0".obs;
  final bathroomsNo = "0".obs;
  final guestsNo = 1.obs;

  final selectedBookingType = RxnString();
  // Map filterFormData = {
  //   "min_price": "5",
  //   "max_price": "999999",
  //   // "amenities": "",
  //   // "property_type": null,
  //   // "book_type" : null,
  //   // "space_type": null,
  //   // "beds": 1,
  //   // "bathrooms": 1,
  //   // "bedrooms": 1,
  //   // "checkin": null,
  //   // "checkout": null,
  //   // "guest": "1",
  //   // "map_details":
  //   //     "10~((21.126932292735926, 38.965225136914064), (21.843807066571713, 39.41978446308594))~21.126932292735926~38.965225136914064~21.843807066571713~39.41978446308594~21.48536967965382~39.1925048"
  // };

  final wishlistGroups = RxList<WishlistGroupModel>();

  // List<InboxModel> inboxList = [];
  int inboxPage = 1, inboxLastPage = 1;

  final notifyList = <NotifyModel>[].obs;
  int? loadingNotify;
  int notifyPage = 1, notifyLastPage = 1;
  // bool isInboxLoading = false;

  // final paymentMethods = RxList<MFPaymentMethod>();
  final paymentMethodId = RxnInt();
  final isEnableWalletPay = RxBool(false);
  final paymentDeductFromWallet = RxDouble(0.0);
  final totalAfterWallet = RxDouble(0.0);

  final darentRequirements = false.obs,
      profileImage = false.obs,
      generalLoader = false.obs;
  final wishlistLoading = RxnString();

  final priceLoading = false.obs;
  final priceMessage = RxnString();

  changeIndex(int i) {
    if (index == 0 && i == 0) {
      if (scrollController.hasClients) {
        scrollController.animateTo(0.0,
            duration: const Duration(milliseconds: 200), curve: Curves.easeIn);
      }
      return;
    }

    menuSelectedEventCalled(menuNames[i]);
    index = i;
    update();
  }

  enableWalletPay(ReservationDetailModel c) async {
    if (paymentMethodId.value != 100) {
      isEnableWalletPay.value = !isEnableWalletPay.value;
      if (isEnableWalletPay.value) {
        bool isAllFromWallet = await calculateAmountNeedToDeductFromWallet(c);
        if(isAllFromWallet){
          //first of all assigning null to select payment method incase of all the payment is done from wallet
          //so that unselect any payment method as per requirement
          paymentMethodId.value = PaymentHelper.walletPaymentMethod.id;
        }
      } else {
        paymentDeductFromWallet.value = 0;
      }
    }
  }

  unSelectWalletPay() {
    isEnableWalletPay.value = false;
    paymentDeductFromWallet.value = 0;
  }

  Future<bool> calculateAmountNeedToDeductFromWallet([ReservationDetailModel? data]) async{
    double subTotal =
        discountObject.value?.amountAfterDiscount ?? data?.total ?? 0;
    if (AuthHelper.c.myWallet.first.balance! >= subTotal) {
      paymentDeductFromWallet.value = subTotal;
      totalAfterWallet.value = 0;
      return true;
    } else {
      paymentDeductFromWallet.value = AuthHelper.c.myWallet.first.balance ?? 0;
      double remaining = subTotal - paymentDeductFromWallet.value;
      totalAfterWallet.value = remaining;
      return false;
    }
  }

  menuSelectedEventCalled(String menuName) async {
    await WebEngagePlugin.trackEvent('Menu Selected', {'Menu Name': menuName});
  }

  toggleCalendarView() {
    isCalenderView = !isCalenderView;
    update(['bookings']);
  }

  setInboxTab(val) {
    if (inboxTab != val) {
      inboxTab = val;
      update(['inbox']);
    }
  }

  final finalCount = RxnInt();
  setApplicationBadgeCount(
      {bool isForIncrement = true,
      bool iResetToNull = false,
      bool forceIncreaseValue = false}) async {
    // try{
    //   final count = await FlutterDynamicIcon.getApplicationIconBadgeNumber();
    //   if(iResetToNull){
    //     finalCount.value = null;
    //   }
    //   if(forceIncreaseValue){
    //     finalCount.value = count+1;
    //   }
    //   if(isForIncrement && !iResetToNull && !forceIncreaseValue){
    //     finalCount.value = count+1;
    //   }
    //   if(!isForIncrement && !iResetToNull && !forceIncreaseValue) {
    //     finalCount.value = count-1;
    //   }
    //   await FlutterDynamicIcon.setApplicationIconBadgeNumber(finalCount.value??0);
    // }catch(e){
    //   debugPrint("getting error while getting count ${e.toString()}");
    // }
  }

  toggleHomeView() {
    listView.value = !listView.value;
  }

  selectBookingType(val) {
    selectedBookingType.value = val;
    SearchHelper.c.getResultCount();
  }

  checkPropertyType(id, {toApplyFilter = false}) {
    selectedPropertyType.value = id;
    SearchHelper.filterCleared = false;
    if (toApplyFilter) {
      dataPage = 1;
      dataSize = 8;
      SearchHelper.applyFilter(toApplyFilter: toApplyFilter);
    }
    filters.refresh();
    getResultCount();
  }

  checkFilter(value, {isRecommended = false}) {
    value.isChecked = !value.isChecked;
    // if (toApplyFilter) {
    //   dataPage = 1;
    //   dataSize = 8;
    //   SearchHelper.filterCleared = false;
    //   SearchHelper.applyFilter(toApplyFilter: toApplyFilter);
    // }
    if (isRecommended) {
      final amenity =
          filters.value?.amenities.firstWhereOrNull((e) => e.id == value.id);
      if (amenity != null) {
        amenity.isChecked = value.isChecked;
      }
      final safetyAmenity = filters.value?.safetyAmenities
          .firstWhereOrNull((e) => e.id == value.id);
      if (safetyAmenity != null) {
        safetyAmenity.isChecked = value.isChecked;
      }
    } else {
      final recommendedAmenity = filters.value?.recommendedAmenities
          .firstWhereOrNull((e) => e.id == value.id);
      if (recommendedAmenity != null) {
        recommendedAmenity.isChecked = value.isChecked;
      }
    }
    filters.refresh();
    SearchHelper.filterCleared = false;
    getResultCount();
  }

  destinationSelectedEventCalled() async {
    await WebEngagePlugin.trackEvent('Destination Selected', {
      'Destination Name': (Get.locale?.languageCode ?? 'en') == 'en'
          ? selectedCity.value!.name
          : selectedCity.value!.nameAr
    });
  }

  showContactHost(
      chatHeadId, phone, isAccepted, Map<String, dynamic> webEngageData,
      {bool isMasked = false}) {
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        maxChildSize: .35,
        initialChildSize: .35,
        expand: false,
        builder: (context, scrollController) {
          return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Align(
                  alignment: Alignment.center,
                  child: Container(
                    width: 26,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                AppBar(
                  automaticallyImplyLeading: false,
                  elevation: 1,
                  title: Text(Get.find<TranslationHelper>()
                      .translations
                      .propertySingle
                      .chatWithTheHost!),
                ),
                InkWell(
                  onTap: () => ChatHelper.gotoChats(chatHeadId,
                      webEngageData: webEngageData),
                  child: Row(children: [
                    SizedBox(width: widthSpace(2)),
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .reservation
                            .throughChat,
                        size: 1.9,
                        weight: FontWeight.bold),
                    const Spacer(),
                    const Icon(Icons.messenger_outline, size: 22),
                    SizedBox(width: widthSpace(2)),
                  ]).paddingSymmetric(vertical: widthSpace(1.6)),
                ),
                if (!isMasked && (phone ?? '').isNotEmpty) ...[
                  Divider(height: heightSpace(4)),
                  InkWell(
                    onTap: isAccepted
                        ? () => launchUrl(Uri.parse('tel:$phone'))
                        : null,
                    child: Row(children: [
                      SizedBox(width: widthSpace(2)),
                      Expanded(
                        child: CustomText(
                            isAccepted
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .reservation
                                    .callHost
                                : Get.find<TranslationHelper>()
                                    .translations
                                    .reservation
                                    .youWillSeeContact,
                            size: 1.9,
                            weight: FontWeight.bold),
                      ),
                      const Icon(Icons.phone_outlined, size: 22),
                      SizedBox(width: widthSpace(2)),
                    ]).paddingSymmetric(vertical: widthSpace(1.6)),
                  ),
                ],
              ]).paddingAll(widthSpace(viewPadding));
        }));
  }

  onContactHost(query, {RxBool? isLoading}) async {
    // try{
    //   (isLoading??isBtnLoading).value = true;
    //   if(query is ReservationModel){
    //     update(["bookings"]);
    //     int? chatHeadId = await ChatHelper.contactHost(query.propertyId!,query.startDate,query.endDate, query.adultGuest??1,query.childGuest??0,chatHeadId: query.chatHeadId);
    //     print(chatHeadId);
    //     if(chatHeadId!=null){
    //       query.chatHeadId = chatHeadId;
    //     }
    //     isBtnLoading.value=false;
    //     update(["bookings"]);
    //   }else if(query is BookingModel){
    //     int? chatHeadId = await ChatHelper.contactHost(query.propertyId!,query.startDate,query.endDate, query.adults??1,query.children??0,chatHeadId: query.chatHeadId);
    //     (isLoading??isBtnLoading).value = false;
    //     if(chatHeadId!=null){
    //       query.chatHeadId = chatHeadId;
    //     }
    //   }
    //   update(["bookings"]);
    // }catch(e){
    //   print(e);
    //   (isLoading??isBtnLoading).value = false;
    //   update(["bookings"]);
    // }
    // InboxModel? temp;
    // if (query is ReservationComing) {
    //   temp = inboxList.firstWhereOrNull((item) => query.propertyId == item.propertyId);
    // } else {
    //   temp = inboxList.firstWhereOrNull((item) =>query.propertyId == item.propertyId &&(query.guestId == item.receiverId || query.guestId == item.senderId));
    // }
    // if (temp == null) {
    //   ResponseModel response = await ApiServices.getApi(
    //       "v1/manualmsg?${query is ReservationComing ? "property_id=${query.propertyId}" : "booking_id=${query.id}"}",
    //       isAuth: true);
    //   if (response.status) {
    //     getInbox();
    //     Get.to(() => InboxWindow(
    //         hostId: query is ReservationComing ? query.hostId : query.guestId,
    //         propertyId: query.propertyId));
    //   }
    // } else {
    //   if (query is ReservationComing) {
    //     DateTime start = formDateFormat.parse(query.startDate!);
    //     DateTime end = formDateFormat.parse(query.endDate!);
    //     Get.to(() => InboxWindow(
    //         hostId: temp?.hostId,
    //         propertyId: temp?.propertyId,
    //         dates: [start, end]));
    //   } else {
    //     DateTime start = formDateFormat.parse(query.startDate!);
    //     DateTime end = formDateFormat.parse(query.endDate!);
    //     Get.to(() => InboxWindow(
    //         hostId: query.guestId,
    //         propertyId: temp?.propertyId,
    //         dates: [start, end]));
    //   }
    // }
    // AccountController accountC = Get.find();
    // if(query is ReservationComing){
    //   analytics.logEvent(
    //       name: "contact_host",
    //       parameters: {
    //         "user_name":"${accountC.userModel.value?.first_name} ${accountC.userModel.value?.last_name}",
    //         "host_name": query.hostName,
    //         "property_id": query.propertyId,
    //         "property_slug":query.slug,
    //         "property_name":query.propertyName,
    //       }
    //   );
    // }else{
    //   analytics.logEvent(
    //       name: "contact_guest",
    //       parameters: {
    //         "host_name":"${accountC.userModel.value?.first_name} ${accountC.userModel.value?.last_name}",
    //         "guest_name": query is BookingHistory?query.guestName:query.firstName,
    //         "property_id": query.propertyId,
    //         "property_slug": query.slug,
    //         "property_name":query.propertyName,
    //       }
    //   );
    // }
  }
  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    DeepLinkHelper.dispose();
    ApiServices.closeClient();
    super.dispose();
  }

  @override
  void onInit() async {
    // initMyFatoorah();
    isUser = GlobalHelper.storageBox.hasData('user');
    if (isUser) {
      final lang = GlobalHelper.storageBox.read('user')['lang'] ?? 'en';
      ApiServices.postApi("v1/set-language",
          body: {"lang": lang}, isAuth: true);
    }
    SearchHelper(this);
    // await ConnectivityHelper.checkInternet().then((value) async {
    //   if(value){
    getInitialData();
    getCitiesData();
    if (isUser) {
      initFcm();
      reloadForLanguage();
      WishlistHelper(this);
    } else {
      // WidgetsBinding.instance.addPostFrameCallback(bottomSheetListener);
    }
    //   }
    //   else{
    //     getHomeData(value: false);
    //     WidgetsBinding.instance.addPostFrameCallback(bottomSheetListener);
    //   }
    // });
    super.onInit();
    reservationTabController = TabController(length: 5, vsync: this);
    reservationTabController.addListener(() {
      if (reservationTabController.index == 3) {
        WebEngagePlugin.trackEvent('Canceled Reservations Viewed', {
          'User': 'Customer',
          'Date': formDateFormatCservice.format(DateTime.now())
        });
      }
    });
    WidgetsBinding.instance.addObserver(this);
  }

  initFcm() async {
    await Future.delayed(const Duration(seconds: 1));
    String? firebaseToken = await FirebaseMessaging.instance.getToken();
    debugPrint("FCM token : $firebaseToken");
    if (firebaseToken != null) {
      ApiServices.postApi("v1/set-fcm-token",
          isAuth: true, body: {"fcm_token": firebaseToken});
      FirebaseMessaging.onMessageOpenedApp
          .listen((message) => handleMessage(message.data));
      FirebaseMessaging.instance.getInitialMessage().then((message) {
        if (message != null) {
          handleMessage(message.data);
        }
      });
    }
  }

  // bottomSheetListener(_){
  //   AuthHelper.homeAuthSheet.addListener(()=>AuthHelper.bottomSheetListener(AuthHelper.homeAuthSheet));
  // }
  getCitiesData() {
    // isLoading = false;
    // update();
    try {
      ApiServices.getApi(
              "v4/cityBasedDistricts?lang=${Get.locale?.languageCode ?? "en"}",
              isAuth: true,
              showDialog: false)
          .then((response) {
        if (response.status) {
          cityData = response.data;
          cities.value = response.data
              .map<CityBasedDistricModel>(
                  (item) => CityBasedDistricModel.fromJson(item))
              .toList();
          // districts.value = response.data.expand<Map<String, dynamic>>(
          //         (e) => (e["districts"] as List<dynamic>).map<Map<String, dynamic>>(
          //                 (district) => {"name": district, "isChecked": false})).toList();
        }
        // isLoading = false;
        // update();
      });
    } catch (e) {
      // isLoading = false;
      // update();
    }
  }

  refreshHome() {
    onInit();
    AuthHelper.c.onInit();
    ChatHelper.c.onInit();
  }

  getInitialData() {
    isLoading = true;
    update();
    try {
      ApiServices.getApi(
              "v3/initialData?lang=${Get.locale?.languageCode ?? "en"}",
              isAuth: true,
              showDialog: false)
          .then((response) async {
        if (response.status) {
          initTranslation(response.data['settings']);
          getHomeData(dataTemp: response.data['home']);
          dataTotalPages = response.data['home_pagination']['last_page'];
          initListingPrefs(response.data['settings']);
          initBanksList(response.data['bank'] ?? []);
          AuthHelper.checkAndSetGuesUuid(response.data['guest_uuid']);

          //get-filter
          filters.value = FilterModel.fromJson(response.data['filter']);

          banners.value = response.data['banners'] ?? [];
          priceRange.value = SfRangeValues(
            filters.value!.min_price,
            filters.value!.max_price > 5000.0
                ? 5000.0
                : filters.value?.max_price,
          );
          minController.text =
              "${filters.value!.min_price >= 5000 ? "5000+" : filters.value!.min_price}";
          maxController.text =
              "${filters.value!.max_price >= 5000 ? "5000+" : filters.value!.max_price}";
          ViewsCommon.addPriceControllers(response.data['filter']['special_days']);
          Get.find<RemoteConfig>().setCountries(response.data['filter']['country_code']);
          //countryCodes = response.data['filter']['country_code'].map<CountryCode>((e)=>CountryCode.fromJson(e)).toList();

          // removed
          // districts.value = response.data['districts'].map((e)=>{"name":e,"isChecked":false}).toList();
          displayPromo = response.data['display_promo'] ?? false;
        }
        isLoading = false;
        Get.forceAppUpdate();
        if (toRedirect != null) {
          await Future.delayed(const Duration(milliseconds: 3500));
          if (toRedirect!.contains(Routes.dwellings)) {
            DwellingDetail? dwelling = await ListingHelper.c.getDwellingDetail(
                Uri.parse(toRedirect!).queryParameters['code']);
            if (dwelling != null) {
              ListingHelper.completeListing(dwelling);
            }
          } else {
            int? chatHead = int.tryParse(toRedirect!);
            if (chatHead != null) {
              ChatHelper.gotoChats(toRedirect);
            } else {
              Get.toNamed(toRedirect!);
            }
            toRedirect = null;
          }
        }
      });
    } catch (e) {
      isLoading = false;
      update();
    }
  }

  getHomeData({List? dataTemp, bool value = true}) async {
    if (value) {
      if (dataTemp == null) {
        final response = await ApiServices.getApi(
            "v1/home?lang=${Get.locale?.languageCode ?? "en"}",
            isAuth: true);
        if (response.status) {
          dataTemp = response.data;
        }
      }
      data.value = dataTemp!
          .map<HomeProperty>((item) => HomeProperty.fromJson(item))
          .toList();
      // await DbHelper.init().then((val) async {
      //   await DbHelper.insertPropertiesToDb(data.toList());
      //   await insertOfflineTranslation();
      // });
    } else {
      // await DbHelper.init().then((val) async {
      //   data.value = await DbHelper.getHomePageOfflineData();
      //   //List translationData = await DbHelper.getTranslationOfflineData();
      //   // translateOfflineKeywords['en'] = translationData[0];
      //   // translateOfflineKeywords['ar'] = translationData[1];
      // });
    }
    for (var item in data) {
      // if (item.latitude != null) {
      //   markers.addLabelMarker(LabelMarker(
      //       label: "${item.price} SAR",
      //       markerId: MarkerId(item.title!),
      //       position: LatLng(item.latitude!, item.longitude!),
      //       backgroundColor: const Color(themeColor),
      //       textStyle: const TextStyle(fontSize: 33, color: Colors.white),
      //       onTap: () {
      //         customInfoWindow.addInfoWindow!(
      //           ProductItemInfo(
      //               data: item,
      //               imgHeight: 20,
      //               noDescription: Get.find<TranslationHelper>()
      //                   .translations
      //                   .home
      //                   .noDescription),
      //           LatLng(item.latitude!, item.longitude!),
      //         );
      //         update();
      //       }));
      // }
    }
    isLoading = false;
    update();
  }

  // insertOfflineTranslation() async{
  //   List<Map<String, dynamic>> temporaryTranslations = [];
  //
  //   Get.find<TranslationHelper>().translateKeywords.forEach((languageCode, translationModel) {
  //     // Create a map with the extracted keywords
  //     Map<String, dynamic> temporaryMap = {
  //       "wishlist" : translationModel.wishlist.wishlist,
  //           "inbox"  : translationModel.header.inbox,
  //           "home " : translationModel.footer.home,
  //           "booking" : translationModel.listingSidebar.booking,
  //           "account" : translationModel.header.account,
  //           "no_result_found" : translationModel.search.noResultFound,
  //           "location" : translationModel.tripsActive.location,
  //           "empty_wishlist_title" : translationModel.usersDashboard.emptyWishlistTitle,
  //           "empty_inbox" : translationModel.message.emptyInbox,
  //           "no_notification" : translationModel.notification.noNotification,
  //           "upcoming" : translationModel.reservation.upComing,
  //           "ongoing" : translationModel.reservation.ongoing,
  //           "history" : translationModel.reservation.history,
  //           "cancelled" : translationModel.hostDashboard.cancel,
  //           "no_upcoming_bookings" : translationModel.reservation.noUpcomingBookings,
  //           "today" : translationModel.hostDashboard.today,
  //       "in_days": translationModel.reservation.inDays,
  //       "in_day": translationModel.reservation.inDay,
  //       "property_location": translationModel.reservation.propertyLocation,
  //       "chat_with_the_host" : translationModel.propertySingle.chatWithTheHost,
  //           "view_profile" : translationModel.accountMobile.viewProfile ,
  //           "show_your_house_on_darent" : translationModel.listing.showYourHouseOnDarent,
  //           "show_house_on_darent_description" : translationModel.listing.showHouseOnDarentDescription,
  //           "setting" : translationModel.header.settings,
  //           "account_info" : translationModel.accountMobile.accountInfo,
  //           "account_manager" : translationModel.sidenav.accountManager,
  //           "login_security" : translationModel.usersProfile.loginSecurity,
  //           "wallet" : translationModel.sidenav.wallet,
  //           "hosting" : translationModel.footer.hosting,
  //           "switch_to_hosting" : translationModel.accountMobile.switchToHosting,
  //           "pages" : translationModel.accountMobile.pages,
  //           "about_us" : translationModel.footer.aboutUs,
  //           "terms_and_conditions" : translationModel.accountMobile.termsAndConditions,
  //           "privacy_policy" : translationModel.footer.privacyPolicy,
  //           "support" : translationModel.footer.support,
  //           "faq" : translationModel.footer.faq,
  //           "customer_services" : translationModel.accountMobile.customerServices,
  //           "create_ticket" : translationModel.wallet.createTicket,
  //           "ticket_listing" : translationModel.wallet.ticketListing,
  //           "language" : translationModel.footer.language,
  //           "browse_in" : translationModel.accountMobile.browseIn,
  //           "wishlist_subtitle": translationModel.footer.wishlistSubTitle,
  //           "wishlist_desc": translationModel.footer.wishlistDescription,
  //           "reservation_subtitle": translationModel.footer.reservationSubTitle,
  //           "reservation_desc": translationModel.footer.reservationDescription,
  //           "inbox_subtitle": translationModel.footer.inboxSubTitle,
  //           "inbox_desc": translationModel.footer.inboxDescription,
  //           "profile_subtitle": translationModel.footer.profileSubTitle,
  //       "button_text": translationModel.login.login,
  //           "profile_desc": translationModel.footer.profileDescription,
  //           "logout" : translationModel.header.logout,
  //     };
  //
  //     // Add the map to temporaryTranslations list
  //     temporaryTranslations.add(temporaryMap);
  //   });
  //
  //   await DbHelper.insertTranslationToDb(temporaryTranslations);
  //
  // }
  clearUserData() {
    WishlistHelper.clearWishlist();
    // inboxList.clear();
    inboxPage = 1;
    inboxLastPage = 1;
    reservationComing.clear();
    upcomingPage = 1;
    upcomingLastPage = 1;
    reservationHistory.clear();
    historyPage = 1;
    historyLastPage = 1;
    cancelledReservation.clear();
    expiredReservation.clear();
    onGoing.clear();
    ongoingPage = 1;
    ongoingLastPage = 1;
    cancelledPage = 1;
    cancelledLastPage = 1;
  }

  reloadForLanguage() {
    userDataLoading = true;
    update(['bookings']);
    ApiServices.getApi(
            "v2/user-data?size=10&lang=${Get.locale?.languageCode ?? "en"}&device=${GetPlatform.isIOS ? "ios" : "android"}")
        .then((response) {
      if (response.status) {
        try {
          getNotifications(tempData: response.data['notification']);

          // inboxList = response.data['inbox']['data'].map<InboxModel>((item) => InboxModel.fromJson(item)).toList();
          inboxLastPage = response.data['inbox']['total_pages'];

          reservationComing = response.data['incoming']['data']
              .map<ReservationModel>((item) => ReservationModel.fromJson(item))
              .toList();
          upcomingLastPage = response.data['incoming']['total_pages'];

          reservationHistory.value = response.data['history']['data'].map<ReservationModel>((item) => ReservationModel.fromJson(item)).toList();
          historyLastPage = response.data['history']['total_pages'];

          cancelledReservation = response.data['cancelled']['data'].map<ReservationModel>((item) => ReservationModel.fromJson(item)).toList();
          cancelledLastPage = response.data['cancelled']['total_pages'];

          expiredReservation = response.data['expired']['data'].map<ReservationModel>((item) => ReservationModel.fromJson(item)).toList();
          expiredLastPage = response.data['expired']['total_pages'];

          onGoing = response.data['ongoing']['data'].map<ReservationModel>((item) => ReservationModel.fromJson(item)).toList();
          ongoingLastPage = response.data['ongoing']['total_pages'];

          // here we are updating current payment gateway
          debugPrint(
              " testing ${PaymentHelper.paymentGateway} aand ${response.data['payment_gateway'] ?? ""}");
          if (PaymentHelper.paymentGateway !=
              (response.data['payment_gateway'] ?? "")) {
            PaymentHelper.paymentGateway =
                response.data['payment_gateway'] ?? "";
            //exclude stc when hyperpay is not activee
            if (PaymentHelper.paymentGateway != 'hyperpay') {
              PaymentHelper.paymentMethods.removeWhere((i) => i.id == 12);
            }
            GlobalHelper.storageBox.write('user', userModel.value?.toJson());
          }

          userDataLoading = false;
          update(['bookings']);

          WebEngagePlugin.setUserEmail(userModel.value?.email ?? '');
          WebEngagePlugin.setUserFirstName(userModel.value?.first_name ?? '');
          WebEngagePlugin.setUserLastName(userModel.value?.last_name ?? '');
          WebEngagePlugin.setUserGender(userModel.value?.gender ?? '');
          WebEngagePlugin.setUserPhone(userModel.value?.phone ?? '');
          WebEngagePlugin.setUserBirthDate(userModel.value?.dateofbirth ?? '');
          Attributes.setGuestAttributes(response.data['attribute']);
          WebEngagePlugin.setUserAttributes(Attributes.guestAttributes ?? {});
        } catch (e) {
          userDataLoading = true;
          update(['bookings']);
        }
      } else if (response.message == "Unauthenticated.") {
        userUnAuthentication();
      }
    });
  }

  reloadOnResume() {
    if (isHost || index == 3) {
      if (inboxTab == "inbox") {
        // getInbox(refresh: true);
      } else {
        getNotifications(refresh: true, increaseCount: true);
      }
    }
    if (!isHost) {
      if (index == 2) {
        if (reservationTabController.index == 0) {
          getBookings(refresh: true);
        }else if (reservationTabController.index == 1) {
          getOnGoing(refresh: true);
        } else if (reservationTabController.index == 2) {
          getBookingsHistory(refresh: true);
        } else if (reservationTabController.index == 3) {
          getCancelledBookings(refresh: true);
        }else{
          getExpiredBookings(refresh: true);
        }
      } else if (index == 3) {
        getBookings();
      }
      AuthHelper.c.getCards();
      // AuthHelper.c.getTransactions();
    }
  }

  userUnAuthentication() {
    AuthHelper.c.onLogout();
    Get.dialog(WarningDialog(
        title: "Session Expired",
        description:
            "Your session has been expired\nPlease login to continue."));
    // if (AuthHelper.homeAuthSheet.isAttached) {
    //   AuthHelper.homeAuthSheet.animateTo(.82,duration: const Duration(milliseconds: 260), curve: Curves.ease);
    // }
  }

  initTranslation(Map data) {
    data['en']['header']['search_here'] = "Search here";
    data['ar']['header']['search_here'] = "ابحث هنا";

    data['en']['search']['plan_trip'] = "Plan your trip";
    data['ar']['search']['plan_trip'] = "خطط لرحلتك";

    data['en']['search']['clear_values'] = "Clear values";
    data['ar']['search']['clear_values'] = "مسح الكل";

    data['en']['search']['safety_items'] = "Safety Items";
    data['ar']['search']['safety_items'] = "عناصر السلامة";

    data['en']['property_single']['by'] = "by";
    data['ar']['property_single']['by'] = "by";

    data['en']['property_single']['reserve'] = "Reserve";
    data['ar']['property_single']['reserve'] = "احجز الان";

    data['en']['users_profile']['click_to_copy'] = "Click to Copy";
    data['ar']['users_profile']['click_to_copy'] = "انقر للنسخ";

    // translateKeywords['en'] = TranslationModel.fromJson(data['en']);
    // translateKeywords['ar'] = TranslationModel.fromJson(data['ar']);
    tawuniya = data['tawuniya'];
    Get.find<TranslationHelper>()
        .setKeywords(data, Get.locale?.languageCode ?? 'en');
  }

  initListingPrefs(Map data) {
    listPrefs['listing_preferences'] =
        ListingPrefsModel.fromJson(data['listing_preferences']);
  }

  initBanksList(List data) {
    for (var item in data) {
      banksList.add(BankModel.fromJson(item));
    }
  }

  var customHouseRules = RxList<Amenity>();
  TextEditingController customRuleController = TextEditingController();

  addMyCustomHouseRule() {
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        maxChildSize: .45,
        initialChildSize: .45,
        expand: false,
        builder: (context, scrollController) {
          return Obx(() => SingleChildScrollView(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(height: heightSpace(3)),
                      CircleAvatar(
                          backgroundColor: const Color(themeColor),
                          radius: widthSpace(3.5),
                          child: const Icon(Icons.info,
                              size: 21, color: Colors.white)),
                      SizedBox(height: heightSpace(2.5)),
                      CustomText(
                          Get.find<TranslationHelper>()
                                  .translations
                                  .hostDashboard
                                  .addCustomRule ??
                              "Add Your Custom HouseRule",
                          size: 3,
                          textAlign: TextAlign.center,
                          color: const Color(themeColor),
                          weight: FontWeight.w500),
                      SizedBox(height: heightSpace(2.5)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: widthSpace(60),
                            child: TextFormField(
                                controller: customRuleController,
                                maxLength: 20,
                                validator: (val) => val!.isEmpty
                                    ? Get.find<TranslationHelper>()
                                        .translations
                                        .jqueryValidation
                                        .required
                                    : null,
                                keyboardType: TextInputType.emailAddress,
                                decoration: InputDecoration(
                                    hintText: Get.find<TranslationHelper>()
                                            .translations
                                            .hostDashboard
                                            .customRule ??
                                        "HouseRule",
                                    hintStyle: const TextStyle(
                                        color: Color(greyBorder)),
                                    counterText: '',
                                    contentPadding: const EdgeInsets.all(10),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: const BorderSide(
                                          color: Color(greyBorder), width: 1),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                            color: Color(themeColor),
                                            width: 2)),
                                    enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(10),
                                        borderSide: const BorderSide(
                                            color: Color(greyBorder),
                                            width: 1)))),
                          ),
                          CommonButton(
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .hostDashboard
                                  .add,
                              onPressed: () {
                                if (customRuleController.text
                                    .trim()
                                    .isNotEmpty) {
                                  customHouseRules.add(Amenity.fromJson({
                                    'title': customRuleController.text,
                                  }));
                                  // customHouseRules.refresh();
                                  customRuleController.clear();
                                  update([]);
                                }
                              },
                              backgroundBg: isHost ? Colors.black : null,
                              horizontalPadding: 8)
                        ],
                      ),
                      SizedBox(height: heightSpace(2.5)),
                      Wrap(
                        runSpacing: heightSpace(2),
                        spacing: widthSpace(2),
                        direction: Axis.horizontal,
                        alignment: WrapAlignment.start,
                        runAlignment: WrapAlignment.start,
                        crossAxisAlignment: WrapCrossAlignment.start,
                        children: [
                          for (int i = 0; i < customHouseRules.length; i++) ...[
                            Stack(
                              alignment: Get.locale?.languageCode == 'en'
                                  ? Alignment.centerRight
                                  : Alignment.centerLeft,
                              children: [
                                Container(
                                  padding: const EdgeInsets.only(
                                      right: 10, left: 10, bottom: 4),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4),
                                    color: Colors.grey.shade100,
                                  ),
                                  child: CustomText(
                                    "${customHouseRules[i].title}       ",
                                    weight: FontWeight.w300,
                                    color: Colors.grey,
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(right: 4.0),
                                  child: GestureDetector(
                                    onTap: () {
                                      removeMyCustomHouseRule(i);
                                    },
                                    child: const Icon(
                                      Icons.clear,
                                      color: Colors.grey,
                                      size: 15,
                                    ),
                                  ),
                                )
                              ],
                            )
                          ]
                        ],
                      ).paddingSymmetric(vertical: widthSpace(viewPadding)),
                      SizedBox(
                          width: double.maxFinite,
                          height: heightSpace(6),
                          child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(themeColor),
                                  shape: RoundedRectangleBorder(
                                      borderRadius:
                                          BorderRadius.circular(100))),
                              onPressed: Get.back,
                              child: CustomText(
                                  Get.find<TranslationHelper>()
                                      .translations
                                      .usersProfile
                                      .save,
                                  weight: FontWeight.w500,
                                  color: Colors.white))),
                    ]).paddingAll(widthSpace(viewPadding)),
              ));
        }));
  }

  removeMyCustomHouseRule(int index) {
    customHouseRules.removeAt(index);
    customHouseRules.refresh();
  }

  ratingHead(text) => CustomText(text, size: 2.3, weight: FontWeight.w500);
  feedBackView(text, value, key) {
    return [
      ratingHead(text),
      SizedBox(height: heightSpace(2.5)),
      for (String i in oneTo5)
        RadioListTile(
          value: i,
          groupValue: value.value,
          onChanged: (val) => value.value = i,
          title: Text(
              '${Get.find<TranslationHelper>().translations.reservation.toJson()['$key$i']}'),
        )
    ];
  }

  showRatingSheet(Rxn<ReservationDetailModel> r) {
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        minChildSize: .90,
        initialChildSize: .90,
        expand: false,
        builder: (context, scrollController) {
          return SingleChildScrollView(
            child: Column(children: [
              Container(
                height: 5,
                width: widthSpace(16),
                margin: EdgeInsets.only(
                    top: heightSpace(3), bottom: heightSpace(2)),
                decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(6)),
              ),
              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: widthSpace(viewPadding)),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                          onTap: () {
                            if (ratingStep > 1) {
                              ratingStep.value -= 1;
                            } else if (ratingStep > 2) {
                              ratingStep.value -= 1;
                            } else {
                              Get.back();
                            }
                          },
                          child: const Icon(Icons.chevron_left)),
                      SizedBox(
                          width: widthSpace(70.0),
                          child: CustomText(r.value!.propertyName,
                              size: 2.3,
                              weight: FontWeight.w500,
                              textOverflow: TextOverflow.ellipsis)),
                      InkWell(
                          onTap: Get.back,
                          child: const Icon(Icons.close, size: 19)),
                    ]),
              ),
              Divider(height: heightSpace(5)),
              Obx(
                () => Padding(
                    padding: EdgeInsets.symmetric(
                        horizontal: widthSpace(viewPadding)),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Row(children: [
                            Container(
                              width: widthSpace(35),
                              height: widthSpace(35),
                              clipBehavior: Clip.antiAlias,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6)),
                              child: r.value!.photos!.isNotEmpty &&
                                      r.value!.photos!.first
                                          .contains("default-image")
                                  ? Image.asset("assets/default-image.png",
                                      fit: BoxFit.cover)
                                  : Image(
                                      image: GlobalHelper.buildNetworkImageProvider(url: r.value?.photos?.first ??''),
                                      fit: BoxFit.cover,
                                      ),
                            ),
                            SizedBox(width: widthSpace(2.5)),
                            Expanded(
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CustomText(r.value!.propertyName,
                                        size: 2.3, weight: FontWeight.w500),
                                    SizedBox(height: heightSpace(1)),
                                    CustomText(
                                        r.value!.propertyAddress ??
                                            "No Address available",
                                        size: 2.1,
                                        color: const Color(greyText)),
                                    SizedBox(height: heightSpace(1)),
                                    Row(children: [
                                      CustomText(DateFormat.MMMEd().format(
                                          r.value?.startDate ??
                                              DateTime.now())),
                                      const Icon(Icons.chevron_right,
                                          size: 22, color: Colors.green),
                                      CustomText(DateFormat.MMMEd().format(
                                          r.value?.endDate ?? DateTime.now())),
                                    ]),
                                  ]),
                            )
                          ]),
                          SizedBox(height: heightSpace(2.5)),
                          Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                  color: const Color(themeColor),
                                  borderRadius: BorderRadius.circular(5)),
                              child: CustomText(
                                r.value?.description ??
                                    Get.find<TranslationHelper>()
                                        .translations
                                        .home
                                        .noDescription,
                                maxlines: 5,
                                textOverflow: TextOverflow.ellipsis,
                                color: Colors.grey[100],
                              )),
                          SizedBox(height: heightSpace(2.5)),
                          if (ratingStep.value == 1) ...[
                            ...feedBackView(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .reservation
                                    .qCleannes,
                                ratingCleanliness,
                                'cleanness_option'),
                            SizedBox(height: heightSpace(2.5)),
                            ...feedBackView(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .reservation
                                    .qCommunication,
                                ratingCommunication,
                                'communication_option'),
                            SizedBox(height: heightSpace(2.5)),
                            ...feedBackView(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .reservation
                                    .qStates,
                                ratingStates,
                                'states_option'),
                            SizedBox(height: heightSpace(2.5)),
                            ...feedBackView(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .reservation
                                    .qLocation,
                                ratingLocation,
                                'location_option'),
                          ] else if (ratingStep.value == 2) ...[
                            ratingHead(Get.find<TranslationHelper>()
                                .translations
                                .reservation
                                .qWhatLikes),
                            SizedBox(height: heightSpace(2.5)),
                            CustomTextField(
                              controller: ratingMessage,
                              maxlines: 5,
                              hint: Get.find<TranslationHelper>()
                                  .translations
                                  .reservation
                                  .writeHere,
                              isRoundedBorder: true,
                            ),
                            // SizedBox(height: heightSpace(2.5)),
                            // SizedBox(
                            //   height: widthSpace(25),
                            //   child: ListView.separated(
                            //       scrollDirection: Axis.horizontal,
                            //       itemBuilder: (context, index) {
                            //         if(index<ratingImages.length){
                            //           return Container(
                            //               width: widthSpace(22),
                            //             padding: const EdgeInsets.all(5),
                            //             alignment: Alignment.topRight,
                            //             decoration: BoxDecoration(
                            //               borderRadius: BorderRadius.circular(12),
                            //               color: Colors.black12,
                            //               image: DecorationImage(
                            //                   image: FileImage(File(ratingImages[index])))
                            //             ),child: InkWell(
                            //             onTap: ()=>ratingImages.removeAt(index),
                            //               child: const CircleAvatar(
                            //               backgroundColor: Colors.grey,
                            //                 minRadius: 11, maxRadius: 11,
                            //                 child: Icon(Icons.close,size:18,color: Colors.black)),
                            //             ),
                            //           );
                            //         }else{
                            //           return DottedBorder(
                            //             borderType: BorderType.RRect,
                            //             color: const Color(greyText),
                            //             radius: const Radius.circular(12),
                            //             child: InkWell(
                            //               onTap:pickImages,
                            //               child: SizedBox(
                            //                 width: widthSpace(25),
                            //                 child: Column(
                            //                   mainAxisAlignment: MainAxisAlignment.center,
                            //                   children: [
                            //                     const Icon(Icons.camera_alt_outlined,color:Color(greyText)),
                            //                     CustomText(Get.find<TranslationHelper>().translations.reservation.addImage,color: const Color(greyText))
                            //                   ],
                            //                 ),
                            //               ),
                            //             ),
                            //           );
                            //         }
                            //       }, separatorBuilder:(context, index) => SizedBox(width: widthSpace(1)), itemCount: ratingImages.length+1),
                            // ),
                          ] else ...[
                            ...feedBackView(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .reservation
                                    .qService,
                                ratingService,
                                'service_option'),
                            SizedBox(height: heightSpace(2.5)),
                            ...feedBackView(
                                Get.find<TranslationHelper>()
                                    .translations
                                    .reservation
                                    .qRecomend,
                                ratingRecommend,
                                'recomend_option'),
                            SizedBox(height: heightSpace(2.5)),
                            ratingHead(Get.find<TranslationHelper>()
                                .translations
                                .reservation
                                .qImproment),
                            SizedBox(height: heightSpace(2)),
                            CustomTextField(
                                controller: ratingMessageDarent,
                                maxlines: 5,
                                hint: Get.find<TranslationHelper>()
                                    .translations
                                    .reservation
                                    .writeHere,
                                isRoundedBorder: true),
                          ],
                          SizedBox(height: heightSpace(4)),
                          CommonButton(
                              title: ratingStep > 2
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .hostListing
                                      .finish
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .listingBasic
                                      .next,
                              onPressed: () {
                                if (ratingMessage.text.isEmpty &&
                                    ratingStep.value == 2) {
                                  ViewsCommon.showSnackbar(
                                      Get.find<TranslationHelper>()
                                          .translations
                                          .jqueryValidation
                                          .required,
                                      keyword: DialogKeyword.warning);
                                  return;
                                } else if (ratingStep < 3) {
                                  ratingStep.value += 1;
                                } else {
                                  submitRating(r);
                                }
                                if (ratingStep == 2 || ratingStep == 3) {
                                  GlobalHelper.removeFocus();
                                }
                              },
                              isDisabled: (ratingStep == 1 &&
                                      (ratingCleanliness.value == null ||
                                          ratingCommunication.value == null ||
                                          ratingStates.value == null ||
                                          ratingLocation.value == null)) ||
                                  (ratingStep == 3 &&
                                      (ratingService.value == null ||
                                          ratingRecommend.value == null)),
                              isLoading: generalLoader.value)
                        ])),
              ),
            ]),
          );
        }));
  }

  submitRating(Rxn<ReservationDetailModel> r) {
    // Track review submitted event with Analytics Manager (using Google Sheets event name)
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.submitReview,
      eventAttributes: {
        AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
        AnalyticsAttributes.hostId: r.value?.hostId?.toString(),
        AnalyticsAttributes.propertyId: r.value?.propertyId?.toString(),
        AnalyticsAttributes.bookingId: r.value?.id?.toString(),
        AnalyticsAttributes.reviewScore:
            ((int.tryParse(ratingCleanliness.value!) ?? 0) +
                    (int.tryParse(ratingCommunication.value!) ?? 0) +
                    (int.tryParse(ratingLocation.value!) ?? 0) +
                    (int.tryParse(ratingStates.value!) ?? 0)) /
                4,
        AnalyticsAttributes.reviewText: ratingMessage.text,
        AnalyticsAttributes.sessionTimestamp: DateTime.now().toIso8601String(),
      },
    );

    if (ratingMessage.text.isNotEmpty && ratingMessageDarent.text.isNotEmpty) {
      generalLoader.value = true;
      update(['bookings']);
      Map<String, String> formData = {
        'message': ratingMessage.text,
        'cleanliness': ratingCleanliness.value??"0",
        'location': ratingLocation.value??"0",
        'darent_service': ratingService.value??"0",
        'darent_recomended': ratingRecommend.value??"0",
        'darent_message': ratingMessageDarent.text,
        'communication': ratingCommunication.value??"0",
        'accuracy': ratingStates.value??"0"
      };
      ApiServices.imageUpload("v2/guest/bookings/${r.value?.id}/review",
              imagePaths: ratingImages.toList(), body: formData)
          .then((value) {
        Get.back();
        clearRatingFields();
        // AuthHelper.c.getReviews();
        generalLoader.value = false;
        Get.until((route) => !Get.isDialogOpen! && !Get.isBottomSheetOpen!);
        if (value.status) {
          Get.dialog(
            WarningDialog(
                title: "success".tr,
                keyword: DialogKeyword.success,
                description: Get.find<TranslationHelper>()
                    .translations
                    .reservation
                    .ratingPosted),
          );
          r.value?.rated = true;
          r.refresh();
          update(['bookings']);
          // AuthHelper.c.getReviews();

          // Track review published event with Analytics Manager
          Get.find<AnalyticsManager>().trackEvent(
            AnalyticsEvents.reviewPublished,
            eventAttributes: {
              AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
              AnalyticsAttributes.hostId: r.value?.hostId?.toString(),
              AnalyticsAttributes.propertyId: r.value?.propertyId?.toString(),
              AnalyticsAttributes.bookingId: r.value?.id?.toString(),
              AnalyticsAttributes.reviewScore:
                  ((int.tryParse(ratingCleanliness.value??"0") ?? 0) +
                          (int.tryParse(ratingCommunication.value??"0") ?? 0) +
                          (int.tryParse(ratingLocation.value??"0") ?? 0) +
                          (int.tryParse(ratingStates.value??"0") ?? 0)) /
                      4,
              AnalyticsAttributes.reviewText: ratingMessage.text,
              AnalyticsAttributes.sessionTimestamp:
                  DateTime.now().toIso8601String(),
            },
          );

          // Track rating submitted event with Analytics Manager
          Get.find<AnalyticsManager>().trackEvent(
            AnalyticsEvents.ratingSubmitted,
            eventAttributes: {
              AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
              AnalyticsAttributes.hostId: r.value?.hostId?.toString(),
              AnalyticsAttributes.propertyId: r.value?.propertyId?.toString(),
              AnalyticsAttributes.bookingId: r.value?.id?.toString(),
              AnalyticsAttributes.ratingValue:
                  ((int.tryParse(ratingCleanliness.value??"0") ?? 0) +
                          (int.tryParse(ratingCommunication.value??"0") ?? 0) +
                          (int.tryParse(ratingLocation.value??"0") ?? 0) +
                          (int.tryParse(ratingStates.value??"0") ?? 0)) /
                      4,
              AnalyticsAttributes.sessionTimestamp:
                  DateTime.now().toIso8601String(),
            },
          );
        }
      });
    } else {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.jqueryValidation.required,
          keyword: DialogKeyword.warning);
    }
  }

  clearRatingFields() {
    ratingMessage.clear();
    ratingMessageDarent.clear();
    ratingCleanliness.value = null;
    ratingCommunication.value = null;
    ratingLocation.value = null;
    ratingStates.value = null;
    ratingService.value = null;
    ratingRecommend.value = null;
    ratingStep.value = 1;
    ratingImages.clear();
  }

  pickImages() async {
    try {
      var images =
          await ImagePicker().pickMultiImage(requestFullMetadata: false);
      if (images.isNotEmpty) {
        if ((ratingImages.length + images.length) > 5) {
          ViewsCommon.showSnackbar(
              Get.find<TranslationHelper>()
                  .translations
                  .listing
                  .onlyFiveImages!,
              keyword: DialogKeyword.warning);
        } else {
          ratingImages.value =
              images.map<String>((image) => image.path).toList();
        }
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  DateTime checkin = GlobalHelper.getAbsoluteNowDate();
  DateTime checkout = GlobalHelper.getAbsoluteNowDate().add(const Duration(days: 1));
  int? adults, children;
  callViewItemsEvent() {
    List<AnalyticsEventItem> analyticsItems = [];
    for (int i = 0; i < data.length; i++) {
      final item = data[i];
      analyticsItems.add(AnalyticsEventItem(
          itemId: item.id.toString(),
          affiliation: '',
          discount: item.discountedAmount ?? 0,
          index: i + 1,
          price: (item.price ?? 0) / 1000000,
          quantity: checkout.difference(checkin).inDays,
          parameters: {
            'itemType': item.propertyTypeName ?? '',
            'item_city_name': '${selectedCity.value?.name}',
            'item_host_id': '${item.hostId}',
            'total_price': '${item.totalPrice?.toStringAsFixed(2)}'
          }));
    }
    analytics.logViewItemList(items: analyticsItems);
  }
  getResultCount()async{
    SearchHelper.prepareFiltersForm();
    ResponseModel responseModel;

    if (selectedCity.value != null) {
      responseModel = await callSearchResult(planTripValue: {"location": selectedCity.value!.id.toString()});
    } else {
      responseModel = await callSearchResult();
    }
    if(responseModel.status){
      totalAvailablePages.value = responseModel.data['pagination']['total'];
      showResultsText.value = Get.find<TranslationHelper>().translations.filter.showResults.replaceAll(' ', ' ${(totalAvailablePages.value??0)>1000?"1000+":totalAvailablePages.value} ');
    }
    dataLoading.value = false;

  }
  Future<ResponseModel> callSearchResult({bool isLazyLoading = false, planTripValue})async{
    // Set<Marker> tempMarkers = {};
    if (!isLazyLoading) {
      //lazyLoader.value
      // isLoading = true;
      // update();
      dataLoading.value = true;

      // Track search event with Analytics Manager
      if (planTripValue != null && planTripValue.isNotEmpty) {
        // Track property searched event with Analytics Manager
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.propertySearched,
          eventAttributes: {
            AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
            AnalyticsAttributes.searchQuery: planTripValue['location'] ?? '',
            AnalyticsAttributes.searchLocation: selectedCity.value?.name ?? '',
            AnalyticsAttributes.searchDates:
            "${SearchHelper.c.checkin.toString()} - ${SearchHelper.c.checkout.toString()}",
            AnalyticsAttributes.searchGuests: planTripValue['guest'] ?? '1',
            AnalyticsAttributes.sessionTimestamp:
            DateTime.now().toIso8601String(),
          },
        );
      }
    }
    SearchHelper.setFormBeforeApi(planTripValue: planTripValue);
    return await ApiServices.postApi(
        ApiConstants.getSearchResultEndPoint(dataPage, dataSize, Get.locale?.languageCode ?? "en"),
        body: SearchHelper.filterForm,
        isAuth: true);
  }
  getData({bool isLazyLoading = false, planTripValue}) async {
    ResponseModel responseModel = await callSearchResult(isLazyLoading: isLazyLoading,planTripValue: planTripValue);
    if (responseModel.status) {
      if (dataPage > 1 || isLazyLoading) {
        data.addAll(responseModel.data['result']
            .map<HomeProperty>((item) => HomeProperty.fromJson(item)));
        // data.refresh();
        return;
      } else {
        data.value = responseModel.data['result']
            .map<HomeProperty>((item) => HomeProperty.fromJson(item))
            .toList();
        dataTotalPages = responseModel.data['pagination']['total_pages'];

        // Track search results event with Analytics Manager
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.searchResults,
          eventAttributes: {
            AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
            AnalyticsAttributes.searchResultsCount:
                responseModel.data['result'].length.toString(),
            AnalyticsAttributes.searchQuery: planTripValue?['location'] ?? '',
            AnalyticsAttributes.searchLocation: selectedCity.value?.name ?? '',
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
          },
        );

        // selectedCity.value = locationText;
        // selectedCity.value = locationText;
        // for(var item in cities){
        //   if(item['name'] == selectedCity.value){
        //     item['isChecked'] = true;
        //   }
        // }
        // cities.refresh();
        // districts.value = responseModel.data['districts'].map((e)=>{"name":e,"isChecked":false}).toList();
        // if(SearchHelper.filterForm['districts']!=null){
        //   for(var item in districts){
        //      item['isChecked'] = SearchHelper.filterForm['districts'].contains(item['name']);
        //   }
        // }
        callViewItemsEvent();
      }
      //commented Map and listview related
      // for (var item in data) {
      //   if (item.latitude != null) {
      //     tempMarkers.addLabelMarker(LabelMarker(
      //         label: "${item.price} SAR",
      //         markerId: MarkerId(item.title!),
      //         position: LatLng(item.latitude!, item.longitude!),
      //         backgroundColor: const Color(themeColor),
      //         textStyle: const TextStyle(fontSize: 33, color: Colors.white),
      //         onTap: () {
      //           customInfoWindow.addInfoWindow!(
      //             ProductItemInfo(
      //                 data: item,
      //                 imgHeight: 20,
      //                 noDescription:Get.find<TranslationHelper>().translations.home.noDescription),
      //             LatLng(item.latitude!, item.longitude!),
      //           );
      //         }));
      //   }
      // }
      // if (data.isEmpty) {
      //   listView.value = true;
      // }
      // markers = tempMarkers;
    } else {
      data.value = [];
      // listView.value = true;
    }
    // lazyLoader.value = false;
    dataLoading.value = false;
    // isLoading = false; loading commented

    // if (responseModel.data['location'] != null &&
    //     responseModel.data['location'].isNotEmpty &&
    //     responseModel.data['location']['lat'] != 0 &&
    //     responseModel.data['location']['long'] != 0) {
    // mapCenter = LatLng(responseModel.data['location']['lat'],
    //     responseModel.data['location']['long']);
    // } else {
    // mapCenter = const LatLng(21.4858, 39.1925);
    // }
    update();
  }

  // getInbox({refresh=false}) async {
  //   if(refresh){
  //     inboxPage = 1;
  //   }
  //     ResponseModel response =await ApiServices.getApi("v2/inbox?page=$inboxPage&size=10", isAuth: true);
  //    if(response.status){
  //      if(inboxPage>1){
  //        inboxList.addAll(response.data['messages'].map<InboxModel>((item) => InboxModel.fromJson(item)));
  //      }else{
  //        inboxList = response.data['messages'].map<InboxModel>((item) => InboxModel.fromJson(item)).toList();
  //      }
  //      inboxLastPage = response.data['total_pages'];
  //      update(["inbox"]);
  //    }
  // }
  Future getNotifications(
      {Map? tempData, refresh = false, increaseCount = false}) async {
    if (refresh) {
      notifyPage = 1;
      notifyPage = 1;
      generalLoader.value = true;
    }
    if (tempData == null) {
      final response = await ApiServices.getApi(
          "v2/notifications?size=10&page=$notifyPage",
          isAuth: true);
      if (response.status) {
        tempData = response.data;
      }
    }
    if (tempData != null) {
      if (notifyPage > 1) {
        notifyList.addAll(tempData['data']
            .map<NotifyModel>((item) => NotifyModel.fromJson(item)));
        notifyList.refresh();
      } else {
        notifyList.value = tempData['data']
            .map<NotifyModel>((item) => NotifyModel.fromJson(item))
            .toList();
        if (increaseCount) {
          setApplicationBadgeCount(forceIncreaseValue: true);
        }
      }
      generalLoader.value = false;
      notifyLastPage = tempData['total_pages'];
      update(["inbox"]);
    }
  }

  getBookings({refresh = false}) async {
    if (refresh) {
      upcomingPage = 1;
      userDataLoading = true;
      update(["bookings"]);
    }
    ResponseModel responseModel = await ApiServices.getApi(
        "v2/guest/coming/reservation?page=$upcomingPage&size=10",
        isAuth: true);
    userDataLoading = false;
    if (responseModel.status) {
      if (!refresh && upcomingPage > 1) {
        reservationComing.addAll(responseModel.data['UpcomingBookings']
            .map<ReservationModel>((item) => ReservationModel.fromJson(item)));
        reservationLazyLoader = false;
      } else {
        reservationComing = responseModel.data['UpcomingBookings']
            .map<ReservationModel>((item) => ReservationModel.fromJson(item))
            .toList();
      }
      update(["bookings"]);
      upcomingLastPage = responseModel.data['pagination']['total_pages'];
    }
  }

  getCancelledBookings({refresh = false}) async {
    if (refresh) {
      cancelledPage = 1;
    }
    ResponseModel responseModel = await ApiServices.getApi(
        "v2/guest/cancelled/reservation?page=$cancelledPage&size=10",
        isAuth: true);
    if (responseModel.status) {
      if (!refresh && cancelledPage > 1) {
        cancelledReservation.addAll(responseModel.data['CancelledBookings']
            .map<ReservationModel>((item) => ReservationModel.fromJson(item)));
        reservationLazyLoader = false;
      } else {
        cancelledReservation = responseModel.data['CancelledBookings']
            .map<ReservationModel>((item) => ReservationModel.fromJson(item))
            .toList();
      }
      update(["bookings"]);
      cancelledLastPage = responseModel.data['pagination']['total_pages'];
    }
  }

  getExpiredBookings({refresh = false}) async {
    if (refresh) {
      expiredPage = 1;
    }
    ResponseModel responseModel = await ApiServices.getApi(
        "v2/guest/expired/reservation?page=$expiredPage&size=10",
        isAuth: true);
    if (responseModel.status) {
      if (!refresh && expiredPage > 1) {
        expiredReservation.addAll(responseModel.data['CancelledBookings']
            .map<ReservationModel>((item) => ReservationModel.fromJson(item)));
        reservationLazyLoader = false;
      } else {
        expiredReservation = responseModel.data['CancelledBookings']
            .map<ReservationModel>((item) => ReservationModel.fromJson(item))
            .toList();
      }
      update(["bookings"]);
      expiredLastPage = responseModel.data['pagination']['total_pages'];
    }
  }

  getOnGoing({refresh = false}) async {
    if (refresh) {
      ongoingPage = 1;
    }
    ResponseModel responseModel = await ApiServices.getApi(
        "v2/guest/current/reservation?page=$ongoingPage&size=10",
        isAuth: true);
    if (responseModel.status) {
      if (!refresh && ongoingPage > 1) {
        onGoing.addAll(responseModel.data['CancelledBookings']
            .map<ReservationModel>((item) => ReservationModel.fromJson(item)));
        reservationLazyLoader = false;
      } else {
        onGoing = responseModel.data['CancelledBookings']
            .map<ReservationModel>((item) => ReservationModel.fromJson(item))
            .toList();
      }
      update(["bookings"]);
      ongoingLastPage = responseModel.data['pagination']['total_pages'];
    }
  }

  getBookingsHistory({refresh = false}) async {
    if (refresh) {
      historyPage = 1;
    }
    ResponseModel responseModel = await ApiServices.getApi(
        "v2/guest/history/reservation?page=$historyPage&size=10",
        isAuth: true);
    if (responseModel.status) {
      if (historyPage > 1) {
        reservationHistory.addAll(responseModel.data['HistoryBookings']
            .map<ReservationModel>(
                (item) => ReservationModel.fromJson(item)));
        reservationLazyLoader = false;
      } else {
        reservationHistory.value = responseModel.data['HistoryBookings']
            .map<ReservationModel>((item) => ReservationModel.fromJson(item))
            .toList();
      }
      update(["bookings"]);
      historyLastPage = responseModel.data['pagination']['total_pages'];
    }
  }

  onCancelReservation(id) async {
    isBtnLoading.value = true;
    update(["bookings"]);
    Get.back();
    final response =
        await ApiServices.getApi("v1/booking/cancel/$id", isAuth: true);
    isBtnLoading.value = false;
    if (response.status) {
      // Track booking cancelled by guest event with Analytics Manager (using Google Sheets event name)
      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.bookingCancelled,
        eventAttributes: {
          AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
          AnalyticsAttributes.bookingId: id.toString(),
          AnalyticsAttributes.cancellationReason: 'user_initiated',
          AnalyticsAttributes.sessionTimestamp:
              DateTime.now().toIso8601String(),
        },
      );

      ViewsCommon.showSnackbar(
          "${Get.find<TranslationHelper>().translations.general.yourReservationCancelSuccessfully}\n${Get.find<TranslationHelper>().translations.general.wouldYouLikeToReserveMore}",
          displayTime: 1500);

      getBookings(refresh: true);
      getCancelledBookings(refresh: true);
    }
    return response.status;
  }

  viewReceipt(code) async {
    if (lazyLoader.isFalse) {
      final url = "$baseUrl/booking/receipt?code=$code";
      try {
        lazyLoader.value = true;
        final file = await loadPdf(url);
        lazyLoader.value = false;
        Get.to(() => PdfReceiptViewer(
            file: file,
            screenName: Get.find<TranslationHelper>()
                .translations
                .reservation
                .reservationSummary));
      } catch (e) {
        lazyLoader.value = false;
      }
    }
  }
  // getReceipt(index)async{
  // ResponseModel response = await ApiServices.getApi(
  //       "v1/booking/receipt?code=${onGoing[index].bookingNumber}",
  //       isAuth: true);
  //       print("api chali ");
  //       print("code : ${onGoing[index].bookingNumber}");
  //       if(response.status){
  //         receiptData.value = ReceiptModel.fromJson(response.data);
  //         Get.to(() => const Receipt());
  //       }
// }
// final screenShot = ScreenshotController();

// Future getPdf() async {
//   final shot = await screenShot.capture();
//   pw.Document pdf = pw.Document();
//   pdf.addPage(
//     pw.Page(
//       pageFormat: PdfPageFormat.a4,
//       build: (context) {
//         return pw.Expanded(
//           child:
//               pw.Center(child: pw.Image(pw.MemoryImage(shot!), fit: pw.BoxFit.contain))
//               );
//       },
//     ),
//   );
//    final directory = await getExternalStorageDirectory();
//
//     final pdfPath = '${directory!.path}/receipt.pdf';
//   final pdfFile = File(pdfPath);
//   pdfFile.writeAsBytesSync(await pdf.save());
//
//   await OpenFile.open(pdfPath).then((response){
//       print(response.message);
//       print(response.type);
//     });
//
// }

  Future<File> loadPdf(String url) async {
    final response = await http.get(Uri.parse(url));
    final bytes = response.bodyBytes;
    return _storeFile(url, bytes);
  }

  Future<File> _storeFile(String url, List<int> bytes) async {
    final dir = await getApplicationDocumentsDirectory();

    final file = File('${dir.path}/pdf');
    await file.writeAsBytes(bytes, flush: true);
    return file;
  }

  TabbySession? tabbySession;

  initTabby(ReservationDetailController c) async {
    final mockPayload = Payment(
      amount: (c.data.value?.total ?? 0.0).toString(),
      currency: Currency.sar,
      buyer: Buyer(
        email: userModel.value?.email ?? "",
        phone: userModel.value?.phone ?? "",
        name: '${userModel.value?.first_name} ${userModel.value?.last_name}',
        dob: userModel.value?.dateofbirth != null
            ? formDateFormat
                .format(DateTime.parse(userModel.value!.dateofbirth!))
            : null,
      ),
      buyerHistory: BuyerHistory(
        loyaltyLevel: 0,
        registeredSince: userModel.value!.createdAt!,
        wishlistCount: 0,
      ),
      shippingAddress: ShippingAddress(
        city: 'string',
        address: userModel.value?.location ?? "",
        zip: 'string',
      ),
      order: Order(referenceId: "000-B-${c.data.value!.id!}", items: [
        OrderItem(
          title: c.data.value!.propertyName!,
          description: c.data.value!.description!,
          quantity: 1,
          unitPrice: c.data.value!.basePrice!.toString(),
          referenceId: "000-B-${c.data.value!.id!}",
          productUrl: 'https://darent.com',
          category: c.data.value!.propertyType!,
        )
      ]),
      orderHistory: [],
    );
    tabbySession =
        await GlobalHelper.tabbySdk.createSession(TabbyCheckoutPayload(
      merchantCode: 'sa',
      lang: Get.locale!.languageCode == 'en' ? Lang.en : Lang.ar,
      payment: mockPayload,
    ));
    if (kDebugMode) {
      print("tabby session id ${tabbySession!.sessionId}");
      print("tabby payment id ${tabbySession!.paymentId}");
      print(
          "tabby session reservation id dashboard 000-B-${c.data.value!.id!}");
    }
  }

  // initMyFatoorah() async {
  //   MFSDK.init(myFatoorahLive, MFCountry.SAUDIARABIA, MFEnvironment.LIVE);
  //   var request = MFInitiatePaymentRequest(
  //       invoiceAmount: 30,
  //       currencyIso: "SAR");
  //   await MFSDK.initiatePayment(
  //     request,
  //     MFLanguage.ENGLISH,
  //   ).then((result) {
  //     paymentMethods.value = result.paymentMethods!.where((item) {
  //       if(GetPlatform.isIOS){
  //         return item.paymentMethodId !=6;
  //       }
  //       else{
  //         return item.paymentMethodId !=6  && item.paymentMethodCode != "ap";
  //       }
  //     }).toList();
  //     if(userModel.value?.paymentGateway=='hyperpay'){
  //     paymentMethods.add(MFPaymentMethod(paymentMethodId: 12,paymentMethodEn: 'stcPay',imageUrl: ""));
  //     paymentMethods.add(MFPaymentMethod(paymentMethodId: 101,paymentMethodEn: 'mada',imageUrl: result.paymentMethods?.firstWhere((e) => e.paymentMethodEn=='mada').imageUrl));
  //     }
  //   }).catchError((error){
  //     debugPrint(error!.toString());
  //   });
  // }

  continuePayment(ReservationDetailController c, refresh) async {
    isBtnLoading.value = true;
    // initMyFatoorah(c);
    isBtnLoading.value = false;
    update(['bookings']);
    Get.bottomSheet(BottomSheet(
        backgroundColor: Colors.white,
        enableDrag: false,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(25))),
        onClosing: () {},
        builder: (context) {
          return SingleChildScrollView(
            padding: EdgeInsets.all(widthSpace(viewPadding)),
            child: Obx(
              () => Column(
                children: [
                  // paymentMethods.isEmpty && tabbySession != null
                  //     ? const SizedBox(width: 16,height: 16,child: CircularProgressIndicator(strokeWidth:2))
                  //     :
                  for (PaymentMethod paymentMethod
                      in PaymentHelper.paymentMethods)
                    CheckboxListTile(
                        value: paymentMethodId.value == paymentMethod.id,
                        title: Text(paymentMethod.name),
                        secondary: Image.asset(
                          'assets/icons/payment_methods/${paymentMethod.id}.png',
                          width: 35,
                          height: 20,
                        ),
                        onChanged: (bool? value) async {
                          paymentMethodId.value = paymentMethod.id;
                          if (paymentMethodId.value == 100) {
                            unSelectWalletPay();
                            // paymentMethods.refresh();
                            if (discountObject.value != null) {
                              isBtnLoading.value = true;
                              await removeCoupon(c.data.value!.id);
                              isBtnLoading.value = false;
                              discountObject.value = null;
                            }
                          }
                        }),
                  // ListView.separated(
                  //     shrinkWrap: true,
                  //     physics: const NeverScrollableScrollPhysics(),
                  //     itemBuilder: (context, index) {
                  //       if(index == paymentMethods.length){
                  //         return CheckboxListTile(
                  //             value: paymentMethodId.value == 100,
                  //             secondary:
                  //             const Row(mainAxisSize: MainAxisSize.min, children: [
                  //               Image(
                  //                 image: AssetImage(
                  //                   'assets/images/tabby-badge.png',
                  //                   package: 'tabby_flutter_inapp_sdk',
                  //                 ),
                  //                 width: 35,
                  //                 height: 20,
                  //               ),
                  //               CustomText(" Pay With Tabby")
                  //             ]),
                  //             onChanged: (bool? value) async{
                  //               paymentMethodId.value = payment;
                  //               if(paymentMethodId.value == 100){
                  //                 unSelectWalletPay();
                  //                 // paymentMethods.refresh();
                  //                 if(discountObject.value != null){
                  //                   isBtnLoading.value = true;
                  //                   await removeCoupon(c.data.value!.id);
                  //                   isBtnLoading.value = false;
                  //                   discountObject.value = null;
                  //                 }
                  //               }
                  //             });
                  //       }
                  //       else{
                  //         return CheckboxListTile(
                  //             value: paymentMethodId.value ==paymentMethods[index].paymentMethodId,
                  //             secondary:
                  //                 Row(mainAxisSize: MainAxisSize.min, children: [
                  //                   paymentMethods[index].paymentMethodId ==12
                  //                   ? Image.asset(
                  //                       'assets/stc-pay.png',
                  //                       width: widthSpace(12))
                  //                   :Image.network(
                  //                 paymentMethods[index].imageUrl ?? "",
                  //                     width: widthSpace(12)),
                  //                   CustomText(paymentMethods[index].paymentMethodEn ?? "")
                  //             ]),
                  //             onChanged: (bool? value) {
                  //               paymentMethodId.value =paymentMethods[index].paymentMethodId;
                  //               paymentMethods.refresh();
                  //             });
                  //       }},
                  //     separatorBuilder: (context, index) =>SizedBox(height: heightSpace(2)),
                  //     itemCount: paymentMethods.length+1),
                  //wallet removed
                  InkWell(
                    onTap: (AuthHelper.c.myWallet.first.balance ?? 0) <= 0
                        ? null
                        : () => enableWalletPay(c.data.value!),
                    borderRadius: BorderRadius.circular(10),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: const Color(greyBorder))),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SvgPicture.asset("assets/wallet.svg",
                              width: widthSpace(6), color: Colors.black),
                          SizedBox(width: widthSpace(3)),
                          CustomText(
                              "${Get.find<TranslationHelper>().translations.general.walletPay} (${Get.find<TranslationHelper>().translations.hostDashboard.sar}-${AuthHelper.c.myWallet.first.balance.toString()})",
                              color:
                                  (AuthHelper.c.myWallet.first.balance ?? 0) <=
                                          0
                                      ? const Color(greyText)
                                      : null,
                              size: 2.2,
                              weight: FontWeight.w500),
                          const Spacer(),
                          CommonSwitch(
                            selectorSize: 5,
                            selectedColor: const Color(themeColor),
                            unSelectedColor: Colors.black26,
                            isSelected: isEnableWalletPay.value,
                          ),
                        ],
                      ),
                    ),
                  ),
                  Align(
                      alignment: Alignment.bottomRight,
                      child: Obx(
                        () => CommonButton(
                            title: Get.find<TranslationHelper>()
                                .translations
                                .property
                                .propertyContinue,
                            isBorder: true,
                            isLoading: isBtnLoading.value,
                            onPressed: () async {
                              // Check selectedPayment only if:
                              // 1. isEnableWalletPay is false, OR
                              // 2. isEnableWalletPay is true AND totalAfterWallet != 0
                              if (!(isEnableWalletPay.value && totalAfterWallet.value == 0) && (paymentMethodId.value == 44 || paymentMethodId.value == null)) {
                                Get.dialog(WarningDialog(
                                  title: "warning".tr,
                                  description: "selectPaymentMethod".tr,
                                ));
                                return;
                              }
                              if (paymentMethodId.value == 100) {
                                isBtnLoading.value = true;
                                await initTabby(c);
                                if (tabbySession != null) {
                                  if (tabbySession!.availableProducts
                                          .installments?.webUrl !=
                                      null) {
                                    makePaymentUsingTabby(c.data.value!);
                                  }
                                  //tabbySession!.availableProducts.installments?.rejectionReason != null
                                  if (tabbySession?.status.name == 'rejected') {
                                    // String description = tabbySession!.availableProducts.installments?.rejectionReason == "under_limit"?
                                    // : Get.find<TranslationHelper>().translations.error.networkError;
                                    Get.dialog(WarningDialog(
                                        title: "paymentFailed".tr,
                                        keyword: DialogKeyword.info,
                                        description:
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .error
                                                .amountToLow));
                                    isBtnLoading.value = false;
                                  }
                                }

                                return;
                              }
                              if (c.data.value?.hasDiscount ?? false) {
                                makePayment(c.data.value!);
                                return;
                              }
                              Get.dialog(Obx(
                                () => SimpleDialog(
                                  title: Center(
                                      child: Text(Get.find<TranslationHelper>()
                                          .translations
                                          .propertySingle
                                          .couponCode!)),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(15)),
                                  contentPadding: const EdgeInsets.all(16),
                                  children: [
                                    if (c.data.value!.hasPromo == true ||
                                        c.data.value?.tamayouzId != null) ...[
                                      CommonButton(
                                          title: Get.find<TranslationHelper>()
                                              .translations
                                              .payment
                                              .removeCode,
                                          isBorder: true,
                                          buttonThemeColor:
                                              const Color(warningColor),
                                          isLoading: isBtnLoading.value,
                                          onPressed: () async {
                                            isBtnLoading.value = true;
                                            if (await removeCoupon(
                                                c.data.value!.id)) {
                                              Get.until((route) =>
                                                  Get.isBottomSheetOpen ==
                                                  false);
                                              refresh();
                                            }
                                            isBtnLoading.value = false;
                                          })
                                    ] else ...[
                                      if (discountObject.value == null)
                                        if (discountType.value != 't1') ...[
                                          discountCheckBox(
                                              'c',
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .modal
                                                  .haveCouponCode),
                                          if (Get.find<RemoteConfig>()
                                              .tamayouzEnabled)
                                            discountCheckBox(
                                                't', 'Have Tamayouz discount?'),
                                        ] else
                                          CustomText(
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .signUp
                                                  .enterCodeWeSent,
                                              color: const Color(greyText)),
                                      if (discountType.value != null)
                                        discountType.value == 't1'
                                            ? PinFieldAutoFill(
                                                codeLength: 4,
                                                controller:
                                                    AuthHelper.c.controller,
                                                decoration: CirclePinDecoration(
                                                    textStyle: TextStyle(
                                                        fontSize:
                                                            heightSpace(2.0),
                                                        color: Colors.black),
                                                    strokeColorBuilder:
                                                        FixedColorBuilder(Colors
                                                            .black
                                                            .withOpacity(0.3)),
                                                    gapSpace: 15),
                                                onCodeChanged: (code) {
                                                  if (code?.length == 4) {
                                                    onApplyTamayouz(
                                                        c.data.value);
                                                    FocusManager
                                                        .instance.primaryFocus
                                                        ?.unfocus();
                                                  }
                                                },
                                              )
                                            : CustomTextField(
                                                controller: couponController,
                                                errorText: couponError.value,
                                                isEnabled:
                                                    discountObject.value ==
                                                        null,
                                                inputType:
                                                    discountType.value == 't'
                                                        ? TextInputType.phone
                                                        : null,
                                                textCapitalization:
                                                    TextCapitalization
                                                        .characters,
                                                isRoundedBorder: true,
                                                suffix: discountObject.value !=
                                                        null
                                                    ? const Icon(
                                                        Icons.check_circle,
                                                        color: Colors.green)
                                                    : IconButton(
                                                        icon: const Icon(
                                                            Icons.send),
                                                        onPressed: () =>
                                                            onApplyCoupon(
                                                                c.data.value!)),
                                              ),
                                      SizedBox(height: heightSpace(3)),
                                      priceUI(
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            crossAxisAlignment: CrossAxisAlignment.center,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              CustomText("${c.data.value?.totalNights} ${Get.find<TranslationHelper>().translations.listingBasic.nights}"),
                                              const CustomText(" x "),
                                              CustomText("${c.data.value?.perNight} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}"),
                                            ],
                                          ),
                                          // "${c.data.value!.perNight} x ${c.data.value?.totalNights} ${Get.find<TranslationHelper>().translations.listingBasic.nights}",
                                          '${c.data.value?.basePrice?.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}'),
                                      if (c.data.value?.discountType != 'no_discount') ...[
                                        Divider(height: heightSpace(3)),
                                        priceUI(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .propertySingle
                                                .toJson()[
                                            c.data.value?.discountType],
                                            '- ${c.data.value!.youSaved} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}',
                                            color: const Color(warningColor))
                                      ],
                                      Divider(height: heightSpace(3)),
                                      priceUI(
                                          Get.find<TranslationHelper>()
                                              .translations
                                              .propertySingle
                                              .serviceFee,
                                          '${c.data.value?.serviceFee?.toStringAsFixed(2)}'),
                                      if ((c.data.value!.cleaningFee ?? 0) > 0) ...[
                                        Divider(height: heightSpace(3)),
                                        priceUI(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .propertySingle
                                                .cleaningFee,
                                            c.data.value!.cleaningFee
                                                ?.toStringAsFixed(2))
                                      ],
                                      if ((c.data.value!.securityFee ?? 0) > 0) ...[
                                        Divider(height: heightSpace(3)),
                                        priceUI(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .propertySingle
                                                .securityFee,
                                            c.data.value!.securityFee
                                                ?.toStringAsFixed(2))
                                      ],
                                      if ((c.data.value?.ivaTax ?? 0) > 0) ...[
                                        Divider(height: heightSpace(3)),
                                        priceUI(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .propertySingle
                                                .ivaTax,
                                            c.data.value!.ivaTax)
                                      ],
                                      Divider(
                                          height: heightSpace(3),
                                          color: Colors.grey[400]),
                                      priceUI(
                                          Get.find<TranslationHelper>().translations.payment.total,
                                          "${c.data.value!.total!.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}",
                                          color: Colors.blue),
                                      if (discountObject.value != null) ...[
                                        Divider(height: heightSpace(3)),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(
                                                Get.find<TranslationHelper>()
                                                    .translations
                                                    .payment
                                                    .discountType,
                                                size: 1.8,
                                                weight: FontWeight.w500),
                                            CustomText(
                                                discountObject
                                                    .value?.discountType,
                                                weight: FontWeight.w500,
                                                color: Colors.blueGrey),
                                          ],
                                        ),
                                        Divider(height: heightSpace(3)),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(
                                                Get.find<TranslationHelper>()
                                                    .translations
                                                    .payment
                                                    .discountValue,
                                                size: 1.8,
                                                weight: FontWeight.w500),
                                            CustomText(
                                                "${discountObject.value?.discountValue} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}",
                                                weight: FontWeight.w500,
                                                color:
                                                    const Color(warningColor)),
                                          ],
                                        ),
                                        Divider(height: heightSpace(3)),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            CustomText(
                                                Get.find<TranslationHelper>()
                                                    .translations
                                                    .payment
                                                    .amountAfterDiscount,
                                                size: 1.8,
                                                weight: FontWeight.w500),
                                            CustomText(
                                                "${(discountObject.value?.amountAfterDiscount ?? 0.0).toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}",
                                                weight: FontWeight.w500,
                                                color: Colors.green),
                                          ],
                                        )
                                      ],
                                      if (paymentDeductFromWallet > 0) ...[
                                        Divider(height: heightSpace(3)),
                                        priceUI(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .sidenav
                                                .wallet,
                                            '- ${paymentDeductFromWallet.value.toStringAsFixed(2)} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}',
                                            color: const Color(warningColor)),
                                        Divider(
                                            height: heightSpace(3),
                                            color: Colors.grey[400]),
                                        priceUI(
                                            Get.find<TranslationHelper>()
                                                .translations
                                                .withdraw
                                                .amount,
                                            totalAfterWallet.value
                                                .toStringAsFixed(2)),
                                      ],
                                      SizedBox(height: heightSpace(3)),
                                    ],
                                    CommonButton(
                                      title: Get.find<TranslationHelper>()
                                          .translations
                                          .property
                                          .continuE,
                                      fontSize: 2.2,
                                      isLoading: isBtnLoading.value,
                                      onPressed: (discountType.value != null &&
                                                  discountObject.value ==
                                                      null) ||
                                              lazyLoader.value
                                          ? null
                                          : () async {
                                        // Check selectedPayment only if:
                                        // 1. isEnableWalletPay is false, OR
                                        // 2. isEnableWalletPay is true AND totalAfterWallet != 0
                                        if (!(isEnableWalletPay.value && totalAfterWallet.value == 0) && (paymentMethodId.value == 44 || paymentMethodId.value == null)) {
                                          Get.dialog(WarningDialog(
                                            title: "warning".tr,
                                            description: "selectPaymentMethod".tr,
                                          ));
                                          return;
                                        }
                                              if (paymentMethodId.value == 100) {
                                                isBtnLoading.value = true;
                                                await initTabby(c);
                                                if (tabbySession != null &&
                                                    tabbySession!
                                                            .availableProducts
                                                            .installments
                                                            ?.webUrl !=
                                                        null) {
                                                  makePaymentUsingTabby(
                                                      c.data.value!);
                                                }
                                                //tabbySession?.availableProducts.installments?.rejectionReason != null
                                                if (tabbySession?.status.name ==
                                                    'rejected') {
                                                  // String description = tabbySession!.availableProducts.installments?.rejectionReason == "under_limit"
                                                  //     ?
                                                  //     : Get.find<TranslationHelper>().translations.error.networkError;
                                                  Get.dialog(WarningDialog(
                                                      title: "paymentFailed".tr,
                                                      keyword:
                                                          DialogKeyword.info,
                                                      description: Get.find<
                                                              TranslationHelper>()
                                                          .translations
                                                          .error
                                                          .amountToLow));
                                                  isBtnLoading.value = false;
                                                }
                                              }
                                              else {
                                                makePayment(c.data.value!);
                                              }
                                            },
                                    ),
                                  ],
                                ),
                              )).then((value) {
                                discountType.value = null;
                                discountObject.value = null;
                                couponError.value = null;
                                AuthHelper.c.controller.clear();
                                couponController.clear();
                              });
                            }),
                      ))
                ],
              ),
            ),
          );
        })).then((value) {
      isBtnLoading.value = false;
      refresh();
    });
  }

  priceUI(title, val, {color = Colors.black}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if(title is String)...[
          CustomText(title, size: 1.8, weight: FontWeight.w500)
        ]
        else...[
          title
        ],
        CustomText("$val", weight: FontWeight.w500, color: color),
      ],
    );
  }

  discountCheckBox(val, title) {
    return CheckboxListTile(
      title: Row(
        children: [
          Expanded(
            child: CustomText(
              title,
              size: 2.3,
              weight: FontWeight.w400,
            ),
          ),
          SizedBox(width: widthSpace(6))
        ],
      ),
      value: discountType.value == val,
      onChanged: (value) {
        if (discountType.value == val) {
          discountType.value = null;
        } else {
          discountType.value = val;
        }
      },
    );
  }

  removeCoupon(int? id) async {
    final response = await ApiServices.postApi('v1/bookings/$id/remove/coupon',
        isAuth: true);
    return response.status;
  }

  AnalyticsEventItem getEventItem(ReservationDetailModel reservation) {
    int i = SearchHelper.c.data
        .indexWhere((item) => item.id == reservation.propertyId);
    return AnalyticsEventItem(
        itemId: reservation.propertyId.toString(),
        affiliation: '',
        discount: reservation.totalDiscount,
        index: i + 1,
        price: (reservation.subTotal ?? 0) / 1000000,
        quantity: reservation.totalNights,
        parameters: {
          'item_type': reservation.propertyType ?? '',
          'item_city_name': reservation.city ?? '',
          'item_host_id': reservation.hostId ?? 0,
          'total_price': reservation.total ?? 0,
        });
  }

  makePayment(ReservationDetailModel reservation) async {
    isBtnLoading.value = true;
    Map formData = {
      "bookingId": reservation.id,
      "paymentMethodId": paymentMethodId.value,
      "pay_by_wallet": isEnableWalletPay.value ? 1 : 0,
    };
    formData['api_version'] = 1;
    if (discountObject.value != null) {
      if ((discountObject.value?.amountAfterDiscount ?? 0.0) <= 0) {
        Get.until((route) => !Get.isDialogOpen! && !Get.isBottomSheetOpen!);
        isBtnLoading.value = false;
        Get.dialog(WarningDialog(
            title: "Error",
            description:
                "We apologize for any inconvenience, Contact to Admin for further assistance. Booking Id: ${reservation.id}"));
        return;
      }
      formData['coupon'] = couponController.text;
    }
    if (reservation.paymentGetway != 'hyperpay' &&
        paymentMethodId.value == 2 &&
        AuthHelper.c.myCards.isEmpty) {
      final cardFormData =
          await AuthHelper.c.showAddCardSheet(fromCheckout: true);
      if (cardFormData == null) {
        isBtnLoading.value = false;
        return;
      } else {
        formData.addAll(cardFormData);
      }
    }
    Get.back();
    ResponseModel response = await ApiServices.postApi("v1/makepayment",
        body: formData, isAuth: true);
    isBtnLoading.value = false;
    discountType.value = null;
    couponError.value = null;
    if (response.status) {
      Get.until((route) =>
          route.settings.name?.contains(Routes.reservationDetail) == true);
      if (isEnableWalletPay.value &&
          (response.data['fullPaymentByWallet'] ?? false)) {
        ReservationDetailController c = Get.find();

        // Track payment success event with Analytics Manager
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.paymentCompleted,
          eventAttributes: {
            AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
            AnalyticsAttributes.bookingId: c.data.value?.id?.toString(),
            AnalyticsAttributes.propertyId:
                c.data.value?.propertyId?.toString(),
            AnalyticsAttributes.paymentMethod: 'wallet',
            AnalyticsAttributes.paymentAmount:
                response.data['total']?.toString(),
            AnalyticsAttributes.paymentCurrency: "SAR",
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
          },
        );

        getBookings();
        Timer(const Duration(seconds: 1), () {
          AuthHelper.c.getCards();
          AuthHelper.c.getTransactions();
        });
        Get.until((route) => route.isFirst);
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>()
                .translations
                .listing
                .paymentSuccessful!,
            displayTime: 1500);
        SearchHelper.c.changeIndex(2);
        if (response.data['is_yaqeen_verified'] != "1" &&
            Get.isDialogOpen == false &&
            kReleaseMode) {
          Get.dialog(const YaqeenVerificationDialog());
        }
        placeReservationCompleted(c.data.value!);
        purchaseEvent(
            "SAR",
            "${response.data['transaction_id']}",
            response.data['total'],
            response.data['tax'] is int
                ? response.data['tax'].toDouble()
                : (response.data['tax'] ?? 0.0),
            reservation.bookingType!,
            DateFormat('MMMM').format(reservation.startDate!),
            reservation.totalNights ?? 0,
            getEventItem(reservation),
            coupon: couponController.text);
        return;
      }
      if (paymentMethodId.value == 2 ||
          paymentMethodId.value == 101 ||
          paymentMethodId.value == 12) {
        // Uri paymentUrl = Uri.parse(response.data['paymentURL']);
        Get.to(() => MyFatoorahScreen(
            url: response.data['paymentURL'],
            logEvent: () {
              purchaseEvent(
                  "SAR",
                  "${response.data['transaction_id']}",
                  response.data['total'],
                  response.data['tax'] is int
                      ? response.data['tax'].toDouble()
                      : (response.data['tax'] ?? 0.0),
                  reservation.bookingType!,
                  DateFormat('MMMM').format(reservation.startDate!),
                  reservation.totalNights ?? 0,
                  getEventItem(reservation),
                  coupon: couponController.text);
              placeReservationCompleted(reservation);
            },
            isDisplayYaqeen: response.data['is_yaqeen_verified'] != "1"));
        return;
      }
      if (response.data['payment_service'] == "Fatoorah") {
        var request = MFExecutePaymentRequest(
            invoiceValue: response.data['total'] is Double
                ? response.data['total']
                : response.data['total'].toDouble());
        request.paymentMethodId = paymentMethodId.value ?? 6;
        request.customerReference = response.data['customer_ref'];
        request.customerName = userModel.value!.first_name;
        request.customerEmail = userModel.value!.email;

        await MFSDK.executePayment(request, MFLanguage.ENGLISH, (invoiceId) {
          debugPrint(invoiceId);
        }).then((result) {
          // Track payment success event with Analytics Manager
          Get.find<AnalyticsManager>().trackEvent(
            AnalyticsEvents.paymentCompleted,
            eventAttributes: {
              AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
              AnalyticsAttributes.bookingId: reservation.id?.toString(),
              AnalyticsAttributes.propertyId:
                  reservation.propertyId?.toString(),
              AnalyticsAttributes.paymentMethod: paymentMethodId.value == 2
                  ? 'credit_card'
                  : paymentMethodId.value == 101
                      ? 'mada'
                      : paymentMethodId.value == 12
                          ? 'stc_pay'
                          : 'other',
              AnalyticsAttributes.paymentAmount:
                  response.data['total']?.toString(),
              AnalyticsAttributes.paymentCurrency: "SAR",
              AnalyticsAttributes.sessionTimestamp:
                  DateTime.now().toIso8601String(),
            },
          );

          purchaseEvent(
              "SAR",
              "${response.data['transaction_id']}",
              response.data['total'],
              response.data['tax'] is int
                  ? response.data['tax'].toDouble()
                  : (response.data['tax'] ?? 0.0),
              reservation.bookingType!,
              DateFormat('MMMM').format(reservation.startDate!),
              reservation.totalNights ?? 0,
              getEventItem(reservation),
              coupon: couponController.text);
          placeReservationCompleted(reservation);
          Timer(const Duration(seconds: 1), () {
            AuthHelper.c.getCards();
            AuthHelper.c.getTransactions();
          });
          getBookings();
          Get.until((route) => route.isFirst);
          Get.dialog(
            WarningDialog(
                title: "congratulations".tr,
                keyword: DialogKeyword.success,
                description: "propertyReserved".trArgs([
                  reservation.propertyName ?? "",
                  formData['checkin'],
                  formData['checkout']
                ])),
          );
        }).catchError((error) {
          debugPrint(error.toString());
          Get.dialog(WarningDialog(
              title: "paymentFailed".tr, description: error ?? ""));
        });
        return;
      } else {
        Get.to(() => MyFatoorahScreen(
            url: response.data['payment_service'],
            logEvent: () {
              purchaseEvent(
                  "SAR",
                  "${response.data['transaction_id']}",
                  response.data['total'],
                  response.data['tax'] is int
                      ? response.data['tax'].toDouble()
                      : (response.data['tax'] ?? 0.0),
                  reservation.bookingType!,
                  DateFormat('MMMM').format(reservation.startDate!),
                  reservation.totalNights ?? 0,
                  getEventItem(reservation),
                  coupon: couponController.text);
              placeReservationCompleted(reservation);
            },
            isDisplayYaqeen: response.data['is_yaqeen_verified'] != "1"));
        isBtnLoading.value = false;
        return;
      }
    }
  }

  makePaymentUsingTabby(ReservationDetailModel reservation) async {
    ReservationDetailController c = Get.find();
    TabbyWebView.showWebView(
      // onWillPop: (){
      //   isBtnLoading.value = false;
      //   Get.until((route) => route.settings.name?.contains(Routes.reservationDetail)==true);
      //   Get.dialog(WarningDialog(
      //       title: "paymentFailed".tr,
      //       description: ""));
      // },
      context: Get.context!,
      webUrl: tabbySession!.availableProducts.installments!.webUrl!,
      onResult: (WebViewResult resultCode) async {
        if (kDebugMode) {
          print("tabby result=$resultCode");
        }
        if (resultCode.name == "authorized") {
          // Track payment success event with Analytics Manager
          Get.find<AnalyticsManager>().trackEvent(
            AnalyticsEvents.paymentCompleted,
            eventAttributes: {
              AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
              AnalyticsAttributes.bookingId: reservation.id?.toString(),
              AnalyticsAttributes.propertyId:
                  reservation.propertyId?.toString(),
              AnalyticsAttributes.paymentMethod: 'tabby',
              AnalyticsAttributes.paymentAmount:
                  (c.data.value!.total!).toString(),
              AnalyticsAttributes.paymentCurrency: "SAR",
              AnalyticsAttributes.sessionTimestamp:
                  DateTime.now().toIso8601String(),
            },
          );

          purchaseEvent(
              "SAR",
              reservation.bookingCode ?? '',
              c.data.value!.total!,
              reservation.accommodationTax!,
              reservation.bookingType!,
              DateFormat('MMMM').format(reservation.startDate!),
              reservation.totalNights ?? 0,
              getEventItem(reservation),
              coupon: couponController.text);
          placeReservationCompleted(reservation);
          getBookings();
          Timer(const Duration(seconds: 1), () {
            AuthHelper.c.getCards();
            AuthHelper.c.getTransactions();
          });
          Get.until((route) => route.isFirst);
          Get.dialog(
            WarningDialog(
                title: "congratulations".tr,
                keyword: DialogKeyword.success,
                description: "propertyReserved".trArgs([
                  "",
                  formatter.format(reservation.startDate!),
                  formatter.format(reservation.endDate!),
                ])),
          );
        } else {
          isBtnLoading.value = false;
          Get.until((route) =>
              route.settings.name?.contains(Routes.reservationDetail) == true);
          Get.dialog(WarningDialog(
              title: "paymentFailed".tr,
              description: "Payment ${resultCode.name}"));
        }
      },
    );
  }

  initMoyasar(var response) {
    // remember Moyasar is considering amount in halalas(same as cents iin dolor or paisa in rupee) not in riyals so sending amount with
    // multiple of 100.
    var amount = int.parse((response.data['total'] * 100).toStringAsFixed(0));
    PaymentConfig? paymentConfig;
    try {
      paymentConfig = PaymentConfig(
        publishableApiKey: moyasarTestAPIKey,
        amount: amount,
        description: 'booking_id #${response.data['booking_id']}',
        metadata: {
          'booking': '${response.data['booking_id']}',
          'walletDeduction': isEnableWalletPay.value.toString(),
        }, //customer_ref
        creditCard: CreditCardConfig(saveCard: true, manual: true),
        applePay: ApplePayConfig(
          merchantId: 'merchant.darent.com',
          label: 'Darent',
          manual: false,
        ),
      );
    } catch (e) {
      if (kDebugMode) {
        ViewsCommon.showSnackbar(e.toString(), displayTime: 1500);
        print("onApplePay Error paymentConfig $e");
        return;
      }
    }
    ViewsCommon.showModalBottom(
        DraggableScrollableSheet(
            maxChildSize: .30,
            initialChildSize: .30,
            expand: false,
            builder: (context, scrollController) {
              return Column(
                children: [
                  SizedBox(height: heightSpace(3)),
                  ApplePay(
                    config: paymentConfig!,
                    onPaymentResult: (result) {
                      onPaymentResult(result, response);
                    },
                  ),
                ],
              ).paddingAll(widthSpace(viewPadding));
            }), then: (value) {
      isBtnLoading.value = false;
    });
  }

  void onPaymentResult(result, var response) {
    ReservationDetailController c = Get.find();
    if (result is PaymentResponse) {
      if (result.status == PaymentStatus.paid) {
        // Track payment success event with Analytics Manager
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.paymentCompleted,
          eventAttributes: {
            AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
            AnalyticsAttributes.bookingId: c.data.value?.id?.toString(),
            AnalyticsAttributes.propertyId:
                c.data.value?.propertyId?.toString(),
            AnalyticsAttributes.paymentMethod: 'apple_pay',
            AnalyticsAttributes.paymentAmount:
                (c.data.value?.total ?? 0).toString(),
            AnalyticsAttributes.paymentCurrency: "SAR",
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
          },
        );

        purchaseEvent(
            "SAR",
            response['transaction_id'] ?? result.id,
            c.data.value?.total ?? 0,
            c.data.value!.accommodationTax!,
            c.data.value!.bookingType!,
            DateFormat('MMMM').format(c.data.value!.startDate!),
            c.data.value!.totalNights ?? 0,
            getEventItem(c.data.value!),
            coupon: couponController.text);
        placeReservationCompleted(c.data.value!);
        getBookings();
        Timer(const Duration(seconds: 1), () async {
          await AuthHelper.c.getCards();
          double balanceBefore = AuthHelper.c.myWallet.first.balance ?? 0;
          if (balanceBefore == AuthHelper.c.myWallet.first.balance) {
            deductionManually(c.data.value!.total!, response.data['total']);
          }
          AuthHelper.c.getTransactions();
        });
        getBookings();
        Get.until((route) => route.isFirst);
        if (!userModel.value!.yaqeenVerified) {
          Get.dialog(const YaqeenVerificationDialog());
        } else {
          Get.dialog(
            WarningDialog(
                title: "congratulations".tr,
                keyword: DialogKeyword.success,
                description: "propertyReserved".trArgs([
                  "",
                  DateFormat('dd-MM-yyyy').format(c.data.value!.startDate!),
                  DateFormat('dd-MM-yyyy').format(c.data.value!.endDate!),
                ])),
          );
        }
      } else {
        isBtnLoading.value = false;
        Get.back();
        Get.dialog(WarningDialog(title: "paymentFailed".tr, description: ""));
      }
    } else {
      isBtnLoading.value = false;
      Get.back();
      Get.dialog(WarningDialog(title: "paymentFailed".tr, description: ""));
    }
  }

  final formatter = DateFormat("dd-MM-yyyy");

  deductionManually(total, finalAmountNeedToPay) {
    double amountDeductFromWallet = total - finalAmountNeedToPay;

    AuthHelper.c.myWallet.first.balance =
        AuthHelper.c.myWallet.first.balance! - amountDeductFromWallet;
    AuthHelper.c.myWallet.refresh();
  }

  placeReservationCompleted(ReservationDetailModel data) {
    final now = DateTime.now();
    Map<String, dynamic> myMap = {
      "Cancellation Policy": '${data.cancelPolicy}',
      "Checkin Date": formatter.format(data.startDate!),
      "Checkout Date": formatter.format(data.endDate!),
      "Checkin Time": "${data.checkinTime}",
      "Checkout Time": "${data.checkoutTime}",
      "User": isHost ? "Host" : "Customer",
      "Reservation number": "${data.bookingCode}",
      "Cost Per Night": "${data.perNight ?? 0.0}",
      "Unit Code": "${data.unitCode}",
      "Date": "${now.day}/${now.month}/${now.year}",
      "Number of Adults": data.adults ?? 0,
      "Number of Children": data.children ?? 0,
      "Service fees": '${data.serviceFee ?? 0}',
      "Total": '${data.total}',
    };
    if (isHost) {
      myMap['User Customer'] = "Host";
    } else {
      myMap['User Host'] = "Customer";
    }
    if (data.latitude != null && data.longitude != null) {
      myMap['Location'] =
          'https://www.google.com/maps/search/?api=1&query=${data.latitude},${data.longitude}';
    }
    if (kDebugMode) {
      print("Place Reservation Completed dashboard controller $myMap ");
    }
    WebEngagePlugin.trackEvent('Place Reservation Completed', myMap);
  }

  onApplyCoupon(ReservationDetailModel reservation) {
    if (couponController.text.isEmpty) {
      couponError.value = 'emptyError'.tr;
      return;
    } else {
      couponError.value = null;

      if (discountType.value == 'c') {
        Map formData = {
          "code": couponController.text,
          "propert_id": reservation.propertyId,
          "amount": ((reservation.total ?? 0.0)),
          "checkin": reservation.startDate.toString(),
          "checkout": reservation.endDate.toString()
        };
        ApiServices.postApi("v2/coupon/check",
                body: formData, isAuth: true, allowDialog: false)
            .then((response) {
          if (response.message == 'campaign') {
            response.data['discount_type'] = 'campaign';
            discountObject.value = PromoDataModel.fromJson(response.data);
            return;
          }
          setToPromoModel(response, reservation);
        });
      } else {
        sendTamayouzOtp(reservation.propertyId);
      }
    }
  }

  setToPromoModel(response, reservation) {
    if (response.status) {
      if (response.message == 'campaign') {
        response.data['discount_type'] = 'campaign';
        discountObject.value = PromoDataModel.fromJson(response.data);
        return;
      }
      response.data['amount_after_discount'] = (response.data['amount_after_discount'] ?? 0.0);
              // +(reservation.serviceFeeCleaning ?? 0.0) +
              // (reservation.serviceFeeSecurity ?? 0.0) +
              // (reservation.additionalGuest ?? 0.0) +
              // (reservation.ivaTax ?? 0.0) +
              // (reservation.accommodationTax ?? 0.0) +
              // (reservation.securityFee ?? 0.0) +
              // (reservation.cleaningFee ?? 0.0); //(reservation.hostFee??0.0)
      discountObject.value = PromoDataModel.fromJson(response.data);
      if (isEnableWalletPay.value) calculateAmountNeedToDeductFromWallet();
    } else {
      couponError.value = response.message;
      ViewsCommon.showSnackbar(response.message);
    }
  }

  sendTamayouzOtp(id) async {
    couponError.value = null;
    Map form = {'phone': couponController.text, 'property_id': id};
    final res = await ApiServices.postApi("v1/tamayouz/otp/send",
        body: form, isAuth: true, allowDialog: false);
    if (res.status) {
      discountType.value = 't1';
    } else {
      couponError.value = res.message;
    }
  }

  onApplyTamayouz(ReservationDetailModel? data) async {
    Map formData = {
      "phone": couponController.text,
      "property_id": data?.propertyId,
      "amount": data?.basePrice ?? 0.0,
      "otp": AuthHelper.c.controller.text
    };
    if (AuthHelper.c.phone.text != userModel.value?.formatted_phone) {
      AuthHelper.c.phone.text = userModel.value?.formatted_phone ?? '';
    }
    final response = await ApiServices.postApi("v1/tamayouz/otp/verify",
        body: formData, isAuth: true, allowDialog: false);
    //{"status":200,"tamayouz_id":20,"amount":"456","amount_after_discount":496.15999999999997,"discount_value":"10","servicefee":50.16}
    response.data['discount_type'] =
        Get.find<TranslationHelper>().translations.propertySingle.tamayouz;
    setToPromoModel(response, data);
    discountType.value = 't';
  }

  switchMode({index = 0}){
    isHost = !isHost;
    if (!isHost) {
      changeIndex(index);
    }
    if (Get.isRegistered<ChatController>()) {
      ChatController c = Get.find();
      c.getChatHeads();
    }
    String route = isHost ? '${Routes.hostHome}?index=$index' : Routes.home;
    bool currentEquals = route==Routes.home?Get.currentRoute==route:Get.currentRoute.contains(route);
    if(!currentEquals){
      Get.offAllNamed(route);
    }
    GlobalHelper.storageBox.write("isHost", isHost);
  }

  String? toRedirect;

  void handleMessage(Map message) async {
    if (kDebugMode) print('Messsage received: $message');
    if (message['slug'] == "booking") {
      String tab = message['tab'] ?? "ongoing-bookings";

      final Map<String, String> parameters = {
        'tab': tab,
        if (message['code'] != null) 'code': message['code'],
      };

      final BookingsController c;
      if (isHost) {
        if (!Get.isRegistered<BookingsController>()) {
          c = Get.put(BookingsController(val: tab, code: message['code']));
          // Future.delayed(const Duration(milliseconds: 1000), () {
          //   Get.toNamed(Routes.hostReservations, parameters: parameters);
          // });
        } else {
          c = Get.find();
          // Get.toNamed(Routes.hostReservations, parameters: parameters);
          c.bookingCode = message['code'];
          c.onInit();
        }
        c.changeTabBasedOnSlug(tab);
      } else {
        switchMode();
        if (!Get.isRegistered<BookingsController>()) {
          c = Get.put(BookingsController(val: tab, code: message['code']));
        } else {
          c = Get.find();
          c.changeTabBasedOnSlug(tab);
        }
        await Future.delayed(const Duration(seconds: 1));
      }
      String route = Routes.hostReservations;
      if (Get.find<TranslationHelper>().translateKeywords.isEmpty) {
        toRedirect = Uri(path:route,queryParameters: parameters).toString();
      } else {
        Future.delayed(const Duration(milliseconds: 1000), () {
          Get.toNamed(route, parameters: parameters);
        });
        // Get.toNamed(route,parameters: parameters);
      }
      // if(Get.find<TranslationHelper>().translateKeywords.isEmpty){
      //   toRedirect = '${Routes.hostReservations}?tab=${message['tab']}';
      // }else{
      //   Future.delayed(const Duration(milliseconds: 500),(){
      //     Get.toNamed(Routes.hostReservations,parameters: {'tab':message['tab']});
      //   });
      // }
    } else if (message['slug'] == "reservation") {
      reservationTabController.animateTo(message['tab'] is String
          ? int.parse(message['tab'])
          : message['tab']);
      if (isHost) {
        switchMode(index: 2);
      } else {
        changeIndex(2);
      }
      if (Get.currentRoute != Routes.home) {
        Get.until((route) => route.isFirst);
      }
      bool isAccepted =
          message['is_accepted'] == true || message['is_accepted'] == '1';
      if (userModel.value!.showYaqeenReservation &&
          !userModel.value!.yaqeenVerified &&
          isAccepted) {
        Get.dialog(const YaqeenVerificationDialog());
      } else if (message['booking_id'] != null) {
        String route =
            '${Routes.reservationDetail}/${message['booking_id']}?review=${message['for_review'] == true || message['for_review'] == '1'}';
        if (Get.find<TranslationHelper>().translateKeywords.isEmpty) {
          toRedirect = route;
        } else {
          Get.toNamed(route);
        }
      }
    } else if (message['slug'] == "guest_review") {
      changeIndex(4);
      Get.toNamed(Routes.profile);
    } else if (message['slug'] == "host_review") {
      // late HostDashboardController hostC;
      // if(!isHost){
      //   isHost = true;
      //   hostC = Get.put(HostDashboardController());
      //   Get.offAll(() => const Host(index: ));
      // }else{
      //   hostC = Get.find();
      // }
      // hostC.changeIndex(4);
      // Get.to(() => const Profile());
      // } else if (message['slug'] == "hosthome") {
      //   if (!isHost) {
      //     isHost = true;
      //     Get.offAll(() => Host());
      //   } else {
      //     HostDashboardController hostC = Get.find();
      //     if (hostC.index <4) {
      //       hostC.changeIndex(4);
      //     }
      //     Future.delayed(const Duration(milliseconds: 500),() {
      //       Get.toNamed(Routes.dwellings);
      //     });
      //   }
      //   }
    } else if (message['slug'].contains(Routes.inbox)) {
      if (message['slug'].contains('host')) {
        if (isHost) {
          final HostDashboardController hostC;
          if (!Get.isRegistered<HostDashboardController>()) {
            hostC = Get.put(HostDashboardController());
            await Future.delayed(const Duration(milliseconds: 600));
            hostC.changeIndex(2);
          } else {
            hostC = Get.find();
            await Future.delayed(const Duration(milliseconds: 600));
            hostC.changeIndex(2);
          }
        } else {
          switchMode(index: 2);
        }
      } else {
        if (isHost) {
          switchMode(index: 3);
        } else {
          changeIndex(3);
        }
      }
      if (message['chat_head_id'] != null) {
        if (Get.find<TranslationHelper>().translateKeywords.isEmpty) {
          toRedirect = message['chat_head_id'];
        } else {
          ChatHelper.gotoChats(message['chat_head_id']);
        }
      }
    } else if (message['slug'] == "dwelling") {
      Map data = message['value'] is Map
          ? message['value']
          : jsonDecode(message['value']);
      if (!isHost) {
        switchMode();
        await Future.delayed(const Duration(milliseconds: 100));
      }
      HostDashboardController host = Get.find();
      if (data.containsKey("id")) {
        if (data['id'] == "listed") {
          host.changeDwellingTab("Listed");
        }
        Get.toNamed(Routes.dwellings);
      } else {
        host.getDwellingDetail(data['unit_code']);
        Get.toNamed(Routes.editMain)!.then((value) {
          host.getDwellings();
          host.dwellingDetail = null;
        });
      }
    } else if (message['slug'] == "properties") {
      if (!isHost) {
        switchMode(index: 3);
      } else {
        Get.until((route) => route.isFirst);
        HostDashboardController host = Get.find();
        host.changeIndex(3);
      }
    } else if (message['slug'] == "complete-listing") {
      if (!isHost) {
        switchMode();
      }
      if (Get.find<TranslationHelper>().translateKeywords.isEmpty) {
        toRedirect = '${Routes.dwellings}?code=${message['property_code']}';
      } else {
        await Future.delayed(const Duration(milliseconds: 150));
        Get.toNamed(Routes.dwellings);
        DwellingDetail? dwelling =
            await ListingHelper.c.getDwellingDetail(message['property_code']);
        if (dwelling != null) {
          ListingHelper.completeListing(dwelling);
        }
      }
    } else if (message['slug'] == 'wallet_deposit') {
      if (isHost) {
        switchMode(index: 2);
      }
      if (Get.find<TranslationHelper>().translateKeywords.isEmpty) {
        toRedirect = Routes.walletPaymentRoute;
      } else {
        Get.toNamed(Routes.walletPaymentRoute);
      }
    } else if (Routes.customerServices.contains('customer-services')) {
      Get.toNamed(Routes.customerServices);
    }
  }

  updateOnNotify(Map message) {
    if (kDebugMode) {
      print("updateOnNotify ; ${message['slug']} and $message");
    }
    if (isUser) {
      if (message['slug'] != null) {
        if (message['slug'] == "chat") {
          //todo: chat notification
          if (Get.currentRoute == "/InboxWindow") {
            // InboxWindowController inboxW = Get.find();
            // inboxW.getData();
          }
        } else if (message['slug'] == "hosthome") {
          if (Get.currentRoute == Routes.dwellings) {
            HostDashboardController hostC = Get.find();
            hostC.getDwellings();
          }
        } else if (message['slug'] == "booking") {
          if (isHost) {
            if (Get.isRegistered<BookingsController>()) {
              BookingsController bookingsC = Get.find();
              bookingsC.getData(refresh: true);
            }
          }
        } else if (message['slug'] == "reservation") {
          if (message['value'] != null) {
            final json = jsonDecode(message['value']);
            if (json['id'] == "Cancelled") {
              getCancelledBookings();
              if (Get.isRegistered<ReservationDetailController>()) {
                ReservationDetailController c = Get.find();
                c.getData();
              }
            }
          }
          AuthHelper.c.getCards();
          getBookings();
        }
        // else if (message['slug'].contains("review")) {
        //   AuthHelper.c.getReviews();
        // }
        else if (message['slug'] == "dwelling") {
          if (message['value'] != null) {
            Map data = jsonDecode(message['value']);
            if (data.containsKey("id")) {
              if (isHost) {
                HostDashboardController c = Get.find();
                if (c.dwellingTab.value != "Listed") {
                  c.changeDwellingTab('Listed');
                }
                c.getDwellings(refresh: true);
                //c.dwellings.value.isEmpty;
              }
            } else {
              if (data["status"] == 'rejected') {
                hostPropertyRejectedEvent(message);
              } else if (data["status"] == 'approved') {
                hostPropertyApprovedEvent(message);
              }
            }
          }
        } else if (message['slug'] == 'wallet_deposit') {
          AuthHelper.c.getCards();
          AuthHelper.c.getTransactions();
        } else if (message['slug'] == 'iqama_update') {
          // if received this slug then it means iqama of this user has been
          // updated and we will update that updated value (coming in notification) in user model
          if (message['value'] != null) {
            final json = jsonDecode(message['value']);
            userModel.value?.yaqeenVerified = json['iqama'];
            userModel.value!.showYaqeenReservation = !json['iqama'];
            GlobalHelper.storageBox.write('user', userModel.value?.toJson());
            userModel.refresh();
          }
        }
      }
      getNotifications(refresh: true);
    }
  }

  hostPropertyApprovedEvent(Map data) {
    final json = jsonDecode(data['value']);
    Map<String, dynamic> mapData = {
      "Host id": '${userModel.value?.uuid ?? userModel.value?.id}',
      "Unit Code": json["unit_code"] ?? "",
      "Approval Date": formDateFormat.format(DateTime.now()),
    };
    if (kDebugMode) {
      print('Host property approved $mapData');
    }

    // Track host property approved event with WebEngage (existing)
    WebEngagePlugin.trackEvent('Host property approved', mapData);

    // Track host property approved event with Analytics Manager
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.hostPropertyApproved,
      eventAttributes: {
        AnalyticsAttributes.hostId: userModel.value?.id?.toString(),
        AnalyticsAttributes.propertyId: json["property_id"]?.toString(),
        AnalyticsAttributes.hostApprovalStatus: 'approved',
        AnalyticsAttributes.timestamp: DateTime.now().toIso8601String(),
        'unit_code': json["unit_code"] ?? "",
      },
    );
  }

  hostPropertyRejectedEvent(Map data) {
    final json = jsonDecode(data['value']);
    Map<String, dynamic> mapData = {
      "Host id": '${userModel.value?.uuid ?? userModel.value?.id}',
      "Unit Code": json["unit_code"] ?? "",
      "Rejection Date": formDateFormat.format(DateTime.now()),
      "Rejection Reason ": json["rejection_reason"] ?? "",
    };

    if (kDebugMode) {
      print('Host property rejected $mapData');
    }

    // Track host property rejected event with WebEngage (existing)
    WebEngagePlugin.trackEvent('Host new property rejected', mapData);

    // Track host property rejected event with Analytics Manager
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.hostPropertyRejected,
      eventAttributes: {
        AnalyticsAttributes.hostId: userModel.value?.id?.toString(),
        AnalyticsAttributes.propertyId: json["property_id"]?.toString(),
        AnalyticsAttributes.hostApprovalStatus: 'rejected',
        AnalyticsAttributes.hostRejectionReason: json["rejection_reason"] ?? "",
        AnalyticsAttributes.timestamp: DateTime.now().toIso8601String(),
        'unit_code': json["unit_code"] ?? "",
      },
    );
  }
  getPrice(HomeProperty item) async {
    priceLoading.value = true;
    Map formData = {
      "property_id": item.id,
      "checkin": formDateFormatCservice.format(checkin),
      "checkout": formDateFormatCservice.format(checkout),
      // "guest_count": adults.value + children.value
    };
    ResponseModel response =await ApiServices.postApi("v1/properties/get-price", body: formData);
    if (response.status) {
      if (response.data['status'] != null) {
        String message = response.data['status'] == "nights become max"
            ? 'maxWarn'.trArgs(["${response.data['max_nights']}"])
            : response.data['status'] == "nights become min"
            ? 'minWarn'.trArgs(["${response.data['min_nights']}"])
            : response.data['status'];

        priceMessage.value = message;
        item.priceError = message;
      }
      item.propertyPrice = PropertyCheckoutPrice.fromJson(response.data);
    }
    priceLoading.value = false;
  }
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      if (isUser) {
        reloadOnResume();
      }
    } else if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive) {
      // Track guest inactive event with Analytics Manager
      if (isUser && !isHost) {
        // Track if inactive for more than 5 minutes (300,000 ms)
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.guestInactive,
          eventAttributes: {
            AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
            AnalyticsAttributes.guestLastInteractionType: 'app_backgrounded',
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
          },
        );
      }
    }
  }
}
