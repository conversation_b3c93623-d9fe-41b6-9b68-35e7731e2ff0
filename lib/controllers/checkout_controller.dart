import 'dart:async';

import 'package:darent/analytics/analytics.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/components/warning_dialog.dart';
import 'package:darent/components/yaqeen_verification_dialog.dart';
import 'package:darent/controllers/property_detail_controller.dart';
import 'package:darent/helperMethods/deepLink_helper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/payment_helper.dart';
import 'package:darent/models/promoDataModel.dart';
import 'package:darent/models/propertyCheckoutPrice.dart';
import 'package:darent/screens/myfatoorah_screen.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:moyasar/moyasar.dart';
import 'package:myfatoorah_flutter/myfatoorah_flutter.dart';
import 'package:tabby_flutter_inapp_sdk/tabby_flutter_inapp_sdk.dart';
import 'package:webengage_flutter/webengage_flutter.dart';

import '../helperMethods/search_helper.dart';
import '../helperMethods/translation_helper.dart';
import '../helperMethods/authHelper.dart';
import '../utils/routes.dart';

class CheckoutController extends GetxController {
  final formatter = DateFormat("dd-MM-yyyy");

  final discountTab = 'c'.obs;
  TextEditingController couponController = TextEditingController();
  final couponError = RxnString();
  final discountObject = Rxn<PromoDataModel>();
  final PropertyDetailController propertyC;
  int? bookingId;
  CheckoutController(this.propertyC);
  final selectedPayment = RxnInt();
  final paymentDeductFromWallet = RxnDouble(0.0);
  final totalAfterWallet = RxnDouble(0.0);
  final isEnableWalletPay = RxBool(false);
  final isLoading = false.obs;
  final calenderFormat = DateFormat("dd-MM-yyyy");
  final propertyPrice = Rxn<PropertyCheckoutPrice>();
  selectPayment(val) {
    selectedPayment.value = val;
    if (discountObject.value != null && selectedPayment.value == 100) {
      removeCoupon();
    }
    if (isEnableWalletPay.value && selectedPayment.value == 100) {
      unSelectWalletPay();
    }
  }

  unSelectWallet() {
    totalAfterWallet.value = 0;
    paymentDeductFromWallet.value = 0;
    isEnableWalletPay.value = false;
  }

  Future<bool> calculateAmountNeedToDeductFromWallet() async{
    double subTotal = propertyPrice.value!.totalDiscount ??
        propertyPrice.value!.totalWithDiscount!;
    if (AuthHelper.c.myWallet.first.balance! >= subTotal) {
      paymentDeductFromWallet.value = subTotal;
      totalAfterWallet.value = 0;
      return true;
    } else {
      paymentDeductFromWallet.value = AuthHelper.c.myWallet.first.balance;
      double remaining = subTotal - paymentDeductFromWallet.value!;
      totalAfterWallet.value = remaining;
      return false;
    }
  }

  enableWalletPay() async {
    if (selectedPayment.value != 100) {
      isEnableWalletPay.value = !isEnableWalletPay.value;
      if (isEnableWalletPay.value) {
        bool isAllFromWallet = await calculateAmountNeedToDeductFromWallet();
        if(isAllFromWallet){
          //first of all assigning null to select payment method incase of all the payment is done from wallet
          //so that unselect any payment method as per requirement
          selectedPayment.value = PaymentHelper.walletPaymentMethod.id;
        }
      } else {
        paymentDeductFromWallet.value = 0;
      }
    }
  }

  unSelectWalletPay() {
    isEnableWalletPay.value = false;
  }

  void onPaymentResult(result, double amount, var response) {
    if (result is PaymentResponse) {
      if (result.status == PaymentStatus.paid) {
        // Track payment success event with Analytics Manager
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.paymentCompleted,
          eventAttributes: {
            AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
            AnalyticsAttributes.bookingId:
                response.data['booking']['id']?.toString(),
            AnalyticsAttributes.propertyId:
                propertyC.data.value?.id?.toString(),
            AnalyticsAttributes.paymentMethod: 'credit_card',
            AnalyticsAttributes.paymentAmount: amount.toString(),
            AnalyticsAttributes.paymentCurrency:
                propertyC.data.value!.propertyPrice!.currencyCode ?? "SAR",
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
          },
        );

        purchaseEvent(
            propertyC.data.value!.propertyPrice!.currencyCode ?? "SAR",
            "${response.data['booking']['transaction_id']}",
            amount,
            propertyPrice.value!.ivaTax!,
            'Instant',
            DateFormat('MMMM').format(propertyC.startDate.value),
            propertyPrice.value!.dateWithPrice.length,
            propertyC.getEventItem(),
            coupon: couponController.text);
        placeReservationCompleted(response);
        SearchHelper.c.getBookings();
        Timer(const Duration(seconds: 1), () {
          AuthHelper.c.getCards();
          double balanceBefore = AuthHelper.c.myWallet.first.balance ?? 0;
          if (balanceBefore == AuthHelper.c.myWallet.first.balance) {
            deductionManually(
                propertyC.data.value?.propertyPrice!.price, amount);
          }
          AuthHelper.c.getTransactions();
        });
        Get.until((route) => route.isFirst);
        if (!userModel.value!.yaqeenVerified) {
          Get.dialog(const YaqeenVerificationDialog());
        } else {
          Get.dialog(
            WarningDialog(
                title: "congratulations".tr,
                description: "propertyReserved".trArgs([
                  "",
                  DateFormat('dd-MM-yyyy').format(propertyC.startDate.value),
                  DateFormat('dd-MM-yyyy').format(propertyC.endDate.value),
                ]),
                keyword: DialogKeyword.success),
          );
        }
      } else {
        // Track payment failed event with Analytics Manager
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.paymentFailed,
          eventAttributes: {
            AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
            AnalyticsAttributes.propertyId:
                propertyC.data.value?.id?.toString(),
            AnalyticsAttributes.paymentMethod: 'credit_card',
            AnalyticsAttributes.paymentAmount: amount.toString(),
            AnalyticsAttributes.paymentCurrency:
                propertyC.data.value!.propertyPrice!.currencyCode ?? "SAR",
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
            AnalyticsAttributes.paymentStatus: 'failed',
            'error_reason': result.status.toString(),
          },
        );

        isLoading.value = false;
        Get.back();
        Get.dialog(WarningDialog(title: "paymentFailed".tr, description: ""));
      }
    } else {
      // Track payment failed event with Analytics Manager
      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.paymentFailed,
        eventAttributes: {
          AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
          AnalyticsAttributes.propertyId: propertyC.data.value?.id?.toString(),
          AnalyticsAttributes.paymentMethod: 'credit_card',
          AnalyticsAttributes.paymentAmount: amount.toString(),
          AnalyticsAttributes.paymentCurrency:
              propertyC.data.value!.propertyPrice!.currencyCode ?? "SAR",
          AnalyticsAttributes.sessionTimestamp:
              DateTime.now().toIso8601String(),
          AnalyticsAttributes.paymentStatus: 'failed',
          'error_reason': 'unknown_error',
        },
      );

      isLoading.value = false;
      Get.back();
      Get.dialog(WarningDialog(title: "paymentFailed".tr, description: ""));
    }
  }

  deductionManually(total, finalAmountNeedToPay) {
    double amountDeductFromWallet = total - finalAmountNeedToPay;
    AuthHelper.c.myWallet.first.balance =
        AuthHelper.c.myWallet.first.balance! - amountDeductFromWallet;
    AuthHelper.c.myWallet.refresh();
  }

  // plusMinus(String sign,RxInt value,{int maxCount=0}) {
  //   if (sign == "+") {
  //     if(value<maxCount){
  //       value.value++;
  //     }
  //   } else if (value > 1) {
  //     value.value--;
  //   }
  // }
  onReserve() async {
    //here adding condition if pay by wallet enabled and can pay all amount from
    //wallet then no need to select payment method
    if (propertyC.data.value?.bookingType == "instant") {
      // Check selectedPayment only if:
      // 1. isEnableWalletPay is false, OR
      // 2. isEnableWalletPay is true AND totalAfterWallet != 0
      if (!(isEnableWalletPay.value && totalAfterWallet.value == 0) && (selectedPayment.value == 44 || selectedPayment.value == null)) {
        ViewsCommon.showSnackbar("selectPaymentMethod".tr, keyword: DialogKeyword.warning);
        return;
      }
    }

    // Track payment initiated event with Analytics Manager
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.paymentInitiated,
      eventAttributes: {
        AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
        AnalyticsAttributes.propertyId: propertyC.data.value?.id?.toString(),
        AnalyticsAttributes.paymentMethod: selectedPayment.value == 2
            ? 'credit_card'
            : selectedPayment.value == 100
                ? 'tabby'
                : selectedPayment.value == 101
                    ? 'mada'
                    : selectedPayment.value == 12
                        ? 'stc_pay'
                        : 'other',
        AnalyticsAttributes.paymentAmount:
            (propertyPrice.value?.totalDiscount ??
                    propertyPrice.value?.totalWithDiscount)
                ?.toString(),
        AnalyticsAttributes.paymentCurrency:
            propertyC.data.value?.propertyPrice?.currencyCode ?? "SAR",
        AnalyticsAttributes.sessionTimestamp: DateTime.now().toIso8601String(),
        AnalyticsAttributes.checkInDate:
            formatter.format(propertyC.startDate.value),
        AnalyticsAttributes.checkOutDate:
            formatter.format(propertyC.endDate.value),
      },
    );

    try {
      Map formData = {
        "propertyId": propertyC.data.value?.id,
        "checkin": formatter.format(propertyC.startDate.value),
        "checkout": formatter.format(propertyC.endDate.value),
        'number_of_guests': propertyC.adults.value + propertyC.children.value,
        "guest_adult": propertyC.adults.value,
        "guest_child": propertyC.children.value,
        'booking_source': GetPlatform.isAndroid ? 'android' : 'ios',
      };
      if (bookingId != null) {
        formData['booking_id'] = bookingId;
      }
      formData['api_version'] = 1;
      if (discountObject.value != null) {
        formData['coupon'] = couponController.text;
      }
      if (propertyC.data.value!.bookingType == "instant") {
        formData['paymentMethodId'] = selectedPayment.value;
        formData['pay_by_wallet'] = isEnableWalletPay.value ? 1 : 0;
      }
      if (DeepLinkHelper.code != null) {
        formData['referal_code'] = DeepLinkHelper.code;

        // Track guest referral success event with Analytics Manager
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.guestReferralSuccess,
          eventAttributes: {
            AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
            'referral_code': DeepLinkHelper.code,
            AnalyticsAttributes.propertyId:
                propertyC.data.value?.id?.toString(),
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
            'referral_type': 'booking_completion',
          },
        );
      }
      if (propertyPrice.value?.paymentGetway != 'hyperpay' &&
          selectedPayment.value == 2 &&
          AuthHelper.c.myCards.isEmpty) {
        final cardFormData =
            await AuthHelper.c.showAddCardSheet(fromCheckout: true);
        AuthHelper.c.isLoading.value = false;
        if (cardFormData == null) {
          return;
        } else {
          formData.addAll(cardFormData);
        }
      }
      isLoading.value = true;
      ResponseModel response = await ApiServices.postApi("v1/property/reserve",
          body: formData, isAuth: true);
      DeepLinkHelper.code = null;
      if (response.status) {
        if (isEnableWalletPay.value &&
            (response.data['fullPaymentByWallet'] ?? false)) {
          // Track payment success event with Analytics Manager
          Get.find<AnalyticsManager>().trackEvent(
            AnalyticsEvents.paymentCompleted,
            eventAttributes: {
              AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
              AnalyticsAttributes.bookingId:
                  response.data['booking']['id']?.toString(),
              AnalyticsAttributes.propertyId:
                  propertyC.data.value?.id?.toString(),
              AnalyticsAttributes.paymentMethod: 'wallet',
              AnalyticsAttributes.paymentAmount:
                  (response.data['finalamount'] is double
                          ? response.data['finalamount']
                          : response.data['finalamount'] is int
                              ? double.parse(
                                  response.data['finalamount'].toString())
                              : response.data['finalamount'] is String
                                  ? double.parse(response.data['finalamount'])
                                  : response.data['finalamount'])
                      .toString(),
              AnalyticsAttributes.paymentCurrency:
                  propertyC.data.value!.propertyPrice!.currencyCode ?? "SAR",
              AnalyticsAttributes.sessionTimestamp:
                  DateTime.now().toIso8601String(),
            },
          );

          SearchHelper.c.getBookings(refresh: true);
          Timer(const Duration(seconds: 1), () {
            AuthHelper.c.getCards();
            AuthHelper.c.getTransactions();
          });
          Get.until((route) => route.isFirst);
          ViewsCommon.showSnackbar(
              Get.find<TranslationHelper>()
                  .translations
                  .listing
                  .paymentSuccessful!,
              displayTime: 1500);
          SearchHelper.c.changeIndex(2);
          if (response.data['is_yaqeen_verified'] != "1" &&
              Get.isDialogOpen == false) {
            Get.dialog(const YaqeenVerificationDialog());
          }
          Get.toNamed(Routes.customerServices);
          purchaseEvent(
              propertyC.data.value!.propertyPrice!.currencyCode ?? "SAR",
              "${response.data['booking']['transaction_id']}",
              response.data['finalamount'] is double
                  ? response.data['finalamount']
                  : response.data['finalamount'] is int
                      ? double.parse(response.data['finalamount'].toString())
                      : response.data['finalamount'] is String
                          ? double.parse(response.data['finalamount'])
                          : response.data['finalamount'],
              propertyPrice.value!.ivaTax!,
              'Instant',
              DateFormat('MMMM').format(propertyC.startDate.value),
              propertyPrice.value!.dateWithPrice.length,
              propertyC.getEventItem(),
              coupon: couponController.text);
          placeReservationCompleted(response);
          return;
        }
        if (propertyC.data.value!.bookingType == "request") {
          ViewsCommon.showSnackbar(
              "${Get.find<TranslationHelper>().translations.general.sentReservationRequest}\n${Get.find<TranslationHelper>().translations.general.reservationRequestSuggestion}",
              displayTime: 2500);
          SearchHelper.c.getBookings(refresh: true);
          SearchHelper.c.changeIndex(2);
          SearchHelper.c.reservationTabController.animateTo(0);
          requestBookingEventCalled();
          isLoading.value = false;
          Get.offAllNamed(Routes.home);
          return;
        }
        if (selectedPayment.value == 100) {
          await initTabby(response.data['booking_id']);
          if (tabbySession != null) {
            if (tabbySession!.availableProducts.installments?.webUrl != null) {
              makePaymentUsingTabby(response);
            }
            //tabbySession?.availableProducts.installments?.rejectionReason != null

            if (tabbySession?.status.name == 'rejected') {
              // String description = tabbySession!.availableProducts.installments?.rejectionReason == "under_limit"
              //     ? Get.find<TranslationHelper>().translations.error.amountToLow
              //     : Get.find<TranslationHelper>().translations.error.networkError;
              Get.dialog(WarningDialog(
                  title: "paymentFailed".tr,
                  keyword: DialogKeyword.info,
                  description: Get.find<TranslationHelper>()
                      .translations
                      .error
                      .amountToLow));
              isLoading.value = false;
            }
          }
          return;
        }
        if (selectedPayment.value == 2 ||
            selectedPayment.value == 101 ||
            selectedPayment.value == 12) {
          // Uri paymentUrl = Uri.parse(response.data['paymentURL']);
          Get.to(() => MyFatoorahScreen(
              url: response.data['paymentURL'],
              logEvent: () {
                purchaseEvent(
                    propertyC.data.value!.propertyPrice!.currencyCode ?? "SAR",
                    "${response.data['booking']['transaction_id']}",
                    response.data['finalamount'] is double
                        ? response.data['finalamount']
                        : response.data['finalamount'] is int
                            ? double.parse(
                                response.data['finalamount'].toString())
                            : response.data['finalamount'] is String
                                ? double.parse(response.data['finalamount'])
                                : response.data['finalamount'],
                    propertyPrice.value!.ivaTax!,
                    'Instant',
                    DateFormat('MMMM').format(propertyC.startDate.value),
                    propertyPrice.value!.dateWithPrice.length,
                    propertyC.getEventItem(),
                    coupon: couponController.text);
                placeReservationCompleted(response);
              },
              isDisplayYaqeen: response.data['is_yaqeen_verified'] != "1"));
          isLoading.value = false;
          return;
        }
        if (response.data['payment_service'] == "Fatoorah") {
          var request = MFExecutePaymentRequest(
              invoiceValue: response.data['finalamount'] is double
                  ? response.data['finalamount']
                  : response.data['finalamount'].toDouble());
          request.paymentMethodId = selectedPayment.value ?? 0;
          request.customerReference = response.data['customer_ref'];
          request.customerName = userModel.value!.first_name;
          request.customerEmail = userModel.value!.email;
          await MFSDK.executePayment(request, MFLanguage.ENGLISH, (invoiceId) {
            debugPrint(invoiceId);
          }).then((result) {
            // Track payment success event with Analytics Manager
            Get.find<AnalyticsManager>().trackEvent(
              AnalyticsEvents.paymentCompleted,
              eventAttributes: {
                AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
                AnalyticsAttributes.bookingId:
                    response.data['booking']['id']?.toString(),
                AnalyticsAttributes.propertyId:
                    propertyC.data.value?.id?.toString(),
                AnalyticsAttributes.paymentMethod: selectedPayment.value == 2
                    ? 'credit_card'
                    : selectedPayment.value == 101
                        ? 'mada'
                        : selectedPayment.value == 12
                            ? 'stc_pay'
                            : 'other',
                AnalyticsAttributes.paymentAmount:
                    (response.data['finalamount'] is double
                            ? response.data['finalamount']
                            : response.data['finalamount'] is int
                                ? double.parse(
                                    response.data['finalamount'].toString())
                                : response.data['finalamount'] is String
                                    ? double.parse(response.data['finalamount'])
                                    : response.data['finalamount'])
                        .toString(),
                AnalyticsAttributes.paymentCurrency:
                    propertyC.data.value!.propertyPrice!.currencyCode ?? "SAR",
                AnalyticsAttributes.sessionTimestamp:
                    DateTime.now().toIso8601String(),
              },
            );

            purchaseEvent(
                propertyC.data.value!.propertyPrice!.currencyCode ?? "SAR",
                "${response.data['booking']['transaction_id']}",
                response.data['finalamount'] is double
                    ? response.data['finalamount']
                    : response.data['finalamount'] is int
                        ? double.parse(response.data['finalamount'].toString())
                        : response.data['finalamount'] is String
                            ? double.parse(response.data['finalamount'])
                            : response.data['finalamount'],
                propertyPrice.value!.ivaTax!,
                'Instant',
                DateFormat('MMMM').format(propertyC.startDate.value),
                propertyPrice.value!.dateWithPrice.length,
                propertyC.getEventItem(),
                coupon: couponController.text);

            placeReservationCompleted(response);
            SearchHelper.c.getBookings();
            Timer(const Duration(seconds: 1), () {
              AuthHelper.c.getCards();
              AuthHelper.c.getTransactions();
            });
            Get.until((route) => route.isFirst);
            Get.dialog(
              WarningDialog(
                  title: "congratulations".tr,
                  description: "propertyReserved".trArgs([
                    propertyC.data.value?.name ?? "",
                    formData['checkin'],
                    formData['checkout']
                  ]),
                  keyword: DialogKeyword.success),
            );
          }).catchError((error) {
            debugPrint(error.toString());
            Get.dialog(WarningDialog(
                title: "paymentFailed".tr, description: error ?? ""));
          });
          return;
        } else {
          Get.to(() => MyFatoorahScreen(
              url: response.data['payment_service'],
              logEvent: () {
                purchaseEvent(
                    propertyC.data.value!.propertyPrice!.currencyCode ?? "SAR",
                    "${response.data['booking']['transaction_id']}",
                    response.data['finalamount'] is double
                        ? response.data['finalamount']
                        : response.data['finalamount'] is int
                            ? double.parse(
                                response.data['finalamount'].toString())
                            : response.data['finalamount'] is String
                                ? double.parse(response.data['finalamount'])
                                : response.data['finalamount'],
                    propertyPrice.value!.ivaTax!,
                    'Instant',
                    DateFormat('MMMM').format(propertyC.startDate.value),
                    propertyPrice.value!.dateWithPrice.length,
                    propertyC.getEventItem(),
                    coupon: couponController.text);
                placeReservationCompleted(response);
              },
              isDisplayYaqeen: response.data['is_yaqeen_verified'] != "1"));
        }
      }
      if (kDebugMode) {
        print("Not success");
      }
      isLoading.value = false;
    } catch (e) {
      isLoading.value = false;
    }
  }

  initMoyasar(var response) {
    // remember Moyasar is considering amount in halalas(same as cents iin dolor or paisa in rupee) not in riyals so sending amount with
    // multiple of 100.
    var amount =
        int.parse((response.data['finalamount'] * 100).toStringAsFixed(0));
    PaymentConfig? paymentConfig;
    try {
      paymentConfig = PaymentConfig(
        publishableApiKey: moyasarTestAPIKey,
        amount: amount,
        currency: 'SAR',
        // SAR 257.58
        description: 'booking_id #${response.data['booking_id']}',
        metadata: {
          'booking': '${response.data['booking_id']}',
          'walletDeduction': isEnableWalletPay.value.toString(),
        }, //customer_ref
        creditCard: CreditCardConfig(saveCard: true, manual: true),
        applePay: ApplePayConfig(
            merchantId: 'merchant.darent.com', label: 'Darent', manual: false),
      );
    } catch (e) {
      if (kDebugMode) {
        print("onApplePay Error paymentConfig $e");
      }
    }
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        maxChildSize: .30,
        initialChildSize: .30,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              SizedBox(height: heightSpace(3)),
              ApplePay(
                config: paymentConfig!,
                onPaymentResult: ((result) => onPaymentResult(
                    result, response.data['finalamount'], response)),
              ),
            ],
          ).paddingAll(widthSpace(viewPadding));
        })).then((value) {
      isLoading.value = false;
    });
  }

  requestBookingEventCalled() async {
    Map<String, dynamic> myMap = {
      "Name": propertyC.data.value?.propertyTitle,
      "Checkin Date": formatter.format(propertyC.startDate.value),
      "Checkout Date": formatter.format(propertyC.endDate.value),
      "Unit Code": propertyC.data.value?.propertyCode,
      "Cost Per Night": "${propertyC.data.value?.propertyPrice?.price ?? 0.0}",
      "Category Name": propertyC.data.value?.spaceTypeName,
      "Status": "Pending",
      "Reservation number": propertyC.data.value?.propertyCode,
    };
    if (isHost) {
      myMap['User Customer'] = "Host";
    } else {
      myMap['User Host'] = "Customer";
    }
    await WebEngagePlugin.trackEvent('Request Booking', myMap);
  }

  TabbySession? tabbySession;
  makePaymentUsingTabby(var response) async {
    TabbyWebView.showWebView(
      // onWillPop: (){
      //   isLoading.value = false;
      //   Get.back();
      //   Get.dialog(WarningDialog(
      //       title: "paymentFailed".tr,
      //       description: ""));
      // },
      context: Get.context!,
      webUrl: tabbySession!.availableProducts.installments?.webUrl ?? '',
      onResult: (WebViewResult resultCode) async {
        if (kDebugMode) {
          print("tabby result=$resultCode");
        }
        if (resultCode.name == "authorized") {
          // Track payment success event with Analytics Manager
          Get.find<AnalyticsManager>().trackEvent(
            AnalyticsEvents.paymentCompleted,
            eventAttributes: {
              AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
              AnalyticsAttributes.bookingId:
                  response.data['booking']['id']?.toString(),
              AnalyticsAttributes.propertyId:
                  propertyC.data.value?.id?.toString(),
              AnalyticsAttributes.paymentMethod: 'tabby',
              AnalyticsAttributes.paymentAmount:
                  (propertyC.data.value?.propertyPrice!.price ?? 0).toString(),
              AnalyticsAttributes.paymentCurrency: "SAR",
              AnalyticsAttributes.sessionTimestamp:
                  DateTime.now().toIso8601String(),
            },
          );

          purchaseEvent(
              "SAR",
              "${response.data['booking']['code']}",
              propertyC.data.value?.propertyPrice!.price ?? 0,
              propertyPrice.value!.ivaTax!,
              'Instant',
              DateFormat('MMMM').format(propertyC.startDate.value),
              propertyPrice.value!.dateWithPrice.length,
              propertyC.getEventItem(),
              coupon: couponController.text);
          placeReservationCompleted(response);
          SearchHelper.c.getBookings();
          Timer(const Duration(seconds: 1), () {
            AuthHelper.c.getCards();
            AuthHelper.c.getTransactions();
          });
          Get.offAllNamed(Routes.home);
          Get.dialog(
            WarningDialog(
                title: "congratulations".tr,
                description: "propertyReserved".trArgs([
                  "",
                  DateFormat('dd-MM-yyyy').format(propertyC.startDate.value),
                  DateFormat('dd-MM-yyyy').format(propertyC.endDate.value),
                ]),
                keyword: DialogKeyword.success),
          );
          Get.toNamed(Routes.customerServices);
        } else {
          isLoading.value = false;
          Get.back();
          Get.dialog(WarningDialog(
              title: "paymentFailed".tr,
              description: "Payment ${resultCode.name}"));
        }
      },
    );
  }

  placeReservationCompleted(var response) {
    // Track booking completed event with WebEngage (existing)
    Map<String, dynamic> myMap = {
      "Cancellation Policy": '${propertyC.data.value?.cancellation}',
      "Checkin Date": formatter.format(propertyC.startDate.value),
      "Checkout Date": formatter.format(propertyC.endDate.value),
      "Checkin Time": "${propertyC.data.value?.checkin}",
      "Checkout Time": "${propertyC.data.value?.checkout}",
      "Reservation number": '${response.data['booking_code'] ?? ""}',
      "User": isHost ? "Host" : "Customer",
      "Cost Per Night": "${propertyC.data.value?.propertyPrice?.price ?? 0.0}",
      "Unit Code": "${propertyC.data.value?.propertyCode}",
      "Date": formatter.format(DateTime.now()),
      "Number of Adults": propertyC.adults.value.toString(),
      "Number of Children": propertyC.children.value.toString(),
      "Service fees": (propertyPrice.value?.serviceFeeDiscount ??
              propertyPrice.value?.serviceFeeWithDiscount ??
              0.0)
          .toStringAsFixed(2),
      "Total": (propertyPrice.value!.totalDiscount ??
              propertyPrice.value!.totalWithDiscount)!
          .toStringAsFixed(2),
    };
    if (isHost) {
      myMap['User Customer'] = "Host";
    } else {
      myMap['User Host'] = "Customer";
    }
    if (propertyC.data.value?.propertyAddress?.latitude != null &&
        propertyC.data.value?.propertyAddress?.longitude != null) {
      myMap['Location'] =
          'https://www.google.com/maps/search/?api=1&query=${propertyC.data.value!.propertyAddress!.latitude},${propertyC.data.value!.propertyAddress!.longitude}';
    }
    if (kDebugMode) {
      print("Place Reservation Completed checkout controller $myMap ");
    }
    WebEngagePlugin.trackEvent('Place Reservation Completed', myMap);

    // Track booking completed event with Analytics Manager (using Google Sheets event name)
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.confirmBookingPaymentSuccess,
      eventAttributes: {
        AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
        AnalyticsAttributes.hostId: propertyC.data.value?.hostId?.toString(),
        AnalyticsAttributes.propertyId: propertyC.data.value?.id?.toString(),
        AnalyticsAttributes.bookingId:
            response.data['booking_id']?.toString() ?? "",
        AnalyticsAttributes.checkInDate:
            formatter.format(propertyC.startDate.value),
        AnalyticsAttributes.checkOutDate:
            formatter.format(propertyC.endDate.value),
        AnalyticsAttributes.paymentStatus: "completed",
        AnalyticsAttributes.amountPaid: (propertyPrice.value!.totalDiscount ??
                propertyPrice.value!.totalWithDiscount)!
            .toString(),
        AnalyticsAttributes.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  clearPromo() {
    couponController.clear();
    discountObject.value = null;
    couponError.value = null;
  }

  removeCoupon() {
    couponController.clear();
    propertyPrice.value?.serviceFeeDiscount = null;
    propertyPrice.value?.totalDiscount = null;
    discountObject.value = null;
    propertyPrice.refresh();
    if (isEnableWalletPay.value) {
      calculateAmountNeedToDeductFromWallet();
    }
  }

  sendTamayouzOtp() async {
    if (discountTab.value == 't') {
      if (AuthHelper.c.phone.text.isEmpty) {
        couponError.value = Get.find<TranslationHelper>()
            .translations
            .jqueryValidation
            .required;
      } else {
        isLoading.value = true;
        couponError.value = null;
        Map form = {
          'phone': AuthHelper.c.phone.text,
          'property_id': propertyC.data.value?.id
        };
        final res = await ApiServices.postApi("v1/tamayouz/otp/send",
            body: form, isAuth: true, allowDialog: false);
        if (res.status) {
          discountTab.value = 't1';
        } else {
          couponError.value = res.message;
        }
        isLoading.value = false;
      }
    } else {
      onApplyTamayouz();
    }
  }

  onApplyTamayouz() async {
    if (AuthHelper.c.messageOtpCode.value.length == 4) {
      isLoading.value = true;
      Map formData = {
        "phone": AuthHelper.c.phone.text,
        "property_id": propertyC.data.value?.id,
        "amount": propertyPrice.value!.totalNightPrice,
        "otp": AuthHelper.c.controller.text
      };
      if (AuthHelper.c.phone.text != userModel.value?.formatted_phone) {
        AuthHelper.c.phone.text = userModel.value?.formatted_phone ?? '';
      }

      final response = await ApiServices.postApi("v1/tamayouz/otp/verify",
          body: formData, isAuth: true, allowDialog: false);
      //{"status":200,"tamayouz_id":20,"amount":"456","amount_after_discount":496.15999999999997,"discount_value":"10","servicefee":50.16}
      if (response.status) {
        couponError.value = null;
        response.data['discount_type'] =
            Get.find<TranslationHelper>().translations.propertySingle.tamayouz;
        setPrices(response.data);
      } else {
        couponError.value = response.message;
      }
      isLoading.value = false;
    }
  }

  onApplyCoupon() async {
    if (couponController.text.isEmpty) {
      couponError.value =
          Get.find<TranslationHelper>().translations.jqueryValidation.required;
      return;
    } else {
      couponError.value = null;
    }
    Map formData = {
      "code": couponController.text,
      "propert_id": propertyC.data.value!.id,
      "amount": (propertyPrice.value!.valueOfApplyingCoupon ??
          propertyPrice.value!.totalNightPriceAfterDiscount),
      "checkin": propertyC.startDate.value.toString(),
      "checkout": propertyC.endDate.value.toString(),
    };
    var response = await ApiServices.postApi("v2/coupon/check",
        body: formData, isAuth: true, allowDialog: false);
    if (response.message == 'campaign') {
      response.data['discount_type'] = 'campaign';
      discountObject.value = PromoDataModel.fromJson(response.data);

      // Track coupon applied event with Analytics Manager
      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.couponApplied,
        eventAttributes: {
          AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
          AnalyticsAttributes.propertyId: propertyC.data.value?.id?.toString(),
          AnalyticsAttributes.couponCode: couponController.text,
          AnalyticsAttributes.discountAmount:
              response.data['discount_value']?.toString(),
          AnalyticsAttributes.sessionTimestamp:
              DateTime.now().toIso8601String(),
          'coupon_type': 'campaign',
        },
      );

      return;
    }
    if (response.status) {
      setPrices(response.data);
    } else {
      couponError.value = response.message;
      ViewsCommon.showSnackbar(response.message);
    }
  }

  setPrices(Map<String, dynamic> data) {
    discountObject.value = PromoDataModel.fromJson(data);

    // propertyPrice.value!.serviceFeeDiscount =
    //     (discountObject.value!.serviceFee ?? 0.0) +
    //         (propertyPrice.value?.serviceFeeCleaning ?? 0.0) +
    //         (propertyPrice.value?.serviceFeeSecurity ?? 0.0);

    propertyPrice.value!.totalDiscount =
        (discountObject.value?.amountAfterDiscount ?? 0.0);

    if (isEnableWalletPay.value) {
      calculateAmountNeedToDeductFromWallet();
    }
    propertyPrice.refresh();
  }
  // @override
  // void onInit() {
  //   if(propertyC.data.value!.bookingType=="instant"){
  //     initMFSDK();
  //   }
  //   super.onInit();
  // }
  // initMFSDK() async {
  //   MFSDK.init(myFatoorahLive , MFCountry.SAUDIARABIA,MFEnvironment.LIVE);
  //   var request = MFInitiatePaymentRequest(
  //       invoiceAmount: propertyPrice.value?.totalWithDiscount??0.0,
  //       currencyIso: propertyC.data.value?.propertyPrice?.currencyCode ?? "SAR");
  //
  //   await MFSDK.initiatePayment(
  //       request,
  //       MFLanguage.ENGLISH,
  //       ).then((result) {
  //         paymentMethods.value = result.paymentMethods!.where((item) {
  //           if(GetPlatform.isIOS){
  //             return item.paymentMethodId !=6;
  //           }
  //           else{
  //             return item.paymentMethodId !=6  && item.paymentMethodCode != "ap";
  //           }
  //
  //
  //         }).toList();
  //         if(userModel.value?.paymentGateway=='hyperpay'){
  //         paymentMethods.add(MFPaymentMethod(paymentMethodId: 12,paymentMethodEn: 'stcPay',imageUrl: ""));
  //         paymentMethods.add(MFPaymentMethod(paymentMethodId: 101,paymentMethodEn: 'mada',imageUrl: result.paymentMethods?.firstWhere((e) => e.paymentMethodEn=='mada').imageUrl));
  //         }
  //
  //
  //   }).catchError((error){
  //         debugPrint(error.toString());
  //       });
  // }

  Future<void> initTabby(bookingId) async {
    if (kDebugMode) {
      print(
          "initialize tabby user date of birth ${userModel.value?.dateofbirth} and ${userModel.value!.createdAt}");
    }
    final mockPayload = Payment(
      amount: (propertyPrice.value!.totalDiscount ??
              propertyPrice.value!.totalWithDiscount)!
          .toStringAsFixed(2),
      currency: Currency.sar,
      buyer: Buyer(
        email: userModel.value?.email ?? "",
        phone: userModel.value?.phone ?? "",
        name: '${userModel.value?.first_name} ${userModel.value?.last_name}',
        dob: userModel.value?.dateofbirth != null
            ? formDateFormat
                .format(DateTime.parse(userModel.value!.dateofbirth!))
            : null,
      ),
      buyerHistory: BuyerHistory(
        loyaltyLevel: 0,
        registeredSince: userModel.value?.createdAt ?? "",
        wishlistCount: 0,
      ),
      shippingAddress: ShippingAddress(
        city: 'string',
        address: userModel.value?.location ?? "",
        zip: 'string',
      ),
      order: Order(referenceId: "000-B-$bookingId", items: [
        OrderItem(
          title: propertyC.data.value?.propertyTitle ?? "",
          description: propertyC.data.value?.propertyDescription?.summary ?? "",
          quantity: 1,
          unitPrice: (propertyPrice.value?.totalDiscount ??
                  propertyPrice.value!.totalWithDiscount)!
              .toStringAsFixed(2),
          referenceId: "000-B-$bookingId",
          productUrl: 'https://darent.com',
          category: propertyC.data.value?.propertyType?.name ?? "",
        )
      ]),
      orderHistory: [],
    );

    tabbySession =
        await GlobalHelper.tabbySdk.createSession(TabbyCheckoutPayload(
      merchantCode: 'sa',
      lang: Get.locale!.languageCode == 'en' ? Lang.en : Lang.ar,
      payment: mockPayload,
    ));
    if (kDebugMode) {
      print("tabby session id ${tabbySession!.sessionId}");
      print("tabby payment id ${tabbySession!.paymentId}");
      print(
          "tabby session reservation id checkout 000-B-${propertyC.data.value!.id}");
      print(
          "tabby session reservation id ${tabbySession?.availableProducts.installments?.webUrl}");
    }
  }
}
