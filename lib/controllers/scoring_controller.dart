import 'package:darent/utils/api_service.dart';
import 'package:get/get.dart';

import '../models/host/host_property.dart';

class ScoringController extends GetxController{
  final HostProperty first;
  ScoringController({required this.first});
  int total = 0;
  Map reviewScore = {"data":{}};
  Map hostScore = {};
  Map listingScore = {};
  final isLoading = RxBool(true);
  @override
  void onInit() {
    getData(first.id);
    super.onInit();
  }
  getData(id){
    isLoading.value = true;
    try{
      ApiServices.getApi('v1/properties/$id/score').then((r) async {
        if(r.status){
          r.data.forEach((key, value) async {
            if(key=="total"){
              return;
            }
            var modifiedData = await modifyData(value);
            Map<String, dynamic> assigningData = {convertToTitleCase(key) : modifiedData};
            reviewScore["data"].addAll(assigningData);
          });
          total = r.data['total'];
        }
        isLoading.value = false;
      });
    }catch(e){
      print('Excpetion: $e');
      isLoading.value = false;
    }
  }

  Future<Map<String, dynamic>> modifyData(Map<String, dynamic> apiData) async{
      var data = apiData['data'];
      var outofSum = data.values.fold(0, (prev, e) => prev + e['outof']);
      var valueSum = data.values.fold(0, (prev, e) => prev + e['value']);
      var logSum = data.values.fold(0, (prev, e) => prev + e['log']);
      return {
        'outof': outofSum,
        'value': valueSum,
        "log":logSum,
        "heading": data["heading"]??"حساب نقاط التهديف",
        "query": data["query"]??"كيف يتم حساب نقاطي؟",
        "answer":data["explain"]??"تعتمد نقاطك على تقييمات المستخدمين بالإضافة إلى الخدمة المدرجة لديك."
      };
  }

  String convertToTitleCase(String input) {
    List<String> words = input.split('_');
    words = words.map((word) {
      return word.substring(0, 1).toUpperCase() + word.substring(1);
    }).toList();
    return words.join(' ');
  }
}