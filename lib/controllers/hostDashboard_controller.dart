import 'dart:convert';
import 'package:darent/components/host/acknowledge_teerms_dialogue.dart';
import 'package:darent/components/host/license_warning.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/components/warning_dialog.dart';
import 'package:darent/helperMethods/chat_helper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/listing_helper/listing_helper.dart';
import 'package:darent/analytics/analytics.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:darent/models/host/ReportModel.dart';
import 'package:darent/screens/co-host/invite_cohost.dart';
import 'package:darent/screens/co-host/permission_review.dart';
import 'package:intl_phone_field/countries.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';
import '../helperMethods/translation_helper.dart';
import '../models/avg_scoring_model.dart';
import '../models/bookingModel.dart';
import 'package:darent/models/host/dwelling_model.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:webengage_flutter/webengage_flutter.dart';
import '../models/filterModel.dart';
import '../models/host/property_photo.dart';
import '../models/host/bookings/bookings_total.dart';
import 'package:darent/models/host/host_property.dart';
import 'package:darent/models/promoModel.dart' show PromoModel;
import 'package:darent/screens/promotion/promo_form.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../models/host/dwelling_detail.dart';
import '../models/pagination_model.dart';
import 'package:http/http.dart' as http;
import '../screens/host/host_review/review_instructions.dart';

import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class Photos {
  final String path;
  final bool isCover;
  Photos({required this.path, required this.isCover});
}

class HostDashboardController extends GetxController {
  String title = "";
  int index = 0;
  HostDashboardController({this.index = 0}) {
    if (index != 0) setTabTitle(index);
  }
  final isLoading = true.obs;
  final isBtnLoading = false.obs;
  final isDarStayLoading = false.obs;

  final selectedProperty = Rxn<HostProperty>();
  final properties = RxList<HostProperty>();

  final isAllSelected = false.obs;
  void selectAll(bool isSelected) {
    isAllSelected.value = isSelected;
    for (var property in properties) {
      property.checked = isSelected;
    }
    properties.refresh();
  }

  //bookings
  final bookings = RxList<BookingModel>();
  Pagination bookingsPagination = Pagination();
  final bookingsTotal = Rx<BookingsTotal>(BookingsTotal());
  //dwellings
  Map dwellingsFilterForm = {
    "page": 1,
    "size": 8,
    // "property_status" : "Listed"
  };
  TextEditingController dwellingSearch = TextEditingController();
  Country invitePhoneCountry = countries.firstWhere((e) => e.code == 'SA');
  TextEditingController invitePhoneController = TextEditingController();
  RxBool isInviteButtonEnabled = false.obs;
  TextEditingController inviteEmailController = TextEditingController();
  final access = RxList();
  final coHostList = RxList();
  final inviteMode = 'Phone'.obs;
  final inviteFormKey = GlobalKey<FormState>();
  //new
  final selectedPayDuration = 'direct'.obs;
  final transferDay = RxnString();
  selectPayDuration(value) {
    selectedPayDuration.value = value;
  }

  navigateInviteCoHost() {
    isInviteButtonEnabled.value = false;
    invitePhoneController.clear();
    inviteEmailController.clear();
    setAccess();
    Get.to(() => const InviteCoHost());
  }

  setAccess() {
    access.value = [
      {
        "title":
            Get.find<TranslationHelper>().translations.coHost.fullAccessTitle,
        "subtitle": Get.find<TranslationHelper>()
            .translations
            .coHost
            .fullAccessSubtitle,
        "isSelected": false,
      },
      {
        "title": "Calender and inbox access",
        "subtitle": "Show and edit calender and message guest.",
        "isSelected": false,
      },
      {
        "title": "Calender access",
        "subtitle": "Show calender",
        "isSelected": false,
      },
    ];
    access.refresh();
  }
  // void updateButtonState(value, String inputType) {
  //   switch(inputType){
  //     case "Phone":
  //       isInviteButtonEnabled.value = value.length == (invitP.value?.limit??9);
  //       inviteMode.value = "Phone";
  //       break;
  //     case "Email":
  //       isInviteButtonEnabled.value = GetUtils.isEmail(value);
  //       inviteMode.value = "Email";
  //       break;
  //   }
  // }

  onDeleteCoHost(int index) async {
    Get.dialog(WarningDialog(
        title: 'warning'.tr,
        keyword: DialogKeyword.info,
        description: Get.find<TranslationHelper>()
            .translations
            .hostDashboard
            .cohostDeleteWarn!,
        confirmText: Get.find<TranslationHelper>().translations.general.yes,
        onConfirmed: () {
          ApiServices.getApi(
                  "v1/delete/cohost?property_id=${dwellingDetail!.id}&id=${dwellingDetail!.propertyCoHost![index].id}&isrequest=${dwellingDetail!.propertyCoHost![index].isRequest!}",
                  isAuth: true)
              .then((response) {
            if (response.status) {
              ViewsCommon.showSnackbar(
                  Get.find<TranslationHelper>()
                      .translations
                      .success
                      .deleteSuccess,
                  keyword: DialogKeyword.warning,
                  displayTime: 2000);
              getDwellingDetail(dwellingDetail?.propertyCode);
              Get.until((route) => route.isFirst);
              ViewsCommon.showSnackbar(response.message,
                  keyword: DialogKeyword.warning, displayTime: 2000);
            }
          });
        }));
  }

  onSubmitInvite() {
    if (invitePhoneController.text.isNotEmpty &&
        inviteEmailController.text.isNotEmpty) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.listing.numOrMailOnce!,
          keyword: DialogKeyword.warning);
    } else {
      Get.to(() => const PermissionReview());
    }
  }

  onSubmitInvitePermission() async {
    isBtnLoading.value = true;
    Map formData = {
      "code":
          inviteMode.value == "Phone" ? "+${invitePhoneCountry.dialCode}" : "",
      "phone": inviteMode.value == "Phone" ? invitePhoneController.text : "",
    };
    ResponseModel payload = await ApiServices.postApi(
        "v1/update/property/${dwellingDetail?.id}?step=co_host",
        isAuth: true,
        body: formData);
    if (payload.status) {
      ListingHelper.updatePropertyEventCalled("co_host");
      Get.until((route) => route.isFirst);
      getDwellingDetail(dwellingDetail?.propertyCode);
    }
    isBtnLoading.value = false;
  }

  final dwellingTab = "".obs;
  final dwellings = RxList([]); //Rx<Map<String,List?>>({});
  final inProgressDuplication = RxList<DwellingModel>();
  Pagination? inProgressDuplicationPagination = Pagination();
  final isPropertyManageByDarentEnabled = false.obs;

  final selectedDwelling = RxnInt();
  setDwellingId(int id) {
    selectedDwelling.value = id;
    selectedDwelling.refresh();
  }

  removeDiscount({bool isForWeekly = false}) {
    if (isForWeekly) {
      weeklyDiscount.text = "0";
      ListingHelper.submitWeeklyDiscount(getPrice: true);
    } else {
      monthlyDiscount.text = "0";
      ListingHelper.submitMonthlyDiscount(getPrice: true);
    }
  }

  clearForDiscountModal() {
    selectedDwelling.value = null;
    weeklyDisabled.value = true;
    monthlyDisabled.value = true;
  }

  var priceBeforeDiscount = RxDouble(0.0);
  var priceAfterDiscount = RxDouble(0.0);
  final weeklyDisabled = true.obs, monthlyDisabled = true.obs;
  Pagination? dwellingsPagination = Pagination();
  final calendarProperties = RxList<DwellingModel>();
  final allPropertiesCount = 0.obs;

  DwellingDetail? dwellingDetail;

  GlobalKey<FormState> nameForm = GlobalKey<FormState>();
  TextEditingController name = TextEditingController();
  TextEditingController nameAr = TextEditingController();

  GlobalKey<FormState> descriptionForm = GlobalKey<FormState>();
  TextEditingController description = TextEditingController();
  TextEditingController descriptionAr = TextEditingController();
  TextEditingController extraPrice = TextEditingController();
  final propertyBathrooms = 0.0.obs,
      propertyBedrooms = 0.obs,
      propertySingleBeds = 0.obs,
      propertyDoubleBeds = 0.obs;

  GlobalKey<FormState> extraPriceKey = GlobalKey<FormState>();
  GlobalKey<FormState> addressKey = GlobalKey<FormState>();
  TextEditingController address = TextEditingController();
  // final bathtooms = 0.obs,bedrooms=0.obs,beds=0.obs;
  //property price
  final notificationSetting = false.obs;
  final instantBook = "instant".obs;
  final adults = 1.obs, children = 0.obs;
  final cancellation = "Flexible".obs;
  final status = true.obs;

  GlobalKey<FormState> importCalendarKey = GlobalKey<FormState>();
  final calenderColor = RxnString();
  TextEditingController calendarUrl = TextEditingController();
  TextEditingController calendarName = TextEditingController();

  TextEditingController weeklyDiscount = TextEditingController();
  TextEditingController monthlyDiscount = TextEditingController();

  final checkIn = const Duration(hours: 12, minutes: 00).obs;
  final checkOut = const Duration(hours: 14, minutes: 00).obs;
  //photos
  final tempPhotos = RxList<PropertyPhoto>();
  List deletePhotoIds = [];
  int coverIndex = 0;
  //basics
  int basicStep = 1;
  PageController basicStepController = PageController();
  // final necessities=false.obs,airConditioner=false.obs,cleaningProducts=false.obs,cookingEssentials=false.obs,workSpace=false.obs;

  final RxList<Map<String, dynamic>> calendarFeatures = [
    {"value": "nights_rates", "title": "Night rates", "checked": false},
    {"value": "promotions", "title": "Promotions", "checked": false},
    {"value": "popular_dates", "title": "Popular dates", "checked": false},
    {"value": "comments", "title": "Comments", "checked": false}
  ].obs;
  setTabTitle(i) {
    if (i == 0) {
      title = Get.find<TranslationHelper>().translations.footer.home;
    } else if (i == 1) {
      title =
          Get.find<TranslationHelper>().translations.hostDashboard.calendar!;
    } else if (i == 2) {
      title = Get.find<TranslationHelper>().translations.usersDashboard.message;
    } else if (i == 3) {
      title = Get.find<TranslationHelper>().translations.footer.properties;
    } else {
      title = Get.find<TranslationHelper>().translations.account.account;
    }
  }

  changeIndex(int val) {
    index = val;
    setTabTitle(val);

    // Track host dashboard opened event with Analytics Manager
    if (val == 0) {
      // Assuming index 0 is the main dashboard
      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.hostDashboardOpened,
        eventAttributes: {
          AnalyticsAttributes.hostId: userModel.value?.id?.toString(),
          AnalyticsAttributes.sessionTimestamp:
              DateTime.now().toIso8601String(),
          'dashboard_section': _getHostDashboardSection(val),
        },
      );
    }

    update();
  }

  String _getHostDashboardSection(int tabIndex) {
    switch (tabIndex) {
      case 0:
        return 'main_dashboard';
      case 1:
        return 'reservations';
      case 2:
        return 'calendar';
      case 3:
        return 'properties';
      case 4:
        return 'account';
      default:
        return 'unknown';
    }
  }

  // host DashBoard
  final avgScoring = Rxn<AvgScoringModel>();
  int unverifiedProperties = 0;
  final performanceReports = RxList<ReportModel>();
  //promotions
  int promoPage = 1, promoLastPage = 1;
  final promotions = <PromoModel>[].obs;
  TextEditingController propertySearch = TextEditingController();
  final propertySearchKey = "".obs;
  final promoFormKey = GlobalKey<FormState>();
  final selectPropsError = RxnString();

  bool isRangeCalendarOpen = false;
  bool toggleRangeDaySelection = false;
  toggleCalendarOpen() {
    isRangeCalendarOpen = !isRangeCalendarOpen;
    if (!isRangeCalendarOpen) {
      toggleRangeDaySelection = !toggleRangeDaySelection;
    } else {
      GlobalHelper.removeFocus();
    }
    update(["promo_form"]);
  }

  bool toggleDaySelection = false;

  final promoTitle = TextEditingController();
  final promoDiscount = TextEditingController();
  final promoCode = TextEditingController();
  final promoUpto = TextEditingController();
  final promoMaxUsage = TextEditingController();
  final promoUserUsage = TextEditingController();
  final promoDescription = TextEditingController();
  final forNewUser = false.obs;
  DateRangePickerController rangeController = DateRangePickerController();
  final selectedProps = RxnString();
  clearPromoFields() {
    promoTitle.clear();
    promoDiscount.clear();
    promoCode.clear();
    promoUpto.clear();
    promoMaxUsage.clear();
    promoUserUsage.clear();
    promoDescription.clear();
    forNewUser.value = false;
    rangeController.selectedRange = PickerDateRange(
        DateTime.now(), DateTime.now().add(const Duration(days: 1)));
    // promoStart.value = now;
    // promoEnd.value = now.add(const Duration(days: 1));
    propertySearchKey.value = "";
    for (var item in properties) {
      item.checked = false;
    }
    selectedProps.value = null;
    update();
  }
  // final promoStart = Rx(now);
  // final promoEnd = Rx(now.add(const Duration(days: 1)));

  final reservationTab = "checking-out".obs;
  final pageIndex = 0.obs;
  final transactionHistory = "return".obs;
  final taxes = "taxPayers".obs;
  final notificatons = "offer".obs;
  var emailRecog = true.obs;
  var notificationRecog = true.obs;
  var smsRecog = true.obs;
  var callRecog = true.obs;

  var emailInsig = true.obs;
  var notificationInsig = true.obs;
  var smsInsig = true.obs;
  var callInsig = true.obs;

  var emailPrice = true.obs;
  var notificationPrice = true.obs;
  var smsPrice = true.obs;
  var callPrice = true.obs;

  var emailPerks = true.obs;
  var notificationPerks = true.obs;
  var smsPerks = true.obs;
  var callPerks = true.obs;

  var emailNews = true.obs;
  var notificationNews = true.obs;
  var smsNews = true.obs;
  var callNews = true.obs;

  var emailLaws = true.obs;
  var notificationLaws = true.obs;
  var smsLaws = true.obs;
  var callLaws = true.obs;

  var emailInsp = true.obs;
  var notificationInsp = true.obs;
  var smsInsp = true.obs;

  var emailTrip = true.obs;
  var notificationTrip = true.obs;
  var smsTrip = true.obs;
  //editProperty
  int editStep = 1;
  changeEditStep(s) {
    editStep = s;
    update(['edit_main']);
  }

  discardPhotos() {
    if (kDebugMode) {
      print(Get.currentRoute);
    }
    Get.dialog(AlertDialog(
        content: const Text("Your changes will be discarded"),
        actions: [
          TextButton(onPressed: Get.back, child: const Text("Cancel")),
          TextButton(
              onPressed: () {
                if (Get.currentRoute == "/EditCover") {
                  coverIndex = dwellingDetail!.propertyPhotos!
                      .indexWhere((e) => e.coverPhoto == 1);
                } else {
                  tempPhotos.value = dwellingDetail?.tempPhotos ?? [];
                }
                Get.until((route) => route.settings.name == '/EditPhotos');
              },
              child: const Text("Okay",
                  style: TextStyle(color: Color(warningColor)))),
        ]));
  }

  changeCover(index) {
    if (coverIndex != index) {
      coverIndex = index;
      update();
    }
  }

  removePhoto(i, bool isApproved) {
    if (isApproved) {
      dwellingDetail!.propertyPhotos![i].softDelete =
          !dwellingDetail!.propertyPhotos![i].softDelete;
      tempPhotos.refresh();
    } else {
      if (tempPhotos[i].id > 0) {
        tempPhotos[i].softDelete = !tempPhotos[i].softDelete;
        tempPhotos.refresh();
      } else {
        tempPhotos.removeAt(i);
      }
    }
  }

  addPhotos() async {
    try {
      List<XFile> picked = await ImagePicker().pickMultiImage();
      if (picked.isNotEmpty) {
        tempPhotos.addAll(picked.map((e) => (PropertyPhoto(0, e.path))));
      }
    } catch (e) {
      e as PlatformException;
      if (e.code == "multiple_request") {
        return;
      }
      var status = await Permission.photos.status;
      if (status.isDenied || status.isPermanentlyDenied) {
        final request = await Permission.photos.request();
        if (request.isPermanentlyDenied) {
          Get.dialog(WarningDialog(
              title: "Permission Denied",
              description: "Please grant gallery permission to insert image.",
              onConfirmed: () async {
                final settings = await openAppSettings();
                if (kDebugMode) {
                  print("settings; $settings");
                }
                if (settings) {
                  addPhotos();
                }
              },
              keyword: DialogKeyword.info));
        }
      } else {
        addPhotos();
      }
    }
  }

  // backBasicStep(){
  //   isLoading.value = false;
  //   if(basicStep>1){
  //     basicStep--;
  //     update(['basic_step']);
  //     basicStepController.jumpToPage(basicStep-1);
  //   }else{
  //     Get.back();
  //   }
  // }
  submitNumberOfRoom() {
    int beds = propertySingleBeds.value + propertyDoubleBeds.value;
    if (propertyBedrooms.value > 0 && beds <= 0) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.listing.bedValidation!,
          keyword: DialogKeyword.warning);
      return;
    }
    isLoading.toggle();
    ListingHelper.updateProperty("numberofRoom", {
      "bedroom": propertyBedrooms.value,
      "single_beds": propertyBedrooms.value > 0 ? propertySingleBeds.value : 0,
      "double_beds": propertyBedrooms.value > 0 ? propertyDoubleBeds.value : 0,
      "bathroom": propertyBathrooms.value,
    }).then((value) {
      if (value) {
        dwellingDetail?.bedrooms = propertyBedrooms.value;
        dwellingDetail?.beds =
            propertySingleBeds.value + propertyDoubleBeds.value;
        dwellingDetail?.bathrooms = propertyBathrooms.value;
        update(['detail']);
      }
    });
  }

  submitName() {
    if (nameForm.currentState?.validate() ?? false) {
      GlobalHelper.removeFocus();
      isLoading.value = true;
      dwellingDetail?.name = name.text;
      dwellingDetail?.nameAr = nameAr.text;
      update(['detail']);
      ListingHelper.updateProperty(
          "title", {"name": name.text, "name_ar": nameAr.text},
          isSubmitted: true);
      Get.back();
    }
  }

  submitDescription() {
    if (descriptionForm.currentState?.validate() ?? false) {
      GlobalHelper.removeFocus();
      isLoading.value = true;
      dwellingDetail?.propertyDescription!.summary.value = description.text;
      dwellingDetail?.propertyDescription!.summaryAr.value = descriptionAr.text;
      update(['detail']);
      update(["edit_description"]);
      ListingHelper.updateProperty("description",
          {"summary": description.text, "summary_ar": descriptionAr.text},
          isSubmitted: true);
      Get.back();
    }
  }

  translate(String target, TextEditingController enC,
      TextEditingController arC) async {
    if (target == "ar") {
      if (enC.text.isEmpty) {
        return;
      }
    } else {
      if (arC.text.isEmpty) {
        return;
      }
    }
    http
        .get(Uri.parse(
            "https://translation.googleapis.com/language/translate/v2?target=$target&key=$mapKey&q=${target == "ar" ? enC.text : arC.text}"))
        .then((value) {
      if (value.statusCode == 200) {
        Map response = json.decode(value.body);
        if (target == "ar") {
          if (response['data']['translations'].isNotEmpty) {
            arC.text = response['data']['translations']
                .first['translatedText']
                .replaceAll('&quot;', '"')
                .replaceAll('&#39;', "'");
          }
        } else if (response['data']['translations'].isNotEmpty) {
          enC.text = response['data']['translations']
              .first['translatedText']
              .replaceAll('&quot;', '"')
              .replaceAll('&#39;', "'");
        }
      }
    });
  }

  nextStep({step}) {
    if (step != null) {
      basicStep = step;
    } else if (basicStep < 3) {
      basicStep++;
      basicStepController.jumpToPage(basicStep - 1);
    }
    update(['basic_step']);
  }

  setTransactionTab(val) {
    if (transactionHistory.value != val) {
      transactionHistory.value = val;
    }
  }

  setTaxesTab(val) {
    if (taxes.value != val) {
      taxes.value = val;
    }
  }

  reservationEvent() {
    String name = reservationTab.value == 'checking-out'
        ? 'Guests Checking out Viewed'
        : reservationTab.value == 'ongoing-bookings'
            ? 'Current Hosting Viewed'
            : 'Guest Arriving Soon Viewed';
    WebEngagePlugin.trackEvent(name, {
      'User': isHost ? 'Host' : 'Customer',
      'Date': formDateFormatCservice.format(DateTime.now())
    });
  }

  setReservationtab(val) {
    if (reservationTab.value != val) {
      reservationTab.value = val;
      getHomeBookings();
      if (val != 'pending-reviews' && val != 'upcoming-bookings') {
        reservationEvent();
      }
    }
  }

  setNotificationTab(val) {
    if (notificatons.value != val) {
      notificatons.value = val;
    }
  }

  nextPage() {
    if (pageIndex.value < 1) {
      pageIndex.value++;
    }
  }

  previousPage() {
    if (pageIndex.value > 0) {
      pageIndex.value--;
    }
  }

  checkCalendarFeature(item) {
    item['checked'] = !item['checked'];
    calendarFeatures.refresh();
  }

  clearSearch() {
    dwellingSearch.clear();
    GlobalHelper.removeFocus();
    if (dwellingsFilterForm.containsKey('search')) {
      dwellingsFilterForm.remove('search');
      getDwellings();
    }
  }

  submitSearch() {
    GlobalHelper.removeFocus();
    if (dwellingSearch.text.isNotEmpty) {
      dwellingsFilterForm['search'] = dwellingSearch.text;
    }
    getDwellings();
  }

  changeDwellingTab(value) {
    dwellingTab.value = value;
    dwellingsFilterForm['property_status'] = dwellingTab.value;
    dwellingsFilterForm['page'] = 1;
    // if(dwellings.value==null){
    getDwellings();
    Get.back();
    // }
  }

  clearDwellingFilter({onlyClear = false}) {
    // bedrooms.value = 0;
    // beds.value = 0;
    // bathtooms.value = 0;
    dwellingTab.value = '';
    dwellingsFilterForm = {"page": 1, "size": 8};
    SearchHelper.c.priceRange.value = SfRangeValues(
        SearchHelper.c.filters.value?.min_price ?? 5,
        SearchHelper.c.filters.value?.max_price ?? 5000);
    for (var item in SearchHelper.c.filters.value!.property_type) {
      item.isChecked = false;
    }
    dwellingsFilterForm['page'] = 1;
    if (!onlyClear) {
      getDwellings();
    }
    Get.back(result: false);
  }

  applyDwellingFilter() {
    List propertyTypeIds = [];
    for (var item in SearchHelper.c.filters.value!.property_type) {
      if (item.isChecked) {
        propertyTypeIds.add(item.id);
      }
    }
    dwellingsFilterForm = {
      "size": 8,
      // "bedroom":bedrooms.value,
      // "beds":beds.value,
      // "bathroom":bathtooms.value,
      'property_type': propertyTypeIds,
      'min_price': SearchHelper.c.priceRange.value.start,
      'max_price': SearchHelper.c.priceRange.value.end,
      "property_status": dwellingTab.value
    };
    Get.back(result: true);
    getDwellings(refresh: true);
  }

  getDwellingDetail(slug, {isWithLoading = false}) async {
    if (isWithLoading) {
      isLoading.value = true;
      isLoading.refresh();
    }
    final response = await ApiServices.getApi("v1/host-property/$slug");

    if (response.status) {
      dwellingDetail = DwellingDetail.fromJson(response.data);
      if (dwellingDetail!.missedStep!.isNotEmpty) {
        return dwellingDetail;
      }
      update(['detail']);
      update(['edit_main']);
      tempPhotos.value = dwellingDetail?.tempPhotos ?? [];
      coverIndex =
          dwellingDetail!.propertyPhotos!.indexWhere((e) => e.coverPhoto == 1);
      propertyBathrooms.value = dwellingDetail?.bathrooms ?? 0.0;
      propertyBedrooms.value = dwellingDetail?.bedrooms ?? 0;
      propertySingleBeds.value = dwellingDetail?.singleBeds ?? 0;
      propertyDoubleBeds.value = dwellingDetail?.doubleBeds ?? 0;
      address.text = dwellingDetail?.propertyAddress?.addressLine1 ?? "";
      name.text = dwellingDetail?.name ?? "";
      nameAr.text = dwellingDetail?.nameAr ?? "";
      description.text =
          dwellingDetail?.propertyDescription?.summary.value ?? "";
      descriptionAr.text =
          dwellingDetail?.propertyDescription?.summaryAr.value ?? "";
      List customAmenities =
          jsonDecode(dwellingDetail?.customAmenities ?? "[]");
      SearchHelper.c.customHouseRules.clear();
      for (var item in customAmenities) {
        if (item != null && item != 'null') {
          SearchHelper.c.customHouseRules
              .add(Amenity.fromJson({'title': item}));
        }
      }
      List amenities = dwellingDetail!.amenities!.split(',');
      List amen = [];
      List houseRules = [];
      for (var item in SearchHelper.c.filters.value?.amenities ?? []) {
        item.isChecked = amenities.contains("${item.id}");
        if (amenities.contains("${item.id}")) {
          amen.add(item.id);
        }
      }
      for (var item in SearchHelper.c.filters.value?.houseRules ?? []) {
        item.isChecked = amenities.contains("${item.id}");
        if (amenities.contains("${item.id}")) {
          houseRules.add(item.id);
        }
      }
      ListingHelper.setPrice();
      //cleaningFee.text = (dwellingDetail?.propertyPrice?.cleaningFee??0).toString();
      weeklyDiscount.text =
          (dwellingDetail?.propertyPrice?.weeklyDiscount ?? 0).toString();
      monthlyDiscount.text =
          (dwellingDetail?.propertyPrice?.monthlyDiscount ?? 0).toString();

      adults.value = dwellingDetail?.adultGuest ?? 1;
      children.value = dwellingDetail?.adultGuest ?? 0;
      instantBook.value = dwellingDetail?.bookingType ?? "instant";
      cancellation.value = dwellingDetail?.cancellation ?? 'Flexible';
      status.value = dwellingDetail?.visibility == 1;
      setCheckInCheckout();
    }
    if (isWithLoading) {
      isLoading.value = false;
      isLoading.refresh();
    }
  }

  setCheckInCheckout() {
    List<String> checkInList = dwellingDetail!.checkinTime!.split(":");
    checkIn.value = Duration(
        hours: int.tryParse(checkInList[0]) ?? 0,
        minutes: int.tryParse(checkInList[1]) ?? 0);

    List<String> checkOutList = dwellingDetail!.checkoutTime!.split(":");
    checkOut.value = Duration(
        hours: int.tryParse(checkOutList[0]) ?? 0,
        minutes: int.tryParse(checkOutList[1]) ?? 0);
  }

  getDwellings({refresh = true, displayWarning = false}) async {
    if (refresh) {
      dwellings.clear();
      isLoading.value = true;
      // dwellings.refresh();
    }
    if (dwellingsPagination != null) {
      if (refresh) {
        dwellingsPagination?.currentPage = 1;
      } else {
        dwellingsPagination?.currentPage++;
      }
      dwellingsFilterForm['page'] = dwellingsPagination?.currentPage;
    }
    final response = await ApiServices.postApi("v1/host/all-listing",
        body: dwellingsFilterForm, isAuth: true);
    if (response.status) {
      dwellingsPagination = Pagination.fromJson(response.data['pagination']);
      if (properties.isEmpty) {
        allProperties();
      }
      if (refresh) {
        avgScoring.value = AvgScoringModel.fromJson(response.data);
        unverifiedProperties = response.data['unverified_properties'] ?? 0;
        update();
        dwellings.value = response.data['hostListing']
            .map((item) => DwellingModel.fromJson(item))
            .toList();
        if (displayWarning && response.data['all_verified'] != 1) {
          await Future.delayed(const Duration(seconds: 2));
          Get.dialog(const LicenseWarningDialog());
        }
        isLoading.value = false;
      } else {
        dwellings.addAll(response.data['hostListing']
            .map((item) => DwellingModel.fromJson(item)));
      }
      // dwellings.refresh();
    }
  }

  getWeeklyScoring(int id) async {
    isLoading.value = true;
    final response =
        await ApiServices.getApi("v1/performance-report/$id", isAuth: true);
    if (response.status) {
      performanceReports.value = response.data
          .map<ReportModel>((e) => ReportModel.fromJson(e))
          .toList();
    }
    isLoading.value = false;
  }

  createPromo({int? id}) {
    final selectedProperties = properties.where((item) => item.checked);
    if (selectedProps.value == null || selectedProperties.isEmpty) {
      selectPropsError.value = Get.find<TranslationHelper>()
          .translations
          .jqueryValidation
          .selectPropertyDiscount;
    }
    if (promoFormKey.currentState!.validate() &&
        selectPropsError.value == null) {
      isLoading.value = true;
      Map formData = {
        "title": promoTitle.text,
        "promo_code": promoCode.text,
        "description": promoDescription.text,
        "percentage": promoDiscount.text,
        "discount_upto": promoUpto.text,
        "max_usage": promoMaxUsage.text,
        "per_user_usage": promoUserUsage.text,
        "for_new_customer": forNewUser.value,
        "startdate":
            formDateFormat.format(rangeController.selectedRange!.startDate!),
        "enddate":
            formDateFormat.format(rangeController.selectedRange!.endDate!),
        "property_id": []
      };
      for (final item in selectedProperties) {
        formData['property_id'].add(item.id);
      }
      ApiServices.postApi(
              "v1/promocode/${id != null ? "update/$id" : "insert"}",
              body: formData,
              isAuth: true)
          .then((response) {
        if (response.status) {
          getPromoCode(refresh: true);
          Get.dialog(WarningDialog(
              title: "success".tr,
              keyword: DialogKeyword.success,
              description: id != null
                  ? Get.find<TranslationHelper>()
                      .translations
                      .header
                      .promoUpdated
                  : Get.find<TranslationHelper>()
                      .translations
                      .header
                      .promoCreated,
              confirmText: 'okay'.tr,
              onConfirmed: () {
                Get.close(2);
                // Get.to(()=>const Promotions());
              }));
          clearPromoFields();
        }
        isLoading.value = false;
      });
    }
  }

  promoOptionSelected(String value, index) {
    if (value == "edit") {
      promoCode.text = promotions[index].code ?? "";
      promoTitle.text = promotions[index].title ?? "";
      promoDescription.text = promotions[index].description ?? "";
      promoDiscount.text = "${promotions[index].percentage ?? " "}";
      promoUpto.text = "${promotions[index].discountUpto ?? ""}";
      promoMaxUsage.text = "${promotions[index].maxUsage ?? ""}";
      promoUserUsage.text = "${promotions[index].perUserUsage ?? ""}";
      forNewUser.value = promotions[index].forNewCustomer == 1;
      if (promotions[index].startDate != null &&
          promotions[index].expiryDate != null) {
        rangeController.selectedRange = PickerDateRange(
            DateTime.parse(promotions[index].startDate!),
            DateTime.parse(promotions[index].expiryDate!));
      } else {
        rangeController.selectedRange = PickerDateRange(
            DateTime.now(), DateTime.now().add(const Duration(days: 1)));
      }
      for (var item in properties) {
        item.checked = promotions[index]
                .codeProperties
                ?.firstWhereOrNull((e) => e.property == item.id) !=
            null;
      }
      final temp = properties
          .where((item) => item.checked && item.id != 0)
          .map((filter) => filter.name);
      selectedProps.value = temp.join(", ");
      Get.to(() => PromoForm(id: promotions[index].id));
    } else if (value == "delete") {
      deletePromo(index);
    }
  }

  generateCode() {
    ApiServices.getApi("v1/promo/generate").then((response) {
      if (response.status) {
        promoCode.text = response.data;
      }
    });
  }

  toggleCheckAll() {
    for (var item in properties) {
      item.checked = properties[0].checked;
    }
    properties.refresh();
  }

  deletePromo(index) {
    Get.dialog(WarningDialog(
        title: 'warning'.tr,
        description:
            Get.find<TranslationHelper>().translations.header.sureToDelete,
        keyword: DialogKeyword.info,
        onConfirmed: () {
          ApiServices.getApi("v1/promocode/delete/${promotions[index].id}",
                  isAuth: true)
              .then((response) {
            if (response.status) {
              promotions.removeAt(index);
              promotions.refresh();
              Get.back();
            }
          });
        }));
  }

  contactGuest(BookingModel data) async {
    isBtnLoading.value = true;
    bookings.refresh();
    int? chatHeadId = await ChatHelper.contactHost(data.propertyId!,
        data.startDate, data.endDate, data.adults ?? 1, data.children ?? 0,
        chatHeadId: data.chatHeadId);
    if (chatHeadId != null) {
      data.chatHeadId = chatHeadId;
    }
    isBtnLoading.value = false;
    bookings.refresh();
  }

  final guestData = Rxn<BookingModel>();

  writeAReview(BookingModel data) {
    defaultReviews.clear();
    defaultCommunication.clear();

    somethingElseCommController.clear();
    overallRatingController.clear();
    privateNoteController.clear();
    // here
    defaultReviews.value = [
      {
        "title": Get.find<TranslationHelper>()
            .translations
            .hostReview
            .cleanlinessOption1,
        "isChecked": false,
      },
      {
        "title": Get.find<TranslationHelper>()
            .translations
            .hostReview
            .cleanlinessOption2,
        "isChecked": false,
      },
      {
        "title": Get.find<TranslationHelper>()
            .translations
            .hostReview
            .cleanlinessOption3,
        "isChecked": false,
      },
      {
        "title": Get.find<TranslationHelper>()
            .translations
            .hostReview
            .cleanlinessOption4,
        "isChecked": false,
      },
      {
        "title": Get.find<TranslationHelper>()
            .translations
            .hostReview
            .cleanlinessOption5,
        "isChecked": false,
      },
    ];
    defaultCommunication.value = [
      {
        "title": Get.find<TranslationHelper>()
            .translations
            .hostDashboard
            .communicationOption1,
        "isChecked": false,
      },
      {
        "title": Get.find<TranslationHelper>()
            .translations
            .hostDashboard
            .communicationOption2,
        "isChecked": false,
      },
      {
        "title": Get.find<TranslationHelper>()
            .translations
            .hostReview
            .communicationOption3,
        "isChecked": false,
      },
      {
        "title": Get.find<TranslationHelper>()
            .translations
            .hostReview
            .communicationOption4,
        "isChecked": false,
      },
      {
        "title": Get.find<TranslationHelper>()
            .translations
            .hostReview
            .communicationOption5,
        "isChecked": false,
      },
    ];
    guestData.value = data;
    update();
    Get.to(() => const ReviewInstructions());
  }

  final defaultReviews = RxList();
  final defaultCommunication = RxList();
  String selection = "Yes";
  double hostRating = 0.0;
  double evaluateRating = 0.0;
  bool somethingElse = false;
  bool somethingElseCommunicate = false;
  TextEditingController somethingElseController = TextEditingController();
  TextEditingController somethingElseCommController = TextEditingController();
  TextEditingController overallRatingController = TextEditingController();
  TextEditingController privateNoteController = TextEditingController();
  bool isReviewRequest = false;
  bool blockUser = false;

  submitHostReview() async {
    isReviewRequest = true;
    update(["private"]);
    List<String> cleanlinessMessage = [];
    cleanlinessMessage.addAll(defaultReviews
        .where((e) => e['isChecked'] && e['title'] != "Something Else")
        .map((el) => el['title'].toString())
        .toList());
    if (somethingElseController.text.isNotEmpty) {
      cleanlinessMessage.add(somethingElseController.text);
    }
    List<String> communicationMessage = [];
    communicationMessage.addAll(defaultCommunication
        .where((e) => e['isChecked'] && e['title'] != "Something Else")
        .map((el) => el['title'].toString())
        .toList());
    if (somethingElseCommController.text.isNotEmpty) {
      communicationMessage.add(somethingElseCommController.text);
    }
    Map formData = {
      "cleanliness": hostRating.ceil(),
      "cleanliness_message": cleanlinessMessage,
      "communication": evaluateRating.ceil(),
      "communication_message": communicationMessage,
      "message": overallRatingController.text,
      "ispublic": selection == "Yes" ? 1 : 0,
      "private_message": privateNoteController.text,
      "step": 5
    };
    debugPrint("Response = ${formData.toString()}");

    ResponseModel response = await ApiServices.postApi(
        "v2/host/bookings/${guestData.value!.id}/review",
        body: formData,
        isAuth: true);
    debugPrint("Response = ${response.data.toString()}");
    isReviewRequest = false;
    update(["private"]);
    if (response.status) {
      // Track host guest review published event with Analytics Manager
      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.hostGuestReviewPublished,
        eventAttributes: {
          AnalyticsAttributes.hostId: userModel.value?.id?.toString(),
          AnalyticsAttributes.guestId: guestData.value?.guest?.toString(),
          AnalyticsAttributes.bookingId: guestData.value?.id?.toString(),
          AnalyticsAttributes.reviewScore:
              ((hostRating + evaluateRating) / 2).round(),
          AnalyticsAttributes.reviewText: overallRatingController.text,
          AnalyticsAttributes.sessionTimestamp:
              DateTime.now().toIso8601String(),
          'is_public': selection == "Yes",
          'has_private_note': privateNoteController.text.isNotEmpty,
        },
      );

      bookingsTotal.value.pendingreviewsCount--;
      getHomeBookings(refresh: true);
      Get.until((route) => route.isFirst);
    }
  }

  getPromoCode({refresh = false}) async {
    if (refresh) {
      promoPage = 1;
    }
    ResponseModel responseModel = await ApiServices.getApi(
        "v1/get/promocode?page=$promoPage&size=12",
        isAuth: true);
    if (responseModel.status) {
      if (promoPage > 1) {
        promotions.addAll(responseModel.data['collection']
            .map<PromoModel>((item) => PromoModel.fromJson(item)));
        promotions.refresh();
      } else {
        promotions.value = responseModel.data['collection']
            .map<PromoModel>((item) => PromoModel.fromJson(item))
            .toList();
      }
      promoLastPage = responseModel.data['pagination']['total_pages'];
    }
  }

  generateReferralCode() async {
    if (!isBtnLoading.value) {
      isBtnLoading.value = true;
      update(['detail']);
      final response = await ApiServices.getApi(
          'v2/generateReferalLink/${dwellingDetail?.propertyCode}');
      if (response.status) {
        String url = '$baseUrl/ar${response.data['link']}';

        // Track guest referral sent event with Analytics Manager
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.guestReferralSent,
          eventAttributes: {
            AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
            AnalyticsAttributes.guestReferralLink: url,
            AnalyticsAttributes.guestReferralChannel: 'property_referral',
            AnalyticsAttributes.propertyId: dwellingDetail?.id?.toString(),
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
            'referral_type': 'property_share',
          },
        );

        Clipboard.setData(ClipboardData(text: url));
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>()
                .translations
                .hostDashboard
                .referralCopied!,
            displayTime: 750);
        Future.delayed(const Duration(milliseconds: 800), () {
          ViewsCommon.share(url,
              title: 'Referral Link', itemId: '${dwellingDetail?.id}');
        });
      }
      isBtnLoading.value = false;
      update(['detail']);
    }
  }

  updateOnRefresh() {
    getDwellings(refresh: true);
  }

  @override
  void onInit([bool displayWarning = true]) {
    ListingHelper(this);
    getHomeBookings();
    allProperties();
    getCalendarProperties();
    getPromoCode();
    super.onInit();
    getDwellings(displayWarning: displayWarning);
    reservationEvent();
    assignCoHostOwner();
    checkAndDisplayTermsAndCondition();
    //to be removed: invitePhoneCode.value = countryCodes.firstWhereOrNull((e) => e.phoneCode=="966");
    rangeController.selectedRange = PickerDateRange(
        DateTime.now(), DateTime.now().add(const Duration(days: 1)));

    // Track host dashboard opened event with Analytics Manager
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.hostDashboardOpened,
      eventAttributes: {
        AnalyticsAttributes.hostId: userModel.value?.id?.toString(),
        AnalyticsAttributes.sessionTimestamp: DateTime.now().toIso8601String(),
        AnalyticsAttributes.platform: 'mobile_app',
      },
    );
  }

  getNotificationPermission() async {
    final status = await Permission.notification.status;
    if (status.isGranted) {
      notificationSetting.value = true;
      return;
    } else {
      notificationSetting.value = false;
      return;
    }
  }

  final termsMarked = RxBool(false);
  markedTerms() {
    termsMarked.value = !termsMarked.value;
    termsMarked.refresh();
  }

  submitTerms() async {
    isLoading.value = true;

    Map formData = {
      "terms_condition": termsMarked.value,
    };

    final response = await ApiServices.postApi(
      "v1/host-agreement",
      body: formData,
      isAuth: true,
    );
    if (response.status) {
      userModel.value?.isTermsAndConditionMarked = 1;
      userModel.refresh();
      GlobalHelper.storageBox.write('user', userModel.value?.toJson());
      Get.back();
    }
    isLoading.value = false;
  }

  Future<WebViewController> getWebViewFuture() async {
    final url =
        "$baseUrl/${Get.locale?.languageCode ?? "en"}/property/hostAgreement?lang=${Get.locale?.languageCode ?? "en"}&mobile=1";

    final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }
    final controller = WebViewController.fromPlatformCreationParams(params);
    await controller.setJavaScriptMode(JavaScriptMode.unrestricted);
    await controller.setNavigationDelegate(NavigationDelegate(
        onNavigationRequest: (NavigationRequest navigation) =>
            NavigationDecision.navigate));
    await controller.loadRequest(Uri.parse(url));
    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }
    return controller;
  }

  checkAndDisplayTermsAndCondition() async {
    if (userModel.value?.isTermsAndConditionMarked != 1) {
      final controller = await getWebViewFuture();
      Get.to(() => AcknowledgeTermsDialogue(controller: controller));
    }
  }

  assignCoHostOwner() {
    coHostList.clear();
    Map myMap = {
      "title": "${userModel.value?.first_name} ${userModel.value?.last_name}",
      "designation": "Primary Host",
      "subtitle": "Listing Owner",
      "image": userModel.value?.profile_src ?? "",
    };
    coHostList.add(myMap);
  }

  getCalendarProperties() async {
    final response = await ApiServices.getApi(
        "v1/host/all-listing?page=1&size=100",
        isAuth: true);

    if (response.status) {
      calendarProperties.value = response.data['hostListing']
          .map<DwellingModel>((e) => DwellingModel.fromJson(e))
          .toList();
      allPropertiesCount.value = response.data['pagination']['total'] ?? 0;
    }
  }

  getHomeBookings({refresh = true, int increment = 1}) async {
    if (refresh) {
      bookingsPagination.currentPage = 1;
    } else {
      bookingsPagination.currentPage += increment;
    }
    try {
      isLoading.toggle();
      final response = await ApiServices.postApi("v1/${reservationTab.value}",
          isAuth: true,
          body: {"page": bookingsPagination.currentPage, "size": 5});
      if (response.status) {
        if (reservationTab.string == "checking-out") {
          bookingsTotal.value =
              BookingsTotal.fromJson(response.data['bookingsCount']);
        }
        bookingsPagination = Pagination.fromJson(response.data['pagination']);
        bookings.value = response.data['hostbookings']
            .map<BookingModel>((item) => BookingModel.fromJson(item))
            .toList();
      }
      isLoading.value = false;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      isLoading.value = false;
    }
  }

  allProperties() async {
    final response = await ApiServices.getApi("v1/host/all/properties");
    if (response.status) {
      properties.value = response.data
          .map<HostProperty>((item) => HostProperty.fromJson(item))
          .toList();
      if (properties.isNotEmpty) {
        selectedProperty.value = properties.first;
        getWeeklyScoring(properties.first.id);
      } else {
        selectedProperty.value = null;
      }
    }
  }
}
