import 'package:webengage_flutter/webengage_flutter.dart';

import '../helperMethods/globalHelpers.dart';
import '../helperMethods/search_helper.dart';
import '../utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
class PlanTripController extends GetxController{
  TextEditingController searchC = TextEditingController();
  TextEditingController filterC = TextEditingController();
  FocusNode searchFocus = FocusNode();
  FocusNode filterFocus = FocusNode();
  // final mapPlaces = RxList();
  final String? location;
  final List<DateTime>? dates;
  PlanTripController({this.location,this.dates});


  DateRangePickerController rangeC = DateRangePickerController();
  bool toggleDaySelection = false;

  int adults=1,children=0,infants=0,pets=0;

  bool isLocationOpen = false;
  List<dynamic> updatedList=[];
  bool isCalendarOpen = false;
  bool isGuestsOpen = false;

  toggleCalendarOpen(){
    isCalendarOpen = !isCalendarOpen;
    if(!isCalendarOpen){
      toggleDaySelection = !toggleDaySelection;
    }
    update();
  }
  toggleLocationOpen(){
    isLocationOpen = true;
    update();
  }
  toggleLocationClosed(){
    isLocationOpen = false;
    update();
  }
  toggleGuestsOpen(){
    isGuestsOpen = !isGuestsOpen;
    update();
  }
  String getFormattedDate(date){
    DateTime originalDate = DateTime.parse(date);
    String formattedDate = DateFormat('dd-MM-yyyy').format(originalDate);
    return formattedDate;
  }
  onRangeSelected(DateRangePickerSelectionChangedArgs args) async{
    if(args.value.startDate!=null && args.value.endDate!=null) {
      await WebEngagePlugin.trackEvent('Arrival Date & Departure Date', {
        "Date" : "${getFormattedDate(args.value.startDate.toString())} - ${getFormattedDate(args.value.endDate.toString())}",
        "User" : isHost ? "Host" : "Customer"
      });
    }
    update();
  }
  plusMinusAdults(String sign){
    if(sign=="+"){
      adults++;
    }else if(adults>1){
      adults--;
    }
    update();
  }
  plusMinusChildren(String sign){
    if(sign=="+"){
      children++;
    }else if(children>0){
      children--;
    }
    update();
  }
  plusMinusInfants(String sign){
    if(sign=="+"){
      infants++;
    }else if(children>0){
      infants--;
    }
    update();
  }
  plusMinusPets(String sign){
    if(sign=="+"){
      pets++;
    }else if(children>0){
      pets--;
    }
    update();
  }
  // getData()async{
  //   ResponseModel response = await ApiServices.getApi('https://maps.google.com/maps/api/geocode/json?key=$mapKey&address=${searchC.text}&sensor=false&libraries=places',fullUrl: true);
  //   if(response.status){
  //     mapPlaces.value = response.data['results'];
  //     update();
  //   }
  // }
  clearFilter(){
    Get.back(result:{});
    // rangeC.selectedRange = PickerDateRange(DateTime.now(), DateTime.now().add(const Duration(days: 1)));
    // searchC.clear();
    // adults = 1;
    // children = 0;
  }
  onSubmit(selectedCityId){
    Map formData = {
      "adult_guest":adults,
      "child_guest":children
    };
    formData['location']= selectedCityId;
    if(rangeC.selectedRange!=null){
      formData['checkin']= rangeC.selectedRange!.startDate!;
      if(rangeC.selectedRange!.endDate!=null && rangeC.selectedRange!.endDate!.difference(rangeC.selectedRange!.startDate!).inDays>0){
        formData['checkout']= rangeC.selectedRange!.endDate!;
      }else{
        formData['checkout']= rangeC.selectedRange!.startDate!.add(const Duration(days: 1));
        rangeC.selectedRange = PickerDateRange(formData['checkin'], formData['checkout']);
      }
    }
    Get.back(result: formData);
  }
  @override
  void onInit() {
    // here adding name of city on 1st index of c.cities also marking isChecked to true for 1st index
    if(SearchHelper.c.locationText == null && SearchHelper.c.cities.isNotEmpty){
      searchC.text = location??((Get.locale?.languageCode??'en') == 'en' ? SearchHelper.c.cities[0].name : SearchHelper.c.cities[0].nameAr);
      SearchHelper.c.selectedCity.value = SearchHelper.c.cities[0];
      SearchHelper.c.cities[0].isChecked = true;
      SearchHelper.c.cities.refresh();
    }else{
      searchC.text = SearchHelper.c.locationText??((Get.locale?.languageCode??'en') == 'en' ? SearchHelper.c.cities[0].name : SearchHelper.c.cities[0].nameAr);
    }
    if(dates!=null){
      rangeC.selectedRange = PickerDateRange(dates![0], dates![1]);
    }else{
      rangeC.selectedRange = PickerDateRange(GlobalHelper.getAbsoluteNowDate(), GlobalHelper.getAbsoluteNowDate().add(const Duration(days: 1)));
    }
    super.onInit();
  }
}