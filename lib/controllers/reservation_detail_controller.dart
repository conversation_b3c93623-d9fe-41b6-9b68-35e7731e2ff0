import 'package:darent/utils/api_service.dart';
import 'package:get/get.dart';
import '../helperMethods/search_helper.dart';
import '../models/reservation_detail_model.dart';

class ReservationDetailController extends GetxController{
  final String code;
  final bool forReview;
  ReservationDetailController(this.code,this.forReview);
  final data = Rxn<ReservationDetailModel>();
  bool allowCancel = false;
  @override
  void onInit() {
    getData();
    super.onInit();
  }
  getData(){
    ApiServices.getApi('v1/booking/$code').then((response){
      if(response.status){
        data.value = ReservationDetailModel.fromJson(response.data['guestbooking']);
        setCancellationCondition();
        calculateAmountNeedToDeductFromWallet();
        if(forReview){
          SearchHelper.c.clearRatingFields();
          SearchHelper.c.showRatingSheet(data);
        }
      }
    });
  }
  setCancellationCondition(){
    final now = DateTime.now();

    //parsing deadline
    List<String> checkInList = data.value!.checkinTime!.split(":");

    final checkInHr = int.tryParse(checkInList[0])??0;
    final checkInMin = int.tryParse(checkInList[1])??0;

    final checkInDateTime = DateTime(
        data.value!.startDate!.year,
        data.value!.startDate!.month,
        data.value!.startDate!.day,
        checkInHr,checkInMin
    );
    final deadline = checkInDateTime.subtract(const Duration(hours: 24));

    allowCancel = now.isBefore(deadline);
  }

  final totalAfterWallet = RxnDouble(0.0);

  calculateAmountNeedToDeductFromWallet(){
    double subTotal = data.value?.total??0.0;
    double remaining = subTotal - (data.value!.walletDeduction)!;
    totalAfterWallet.value = remaining;
  }
}