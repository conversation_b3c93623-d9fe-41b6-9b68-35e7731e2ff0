import 'package:darent/analytics/analytics_manager.dart';
import 'package:darent/components/common_button.dart';
import 'package:darent/components/common_checkbox.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/floating_text_field.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/models/bookingModel.dart';
import 'package:darent/models/host/review_model.dart';
import 'package:darent/models/pagination_model.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:webengage_flutter/webengage_flutter.dart';

import '../analytics/analytics.dart';
import '../helperMethods/translation_helper.dart';
import '../screens/new_host/host_reservation_details.dart';

class BookingsController extends GetxController
    with GetTickerProviderStateMixin {
  String? bookingCode;
  BookingsController({val, code}) {
    if (val != null && val != 'null') {
      //(val=='upcoming-bookings' || val=='cancelled-bookings' || val == 'all_reservation')
      tab = val;
      bookingCode = code;
    }
  }
  final isLoading = true.obs;
  String tab = "pending-bookings";
  Map formData = {"page": 1, "size": 8};
  late TabController tabController;
  // late TabController hostReviewTabController;
  var selectedSort = RxnString();

  final start = Rxn<DateTime>();
  final end = Rxn<DateTime>();
  Map pagination = <String, Pagination>{};
  final hostReviewData = Rx<Map<String, List<ListElement?>>>({});
  Map hostReviewPagination = <String, String?>{};
  Map hostReviewOverAllRating = <String, Average?>{};
  String hostReviewTab = "receiver_id";
  final data = Rx<Map<String, List?>>({});
  // final incomingData = Rx<Map<String,List?>>({});

  //acceptdecline
  TextEditingController optionalMsg = TextEditingController();
  TextEditingController otherReason = TextEditingController();
  TextEditingController bookingCodeController = TextEditingController();
  final isExpandNow = RxBool(false);

  final blockCalendar = false.obs;
  final declineReason = RxnString();
  final declineReasons = [
    "date_are_not_avialable",
    "not_feel_comfortable_guest",
    "listing_is_not_good",
    "waiting_more_attractive",
    "different_date_one_selected",
    "spam_message",
    "other"
  ];
  final declineError = RxnString();
  final otherDeclineError = RxnString();
  // String getReviewTab(){
  //   return hostReviewTabController.index==0
  //       ?'receiver_id'
  //       :hostReviewTabController.index==1
  //        ?'sender_id'
  //        :'receiver_id';
  // }
  changeReviewTab(val) {
    // String val = getReviewTab();
    if (hostReviewTab != val) {
      debugPrint("Change Review Tab = $val");
      hostReviewTab = val;
      update(['reviews']);
    }
  }

  String getTab() {
    return tabController.index == 0
        ? 'pending-bookings'
        : tabController.index == 1
            ? 'upcoming-bookings'
            : tabController.index == 2
                ? 'ongoing-bookings'
                : tabController.index == 3
                    ? 'history-bookings'
                    : tabController.index == 4
                        ? 'cancelled-bookings'
                        : tabController.index == 5
                            ? 'expired-bookings'
                            : 'all_reservation';
  }

  int getTabIndexBasedOnSLug(value) {
    return value == 'pending-bookings'
        ? 0
        : value == 'upcoming-bookings'
            ? 1
            : value == 'ongoing-bookings'
                ? 2
                : value == 'history-bookings'
                    ? 3
                    : value == 'cancelled-bookings'
                        ? 4
                        : value == 'expired-bookings'
                            ? 5
                            : 0;
  }

  changeTabBasedOnSlug(value) async {
    var index = getTabIndexBasedOnSLug(value);
    tabController.animateTo(index);
    checkAndNavigateToDetail();
  }

  changeTab() {
    String val = getTab();
    if (tab != val) {
      tab = val;
      // every time when tab change clear filter runs
      clearFilterValues(alsoGetData: data.value[val] == null);
      if (val == 'ongoing-bookings' ||
          val == 'cancelled-bookings' ||
          val == 'all_reservation') {
        String name = val == 'ongoing-bookings'
            ? 'Current Hosting Viewed'
            : val == 'cancelled-bookings'
                ? 'Canceled Reservations Viewed'
                : 'All Reservations Viewed';
        WebEngagePlugin.trackEvent(name, {
          'User': isHost ? 'Host' : 'Customer',
          'Date': formDateFormatCservice.format(DateTime.now())
        });
      }
    }
  }

  selectDate(dateTime, slug) {
    showDatePicker(
            context: Get.context!,
            initialDate: dateTime.value ?? DateTime.now(),
            firstDate: DateTime(DateTime.now().year - 20),
            lastDate: DateTime(DateTime.now().year + 15))
        .then((value) {
      if (value != null) {
        if (slug == 's') {
          if (end.value != null && value.isAfter(end.value!)) {
            start.value = end.value;
            end.value = value;
          } else {
            start.value = value;
          }
        } else if (slug == 'e') {
          if (start.value != null && value.isBefore(start.value!)) {
            end.value = start.value;
            start.value = value;
          } else {
            end.value = value;
          }
        }
      }
    });
  }

  selectSort(String? value) {
    selectedSort.value = value!;
  }

  clearFilters() {
    clearFilterValues();
    runFilter();
  }

  clearFilterValues({alsoGetData = false}) {
    formData.remove("property_ids");
    formData.remove("start_date");
    formData.remove("end_date");
    bookingCodeController.clear();
    formData.remove("booking_code");
    if (alsoGetData) {
      getData();
    }
    isExpandNow.value = false;
    GlobalHelper.removeFocus();
    start.value = null;
    end.value = null;
    bookingCodeController.clear();
  }

  applyFilter() {
    if (start.value != null && end.value != null) {
      formData['start_date'] = formDateFormat.format(start.value!);
      formData['end_date'] = formDateFormat.format(end.value!);
    }
    if (bookingCodeController.text.trim().isNotEmpty) {
      formData["booking_code"] = bookingCodeController.text;
    } else {
      formData.remove("booking_code");
    }
    getData();
    GlobalHelper.removeFocus();
  }

  applySort() {
    if (selectedSort.value != null) {
      List<String> sort = selectedSort.split("_")!;
      formData['filter_type'] = sort[0];
      formData['filter_value'] = sort[1];
      runFilter();
    }
  }

  runFilter() {
    getData();
    Get.back();
  }

  acceptBooking(BookingModel booking) {
    isLoading.value = false;
    Get.dialog(
      Dialog(
        insetPadding: EdgeInsets.symmetric(horizontal: widthSpace(10)),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(23)),
        child: Padding(
          padding: EdgeInsets.symmetric(
              horizontal: widthSpace(9), vertical: widthSpace(viewPadding)),
          child: Column(mainAxisSize: MainAxisSize.min, children: [
            FloatingTextField(
                controller: optionalMsg,
                labelText: "Write a message to Guest (Optional)"),
            SizedBox(height: heightSpace(2)),
            SizedBox(
                width: double.maxFinite,
                child: Obx(
                  () => CommonButton(
                      title: "Confirm",
                      isLoading: isLoading.isTrue,
                      onPressed: () {
                        isLoading.value = true;
                        ApiServices.postApi("v1/booking/accept/${booking.id}",
                                body: {"message": optionalMsg.text},
                                isAuth: true)
                            .then((response) {
                          if (response.status) {
                            booking.status = "Processing";
                            data.refresh();
                            // incomingData.refresh();
                            hostBookingRequestEventCalled(booking,
                                response.data['properties']['property_code']);
                            Get.back();
                          }
                          isLoading.value = false;
                        });
                      }),
                ))
          ]),
        ),
      ),
    ).then((value) {
      optionalMsg.clear();
    });
  }

  declineBooking(BookingModel booking) {
    DateTime start = booking.startDate!;
    DateTime end = booking.endDate!;
    Get.dialog(Dialog(
        insetPadding: EdgeInsets.symmetric(horizontal: widthSpace(5)),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(23)),
        child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(
                horizontal: widthSpace(9), vertical: widthSpace(viewPadding)),
            child: Obx(
              () => Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomText(
                        "${Get.find<TranslationHelper>().translations.bookingDetail.improveExperience} ${Get.find<TranslationHelper>().translations.bookingDetail.whatReasonCancelling}",
                        lineSpacing: 1.5,
                        color: const Color(greyText)),
                    SizedBox(height: heightSpace(2.5)),
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .bookingDetail
                            .responseNotShared,
                        size: 2.1,
                        color: const Color(greyText),
                        weight: FontWeight.bold),
                    SizedBox(height: heightSpace(3.5)),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 25),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(50),
                          border: Border.all(color: const Color(greyBorder))),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          isExpanded: true,
                          value: declineReason.value,
                          icon: const Icon(Icons.arrow_drop_down),
                          elevation: 16,
                          hint: CustomText(Get.find<TranslationHelper>()
                                  .translations
                                  .bookingDetail
                                  .toJson()[
                              declineReason.value?.tr ?? 'why_declining']),
                          style: const TextStyle(
                              fontSize: 16, color: Colors.black),
                          onChanged: (String? newValue) {
                            declineReason.value = newValue;
                            declineError.value = null;
                            otherDeclineError.value = null;
                          },
                          items: declineReasons
                              .map<DropdownMenuItem<String>>((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: CustomText(
                                  Get.find<TranslationHelper>()
                                      .translations
                                      .bookingDetail
                                      .toJson()[value],
                                  size: 1.8),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                    if (declineError.value != null)
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 18.0, vertical: 5),
                        child: CustomText("Please select decline reason",
                            size: 1.55, color: Colors.red[600]),
                      ),
                    if (declineReason.value == "other") ...[
                      SizedBox(height: heightSpace(3.5)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .bookingDetail
                              .whyDeclining,
                          weight: FontWeight.w500),
                      SizedBox(height: heightSpace(1.5)),
                      FloatingTextField(
                          controller: otherReason,
                          labelText: Get.find<TranslationHelper>()
                              .translations
                              .bookingDetail
                              .other,
                          fontSize: 2.5,
                          fontWeight: FontWeight.normal,
                          errorText: otherDeclineError.value)
                    ],
                    SizedBox(height: heightSpace(3.5)),
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .bookingDetail
                            .optionalMessageRequest,
                        weight: FontWeight.w500),
                    SizedBox(height: heightSpace(1.5)),
                    FloatingTextField(
                        controller: optionalMsg,
                        fontSize: 2.5,
                        fontWeight: FontWeight.normal,
                        labelText: Get.find<TranslationHelper>()
                            .translations
                            .hostDashboard
                            .message!),
                    SizedBox(height: heightSpace(3.5)),
                    CommonCheckBox(
                        isSelected: blockCalendar.isTrue,
                        onPressed: () {
                          blockCalendar.value = !blockCalendar.value;
                        },
                        title:
                            "${Get.find<TranslationHelper>().translations.bookingDetail.blockCalender} ${DateFormat.yMMMd().format(start)} ${Get.find<TranslationHelper>().translations.hostReservation.to} ${DateFormat.yMMMd().format(end)}",
                        isExpanded: true),
                    SizedBox(height: heightSpace(4.5)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        CommonButton(
                            fontSize: 1.8,
                            title: Get.find<TranslationHelper>()
                                .translations
                                .utility
                                .close,
                            onPressed: () => Get.back(),
                            backgroundBg: const Color(warningColor)),
                        SizedBox(width: widthSpace(2)),
                        CommonButton(
                            onPressed: isLoading.isTrue
                                ? null
                                : () {
                                    if (declineReason.value == null) {
                                      declineError.value = "";
                                      return;
                                    } else {
                                      if (declineReason.value == "other") {
                                        if (otherReason.text.isEmpty) {
                                          otherDeclineError.value =
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .jqueryValidation
                                                  .required;
                                          return;
                                        } else {
                                          otherDeclineError.value = null;
                                        }
                                      }
                                    }
                                    Map formData = {
                                      "decline_reason": declineReason.value,
                                      "decline_reason_other": otherReason.text,
                                      "block_calendar":
                                          blockCalendar.isTrue ? "yes" : "no",
                                      "message": optionalMsg.text
                                    };
                                    isLoading.value = true;
                                    ApiServices.postApi(
                                            "v1/booking/decline/${booking.id}",
                                            body: formData,
                                            isAuth: true)
                                        .then((response) {
                                      if (response.status) {
                                        int i = data.value[tab]!.indexWhere(
                                            (e) => e.id == booking.id);
                                        data.value[tab]![i].status = 'Declined';
                                        data.refresh();

                                        // Track booking cancelled event with Analytics Manager
                                        Get.find<AnalyticsManager>().trackEvent(
                                          AnalyticsEvents.bookingCancelled,
                                          eventAttributes: {
                                            AnalyticsAttributes.hostId:
                                                userModel.value?.id?.toString(),
                                            AnalyticsAttributes.bookingId:
                                                booking.id?.toString(),
                                            AnalyticsAttributes
                                                    .cancellationReason:
                                                declineReason.value ??
                                                    'host_declined',
                                            AnalyticsAttributes
                                                    .sessionTimestamp:
                                                DateTime.now()
                                                    .toIso8601String(),
                                            'cancelled_by': 'host',
                                          },
                                        );

                                        // incomingData.value[tab.value]!.removeWhere((e)=>e.id==booking.id);
                                        data.value['cancelled-bookings'] = null;
                                        hostBookingRequestEventCalled(
                                            booking,
                                            response.data['properties']
                                                ['property_code'],
                                            action: "decline");
                                        Get.back();
                                      }
                                      isLoading.value = false;
                                    });
                                  },
                            title: Get.find<TranslationHelper>()
                                .translations
                                .hostReservation
                                .decline,
                            isLoading: isLoading.isTrue),
                      ],
                    )
                  ]),
            )))).then((value) {
      declineReason.value = null;
      declineError.value = null;
      otherReason.clear();
      optionalMsg.clear();
      blockCalendar.value = false;
    });
  }

  hostBookingRequestEventCalled(BookingModel booking, unitCode,
      {String action = "accept"}) async {
    double costPerNight = booking.total! / booking.totalNights!;
    Map<String, dynamic> dataMap = {
      "action": action,
      "Guest ID": userModel.value!.id,
      "Name": "${userModel.value!.first_name} ${userModel.value!.last_name}",
      "Title": booking.propertyName,
      "Unit Code": unitCode ?? "",
      "Cost Per Night": costPerNight.toString()
    };
    await WebEngagePlugin.trackEvent("Host Booking Request", dataMap);

    // Track booking accepted/declined event with Analytics Manager
    if (action == "accept") {
      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.bookingConfirmed,
        eventAttributes: {
          AnalyticsAttributes.hostId: userModel.value?.id?.toString(),
          AnalyticsAttributes.guestId: booking.guest?.toString(),
          AnalyticsAttributes.bookingId: booking.id?.toString(),
          AnalyticsAttributes.propertyId: booking.propertyId?.toString(),
          AnalyticsAttributes.checkInDate:
              DateFormat('yyyy-MM-dd').format(booking.startDate!),
          AnalyticsAttributes.checkOutDate:
              DateFormat('yyyy-MM-dd').format(booking.endDate!),
          AnalyticsAttributes.timestamp: DateTime.now().toIso8601String(),
        },
      );
    } else if (action == "decline") {
      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.bookingDeclined,
        eventAttributes: {
          AnalyticsAttributes.hostId: userModel.value?.id?.toString(),
          AnalyticsAttributes.guestId: booking.guest?.toString(),
          AnalyticsAttributes.bookingId: booking.id?.toString(),
          AnalyticsAttributes.propertyId: booking.propertyId?.toString(),
          AnalyticsAttributes.declinedReason: declineReason.value,
          AnalyticsAttributes.timestamp: DateTime.now().toIso8601String(),
        },
      );
    }
  }

  @override
  void onInit() {
    getData(initUpcoming: true);
    getHostReviews(initEvaluation: true);
    // tabController.animateTo(scrollController.position.maxScrollExtent+500);
    // now getting again for upcoming
    super.onInit();
    tabController = TabController(
        length: 6,
        vsync: this,
        initialIndex: GlobalHelper.getBookingIndex(tab));
    // hostReviewTabController = TabController(length: 2, vsync: this, initialIndex: GlobalHelper.getHostReviewIndex(hostReviewTab));
    //pending-bookings
    // upcoming-bookings
    // ongoing-bookings
    // history-bookings
    // cancelled-bookings
    // expired-bookings
    // all_reservation
    tabController.addListener(changeTab);
    // hostReviewTabController.addListener(changeReviewTab);
  }

  justGetEvaluation(String tab) async {
    var url = "v2/host/reviews?page=1&type=$tab";
    final response = await ApiServices.getApi(url, isAuth: true);
    hostReviewPagination[tab] = response.data['reviews']['next_page'];
    hostReviewOverAllRating[tab] = Average.fromJson(response.data['average'],
        total: response.data['reviews']['total']);
    hostReviewData.value[tab] = response.data['reviews']['list']
        .map<ListElement>((item) => ListElement.fromJson(item))
        .toList();
    hostReviewData.refresh();
  }

  getHostReviews({refresh = true, bool initEvaluation = false}) async {
    if (refresh) {
      isLoading.value = true;
      update(['reviews']);
    }
    try {
      var url = "v2/host/reviews";
      if (!refresh && (hostReviewPagination[hostReviewTab] ?? '').isNotEmpty) {
        url = hostReviewPagination[hostReviewTab];
      } else {
        url += "?page=1";
      }
      String fullUrl = "$url&type=$hostReviewTab";
      final response = await ApiServices.getApi(
        fullUrl,
        fullUrl: url.isValidUrl(),
        isAuth: true,
      );
      if (response.status) {
        if (refresh) {
          hostReviewData.value[hostReviewTab] = response.data['reviews']['list']
              .map<ListElement>((item) => ListElement.fromJson(item))
              .toList();
        } else {
          hostReviewData.value[hostReviewTab]?.addAll(response.data['reviews']
                  ['list']
              .map<ListElement>((item) => ListElement.fromJson(item)));
        }
        hostReviewData.refresh();
        hostReviewPagination[hostReviewTab] =
            response.data['reviews']['next_page'];
        hostReviewOverAllRating[hostReviewTab] = Average.fromJson(
            response.data['average'],
            total: response.data['reviews']['total']);
        if (initEvaluation) {
          justGetEvaluation('sender_id');
        }
      }
      isLoading.value = false;
      update(['reviews']);
    } catch (e) {
      debugPrint("host/r reviews list error ${e.toString()}");
      isLoading.value = false;
      update(['reviews']);
    }
  }

  justGetData(String tab) async {
    final response =
        await ApiServices.postApi("v1/$tab", body: formData, isAuth: true);
    if (response.data is! Map) return;

    if (response.data.containsKey('pagination')) {
      pagination[tab] = Pagination.fromJson(response.data['pagination']);
    }

    if (response.data.containsKey('hostbookings')) {
      data.value[tab] = response.data['hostbookings']
          .map<BookingModel>((item) => BookingModel.fromJson(item))
          .toList();
    }

    update();
    data.refresh();
  }

  checkAndNavigateToDetail() {
    if (bookingCode != null && data.value['upcoming-bookings'] != null) {
      BookingModel? booking = data.value['upcoming-bookings']
          ?.firstWhereOrNull((i) => i.bookingCode == bookingCode);
      if (booking != null) {
        Get.to(() => HostReservationDetails(data: booking));
        bookingCode = null;
      }
    }
  }

  Future<void> getData({refresh = true, initUpcoming = false}) async {
    if (refresh) {
      isLoading.value = true;
    }
    if (pagination[tab] != null) {
      if (refresh) {
        pagination[tab].currentPage = 1;
        formData["page"] = 1;
      } else {
        pagination[tab].currentPage++;
        formData["page"] = pagination[tab].currentPage;
      }
    }
    // print("Index:// ${tabController.index}");
    try {
      final response =
          await ApiServices.postApi("v1/$tab", body: formData, isAuth: true);
      if (response.status) {
        if (false && tab == "upcoming-bookings") {
          //   if(refresh){
          //     incomingData.value[tab] = response.data['hostbookings'].map<BookingModel>((item)=>BookingModel.fromJson(item)).toList();
          //   }else{
          //     incomingData.value[tab]!.addAll(response.data['hostbookings'].map<BookingModel>((item)=>BookingModel.fromJson(item)));
          //   }
          //   incomingData.refresh();
        } else {
          pagination[tab] = Pagination.fromJson(response.data['pagination']);
          if (refresh) {
            data.value[tab] = response.data['hostbookings']
                .map<BookingModel>((item) => BookingModel.fromJson(item))
                .toList();
          } else {
            data.value[tab]!.addAll(response.data['hostbookings']
                .map<BookingModel>((item) => BookingModel.fromJson(item)));
          }
          checkAndNavigateToDetail();
          update();
          data.refresh();
          if (initUpcoming) {
            justGetData('upcoming-bookings');
            justGetData('ongoing-bookings');
          }
        }
      }
      isLoading.value = false;
    } catch (e) {
      isLoading.value = false;
    }
  }
}

extension _IsValidUrl on String {
  bool isValidUrl() {
    final asUri = Uri.parse(this);
    return asUri.hasScheme && asUri.host.isNotEmpty;
  }
}
