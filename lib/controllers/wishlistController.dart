import 'package:custom_info_window/custom_info_window.dart';
import 'package:darent/models/homeProperty.dart';
import 'package:darent/utils/api_service.dart';
import 'package:get/get.dart';

class WishlistController extends GetxController{
  final int groupId;
  WishlistController(this.groupId);
  int page = 1, totalPages = 1;
  final wishlist = RxList<HomeProperty>();
  final loader = true.obs;

  // Rx<Set<Marker>> markers = Rx<Set<Marker>>({});
  Map? selectedProperty;
  CustomInfoWindowController customInfoWindow = CustomInfoWindowController();

  final listView = true.obs;

  toggleView(){
    listView.toggle();
  }
  @override
  void onInit() {
    getData();
    super.onInit();
  }
  getData({refresh=false})async{
    if(refresh){
      loader.value = true;
      page = 1;
    }
    final response = await ApiServices.getApi("v1/all/wishlist/$groupId/properties?size=12&page=$page");
    if(response.status){
      if(page>1){
        wishlist.addAll(response.data['all_wishlist_properties'].map<HomeProperty>((item) => HomeProperty.fromJson(item)).toList());
        wishlist.refresh();
      }else{
        wishlist.value = response.data['all_wishlist_properties'].map<HomeProperty>((item)=>HomeProperty.fromJson(item)).toList();
      }
      // markers.value = {};
      // for (var item in wishlist) {
      //   if (item.latitude != null) {
      //     markers.value.addLabelMarker(LabelMarker(
      //         label: "${item.price} SAR",
      //         markerId: MarkerId(item.title!),
      //         position: LatLng(item.latitude!, item.longitude!),
      //         backgroundColor: const Color(themeColor),
      //         textStyle: const TextStyle(fontSize: 33, color: Colors.white)));
      //   }
      // }
      totalPages = response.data['pagination']['total_pages'];
    }
    // markers.refresh();
    loader.value = false;
  }
  initializeMap(controller){
    customInfoWindow.googleMapController = controller;
  }
}