import 'dart:async';
import 'dart:convert';
import 'package:darent/analytics/analytics.dart';
import 'package:darent/helperMethods/chat_helper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/models/chatHeadModel.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pusher_channels_flutter/pusher_channels_flutter.dart';
import '../models/bookingModel.dart';
import '../utils/routes.dart';

class ChatController extends GetxController {
  initWithInquiry(int? chatHeadId) {
    getChatHeads(chatHeadId: chatHeadId);
  }

  final chatHeads = <ChatHeadModel>[].obs;
  ChatHeadModel? selectedChatHead;
  BookingModel? booking;
  final messages = {}.obs;

  String? chatHeadNextPage;
  bool hasMoreChatHeads = false;

  String? messagesNextPage;
  bool hasMoreMessage = false;

  TextEditingController messageC = TextEditingController();
  ScrollController scrollController =
      ScrollController(initialScrollOffset: 100);

  final isLazyLoading = false.obs,
      isLoading = true.obs,
      areChatsLoading = false.obs;
  final isBtnLoading = false.obs;
  final currentTab = "inbox".obs;
  @override
  void onInit() {
    if (isUser) {
      getChatHeads();
      super.onInit();
    }
  }

  getChatHeads({bool paginate = false, int? chatHeadId}) {
    String url = "v3/${isHost ? "host" : "guest"}/properties/chats";
    if (paginate) {
      url = chatHeadNextPage ?? url;
    }
    if (chatHeadId != null) {
      url += "${url.contains("?") ? "&" : "?"}chat_head_id=$chatHeadId";
    }
    if (!paginate) {
      isLoading.value = true;
    }
    ApiServices.getApi(url,
            fullUrl: chatHeadNextPage != null && paginate, isAuth: true)
        .then((response) {
      if (response.status) {
        hasMoreChatHeads = response.data['chat_heads']['has_more'];
        chatHeadNextPage = response.data['chat_heads']['next_page'];
        if (chatHeadId != null) {
          //Get.currentRoute!="/Dashboard"
          selectedChatHead =
              ChatHeadModel.fromJson(response.data['chat_heads']['list'][0]);
          getChats(selectedChatHead?.id);
          Get.toNamed(Routes.inbox);
        }

        if (paginate) {
          chatHeads.addAll(response.data['chat_heads']['list']
              .map<ChatHeadModel>((item) => ChatHeadModel.fromJson(item)));
          chatHeads.refresh();
        } else {
          chatHeads.value = response.data['chat_heads']['list']
              .map<ChatHeadModel>((item) => ChatHeadModel.fromJson(item))
              .toList();
        }
        subscribeOnSocket();
      }
      if (!paginate) {
        isLoading.value = false;
      }
    });
  }

  getChats(id, {bool paginate = false}) async {
    String url =
        "v3/${isHost ? "host" : "guest"}/properties/chats/$id/messages";
    if (paginate) {
      url = messagesNextPage ?? url;
      isLazyLoading.value = true;
    } else {
      areChatsLoading.value = true;

  
    }
    try {
      ResponseModel response = await ApiServices.getApi(url,
          isAuth: true, fullUrl: messagesNextPage != null && paginate);
      if (response.status) {
        if (paginate) {
          updateMessages(response.data['messages']['list'], onPaginate: true);
          messages.refresh();
        } else {
          messages.value = response.data['messages']['list'];
          scrollToDown();
        }
        hasMoreMessage = response.data['messages']['has_more'];
        messagesNextPage = response.data['messages']['next_page'];
        if (selectedChatHead?.details?.id != null) {
          final res = await ApiServices.getApi(
              'v1/properties/${isHost ? 'host' : 'guest'}/${selectedChatHead!.details!.type == 'booking' ? 'bookings' : 'property-inquiries'}/${selectedChatHead!.details!.id}/details');
          // final res = await ApiServices.getApi('v1/chat/booking/${selectedChatHead?.details?.id}');
          if (res.status) {
            booking = BookingModel.fromChatJson(res.data['detail']);
          }
        } else {
          booking = null;
        }
      }
    } catch (e) {
      areChatsLoading.value = false;
      isLazyLoading.value = false;
    }
    areChatsLoading.value = false;
    isLazyLoading.value = false;
    if (!paginate) {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        if (scrollController.hasClients) {
          scrollController.jumpTo(scrollController.position.maxScrollExtent);
        }
      });
    }
  }

  sendMessage() async {
    if (messageC.text.trim().isEmpty) {
      return;
    }
    isBtnLoading.value = true;
    Map formData = {"message": messageC.text};
    try {
      final response = await ApiServices.postApi(
          "v3/${isHost ? "host" : "guest"}/properties/chats/${selectedChatHead?.id}/messages",
          body: formData,
          allowDialog: false,
          isAuth: true);
      if (response.status && response.data['messages'] is Map) {


        GlobalHelper.removeFocus();
        messageC.clear();
        Map jsonKey = response.data['messages'];
        selectedChatHead!.latestMessage = response.data['latest_message'];
        selectedChatHead!.latestMessageDate =
            response.data['latest_message_date'];
        updateMessages(jsonKey);
      } else if (response.data['status'] == 'block') {
        ChatHelper.handleBlockMsg(response.data['blocked_word']);
      }
    } catch (e) {
      print(e.toString());
      messageC.clear();
      isBtnLoading.value = false;
    }
    isBtnLoading.value = false;
  }

  updateMessages(Map data, {onPaginate = false}) {
    for (var dateKey in data.keys) {
      if (messages[dateKey] != null) {
        for (String timeKey in data[dateKey].keys) {
          if (messages[dateKey][timeKey] != null) {
            bool doMerMessages = onPaginate
                ? messages[dateKey][timeKey][0]['sender'] != null &&
                    data[dateKey][timeKey].last['sender'] != null &&
                    messages[dateKey][timeKey][0]['sender']['id'] ==
                        data[dateKey][timeKey].last['sender']['id']
                : messages[dateKey][timeKey].last['sender'] != null &&
                    data[dateKey][timeKey][0]['sender'] != null &&
                    messages[dateKey][timeKey].last['sender']['id'] ==
                        data[dateKey][timeKey][0]['sender']['id'];
            if (doMerMessages) {
              if (onPaginate) {
                messages[dateKey][timeKey][0]['messages'] = [
                  ...data[dateKey][timeKey].last['messages'],
                  ...messages[dateKey][timeKey][0]['messages']
                ];
              } else {
                messages[dateKey][timeKey]
                    .last['messages']
                    .addAll(data[dateKey][timeKey][0]['messages']);
              }
            } else {
              if (onPaginate) {
                messages[dateKey][timeKey].insertAll(0, data[dateKey][timeKey]);
              } else {
                messages[dateKey][timeKey].addAll(data[dateKey][timeKey]);
              }
            }
          } else {
            if (onPaginate) {
              messages[dateKey] = {
                ...{timeKey: data[dateKey][timeKey]},
                ...messages[dateKey]
              };
            } else {
              messages[dateKey] = {
                ...messages[dateKey],
                ...{timeKey: data[dateKey][timeKey]}
              };
            }
          }
        }
        messages.refresh();
      } else {
        if (onPaginate) {
          messages.value = {
            ...{dateKey: data[dateKey]},
            ...messages
          };
        } else {
          messages.value = {
            ...messages,
            ...{dateKey: data[dateKey]},
          };
        }
      }
    }
    if (!onPaginate) {
      chatHeads.refresh();
      scrollToDown();
    }
  }

  clearMessages() {
    messages.clear();
    hasMoreMessage = false;
    messagesNextPage = null;
  }

  PusherChannelsFlutter pusher = PusherChannelsFlutter.getInstance();
  subscribeOnSocket() async {
    try {
      await pusher.init(
          apiKey: "9e2445ed5e4a2c3a8af8",
          cluster: "ap2",
          // onAuthorizer:(channelName, socketId, options){
          //   print({
          //     "auth": "731ae615eb253a5c3895:ca8c8aa4a8c3ac6996fefce6391b766d23cdfce8edf1cd71c19526576a79ee05",
          //     "channel_data": '{"user_id": ${accountC.userModel.value!.id}',
          //     "channel_data": '{"user_id": ${accountC.userModel.value!.id}',
          //     "shared_secret": "6d2ba047a6d235ffcdf1",
          //     "socket_id":socketId,
          //     "channel_name":channelName
          //   });
          //   return {
          //     "auth": "Bearer ${accountC.userModel.value!.token}",
          //     "channel_data": '{"user_id": ${accountC.userModel.value!.id}}',
          //     "shared_secret": "6d2ba047a6d235ffcdf1",
          //     "socket_id":socketId,
          //     "channel_name":channelName
          //   };
          // },
          onEvent: onEvent);
      await pusher.connect();
      await pusher.subscribe(channelName: _ChatChannelName().toString());
    } catch (e) {
      debugPrint("ERROR: $e");
    }
  }

  scrollToDown() {
    Timer(const Duration(milliseconds: 600), () {
      if (scrollController.hasClients) {
        scrollController.animateTo(scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 200), curve: Curves.easeOut);
      }
    });
  }

  dynamic onAuthorizer(
      String channelName, String socketId, dynamic options, int id) async {
    return {
      "auth": "foo:bar",
      "channel_data": '{"user_id": $id}',
      "shared_secret": "6d2ba047a6d235ffcdf1",
      "socket_id": socketId,
      "channel_name": channelName
    };
    // String token = await Store.read("token");
    // var authUrl = "$basePath/broadcasting/auth";
    // var result = await http.post(
    //   Uri.parse(authUrl),
    //   headers: {
    //     'Content-Type': 'application/x-www-form-urlencoded',
    //     'Authorization': 'Bearer ${token}',
    //   },
    //   body: 'socket_id=$socketId&channel_name=$channelName',
    // );
    // var json = jsonDecode(result.body);
    // return json;
  }

  void onEvent(PusherEvent event) {
    if (event.eventName.contains("PropertyChatMessageSend")) {
      Map data = jsonDecode(event.data);
      if (data['is_message'] == true &&
          Get.currentRoute == Routes.inbox &&
          data['chat_head_id'] == selectedChatHead?.id) {
        updateMessages(data['messages']);
      }
      getChatHeads();
    }
  }

  changeTab(value) {
    currentTab.value = value;
  }

  @override
  void onClose() {
    try {
      _unsubscribeFromPusher();
    } catch (e) {
      print('Error while subscribing from Channel: $e');
    }
    super.onClose();
  }

  Future<void> closeChatChannel() async {
    await _unsubscribeFromPusher();
    await _disconnectPusher();
  }

  Future<void> _disconnectPusher() async {
    if (pusher.connectionState != 'DISCONNECTED') await pusher.disconnect();
  }

  Future<void> _unsubscribeFromPusher() async {
    if (pusher.channels.containsKey(_ChatChannelName().toString())) {
      await pusher.unsubscribe(channelName: _ChatChannelName().toString());
    }
  }
}

class _ChatChannelName {
  @override
  String toString() {
    return 'Property.Chat.${userModel.value?.id}';
  }
}
