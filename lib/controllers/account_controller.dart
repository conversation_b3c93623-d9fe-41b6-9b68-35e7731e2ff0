import 'dart:async';
import 'dart:io';
import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/components/warning_dialog.dart';
import 'package:darent/controllers/chat_controller.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/controllers/property_detail_controller.dart';
import 'package:darent/helperMethods/authHelper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/payment_helper.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/analytics/analytics.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:darent/inputFormatters/cardExpFormatter.dart';
import 'package:darent/models/cardModel.dart';
import 'package:darent/models/listing_prefs_model.dart';
import 'package:darent/models/ticketModel.dart';
import 'package:darent/models/transactionModel.dart';
import 'package:darent/models/userModel.dart';
import 'package:darent/screens/Transactions.dart';
import 'package:darent/screens/authentication/captcha.dart';
import 'package:darent/screens/authentication/change_email.dart';
import 'package:darent/screens/authentication/login.dart';
import 'package:darent/screens/authentication/login_SignUp.dart';
import 'package:darent/screens/listing_journey/add_property11.dart';
import 'package:darent/screens/myfatoorah_screen.dart';
import 'package:darent/screens/problem/problem_listing.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/routes.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_field/countries.dart';
import 'package:moyasar/moyasar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';
import 'package:syncfusion_flutter_core/core.dart' show HijriDateTime;
import 'package:webengage_flutter/webengage_flutter.dart';
import '../components/custom_email_verification_sheet.dart';
import '../helperMethods/translation_helper.dart';
import '../models/managerModel.dart';
import '../screens/accountManger/account_manage_list.dart';
import '../screens/accountManger/addAccountManage.dart';
import '../screens/authentication/otp.dart';
import '../screens/listing_journey/publish_listing.dart';
import '../utils/api_constants.dart';

class AccountController extends SuperController {
  var messageOtpCode = ''.obs;
  final otpError = RxnString();
  final tmpToken = RxnString();
  var isChecked = false.obs;

  TextEditingController firstName = TextEditingController();
  TextEditingController lastName = TextEditingController();
  TextEditingController phone = TextEditingController();
  //to be removed final phoneError = false.obs;
  // final phoneCode = Rxn<CountryCode>();
  Country phoneCountry = countries.firstWhere((e) => e.code == 'SA');
  TextEditingController controller = TextEditingController();
  final isPassVisible = true.obs;
  final isConfPassVisible = true.obs;
  final isConfPassVisibleAM = true.obs;

  var focusNode = FocusNode();
  var isEmailExist = false.obs;
  DateFormat expMothFormatter = DateFormat("MM");
  DateFormat expYearFormatter = DateFormat("yy");
  TextEditingController cardHolder = TextEditingController();
  TextEditingController expMonthYear = TextEditingController();

  void formatText(String text) {
    if (text.length == 2 && !text.contains('/')) {
      int? month = int.tryParse(text.substring(0, 2));
      if (month! > 12) {
        month = 12;
      }
      expMonthYear.text = '${month.toString().padLeft(2, '0')}/';
      expMonthYear.selection =
          TextSelection.fromPosition(const TextPosition(offset: 3));
    }
  }

  TextEditingController cardNumber = TextEditingController();
  TextEditingController expMonth = TextEditingController();
  TextEditingController expYear = TextEditingController();
  TextEditingController cvvCard = TextEditingController();
  TextEditingController addAmount = TextEditingController();
  final selectedExpMonth = DateTime.now().obs;
  final expDateError = RxnString();

  final defaultCardId = 0.obs;
  final myCards = RxList<CardModel>();
  final myWallet = RxList<WalletModel>();
  final myTransactions = RxList<TransactionModel>();
  final totalSales = RxnString();
  // final myReviews = RxList<ReviewModel>();
  int transactionsPage = 1, transactionsSize = 5, totalPages = 1;
  final dob = Rxn<DateTime>();
  final dobVerification = Rxn<DateTime>();
  final dobHijriDate = Rxn<HijriDateTime>();
  TextEditingController iqma = TextEditingController();
  String selectedCountryCode = "sa";
  // final verificationCode = false.obs;
  var iqamaValue = 5.obs;
  void updateIqamaValue(int? value) {
    iqma.clear();
    iqamaValue.value = value ?? 5;
  }

  TextEditingController liveC = TextEditingController();
  TextEditingController about = TextEditingController();
  //pick month
  final selectedMonth = RxnString();

  final firstNameEnabled = false.obs;
  final lastNameEnabled = false.obs;
  final liveEnabled = true.obs;
  // final aboutEnabled = true.obs;
  final emailChangeEnabled = false.obs;
  final otpChangeEnabled = false.obs;
  final submitOtpEnabled = false.obs;
  final phoneEnabled = false.obs;

  final firstError = RxnString();
  final lastError = RxnString();
  final emailError = RxnString();
  final dobError = false.obs;

  final serviceImage = RxnString();
  TextEditingController serviceMessageField = TextEditingController();
  List<dynamic> serviceMessages = [];
  //List serviceMessages = [];
  final ScrollController serviceScroll = ScrollController();
  //DateTime createdAtDateTime = DateTime.parse(serviceMessages[index]['created_at']);

  int serviceMessagesPage = 1, serviceMessagesSize = 5, totalMessagesPage = 1;
  final reportFormKey = GlobalKey<FormState>();
  TextEditingController ticketSubject = TextEditingController();
  TextEditingController bookingId = TextEditingController();
  TextEditingController ticketDesc = TextEditingController();
  final complainImage = RxnString();
  final ticketType = RxnInt();
  final ticketError = RxnString();
  final ticketListing = RxList<TicketModel>();
  int ticketLastPage = 1, ticketPage = 1;

  // account manager
  final managerFormKey = GlobalKey<FormState>();
  TextEditingController firstNameMang = TextEditingController();
  TextEditingController lastNameMang = TextEditingController();
  TextEditingController emailMang = TextEditingController();
  TextEditingController passMang = TextEditingController();
  TextEditingController phoneMang = TextEditingController();
  final statusMang = "Active".obs;

  final statusMangError = RxnString();
  // manger listing
  final managers = RxList<ManagerList>();
  int managerPage = 1;
  int lastManagerPage = 1;
  final managerLazyLoader = false.obs;
  //bank Account
  final bankFormKey = GlobalKey<FormState>();
  TextEditingController bankName = TextEditingController();
  TextEditingController accTitle = TextEditingController();
  TextEditingController accNumber = TextEditingController();
  TextEditingController accIban = TextEditingController();
  TextEditingController swiftCode = TextEditingController();
  TextEditingController accPhone = TextEditingController();

  final isBankOpen = RxBool(false);
  FocusNode bankFocus = FocusNode();
  TextEditingController filter = TextEditingController();
  final selectedBank = Rxn<BankModel>();
  final updatedList = RxList([]);

  toggleBankOpen() {
    isBankOpen.value = true;
  }

  toggleBankClosed() {
    isBankOpen.value = false;
  }

  double rating = 0.0;
  // final myReviewsByYou = RxList<ReviewModel>();
  TextEditingController ratingController = TextEditingController();
  final ratingImage = RxnString();

  //login
  final loginFormKey = GlobalKey<FormState>();
  final signUpFormKey = GlobalKey<FormState>();

  final changeEmailKey = GlobalKey<FormState>();

  //final detailLoginKey = GlobalKey<FormState>();
  final profileLoginKey = GlobalKey<FormState>();

  // final otpFormKey = GlobalKey<FormState>();
  // final GoogleSignIn _googleSignIn = GoogleSignIn(
  //   scopes: [
  //     'email',
  //     'profile',
  //   ],
  // );

  TextEditingController email = TextEditingController();
  TextEditingController tempEmail = TextEditingController();
  TextEditingController passwordLogin = TextEditingController();
  final isLoading = false.obs;
  final isLoadingGoogle = false.obs;

  // final loginMode = RxnString();
  final selectedCurrency = "sar".obs;

  final contactMsg = TextEditingController();

  final deleteFeedbackForm = GlobalKey<FormState>();
  final deleteFeedback = TextEditingController();

  //otp verification(email, phone)
  final isLoadingEmail = false.obs;
  var enableResend = false.obs;
  RxInt secondsRemaining = 60.obs;
  Timer? timer;

  selectLanguage(val) async {
    isLoadingGoogle.value = true;
    final response = await ApiServices.postApi("v1/set-language",
        body: {"lang": val}, isAuth: true);
    if (response.status) {
      Get.updateLocale(Locale(val));
      userModel.value!.lang = val;
      GlobalHelper.storageBox.write('user', userModel.value?.toJson());
      if (isHost) {
        HostDashboardController c = Get.find();
        c.onInit(false);
      } else {
        SearchHelper.c.getData();
        SearchHelper.clearFilter();
        SearchHelper.planTripSearch({});
        SearchHelper.c.getCitiesData();
        SearchHelper.c.reloadForLanguage();
      }
    }
    isLoadingGoogle.value = false;
  }

  selectCurrency(val) {
    selectedCurrency.value = val;
  }

  showLogout() {
    Get.dialog(WarningDialog(
        keyword: DialogKeyword.info,
        title: Get.find<TranslationHelper>().translations.header.logout,
        description:
            Get.find<TranslationHelper>().translations.modal.logoutWarn,
        isIcon: true,
        onConfirmed: onLogout));
  }

  onLogout() {
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.guestSessionEnd,
      eventAttributes: {
        AnalyticsAttributes.userType: isHost ? 'Host' : 'Customer',

        AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
        AnalyticsAttributes.guestSessionDuration:
            0, // Default to 0 as we don't have lastLogin
        AnalyticsAttributes.guestLastInteractionType: 'logout',
        AnalyticsAttributes.sessionTimestamp: DateTime.now().toIso8601String(),
      },
    );

    ApiServices.getApi("v1/mobile-logout", isAuth: true, showDialog: false);
    isUser = false;
    GlobalHelper.storageBox.write("isHost", false);
    SearchHelper.c.clearUserData();
    SearchHelper.c.changeIndex(0);
    Get.until((route) => route.isFirst);
    GlobalHelper.storageBox.remove('user');
    clearLoginFields();
    ticketListing.value = [];
    ticketPage = 1;
    dob.value = null;
    dobVerification.value = null;
    myCards.value = [];
    myWallet.value = [];
    myTransactions.value = [];
    // myReviews.value = [];
    transactionsPage = 1;
    transactionsSize = 5;
    if (isHost) {
      isHost = false;
      Get.offAllNamed(Routes.home);
    } else {
      Get.until((route) => route.isFirst);
    }
    // c.bottomSheetListener(null);
    SearchHelper.c.listView.value = true;
    SearchHelper.clearFilter();
    SearchHelper.c.getInitialData();
    userModel.value = null;
    bankName.clear();
    accTitle.clear();
    accNumber.clear();
    accIban.clear();
    accPhone.clear();
    phoneCountry = countries.firstWhere((e) => e.code == 'SA');
    selectedBank.value = null;
    updatedList.clear();
    filter.clear();
    toggleBankClosed();
    // _googleSignIn.signOut();
    WebEngagePlugin.userLogout();

    // Reset Analytics Manager user
    Get.find<AnalyticsManager>().resetUser();

    if (Get.isRegistered<ChatController>()) {
      Get.find<ChatController>()
          .closeChatChannel()
          .then((_) => Get.delete<ChatController>());
    }
  }

  clearLoginFields() {
    firstName.clear();
    lastName.clear();
    email.clear();
    phone.clear();
    passwordLogin.clear();
    // loginMode.value = null;
  }

  handleLogin(userData, {bool isForSignup = false}) async {
    if(kDebugMode) {print("User language ${userModel.value?.lang}");}
    userModel.value = UserModel.fromJson(userData);
    if(isForSignup){
      // After the user completes the signup process, we can automatically detect the device's language
      // setting. If the device language is set to Arabic, the app's language will default to Arabic.
      // For all other languages, the app will default to English.
      userModel.value?.lang = (Get.deviceLocale?.languageCode ??"en") == "ar" ? "ar" : "en";
      userModel.refresh();
    }
    AuthHelper.removeGuestUuid();
    Get.updateLocale(Locale(userModel.value?.lang ?? 'en'));
    // userModel.refresh();
    fillUserFields(isHandleLoginResponse: true);
    SearchHelper.c.update();
    GlobalHelper.storageBox.write('user', userModel.value?.toJson());
    if (Get.isRegistered<ChatController>()) {
      ChatController c = Get.find();
      c.getChatHeads();
    }
    // loginMode.value = null;
    passwordLogin.clear();
    SearchHelper.c.onInit();
    // c.reloadForLanguage();
    getCards();
    getTransactions();
    // getReviews();
    if (baseUrl.contains("dev")) {
      getManagerList();
    }
    // if (isHost) {
    //   HostDashboardController hC = Get.find();
    //   hC.onInit();
    // }
    getTicketListing(refresh: true);
    WebEngagePlugin.userLogin(
        '${userModel.value?.uuid ?? userModel.value?.id}');
  }

  showHidePassword() {
    isPassVisible.value = !isPassVisible.value;
  }

  showHideConfirmPassword() {
    isConfPassVisible.value = !isConfPassVisible.value;
  }

  showHideConfirmPasswordAM() {
    isConfPassVisibleAM.value = !isConfPassVisibleAM.value;
  }

  animationSubmit() {
    isLoading.value = !isLoading.value;
  }

  onSubmitChangeEmail({bool isNavigate = true}) async {
    if (changeEmailKey.currentState!.validate()) {
      isLoading.value = true;
      ResponseModel payload = await ApiServices.postApi("v1/email/update",
          isAuth: true,
          body: {"email": isNavigate ? email.text : tempEmail.text});
      if (payload.status) {
        if (isNavigate) {
          email.text = payload.data["user"]["email"];
          Get.to(() => const OtpVerification(isEmailOtp: true));
        } else {
          tempEmail.text = payload.data["user"]["email"];
          secondsRemaining.value = 60;
          startResendTimer();
          otpChangeEnabled.value = true;
        }
      }
      isLoading.value = false;
    }
    return;
  }

  onSubmitVerifyOtp() async {
    if (controller.value.text.isEmpty) {
      otpError.value = 'emptyError'.tr;
      return;
    }
    isLoading.value = true;
    otpError.value = null;
    ResponseModel response = await ApiServices.postApi("v1/email/otp/verify",
        body: {"otp": controller.value.text}, isAuth: true, allowDialog: false);
    if (response.status) {
      if (GlobalHelper.storageBox.hasData('user')) {
        email.text = tempEmail.text;
        userModel.value!.email = tempEmail.text;
        userModel.value!.userVerification?.email = "yes";
        userModel.refresh();
        GlobalHelper.storageBox.write('user', userModel.value?.toJson());
      }
      isLoading.value = false;
      controller.clear();
      timer?.cancel();
      Get.back();
    } else {
      Get.dialog(WarningDialog(
        title: "error".tr,
        description: response.message,
      ));
      isLoading.value = false;
      controller.clear();
    }
  }

  onSubmitLoginEmail() async {
    if (loginFormKey.currentState!.validate()) {
      isLoading.value = true;
      ResponseModel payload = await ApiServices.postApi("v1/mobile-login",
          body: {"email": email.text, "password": passwordLogin.text});
      if (payload.status) {
        handleLogin(payload.data);
        Get.toNamed(Routes.home);
      }
      isLoading.value = false;
    }
    return;
  }

  onSubmitSignUp() async {
    if (signUpFormKey.currentState!.validate()) {
      isLoading.value = true;
      Map formData = {
        "first_name": firstName.text,
        "last_name": lastName.text,
        // "phone": phone.text,
        "token": tmpToken.value
      };
      if (email.text.isNotEmpty) {
        formData['email'] = email.text;
        formData['password'] = passwordLogin.text;
        // formData['password_confirmation'] = confirmPasswordLogin.text;
      }
      ResponseModel payload = await ApiServices.postApi("v1/signup",
          allowDialog: false, body: formData);
      if (payload.status) {
        handleLogin(payload.data, isForSignup: true);
        Get.offAllNamed(Routes.home);
        // Get.dialog(WarningDialog(
        //     title: "signUpSucessful".tr,
        //     description: "verificationCodeSendEmail".tr,
        //     keyword: DialogKeyword.success,
        //     onConfirmed: () =>
        //         Get.to(() => const OtpVerification(isEmailOtp: true))));
        firstName.clear();
        lastName.clear();
        // email.clear();
        phone.clear();
        passwordLogin.clear();
        // confirmPasswordLogin.clear();
        // loginMode.value = "email";
        //start timer
        secondsRemaining.value = 60;
        enableResend.value = false;
        startResendTimer();
        signupEventCalled();
      } else {
        Get.dialog(WarningDialog(
          title: "error".tr,
          description: payload.message,
        ));
      }
      isLoading.value = false;
    }
    return;
  }

  signupEventCalled() {
    // Track signup event with WebEngage
    WebEngagePlugin.trackEvent(
        'Singup', {"User": isHost ? "Host" : "Customer"});

    // Track signup event with Firebase Analytics
    analytics.logSignUp(signUpMethod: 'Mobile');

    // Track signup event with Analytics Manager
    unawaited(Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.userRegister,
      eventAttributes: {
        AnalyticsAttributes.userType: isHost ? 'Host' : 'Customer',
        AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
        AnalyticsAttributes.guestRegistrationChannel: 'mobile_app',
        AnalyticsAttributes.guestType: 'new',
        AnalyticsAttributes.guestLocation: userModel.value?.location,
        AnalyticsAttributes.sessionTimestamp: DateTime.now().toIso8601String(),
        AnalyticsAttributes.guestDeviceType:
            Platform.isAndroid ? 'android' : 'ios',
        AnalyticsAttributes.deviceLanguage: Get.locale?.languageCode ?? 'en',
      },
    ));
  }

  startResendTimer() {
    if (timer != null && timer!.isActive) {
      timer!.cancel();
      secondsRemaining.value = 60;
    }
    timer = Timer.periodic(const Duration(seconds: 1), (_) {
      if (secondsRemaining.value != 0) {
        secondsRemaining--;
      } else {
        enableResend.value = true;
        timer?.cancel();
      }
    });
  }

  onSubmitEmailCheck() async {
    isLoadingEmail.value = true;
    ResponseModel payload = await ApiServices.postApi("v1/check/email/exist",
        body: {
          "email": email.text,
        },
        allowDialog: false);
    isLoadingEmail.value = false;
    if (payload.status) {
      isEmailExist.value = false;
    } else {
      isEmailExist.value = true;
    }
  }

  void onSubmitLogin() async {
    isLoading.value = true;
    try {
      await _tryOnSubmitLogin();
    } catch (e) {
      emailError.value = 'cannot_sign_in'.tr;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> _tryOnSubmitLogin() async {
    mobileNumberEnteredEventCalled("Mobile Number Entered");
    await sendCode();
  }

  mobileNumberEnteredEventCalled(String keyName) async {
    Map<String, dynamic> dataMap = {"User": isHost ? "Host" : "Customer"};
    await WebEngagePlugin.trackEvent(keyName, dataMap);
  }

  gotoChangeEmail() {
    Get.to(() => const ChangeEmail());
  }

  sendCode() async {
    isLoading.value = true;
    await Get.dialog(Dialog(
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Captcha((p0) async {
        Map formData = {
          "phone": "+${phoneCountry.dialCode}${phone.text.trim()}",
          'g-recaptcha-response': p0.message,
          'user_agent': await _getUserAgent()
        };
        ResponseModel response = await ApiServices.postApi(
            ApiConstants.sendOtpPhone,
            body: formData,
            isAuth: true,
            allowDialog: false);
        debugPrint(response.data.toString());
        if (response.status) {
          // OTPInteractor().getAppSignature();
          // controller.startListenUserConsent((code) {
          //   final exp = RegExp(r'(\d{4})');
          //   controller.stopListen();
          //   return exp.stringMatch(code ?? '') ?? '';
          // });

          Get.back();
          Get.to(() => const OtpVerification(isEmailOtp: false));
          emailError.value = null;
          mobileNumberEnteredEventCalled("OTP Verification Started");
          secondsRemaining.value = 60;
          enableResend.value = false;
          startResendTimer();
          // if (!isResend.value) {
          //   Get.dialog(WarningDialog(
          //       title: "Otp sent",
          //       description: "otpSentPhoneVerify".tr,
          //       keyword: DialogKeyword.success,
          //       onConfirmed: () {
          //         Get.to(() => const OtpVerification(isEmailOtp: false));
          //         secondsRemaining.value = 5;
          //         enableResend.value = false;
          //         startResendTimer();
          //       }));
          // } else {
          //   secondsRemaining.value = 5;
          //   enableResend.value = false;
          //   startResendTimer();
          // }
          // loginMode.value = "otp";
          //phone.clear();
          //isButtonEnabled.value =false;
        } else {
          Get.back();
          emailError.value = response.message;
          if (response.message == "Account does not exists with this number.") {
            Get.to(() => const Login());
          }
        }
      }),
    ));
    isLoading.value = false;
  }

  sendEmailCode() async {
    ResponseModel response =
        await ApiServices.getApi("v1/resend/email/resend", isAuth: true);
    if (response.status) {
      secondsRemaining.value = 60;
      enableResend.value = false;
      startResendTimer();
    } else {
      if (response.message == "Account does not exists with this number.") {
        Get.to(() => const Login());
      }
    }
  }

  //login new page
  RxBool isButtonEnabled = false.obs;

  submitOtp() async {
    bool isControllerActive = Get.isRegistered<PropertyDetailController>();
    if (controller.value.text.isEmpty) {
      otpError.value = 'emptyError'.tr;
      return;
    }
    isLoading.value = true;
    SearchHelper.clearFilter();
    otpError.value = null;

    Map formData = {
      "phone": "+${phoneCountry.dialCode}${phone.text.trim()}",
      "otp": controller.value.text,
    };
    formData['user_agent'] = await _getUserAgent();

    ResponseModel response = await ApiServices.postApi(
        ApiConstants.phoneOtpVerify,
        body: formData,
        isAuth: true);
    if (response.status) {
      if (response.data["user"] != null) {
        if (isControllerActive) {
          Get.until((route) =>
              route.settings.name?.contains(Routes.propertySingle) == true);
          // Get.until((route) => route.settings.name?.contains(Routes.propertySingle)==true);
        } else {
          Get.offAllNamed(Routes.home);
          if (SearchHelper.c.index != 0) {
            SearchHelper.c.changeIndex(0);
          }
          mobileNumberEnteredEventCalled("OTP Verification Completed");
        }
        handleLogin(response.data);
        // if(response.data['yaqeen_verified'] == false) {
        //   Get.dialog(YaqeenVerificationDialog(
        //     isIcon: false ,
        //     imagePath: "elm.jpg",
        //     keyword: DialogKeyword.warning,
        //     isDismiss: true,
        //     confirmText: translateKeywords[Get.locale?.languageCode??'ar']!.general.verify,
        //     onConfirmed: () {
        //       Get.off(()=> const LoginSecurity());
        //     },
        //     ));}

        // Track login event with Firebase Analytics
        analytics.logLogin(loginMethod: 'Mobile');

        // Track login event with Analytics Manager
        unawaited(Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.userLogin,
          eventAttributes: {
            AnalyticsAttributes.userType: isHost ? 'Host' : 'Customer',
            'login_method': 'Mobile',
          },
        ));
      } else {
        tmpToken.value = response.data["token"];
        Get.to(() => const LoginSignUp());
      }
      isLoading.value = false;
      controller.clear();
      timer?.cancel();
    } else {
      isLoading.value = false;
      controller.clear();
    }
  }

  Future<String> _getUserAgent() async {
    final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
    try {
      if (GetPlatform.isIOS) {
        IosDeviceInfo ios = await deviceInfoPlugin.iosInfo;
        String infoUpdated =
            "[\"phone\",\"${ios.utsname.machine}\",\"${ios.systemName}\",\"true\",\"${Platform.operatingSystemVersion}\"]";
        debugPrint(" GetPlatform =$infoUpdated");
        return infoUpdated;
      } else {
        AndroidDeviceInfo android = await deviceInfoPlugin.androidInfo;
        String infoUpdated =
            "[\"phone\",\"${android.device}\",\"android ${android.version.release}\",\"true\",\"${Platform.operatingSystemVersion}\"]";
        debugPrint(" GetPlatform =$infoUpdated");
        return infoUpdated.toString();
      }
    } catch (e) {
      return 'Unknown';
    }
  }

  submitEmailOtp() async {
    if (controller.value.text.isEmpty) {
      otpError.value = 'emptyError'.tr;
      return;
    }
    isLoading.value = true;
    otpError.value = null;
    ResponseModel response = await ApiServices.postApi("v1/email/otp/verify",
        body: {"otp": controller.value.text}, isAuth: true, allowDialog: false);
    if (response.status) {
      if (GlobalHelper.storageBox.hasData('user')) {
        userModel.value!.userVerification?.email = "yes";
        userModel.refresh();
        GlobalHelper.storageBox.write('user', userModel.value?.toJson());
      }
      isLoading.value = false;
      controller.clear();
      phone.clear();
      timer?.cancel();
      Get.offAllNamed(Routes.home);
    } else {
      Get.dialog(WarningDialog(
        title: "error".tr,
        description: response.message,
      ));
      isLoading.value = false;
      controller.clear();
    }
  }

  selectGender(val) async {
    userModel.value?.gender = val;
    // if (await updateProfileApi({"gender":val})) {
    //   GlobalHelper.storageBox.write('user', userModel.value?.toJson());
    // }
    userModel.refresh();
  }

  selectDob() async {
    final lastDate = DateTime.now()
        .subtract(Duration(days: Get.find<RemoteConfig>().dobGapeYears * 365));
    showDatePicker(
            context: Get.context!,
            initialDate: lastDate,
            firstDate: DateTime(1950),
            lastDate: lastDate)
        .then((value) async {
      if (value != null) {
        dob.value = value;
        if (Get.currentRoute == "/PersonalInfo") {
          if (await editPersonalInfo({
            "section": "date_of_birth",
            "date_of_birth": formDateFormat.format(dob.value!)
          })) {
            userModel.value!.dateofbirth = formDateFormat.format(dob.value!);
            GlobalHelper.storageBox.write('user', userModel.value?.toJson());
          }
        }
      }
    });
  }

  submitGender() async {
    isLoading.value = true;
    if (await editPersonalInfo(
        {"section": "gender", "gender": userModel.value!.gender})) {
      GlobalHelper.storageBox.write('user', userModel.value?.toJson());
    } else {
      userModel.value!.gender = GlobalHelper.storageBox.read('user')['gender'];
      userModel.refresh();
    }
    isLoading.value = false;
    Get.until((route) => !Get.isDialogOpen! && !Get.isBottomSheetOpen!);
  }

  selectDobVerification() async {
    Get.dialog(Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      insetPadding: EdgeInsets.zero,
      child: Container(
          height: heightSpace(50),
          width: widthSpace(93),
          padding: const EdgeInsets.all(5),
          child: iqamaValue.value == 0
              ? SfHijriDateRangePicker(
                  showNavigationArrow: true,
                  showActionButtons: true,
                  initialSelectedDate: dobHijriDate.value,
                  confirmText:
                      Get.find<TranslationHelper>().translations.general.ok,
                  cancelText: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .cancel!,
                  onCancel: Get.back,
                  onSubmit: (value) {
                    Get.back();
                    if (value != null) {
                      dobHijriDate.value = value as HijriDateTime;
                    }
                  },
                )
              : SfDateRangePicker(
                  showNavigationArrow: true,
                  showActionButtons: true,
                  initialSelectedDate: dobVerification.value,
                  confirmText:
                      Get.find<TranslationHelper>().translations.general.ok,
                  cancelText: Get.find<TranslationHelper>()
                      .translations
                      .hostDashboard
                      .cancel!,
                  onCancel: Get.back,
                  onSubmit: (value) {
                    Get.back();
                    if (value != null) {
                      dobVerification.value = value as DateTime;
                    }
                  },
                )),
    ));
  }

  fireEvent() {
    Future.delayed(const Duration(seconds: 2), () {
      Map<String, dynamic> webForm = {
        'First Name': firstName.text,
        'Last Name': lastName.text,
        'Date of Birth': userModel.value?.dateofbirth,
        'Location': liveC.text,
        'Gender': userModel.value?.gender,
        'User': isHost ? 'Host' : 'Customer'
      };
      if (dob.value != null) {
        webForm['Date of Birth'] = formDateFormat.format(dob.value!);
      }
      WebEngagePlugin.trackEvent('Personal Information Updated', webForm);
      Map<String, dynamic> attributes =
          {}; //userModel.value!.toAttributeJson();
      if (isHost) {
        HostDashboardController c = Get.find();
        attributes.addAll(c.bookingsTotal.toJson());
        WebEngagePlugin.setUserAttributes(attributes);
      }
      WebEngagePlugin.setUserEmail(userModel.value?.email ?? '');
      WebEngagePlugin.setUserFirstName(userModel.value?.first_name ?? '');
      WebEngagePlugin.setUserLastName(userModel.value?.last_name ?? '');
      WebEngagePlugin.setUserGender(userModel.value?.gender ?? '');
      WebEngagePlugin.setUserPhone(userModel.value?.phone ?? '');
      WebEngagePlugin.setUserBirthDate(userModel.value?.dateofbirth ?? '');
    });
  }

  toggleFirst() async {
    if (firstNameEnabled.value) {
      if (firstName.text.trim().isEmpty) {
        firstError.value = Get.find<TranslationHelper>()
            .translations
            .jqueryValidation
            .required;
      } else if (firstName.text.length > 30) {
        firstError.value = Get.find<TranslationHelper>()
            .translations
            .jqueryValidation
            .maxlength30;
      }
      if (firstError.value == null) {
        firstNameEnabled.value = false;
        if (await editPersonalInfo(
            {"section": "first_name", "first_name": firstName.text})) {
          userModel.value!.first_name = firstName.text;
          GlobalHelper.storageBox.write('user', userModel.value?.toJson());
        } else {
          firstName.text = userModel.value?.first_name ?? "";
        }
      }
    } else {
      firstNameEnabled.value = true;
    }
  }

  toggleLast() async {
    if (lastNameEnabled.value) {
      if (lastName.text.trim().isEmpty) {
        lastError.value = Get.find<TranslationHelper>()
            .translations
            .jqueryValidation
            .required;
      } else if (lastName.text.length > 30) {
        lastError.value = Get.find<TranslationHelper>()
            .translations
            .jqueryValidation
            .maxlength30;
      }
      if (lastError.value == null) {
        lastNameEnabled.value = false;
        if (await editPersonalInfo(
            {"section": "last_name", "last_name": lastName.text})) {
          userModel.value!.last_name = lastName.text;
          GlobalHelper.storageBox.write('user', userModel.value?.toJson());
        } else {
          lastName.text = userModel.value?.last_name ?? "";
        }
      }
    } else {
      lastNameEnabled.value = true;
    }
  }

  openChangeEmailSheet() {
    // adding email text in a new temp controller
    tempEmail.text = email.text;
    // clearing otp controller
    controller.clear();
    toggleEmailChanged(tempEmail.text);
    submitOtpEnabled.value = false;
    otpChangeEnabled.value = false;
    // opening custom email changing sheet
    showEmailChangeSheet();
  }

  // toggleEmail() async {
  //   if (emailChangeEnabled.value) {
  //     if(email.text.trim().isEmpty){
  //       emailError.value = Get.find<TranslationHelper>().translations.jqueryValidation.required;
  //     }else if(!GetUtils.isEmail(email.text)){
  //       emailError.value = Get.find<TranslationHelper>().translations.jqueryValidation.email;
  //     }else{
  //       emailError.value = null;
  //     }
  //     if(emailError.value==null){
  //       emailChangeEnabled.value = false;
  //       if (await editPersonalInfo({
  //         "section" : "email",
  //         "email" : email.text})) {
  //         userModel.value!.email = email.text;
  //         GlobalHelper.storageBox.write('user', userModel.value?.toJson());
  //       }else{
  //         email.text = userModel.value?.email ?? "";
  //       }}
  //   }else{
  //     emailChangeEnabled.value = true;
  //   }
  // }
  toggleEmailChanged(String value) async {
    if (tempEmail.text != email.text) {
      emailChangeEnabled.value = true;
    } else {
      emailChangeEnabled.value = false;
    }
  }

  toggleOtpChanged(String value) async {
    if (value.trim().length == 4) {
      submitOtpEnabled.value = true;
    } else {
      submitOtpEnabled.value = false;
    }
  }

  togglePhone() async {
    if (phoneEnabled.value) {
      phoneEnabled.value = false;
      if (await updateProfileApi(formData: {"phone": liveC.text})) {
        userModel.value!.phone = phone.text;
        GlobalHelper.storageBox.write('user', userModel.value?.toJson());
      } else {
        phone.text = userModel.value!.phone ?? "";
      }
      return;
    }
    phoneEnabled.value = true;
  }

  openWebView(slug, title) {
    Get.to(() => MyFatoorahScreen(
        url: "$baseUrl/$slug?lang=${Get.locale?.languageCode ?? "en"}&mobile=1",
        screenName: title,
        isDisplayYaqeen: false));
  }

  clearFields() {
    firstNameEnabled.value = false;
    firstName.text = userModel.value?.first_name ?? "";

    lastNameEnabled.value = false;
    lastName.text = userModel.value?.last_name ?? "";

    liveEnabled.value = false;
    liveC.text = userModel.value?.location ?? "";

    about.text = userModel.value?.about ?? "";
  }
  // initMFSDK() async {
  //   MFSDK.init(myFatoorahLive, MFCountry.SAUDIARABIA,MFEnvironment.LIVE);
  //   var request = MFInitiatePaymentRequest(
  //       invoiceAmount: 0.0,
  //       currencyIso: "SAR");
  //
  //   await MFSDK.initiatePayment(
  //     request,
  //     MFLanguage.ENGLISH,
  //   ).then((result) {
  //     paymentMethods.value = result.paymentMethods!.where((item) {
  //       if(GetPlatform.isIOS){
  //         return item.paymentMethodId !=6;
  //       }
  //       else{
  //         return item.paymentMethodId !=6  && item.paymentMethodCode != "ap";
  //       }
  //
  //
  //     }).toList();
  //     if(userModel.value?.paymentGateway=='hyperpay'){
  //       paymentMethods.add(MFPaymentMethod(paymentMethodId: 12,paymentMethodEn: 'stcPay',imageUrl: ""));
  //       paymentMethods.add(MFPaymentMethod(paymentMethodId: 101,paymentMethodEn: 'mada',imageUrl: result.paymentMethods?.firstWhere((e) => e.paymentMethodEn=='mada').imageUrl));
  //     }
  //   }).catchError((error){
  //     debugPrint("initMFSDK account controller error ${error.toString()}");
  //   });
  // }

  final selectedPayment = RxnInt();
  selectPayment(val) {
    selectedPayment.value = val;
  }

  clearPaymentMethodSelection() {
    selectedPayment.value = 0;
  }

  showPaymentMethodSelectionSheet() {
    clearPaymentMethodSelection();
    return Get.bottomSheet(
        SizedBox(
          height: heightSpace(35),
          child: BottomSheet(
              backgroundColor: Colors.white,
              enableDrag: false,
              shape: const RoundedRectangleBorder(
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(25))),
              builder: (context) => SingleChildScrollView(
                    padding: EdgeInsets.all(widthSpace(7)),
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InkWell(
                                  onTap: Get.back,
                                  child: const Icon(Icons.arrow_back_ios),
                                ),
                                CustomText(
                                  Get.find<TranslationHelper>()
                                      .translations
                                      .listing
                                      .selectPayMethod,
                                  size: 2.7,
                                  weight: FontWeight.w500,
                                ),
                                SizedBox(width: widthSpace(5))
                              ]),
                          SizedBox(height: heightSpace(3)),
                          for (final item in PaymentHelper.paymentMethods)
                            if (item.id != 100) paymentMethodUI(item)
                        ]),
                  ),
              onClosing: clearPaymentMethodSelection),
        ),
        isScrollControlled: true);
  }

  paymentMethodUI(PaymentMethod paymentMethod) {
    return Obx(
      () => InkWell(
        onTap: () {
          selectPayment(paymentMethod.id);
          if (selectedPayment.value == 2 ||
              selectedPayment.value == 101 ||
              selectedPayment.value == 12) {
            // if user choose pay by using "credit or debit card" or mada then we further check either
            // card is added before or not
            // in case payment gateway coming in user data is hyper pay then don't show add card sheet
            if (PaymentHelper.paymentGateway != 'hyperpay' && myCards.isEmpty) {
              showAddCardSheet(addAAmountInWallet: true);
            } else {
              showAddAmountSheet();
            }
          } else if (selectedPayment.value == 11) {
            // or if user choose pay by us ing "Apple Pay" then we only take input of amount
            // and send it to API so that API gives us which SDK should integrate(Moyasaar/ Fatooraah)
            showAddAmountSheet(isForApplePay: true);
          }
        },
        borderRadius: BorderRadius.circular(5),
        child: Container(
          padding: const EdgeInsets.all(12),
          margin: const EdgeInsets.only(bottom: 6),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: const Color(greyBorder))),
          child: Row(children: [
            Container(
              width: 21,
              height: 21,
              margin: EdgeInsets.only(right: widthSpace(3)),
              decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                      width:
                          selectedPayment.value == paymentMethod.id ? 5.7 : 1,
                      color: Colors.black54)),
            ),
            Image.asset('assets/icons/payment_methods/${paymentMethod.id}.png',
                width: widthSpace(12))
          ]),
        ),
      ),
    );
  }

  showAddCardSheet({fromCheckout = false, addAAmountInWallet = false}) {
    return ViewsCommon.showModalBottom(
        SizedBox(
          height: heightSpace(55),
          child: SingleChildScrollView(
            padding: EdgeInsets.all(widthSpace(7)),
            child: Form(
              // key: c.cardFormKey,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: Get.back,
                            child: const Icon(Icons.arrow_back_ios),
                          ),
                          CustomText(
                            Get.find<TranslationHelper>()
                                .translations
                                .wallet
                                .addCard,
                            size: 2.7,
                            weight: FontWeight.w500,
                          ),
                          SizedBox(width: widthSpace(5))
                        ]),
                    SizedBox(height: heightSpace(2)),
                    shadedField(
                      CustomTextField(
                        controller: cardHolder,
                        textCapitalization: TextCapitalization.words,
                        hint: Get.find<TranslationHelper>()
                            .translations
                            .wallet
                            .holderName,
                        isRoundedBorder: true,
                      ),
                    ),
                    Obx(() {
                      return cardNameVal.value != null
                          ? Padding(
                              padding: const EdgeInsets.only(top: 1, left: 7),
                              child: CustomText(
                                cardNameVal.value ?? "",
                                color: Colors.red,
                                size: 1.9,
                              ),
                            )
                          : SizedBox(
                              height: heightSpace(0.2),
                            );
                    }),
                    shadedField(CustomTextField(
                        controller: cardNumber,
                        formatter: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(16),
                        ],
                        inputType: TextInputType.number,
                        hint: Get.find<TranslationHelper>()
                            .translations
                            .payment
                            .cardNumber,
                        isRoundedBorder: true)),
                    Obx(() {
                      return cardNumberVal.value != null
                          ? Padding(
                              padding: const EdgeInsets.only(top: 1, left: 7),
                              child: CustomText(
                                cardNumberVal.value ?? "",
                                color: Colors.red,
                                size: 1.9,
                              ),
                            )
                          : SizedBox(
                              height: heightSpace(0.2),
                            );
                    }),
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              shadedField(
                                CustomTextField(
                                  controller: expMonthYear,
                                  hint:
                                      "${Get.find<TranslationHelper>().translations.payment.mm}/${Get.find<TranslationHelper>().translations.payment.yy}",
                                  inputType: TextInputType.number,
                                  formatter: [
                                    CardExpirationFormatter(),
                                    LengthLimitingTextInputFormatter(5)
                                  ],
                                  isRoundedBorder: true,
                                ),
                              ),
                              Obx(() {
                                return cardMonthYearVal.value != null
                                    ? Padding(
                                        padding: const EdgeInsets.only(
                                            top: 1, left: 7),
                                        child: CustomText(
                                          cardMonthYearVal.value ?? "",
                                          color: Colors.red,
                                          size: 1.9,
                                        ))
                                    : SizedBox(height: heightSpace(0.2));
                              }),
                            ],
                          ),
                        ),
                        SizedBox(
                          width: widthSpace(4),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              shadedField(
                                CustomTextField(
                                  controller: cvvCard,
                                  hint:
                                      "CVV                                  _ _ _",
                                  isRoundedBorder: true,
                                  inputType: TextInputType.number,
                                  formatter: [
                                    FilteringTextInputFormatter.allow(
                                        RegExp(r'^\d{1,3}'))
                                  ],
                                ),
                              ),
                              Obx(() {
                                return cardCvvVal.value != null
                                    ? Padding(
                                        padding: const EdgeInsets.only(
                                            top: 1, left: 7),
                                        child: CustomText(
                                          cardCvvVal.value ?? "",
                                          color: Colors.red,
                                          size: 1.9,
                                        ),
                                      )
                                    : SizedBox(height: heightSpace(0.2));
                              }),
                            ],
                          ),
                        )
                      ],
                    ),
                    if (addAAmountInWallet) ...[
                      shadedField(
                        CustomTextField(
                          controller: addAmount,
                          hint:
                              "${Get.find<TranslationHelper>().translations.bookingDetail.depositAmount}                                  _ _ _",
                          isRoundedBorder: true,
                          inputType: TextInputType.number,
                          formatter: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d{1,3}'))
                          ],
                        ),
                      ),
                      Obx(() {
                        return addAmountVal.value != null
                            ? Padding(
                                padding: const EdgeInsets.only(top: 1, left: 7),
                                child: CustomText(
                                  addAmountVal.value ?? "",
                                  color: Colors.red,
                                  size: 1.9,
                                ),
                              )
                            : SizedBox(height: heightSpace(0.2));
                      }),
                    ],
                    SizedBox(height: heightSpace(2)),
                    CustomText(
                      Get.find<TranslationHelper>()
                          .translations
                          .payment
                          .cardInfo,
                      weight: FontWeight.w500,
                      color: const Color(greyText),
                    ),
                    SizedBox(height: heightSpace(2)),
                    SizedBox(
                        width: double.maxFinite,
                        child: Obx(
                          () => CommonButton(
                              isLoading: isLoading.isTrue,
                              horizontalPadding: 12,
                              title: addAAmountInWallet
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .bookingDetail
                                      .depositAmount
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .usersProfile
                                      .save,
                              onPressed: () async {
                                Map? formData = await submitAddCard(
                                    fromCheckout: fromCheckout,
                                    addAAmountInWallet: addAAmountInWallet);
                                if (formData != null) {
                                  Get.back(result: formData);
                                }
                              }),
                        )),
                  ]),
            ),
          ),
        ),
        then: clearCardFields);
  }

  Widget shadedField(child) {
    return Material(
      color: Colors.white,
      elevation: 4,
      borderRadius: BorderRadius.circular(50),
      child: child,
    );
  }

  showAddAmountSheet({isForApplePay = false}) {
    ViewsCommon.showModalBottom(
        SizedBox(
            height: heightSpace(35),
            child: SingleChildScrollView(
              padding: EdgeInsets.all(widthSpace(7)),
              child: Form(
                // key: c.cardFormKey,
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            InkWell(
                              onTap: Get.back,
                              child: const Icon(Icons.arrow_back_ios),
                            ),
                            CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .bookingDetail
                                  .depositAmountWallet,
                              size: 2.7,
                              weight: FontWeight.w500,
                            ),
                            SizedBox(width: widthSpace(5))
                          ]),
                      SizedBox(height: heightSpace(3)),
                      Material(
                        color: Colors.white,
                        elevation: 4,
                        borderRadius: BorderRadius.circular(50),
                        child: CustomTextField(
                          controller: addAmount,
                          hint:
                              "${Get.find<TranslationHelper>().translations.bookingDetail.depositAmount}                                  _ _ _ _",
                          isRoundedBorder: true,
                          inputType: TextInputType.number,
                          formatter: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d{1,4}'))
                          ],
                        ),
                      ),
                      Obx(() {
                        return addAmountVal.value != null
                            ? Padding(
                                padding: const EdgeInsets.only(top: 1, left: 7),
                                child: CustomText(
                                  addAmountVal.value ?? "",
                                  color: Colors.red,
                                  size: 1.9,
                                ),
                              )
                            : SizedBox(height: heightSpace(0.2));
                      }),
                      SizedBox(height: heightSpace(8)),
                      SizedBox(
                          width: double.maxFinite,
                          child: Obx(
                            () => CommonButton(
                                isLoading: isLoading.isTrue,
                                horizontalPadding: 12,
                                title: Get.find<TranslationHelper>()
                                    .translations
                                    .bookingDetail
                                    .depositAmount,
                                onPressed: () async {
                                  Map? formData = await submitWalletBalance(
                                      isForApplePay: isForApplePay);
                                  if (formData != null) {
                                    Get.back(result: formData);
                                  }
                                }),
                          )),
                      SizedBox(height: heightSpace(2)),
                    ]),
              ),
            )),
        then: clearCardFields);
  }

  clearCardFields([_]) {
    cardHolder.text = "";
    cardNumber.text = "";
    expMonthYear.text = "";
    // c.expMonth.text = "";
    // c.expYear.text = "";
    cvvCard.text = "";
    addAmount.text = "";
    cardNameVal.value = "";
    cardNumberVal.value = "";
    cardMonthYearVal.value = "";
    cardCvvVal.value = "";
    isLoading.value = false;
    //c.isValidationError.value = false;
  }

  final cardNameVal = RxnString();
  final cardNumberVal = RxnString();
  final cardMonthYearVal = RxnString();
  final cardCvvVal = RxnString();
  final addAmountVal = RxnString();

  submitAddCard({fromCheckout = false, addAAmountInWallet = false}) async {
    if (cardHolder.text.trim().isEmpty) {
      cardNameVal.value =
          Get.find<TranslationHelper>().translations.jqueryValidation.required;
    } else {
      cardNameVal.value = null;
    }
    if (cardNumber.text.trim().isEmpty) {
      cardNumberVal.value =
          Get.find<TranslationHelper>().translations.jqueryValidation.required;
    } else if (cardNumber.text.trim().length != 16) {
      cardNumberVal.value = Get.find<TranslationHelper>()
          .translations
          .jqueryValidation
          .cardMust16;
    } else {
      cardNumberVal.value = null;
    }
    if (expMonthYear.text.trim().isEmpty) {
      cardMonthYearVal.value =
          Get.find<TranslationHelper>().translations.jqueryValidation.required;
    } else if (expMonthYear.text.trim().length != 5) {
      cardMonthYearVal.value = Get.find<TranslationHelper>()
          .translations
          .jqueryValidation
          .invalidFormat;
    } else {
      cardMonthYearVal.value = null;
    }
    if (addAAmountInWallet && addAmount.text.trim().isEmpty) {
      addAmountVal.value =
          Get.find<TranslationHelper>().translations.jqueryValidation.required;
    }
    if (cvvCard.text.trim().isEmpty) {
      cardCvvVal.value =
          Get.find<TranslationHelper>().translations.jqueryValidation.required;
    } else if (cvvCard.text.trim().length != 3) {
      cardCvvVal.value =
          Get.find<TranslationHelper>().translations.jqueryValidation.cvvMust3;
    } else {
      cardCvvVal.value = null;
    }
    if (addAAmountInWallet) {
      if (cardNameVal.value == null &&
          cardNumberVal.value == null &&
          cardMonthYearVal.value == null &&
          cardCvvVal.value == null &&
          addAmountVal.value == null) {
        isLoading.value = true;
        Map formData = {
          "card_name": cardHolder.text,
          "card_number": cardNumber.text,
          "expiry_month_wallet": expMonthYear.text.split('/')[0],
          "expiry_year_wallet": expMonthYear.text.split('/')[1],
          "card_cvv": cvvCard.text,
          "amount": addAmount.text,
        };
        formData['api_version'] = 1;

        if (fromCheckout) {
          isLoading.value = false;
          return formData;
        }
        ResponseModel response = await ApiServices.postApi("v1/credit",
            body: formData, isAuth: true);

        if (response.status) {
          if (selectedPayment.value == 2) {
            Get.to(() => MyFatoorahScreen(url: response.data));
            expDateError.value = null;
            cardHolder.text = "";
            cardNumber.text = "";
            expMonthYear.text = "";
            selectedMonth.value = null;
            expMonth.text = "";
            expYear.text = "";
            cvvCard.text = "";
            addAmount.text = "";
          }
        }
        isLoading.value = false;
      }
    } else {
      if (cardNameVal.value == null &&
          cardNumberVal.value == null &&
          cardMonthYearVal.value == null &&
          cardCvvVal.value == null) {
        isLoading.value = true;
        Map formData = {
          "name": cardHolder.text,
          "number": cardNumber.text,
          "month": expMonthYear.text.split('/')[0],
          "year": expMonthYear.text.split('/')[1],
          "cvv": cvvCard.text
        };

        if (fromCheckout) {
          isLoading.value = false;
          return formData;
        }
        ResponseModel response = await ApiServices.postApi("v1/user/card/add",
            body: formData, isAuth: true);
        if (response.status) {
          Get.to(() => MyFatoorahScreen(url: response.data));
          expDateError.value = null;
          cardHolder.text = "";
          cardNumber.text = "";
          expMonthYear.text = "";
          selectedMonth.value = null;
          expMonth.text = "";
          expYear.text = "";
          cvvCard.text = "";
        }
        isLoading.value = false;
      }
    }
  }

  submitWalletBalance({isForApplePay = false}) async {
    Map formData = {};
    String cardHolderName = "";
    String cardNum = "";
    String cardMonth = "";
    String cardYear = "";
    String cardCvv = "";
    // in case of hyper pay not passing card details anymore
    if (PaymentHelper.paymentGateway != 'hyperpay' && !isForApplePay) {
      final defaultCard = myCards.firstWhere((card) => card.isDefault);
      cardHolderName = defaultCard.cardName ?? '';
      cardNum = defaultCard.cardNumber ?? '';
      cardMonth = defaultCard.cardMonth ?? '';
      cardYear = defaultCard.cardYear ?? '';
      cardCvv = defaultCard.cardCvv ?? '';
    }

    if (addAmount.text.trim().isEmpty) {
      addAmountVal.value =
          Get.find<TranslationHelper>().translations.jqueryValidation.required;
    }

    if (addAmountVal.value == null) {
      isLoading.value = true;
      if (PaymentHelper.paymentGateway != 'hyperpay' && !isForApplePay) {
        formData['card_name'] = cardHolderName;
        formData['card_number'] = cardNum;
        formData['expiry_month_wallet'] = cardMonth;
        formData['expiry_year_wallet'] = cardYear;
        formData['card_cvv'] = cardCvv;
      }
      formData['amount'] = addAmount.text;
      formData['paymentMethodId'] = selectedPayment.value;
      formData['api_version'] = 1;

      ResponseModel response =
          await ApiServices.postApi("v1/credit", body: formData, isAuth: true);

      if (response.status) {
        if (selectedPayment.value == 2 ||
            selectedPayment.value == 101 ||
            selectedPayment.value == 12) {
          Get.to(() => MyFatoorahScreen(url: response.data['payment_service']));
          expDateError.value = null;
          cardHolder.text = "";
          cardNumber.text = "";
          expMonthYear.text = "";
          selectedMonth.value = null;
          expMonth.text = "";
          expYear.text = "";
          cvvCard.text = "";
          addAmount.text = "";
        } else if (selectedPayment.value == 11 && isForApplePay) {
          // in case user choose apple pay
          if (response.data['payment_service'] == "Fatoorah") {
            // in case need to integrate My Fatoorah apple SDK
            // var request = MFExecutePaymentRequest(
            //     invoiceValue:response.data['finalamount'] is double
            //         ?response.data['finalamount']
            //         :response.data['finalamount'].toDouble()
            // );
            // request.paymentMethodId = selectedPayment.value??0;
            // request.customerReference = response.data['customer_ref']??"";
            // request.customerName = userModel.value!.first_name;
            // request.customerEmail = userModel.value!.email;
            // await MFSDK
            //     .executePayment(
            //     request, MFLanguage.ENGLISH, (invoiceId) {
            //   debugPrint(invoiceId);
            // })
            //     .then((result) {
            // }).catchError((error){
            //   debugPrint(error.toString());
            //   Get.dialog(WarningDialog(
            //       title: "paymentFailed".tr,
            //       description: error ?? ""));
            // });
          } else {
            // in case need to integrate Moysar apple SDK
            Get.to(
                () => MyFatoorahScreen(url: response.data['payment_service']));
            isLoading.value = false;
            expDateError.value = null;
            cardHolder.text = "";
            cardNumber.text = "";
            expMonthYear.text = "";
            selectedMonth.value = null;
            expMonth.text = "";
            expYear.text = "";
            cvvCard.text = "";
            addAmount.text = "";
            return;
          }
        }
      }
      isLoading.value = false;
    }
  }

  initMoyasar() {
    // remember Moyasar is considering amount in halalas(same as cents in dolor or paisa in rupee) not in riyals so sending amount with
    // multiple of 100.
    int amountEntered = int.parse(addAmount.text);
    var amount = int.parse((amountEntered * 100).toStringAsFixed(0));
    PaymentConfig? paymentConfig;
    try {
      paymentConfig = PaymentConfig(
        publishableApiKey: moyasarTestAPIKey,
        amount: amount,
        currency: 'SAR',
        description: 'Deposit Amount in Wallet',
        metadata: {
          'is_apple_pay': "true",
          'amount': amount.toString(),
          'wallet': myWallet.first.id.toString()
        }, //customer_ref
        creditCard: CreditCardConfig(saveCard: true, manual: true),
        applePay: ApplePayConfig(
            merchantId: 'merchant.darent.com', label: 'Darent', manual: false),
      );
    } catch (e) {
      if (kDebugMode) {
        print("onApplePay Error paymentConfig $e");
      }
    }
    ViewsCommon.showModalBottom(DraggableScrollableSheet(
        maxChildSize: .30,
        initialChildSize: .30,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              SizedBox(height: heightSpace(3)),
              ApplePay(
                config: paymentConfig!,
                onPaymentResult: ((result) => onPaymentResult(result, amount)),
              ),
            ],
          ).paddingAll(widthSpace(viewPadding));
        })).then((value) {
      isLoading.value = false;
    });
  }

  void onPaymentResult(result, int amount) {
    if (result is PaymentResponse) {
      if (result.status == PaymentStatus.paid) {
        // Track payment success event with Analytics Manager
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.paymentCompleted,
          eventAttributes: {
            AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
            AnalyticsAttributes.paymentMethod: 'wallet_deposit',
            AnalyticsAttributes.paymentAmount: amount.toString(),
            AnalyticsAttributes.paymentCurrency: "SAR",
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
          },
        );

        AuthHelper.c.getCards();
        AuthHelper.c.getTransactions();
        Get.until((route) => !Get.isDialogOpen! && !Get.isBottomSheetOpen!);
        ViewsCommon.showSnackbar(
            "SAR-$amount ${Get.find<TranslationHelper>().translations.bookingDetail.depositMessage}",
            displayTime: 1500);
      } else {
        isLoading.value = false;
        Get.back();
        Get.dialog(WarningDialog(title: "paymentFailed".tr, description: ""));
      }
    } else {
      isLoading.value = false;
      Get.back();
      Get.dialog(WarningDialog(title: "paymentFailed".tr, description: ""));
    }
  }

  submitIqma({bool isStep = false, int maxLength = 10}) async {
    if ((iqamaValue.value == 0 && dobHijriDate.value == null) ||
        (iqamaValue.value == 1 && dobVerification.value == null)) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.usersProfile.selectDob!,
          keyword: DialogKeyword.warning);
    } else if (iqma.text.isEmpty) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.usersProfile.fillCode!,
          keyword: DialogKeyword.warning);
    } else if (iqma.text.length < maxLength) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.usersProfile.fillCompCode!,
          keyword: DialogKeyword.warning);
    } else {
      // verificationCode.value = false;
      Map formData = {};
      isLoading.value = true;
      if (iqamaValue.value == 2) {
        formData = {
          "code": iqma.text,
          "nationality": selectedCountryCode,
          "cal": iqamaValue.value
        };
      } else {
        formData = {
          "dob": formDateFormat.format(iqamaValue.value == 0
              ? DateTime(dobHijriDate.value!.year, dobHijriDate.value!.month,
                  dobHijriDate.value!.day)
              : dobVerification.value!),
          "code": iqma.text,
          "cal": iqamaValue.value,
        };
      }
      ResponseModel response = await ApiServices.postApi("v1/elm/verify",
          body: formData, isAuth: true);
      if (response.status) {
        userModel.value!.yaqeenVerified = true;
        userModel.value!.showYaqeenReservation = false;
        GlobalHelper.storageBox.write('user', userModel.value?.toJson());
        userModel.refresh();
        Get.back();
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>()
                .translations
                .listing
                .verificationDone!,
            displayTime: 1500,
            onConfirmd: TextButton(
                onPressed: () {},
                child: CommonButton(
                    title:
                        Get.find<TranslationHelper>().translations.general.ok,
                    onPressed: () {
                      if (isStep) {
                        Get.to(() => const PublishListing());
                      } else {
                        Get.until((route) => route.isFirst);
                      }
                    },
                    backgroundBg: Colors.white,
                    buttonThemeColor: Colors.black54)));
      } else {
        ViewsCommon.showSnackbar(response.message ?? "Invalid Credentials",
            displayTime: 1500, keyword: DialogKeyword.warning);
      }
      isLoading.value = false;
      yaqeenVerificationEventCalled(
        iqma.text,
        isRejected: !response.status,
        dob: iqamaValue.value == 2
            ? null
            : formDateFormat.format(iqamaValue.value == 0
                ? dobHijriDate.value!.toDateTime()
                : dobVerification.value!),
      );
      iqma.text = "";
    }
  }

  yaqeenVerificationEventCalled(String nin,
      {bool isRejected = false, String? dob}) async {
    Map<String, dynamic> dataMap = {
      "NIN number": nin,
      "birth date": dob,
      "Verified (status)": userModel.value!.yaqeenVerified,
      "Rejected": isRejected,
    };
    if (isHost) {
      dataMap['Host Id'] = userModel.value?.id;
    } else {
      dataMap['Guest Id'] = userModel.value?.id;
    }
    await WebEngagePlugin.trackEvent("Yaqeen Verification", dataMap);
  }

  final preferredContactTypeList = RxList<GeneralModel>();

  addPreferredContactTypesFromInitialData(ListingPrefsModel? prefsList) {
    preferredContactTypeList.clear();
    List<dynamic> convertedList =
        prefsList!.toJson().entries.map((MapEntry<String, dynamic> entry) {
      return entry.value;
    }).toList();
    preferredContactTypeList.addAll(
        convertedList.map((dynamic value) => GeneralModel.fromJson(value)));
    modifyPreferredContactTypesAsPerUserData();
  }

  modifyPreferredContactTypesAsPerUserData() {
    List<String> userDataList = userModel.value!.communicationPrefs!.split(',');

    for (GeneralModel item in preferredContactTypeList) {
      if (userDataList.contains(item.name)) {
        item.isChecked = !item.isChecked!;
      }
    }
    preferredContactTypeList.refresh();
  }

  selectPreferredContactType(val) {
    val.isChecked = !val.isChecked;
    preferredContactTypeList.refresh();
  }

  editPreferredContact() async {
    isLoading.value = true;
    List<String> commMethod = [];
    for (var item in preferredContactTypeList) {
      if (item.isChecked!) {
        commMethod.add(item.name ?? '');
      }
    }
    if (commMethod.isEmpty) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>()
                  .translations
                  .usersProfile
                  .selectAtleast1 ??
              "Please select at least 1 contact method",
          keyword: DialogKeyword.warning);
      isLoading.value = false;
      return;
    }
    ApiServices.postApi("v1/managehost/global_preferences",
            body: {"section": "comm_preference", "comm_method": commMethod},
            isAuth: true)
        .then((ResponseModel response) {
      if (response.status) {
        userModel.value!.communicationPrefs = commMethod.join(",");
        userModel.value!.contactMethodAdded = true;
        GlobalHelper.storageBox.write('user', userModel.value?.toJson());
        userModel.refresh();
        Get.back();
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>()
                    .translations
                    .usersProfile
                    .prefMethodUpdate ??
                'Your selected preferred contact method updated',
            displayTime: 1500,
            onConfirmd: TextButton(
                onPressed: () {},
                child: CommonButton(
                    title: "Okay",
                    onPressed: () => Get.until((route) => route.isFirst),
                    backgroundBg: Colors.white,
                    buttonThemeColor: Colors.black54)));
      } else {
        ViewsCommon.showSnackbar(response.message ?? "Invalid Credentials",
            displayTime: 1500, keyword: DialogKeyword.warning);
      }
    });
    isLoading.value = false;
  }

  editPersonalInfo(formData) async {
    final response = await ApiServices.postApi("v1/managehost/personal_info",
        body: formData, isAuth: true);
    if (response.status) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.profile.profileUpdated);

      // Track user profile update event with Analytics Manager
      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.userProfileUpdate,
        eventAttributes: {
          AnalyticsAttributes.userId: userModel.value?.id?.toString(),
          AnalyticsAttributes.userType: isHost ? 'Host' : 'Customer',
          AnalyticsAttributes.sessionTimestamp:
              DateTime.now().toIso8601String(),
          'update_type': 'personal_info',
        },
      );

      fireEvent();
    }
    return response.status;
  }

  // final pickedImage = Rxn<Uint8List?>();
  final pickedImagePath = RxnString();
  final isProfileUploading = false.obs;
  pickProfileImage(ImageSource source) async {
    try {
      var image = await ImagePicker()
          .pickImage(source: source, requestFullMetadata: false);
      if (image != null && image.path.isNotEmpty) {
        isProfileUploading.value = true;
        pickedImagePath.value = image.path;
        pickedImagePath.refresh();
        ApiServices.imageUpload("v1/users/profile/media",
                imagePaths: [image.path], fileKeyName: 'profile_image')
            .then((response) {
          if (response.data != null) {
            final payload = response.data['payload'];
            userModel.value!.profile_image = payload;
            userModel.refresh();
            GlobalHelper.storageBox.write('user', userModel.value?.toJson());
          } else {
            debugPrint("Error: 'payload' key is not present in the response");
          }
          isProfileUploading.value = false;
        });
      }
    } catch (e) {
      debugPrint(e.toString());
      isProfileUploading.value = false;
    }
  }

  updateProfileApi({formData}) async {
    if (formData == null && userModel.value?.gender == null) {
      if (!Get.isSnackbarOpen) {
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>()
                .translations
                .jqueryValidation
                .somethingMiss,
            keyword: DialogKeyword.warning);
      }
      return;
    }
    if (dob.value == null) {
      dobError.value = true;
    } else {
      dobError.value = false;
    }
    if (profileLoginKey.currentState!.validate() && dobError.isFalse) {
      isLoading.value = true;
      formData ??= {
        "first_name": firstName.text,
        "last_name": lastName.text,
        "gender": userModel.value!.gender,
        "location": liveC.text,
        "about": about.text,
        'date_of_birth': formDateFormat.format(dob.value!)
      };
      ResponseModel response = await ApiServices.postApi("v1/users/profile",
          body: formData, isAuth: true);

      if (response.status) {
        userModel.value!.first_name = firstName.text;
        userModel.value!.last_name = lastName.text;
        userModel.value!.location = liveC.text;
        userModel.value!.about = about.text;
        userModel.value!.dateofbirth = formDateFormat.format(dob.value!);

        GlobalHelper.storageBox.write('user', userModel.value?.toJson());
        userModel.refresh();
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>().translations.profile.profileUpdated);

        // Track user profile update event with Analytics Manager
        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.userProfileUpdate,
          eventAttributes: {
            AnalyticsAttributes.userId: userModel.value?.id?.toString(),
            AnalyticsAttributes.userType: isHost ? 'Host' : 'Customer',
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
            'update_type': 'profile_details',
          },
        );

        fireEvent();
      }
      isLoading.value = false;
    }
  }

  //moved to updateHelper
  // String? appVersion;

  // getAppVersion() async{
  //   Upgrader packageInfo = Upgrader();
  //   await packageInfo.initialize();
  //   if(kDebugMode){
  //     print("is blank ${packageInfo.isBlank}");
  //   }
  //   if (!packageInfo.isBlank!) {
  //     appVersion = "${packageInfo.currentInstalledVersion}";
  //     if(packageInfo.isUpdateAvailable()){
  //       packageInfo.shouldDisplayUpgrade()
  //       Get.dialog(PopScope(
  //         canPop: false,
  //         child: UpgradeAlert(
  //           showIgnore: false,
  //           showLater: false,
  //           showReleaseNotes: false,
  //           barrierDismissible: false,
  //         ),
  //       ),barrierDismissible: false);
  //     }
  //   }
  // }

  @override
  void onInit() {
    if (GlobalHelper.storageBox.hasData('user')) {
      userModel.value =
          UserModel.fromLocalJson(GlobalHelper.storageBox.read('user'));
      print("showYaqeenReservation :: ${userModel.value?.showYaqeenReservation}");

      fillUserFields();
      // if(ConnectivityHelper.isConnected) {
      getCards();
      getTransactions();
      getTicketListing();
      // }
      // getManagerList();
    }
    // initMFSDK();
    focusNode.addListener(() {
      if (GetUtils.isEmail(email.text)) {
        onSubmitEmailCheck();
      }
    });
    super.onInit();
  }

  changeDefaultCard(value) {
    ApiServices.getApi("v1/make/default/card/$value", isAuth: true)
        .then((response) {
      if (response.status) {
        defaultCardId.value = value;
        myCards.refresh();
      }
    });
  }

  deleteCard(int id) async {
    Get.dialog(WarningDialog(
      title: Get.find<TranslationHelper>().translations.modal.areYouSure,
      description: Get.find<TranslationHelper>()
          .translations
          .wallet
          .doYouWantToDeleteCard,
      onConfirmed: () async {
        Get.back();
        isLoading.value = true;
        myCards.refresh();
        final response =
            await ApiServices.getApi("v1/delete/card/$id", isAuth: true);
        if (response.status) {
          getCards();
        }
        isLoading.value = false;
      },
    ));
  }

  getCards() {
    ApiServices.getApi("v1/user/card/get", isAuth: true, showDialog: false)
        .then((response) {
      if (response.status) {
        myCards.value = response.data['card_data']
            .map<CardModel>((item) => CardModel.fromJson(item))
            .toList();
        CardModel? defaultCard =
            myCards.firstWhereOrNull((item) => item.isDefault);
        if (defaultCard != null) {
          defaultCardId.value = defaultCard.id;
        }
        myWallet.clear();
        myWallet.add(WalletModel.fromJson(response.data['wallet_data']));
        myCards.refresh();
        myWallet.refresh();
      }
    });
  }

  getTransactions({refresh = false}) {
    if (refresh) {
      transactionsPage = 1;
    }
    ApiServices.getApi(
            "v1/usertransactions?page=$transactionsPage&size=$transactionsSize",
            isAuth: true)
        .then((response) {
      if (response.status) {
        if (transactionsPage > 1) {
          myTransactions.addAll(response.data['transaction']
              .map<TransactionModel>((item) => TransactionModel.fromJson(item))
              .toList());
          myTransactions.refresh();
          return;
        }
        myTransactions.value = response.data['transaction']
            .map<TransactionModel>((item) => TransactionModel.fromJson(item))
            .toList();
        totalSales.value = response.data['sales'] ?? '0';
        totalPages = response.data['pagination']['total_pages'];
      }
    });
  }

  deleteAccount() {
    ViewsCommon.showModalBottom(
        DraggableScrollableSheet(
            maxChildSize: .45,
            initialChildSize: .45,
            expand: false,
            builder: (c, s) => SingleChildScrollView(
                  child: Form(
                    key: deleteFeedbackForm,
                    child: SingleChildScrollView(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomTextField(
                              controller: deleteFeedback,
                              isRoundedBorder: true,
                              hint: Get.find<TranslationHelper>()
                                  .translations
                                  .account
                                  .feedback,
                              validator: (val) => val.isEmpty
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .jqueryValidation
                                      .required
                                  : null,
                              maxlines: 5,
                              maxLength: 100,
                            ),
                            const SizedBox(height: 4),
                            Obx(
                              () => CommonButton(
                                  title: Get.find<TranslationHelper>()
                                      .translations
                                      .hostListing
                                      .continuE,
                                  isLoading: isLoading.value,
                                  backgroundBg: isHost
                                      ? Colors.black
                                      : const Color(themeColor),
                                  onPressed: () {
                                    if (deleteFeedbackForm.currentState
                                            ?.validate() ??
                                        false) {
                                      Get.dialog(WarningDialog(
                                          title: Get.find<TranslationHelper>()
                                              .translations
                                              .modal
                                              .areYouSure,
                                          description:
                                              Get.find<TranslationHelper>()
                                                  .translations
                                                  .modal
                                                  .deleteWarn,
                                          onConfirmed: () async {
                                            isLoading.value = true;
                                            Get.back();
                                            final res =
                                                await ApiServices.postApi(
                                                    'v1/account/delete',
                                                    body: {
                                                      'reason':
                                                          deleteFeedback.text
                                                    },
                                                    isAuth: true);
                                            if (res.status) {
                                              onLogout();
                                              ViewsCommon.showSnackbar(
                                                  Get.find<TranslationHelper>()
                                                      .translations
                                                      .modal
                                                      .afterDelete,
                                                  displayTime: 3000);
                                            }
                                            isLoading.value = false;
                                          }));
                                    }
                                  }),
                            )
                          ]).paddingAll(widthSpace(viewPadding)),
                    ),
                  ),
                )),
        then: (_) => deleteFeedback.clear());
  }

  getTicketListing({refresh = false}) {
    if (refresh) {
      ticketPage = 1;
    }
    ApiServices.getApi("v1/ticket/list?size=12&page=$ticketPage", isAuth: true)
        .then((response) {
      if (response.status) {
        if (ticketPage > 1) {
          ticketListing.addAll(response.data['collection']
              .map<TicketModel>((item) => TicketModel.fromJson(item)));
          ticketListing.refresh();
          isLoading.value = false;
        } else {
          ticketListing.value = response.data['collection']
              .map<TicketModel>((item) => TicketModel.fromJson(item))
              .toList();
        }
        ticketLastPage = response.data['pagination']['total_pages'];
      }
    });
  }

  RxString reviewTab = "reviews".obs;
  setReviewsTab(val) {
    if (reviewTab.value != val) {
      reviewTab.value = val;
    }
  }

  // submitRatingHost(index) {
  //   if (rating > 0 && ratingController.text.isNotEmpty) {
  //     isLoading.value = true;
  //     Map<String, String> formData = {
  //       'rating': rating.toString(),
  //       'message': ratingController.text,
  //       'reviewid': myReviews[index].id.toString()
  //     };
  //     ApiServices.imageUpload("v1/hostrating",
  //             imagePaths:
  //                 ratingImage.value != null ? [ratingImage.value!] : null,
  //             body: formData)
  //         .then((value) {
  //       Get.back();
  //       ratingController.clear();
  //       ratingImage.value = null;
  //       rating = 0.0;
  //       myReviews[index].rated = true;
  //       myReviews.refresh();
  //       getReviews();
  //       isLoading.value = false;
  //       if (value.status) {
  //         ViewsCommon.showSnackbar(
  //           'Your Rating Is Done',
  //         );
  //         // Get.dialog(
  //         //   WarningDialog(
  //         //       title: "success".tr,
  //         //       keyword: DialogKeyword.success,
  //         //       description: "Your Rating Is Done"),
  //
  //         // );
  //         myReviews[index].rated = true;
  //       }
  //     });
  //   } else {
  //     ViewsCommon.showSnackbar(rating == 0 ? Get.find<TranslationHelper>().translations.listing.rateListing! : Get.find<TranslationHelper>().translations.listing.messageAlso!);
  //   }
  // }

  pickImage() async {
    try {
      var image = await ImagePicker()
          .pickImage(source: ImageSource.gallery, requestFullMetadata: false);
      if (image != null) {
        ratingImage.value = image.path;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  pickComplainImage() async {
    try {
      var image = await ImagePicker().pickImage(source: ImageSource.gallery);
      if (image != null) {
        complainImage.value = image.path;
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  clearTicketForm() {
    ticketSubject.clear();
    ticketType.value = null;
    bookingId.clear();
    ticketDesc.clear();
    complainImage.value = null;
    isLoading.value = false;
    ticketError.value = null;
  }

  submitBankAcc({fromListing = false}) async {
    if (bankName.text.trim().isEmpty && selectedBank.value == null) {
      return;
    }
    if (Get.context != null) {
      GlobalHelper.removeFocus();
    }
    // phoneError.value = accPhone.text.trim().isEmpty;
    if ((bankFormKey.currentState?.validate() ?? false)) {
      isLoading.value = true;
      Map<String, String> formData = {
        "bank_name": selectedBank.value == null
            ? bankName.text
            : selectedBank.value!.name!,
        "account_title": accTitle.text,
        "account_number": accNumber.text,
        "iban": accIban.text,
        'swift_code': selectedBank.value == null
            ? swiftCode.text
            : selectedBank.value!.bicCode!,
      };
      if (accPhone.text.isNotEmpty) {
        formData["phone"] = accPhone.text.trim();
        formData["carrier_code"] = '+${phoneCountry.dialCode}';
      }
      ResponseModel response = await ApiServices.postApi(
          "v1/create/bank_account",
          body: formData,
          isAuth: true);
      if (response.status) {
        if (fromListing) {
          Get.off(() => const AddProperty11());
        }
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>().translations.listing.successSubmit!);
        userModel.value!.bank = Bank.fromJson(formData);
        GlobalHelper.storageBox.write('user', userModel.value?.toJson());
      }
      isLoading.value = false;
    }

    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.hostPayoutSuccessful,
      eventAttributes: {
        AnalyticsAttributes.hostId: userModel.value?.id?.toString(),
        AnalyticsAttributes.hostPayoutStatus: 'submitBankAcc',
        AnalyticsAttributes.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  submitManagerAcc(id) async {
    if (Get.context != null) {
      FocusScope.of(Get.context!).requestFocus(FocusNode());
    }
    if ((managerFormKey.currentState?.validate() ?? false) &&
        statusMangError.value == null) {
      if (phoneMang.text.length < 9) {
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>().translations.listing.validNumber!,
            keyword: DialogKeyword.warning);
        return;
      } else if (id == null) {
        if (passMang.text.length < 6) {
          ViewsCommon.showSnackbar(
              Get.find<TranslationHelper>().translations.listing.greaterToSix!,
              keyword: DialogKeyword.warning);
          return;
        }
      }
      isLoading.value = true;
      Map<String, String> formData = {
        "first_name": firstNameMang.text,
        "last_name": lastNameMang.text,
        "email": emailMang.text,
        "phone": "+966${phoneMang.text.trim()}",
        "password": passMang.text,
        "status": statusMang.value,
      };
      ResponseModel response = await ApiServices.postApi(
          id == null
              ? "v1/create/account/manager"
              : "v1/edit/account/manager/$id",
          body: formData,
          isAuth: true);
      if (response.status) {
        getManagerList(refresh: true);
        Get.dialog(WarningDialog(
            title: "success".tr,
            description: "Account Manager Updated",
            keyword: DialogKeyword.success,
            confirmText: 'okay'.tr,
            onConfirmed: () {
              Get.close(2);
              Get.to(() => const AccountManageList());
            }));
      }

      clearMangValue();

      isLoading.value = false;
    }
  }

  clearMangValue() {
    firstNameMang.clear();
    lastNameMang.clear();
    phoneMang.clear();
    emailMang.clear();
    passMang.clear();
    statusMang.value == "";
  }

  getManagerList({refresh = false}) async {
    if (refresh) {
      managerPage = 1;
    }
    ResponseModel responseModel = await ApiServices.getApi(
        "v1/account/manager/list?page=$managerPage&size=20",
        isAuth: true);
    if (responseModel.status) {
      if (managerPage > 1) {
        managers.addAll(responseModel.data['AccountManagers']
            .map<ManagerList>((item) => ManagerList.fromJson(item)));
        managerLazyLoader.value = false;
        managers.refresh();
      } else {
        managers.value = responseModel.data['AccountManagers']
            .map<ManagerList>((item) => ManagerList.fromJson(item))
            .toList();
      }
      lastManagerPage = responseModel.data['pagination']['total_pages'];
    }
  }

  deleteManagers(index) {
    Get.dialog(WarningDialog(
        title: 'warning'.tr,
        keyword: DialogKeyword.success,
        description: "Are you sure you want to delete this Manager?",
        onConfirmed: () {
          ApiServices.getApi("v1/account/manager/delete/${managers[index].id}",
                  isAuth: true)
              .then((response) {
            if (response.status) {
              managers.removeAt(index);
              managers.refresh();
              Get.back();
            }
          });
        }));
  }

  editManager(index) {
    String number = managers[index].formattedPhone ?? "";
    firstNameMang.text = managers[index].firstName ?? "";
    lastNameMang.text = managers[index].lastName ?? "";
    phoneMang.text = number.replaceAll("+966", "");
    emailMang.text = managers[index].email ?? "";
    statusMang.value = managers[index].status ?? "";

    Get.to(() => AddAccountManager(id: managers[index].id));
  }

  submitReport() async {
    if (Get.context != null) {
      FocusScope.of(Get.context!).requestFocus(FocusNode());
    }
    if (ticketType.value == null) {
      ticketError.value = "Please select ticket type.";
    }
    if ((reportFormKey.currentState?.validate() ?? false) &&
        ticketError.value == null) {
      isLoading.value = true;
      Map<String, String> formData = {
        "ticket_subject": ticketSubject.text,
        "ticket_type": ticketType.value.toString(),
        "description": ticketDesc.text
      };
      if (bookingId.text.isNotEmpty) {
        formData['booking_id'] = bookingId.text;
      }
      final response = await ApiServices.imageUpload("v1/ticket/create",
          body: formData,
          imagePath: complainImage.value,
          fileKeyName: "attachment");
      if (response.status) {
        try {
          final ticketType = SearchHelper.c.filters.value?.ticketTypes
              .firstWhere(
                  (item) => "${item.id}" == "response.data['ticket_type_id']");
          response.data['ticket_type'] = ticketType!.name;
          if (response.data['booking_id'] != null) {
            response.data['booking_id'] =
                int.parse(response.data['booking_id']);
          }
          if (ticketListing.length >= 12) {
            ticketListing.value = ticketListing.sublist(0, 11);
          }
          ticketListing.insert(0, TicketModel.fromJson(response.data));
          ticketPage = 1;
        } catch (e) {
          getTicketListing(refresh: true);
        }

        Get.off(() => const ProblemListing());
        ViewsCommon.showSnackbar(Get.find<TranslationHelper>()
            .translations
            .listing
            .ticketSubmitted!);
        // Get.dialog(WarningDialog(title: "success".tr, description: "Your ticket has been submitted.",keyword: DialogKeyword.success));
        clearTicketForm();
      }
      isLoading.value = false;
    }
  }

  // getReviews() {
  //   ApiServices.getApi("v1/get/reviews", isAuth: true).then((response) {
  //     if (response.status) {
  //       myReviews.value = response.data['about_you']
  //           .map<ReviewModel>((item) => ReviewModel.fromJson(item))
  //           .toList();
  //       myReviewsByYou.value = response.data['by_you']
  //           .map<ReviewModel>((item) => ReviewModel.fromJson(item))
  //           .toList();
  //       // double rating =
  //       //     myReviews.fold(0, (int previous, next) => previous + next.rating) /
  //       //         myReviews.length;
  //       // userModel.value!.avgRating =
  //       //     rating.isNaN ? "0" : rating.toStringAsFixed(1);
  //     }
  //   });
  // }

  gotoTransactions() {
    transactionsPage = 1;
    transactionsSize = 50;
    getTransactions();
    Get.to(() => const Transactions());
  }

  resetBankFields() {
    if (userModel.value!.bank != null) {
      bankName.text = userModel.value!.bank!.bankName ?? "";
      accTitle.text = userModel.value!.bank!.accountTitle ?? "";
      accNumber.text = userModel.value!.bank!.accountNumber ?? "";
      accIban.text = userModel.value!.bank!.iban ?? "";
      swiftCode.text = userModel.value!.bank!.swiftCode ?? "";
      accPhone.text = userModel.value!.bank!.phone ?? "";
    }
  }

  fillUserFields({bool isHandleLoginResponse = false}) {
    isUser = true;
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if ((Get.locale?.languageCode??"en") != (userModel.value?.lang??"en")) {
    //     Get.updateLocale(Locale(userModel.value!.lang));
    //   }
    // });
    firstName.text = userModel.value!.first_name;
    lastName.text = userModel.value!.last_name;
    liveC.text = userModel.value!.location ?? "";
    about.text = userModel.value!.about ?? "";
    email.text = userModel.value!.email ?? "";
    phone.text = userModel.value?.formatted_phone ?? "";
    resetBankFields();
    if (userModel.value!.dateofbirth != null &&
        userModel.value!.dateofbirth!.isNotEmpty) {
      List dobList = [];
      if (userModel.value!.dateofbirth!.contains("-")) {
        dobList = userModel.value!.dateofbirth!.split("-");
      } else {
        dobList = userModel.value!.dateofbirth!.split("/");
      }
      if (dobList.isNotEmpty) {
        dob.value = DateTime(int.parse(dobList[0]), int.parse(dobList[1]),
            int.parse(dobList[2]));
      }
    }
  }

  getServiceMessages({onInit = false}) {
    if (onInit) {
      serviceMessagesPage = 1;
      serviceMessagesSize = 5;
    }
    ApiServices.getApi(
            "v1/user/customerservice/messages?page=$serviceMessagesPage&size=$serviceMessagesSize",
            isAuth: true)
        .then((response) {
      if (response.status) {
        if (serviceMessagesPage > 1 && !onInit) {
          serviceMessages.insertAll(0, response.data['customer_support']);
          update(['support']);
          return;
        }
        serviceMessages =
            (response.data['customer_support'] as List).reversed.toList();
        totalMessagesPage = response.data['pagination']['total_pages'];
        update(['support']);
      }
    });
  }

  void attachServiceImage(ImageSource source) async {
    isLoading.value = true;
    final image = await ImagePicker().pickImage(source: source);
    if (image != null) {
      serviceImage.value = (await _compressImage(image)).path;
    }
    isLoading.value = false;
  }

  Future<XFile> _compressImage(XFile file) async {
    // Get the input file path from XFile
    final String inputPath = file.path;

    // Define the output path for the compressed image
    final String outputPath =
        '${(await getTemporaryDirectory()).path}/${DateTime.now().millisecondsSinceEpoch}.jpg';

    // Compress the image
    final XFile? compressedFile = await FlutterImageCompress.compressAndGetFile(
      inputPath,
      outputPath,
      quality: 70,
      format: CompressFormat.jpeg,
    );

    if (compressedFile == null) {
      throw Exception('Image compression failed');
    }

    // Save to temp and return XFile
    return await _saveImageFileInTemp(compressedFile);
  }

  Future<XFile> _saveImageFileInTemp(XFile file) async {
    final Directory tempDir = await _tempDirectory();
    final File tempFile = await _tempImageFile();

    // Read bytes from XFile and write to temp file
    final bytes = await file.readAsBytes();
    await tempFile.writeAsBytes(bytes);

    // Return an XFile pointing to the temp file
    return XFile(tempFile.path);
  }

  Future<Directory> _tempDirectory() async {
    return await getTemporaryDirectory();
  }

  Future<File> _tempImageFile() async {
    final tempDir = await _tempDirectory();
    final fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
    return File('${tempDir.path}/$fileName');
  }

  void sendServiceMsg() async => _canSendServiceMessage()
      ? await _sendServiceMessage()
      : _showSnackbarForRequiredMessage();

  bool _canSendServiceMessage() =>
      serviceImage.value != null || serviceMessageField.text.trim().isNotEmpty;

  Future<void> _sendServiceMessage() async {
    try {
      await _trySendMessage();
    } catch (e) {
      isLoadingGoogle.value = false;
    }
  }

  Future<void> _trySendMessage() async {
    isLoadingGoogle.value = true;
    final res = await ApiServices.imageUpload(
      'v1/user/customerservice/messages',
      body: {"message": serviceMessageField.text.trim()},
      fileKeyName: 'images[]',
      imagePath: serviceImage.value,
    );
    GlobalHelper.removeFocus();
    if (res.status) _whenMsgSentSuccessfully();
    isLoadingGoogle.value = false;
  }

  void _whenMsgSentSuccessfully() {
    _attachSentMessageToServiceChat({
      "is_user": 1,
      "message": serviceMessageField.text,
      "file": serviceImage.value,
      "created_at": DateTime.now().toString(),
    });

    serviceMessageField.clear();
    serviceImage.value = null;
  }

  void _attachSentMessageToServiceChat(
    Map<String, dynamic> sentMessage,
  ) {
    serviceScroll.animateTo(
      1,
      duration: const Duration(milliseconds: 100),
      curve: Curves.bounceOut,
    );
    serviceMessages.add(sentMessage);
    update(["support"]);
  }

  void _showSnackbarForRequiredMessage() {
    ViewsCommon.showSnackbar(
      Get.find<TranslationHelper>().translations.jqueryValidation.required,
      keyword: DialogKeyword.info,
      displayTime: 1200,
    );
  }

  @override
  void onDetached() {}

  @override
  void onInactive() {}

  @override
  void onPaused() {}
  @override
  void onResumed() async {
    // await ConnectivityHelper.checkInternet().then((value) async {
    //   if(value){
    //     if (isUser) {
    //       if (Get.currentRoute == Routes.profile && myReviews.isEmpty) {
    //         getReviews();
    //       }
    //     }
    //   }
    // });
  }

  @override
  void onHidden() {
    // TODO: implement onHidden
  }
}
