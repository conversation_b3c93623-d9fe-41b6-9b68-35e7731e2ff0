import 'dart:async' show Completer;
import 'dart:convert';
import 'dart:math' show min;
import 'package:darent/analytics/analytics.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/components/warning_dialog.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/listing_helper/listing_helper.dart';
import 'package:darent/helperMethods/locationHelper.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/models/filterModel.dart';
import 'package:darent/screens/bank.dart';
import 'package:darent/screens/listing_journey/new_property_manage_screens/landing_property_manage.dart';
import 'package:webengage_flutter/webengage_flutter.dart';
import '../helperMethods/search_helper.dart';
import '../helperMethods/translation_helper.dart';
import '../models/host/property_photo.dart';
import 'package:darent/screens/listing_journey/add_property_confirm.dart';
import 'package:darent/screens/listing_journey/arrange_images.dart';
import 'package:darent/screens/listing_journey/publish_listing.dart';
import 'package:darent/screens/listing_journey/step_main.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:geocoding/geocoding.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:ui' as ui;
import 'package:http/http.dart' as http;
import 'package:permission_handler/permission_handler.dart';
import '../screens/host/calendar_import.dart';
import '../screens/listing_journey/add_property14.dart';
import '../screens/listing_journey/add_property2.dart';
import '../screens/listing_journey/add_property3.dart';
import '../screens/listing_journey/add_property4.dart';
import '../screens/listing_journey/add_property6.dart';
import '../screens/listing_journey/add_property7.dart';
import '../screens/listing_journey/add_property8.dart';
import '../screens/listing_journey/add_property10.dart';
import '../screens/listing_journey/add_property11.dart';
import '../screens/listing_journey/add_property12.dart';
import '../utils/routes.dart';

class PropertyController extends GetxController
    with GetSingleTickerProviderStateMixin {
  bool isEdit;
  bool fromDwelling, forLicense;
  int? dwellingId;
  PropertyController(
      {this.isEdit = false,
      this.fromDwelling = true,
      this.forLicense = false,
      this.dwellingId});
  final placeSharing = "owner".obs;
  final sharedZone = "kitchen".obs;
  final guestSleep = "private".obs;
  bool allowScroll = true;
  late TabController tabController;
  final isLoading = false.obs;

  int? listPropertyId;
  final placeKind = Rxn<PropertyType>();
  final spaceTypes = RxList<SpaceType>();
  final spaceType = Rxn<TypeGeneral>();
  final adultCount = 1.obs;
  final childrenCount = 0.obs;
  final singleBedCount = 0.obs;
  final doubleBedCount = 0.obs;
  final bedroomsCount = 0.obs;
  final bathroomsCount = 1.0.obs;

  // new list journey
  var listStep = 0.obs;

  final noOfApartments = 1.obs;
  final minNights = TextEditingController(text: '1');
  final maxNights = TextEditingController(text: '30');
  final referralController = TextEditingController();
  final referralError = RxnString();

  final checkIn = const Duration(hours: 12, minutes: 00).obs;
  final checkOut = const Duration(hours: 14, minutes: 00).obs;

  // final selectedCoverImageI = RxnInt();
  final coverImage = Rxn<PropertyPhoto>();
  final propertyImages = RxList<PropertyPhoto>();
  final uploading = false.obs;

  final hostingType = "instant".obs;
  final haveAny = [
    {'id': 1, 'title': "security_camera", "isChecked": false},
    {'id': 2, 'title': "weapons", "isChecked": false},
    {'id': 2, 'title': "dangerous_animals", "isChecked": false}
  ].obs;
  final hostAgreed = false.obs;
  Completer<GoogleMapController> mapController = Completer();
  final marker = RxSet<Marker>();
  final circles = RxSet<Circle>();
  final characterCount = 0.obs;
  final characterCountArabic = 0.obs;
  final desCharCount = 0.obs;
  final desCharCountArabic = 0.obs;

  final locationForm = GlobalKey<FormState>();
  TextEditingController country = TextEditingController();
  TextEditingController address1 = TextEditingController();
  TextEditingController address2 = TextEditingController();
  TextEditingController city = TextEditingController();
  TextEditingController state = TextEditingController();
  TextEditingController district = TextEditingController();
  TextEditingController code = TextEditingController();
  bool licenseMandatory = Get.find<RemoteConfig>().licenseMandatory;

  final titleError = RxnString();
  final titleErrorAr = RxnString();
  final descriptionError = RxnString();
  final descriptionErrorAr = RxnString();
  TextEditingController name = TextEditingController();
  TextEditingController nameAr = TextEditingController();
  TextEditingController summary = TextEditingController();
  TextEditingController summaryAr = TextEditingController();
  final propertyAddress = RxnString();
  TextEditingController searchLocation = TextEditingController();

  LatLng propertyLocation = const LatLng(21.492500, 39.177570);
  // CameraPosition(
  //     bearing: 192.8334901395799,
  //     target: ,
  //     tilt: 59.440717697143555,
  //     zoom: 16);

  selectPlaceSharing(val) {
    placeSharing.value = val;
  }

  selectSharedZone(val) {
    sharedZone.value = val;
  }

  selectGuestSleep(val) {
    guestSleep.value = val;
  }

  selectPlaceKind(val) {
    placeKind.value = val;
  }

  selectSpaceType(val) {
    spaceType.value = val;
  }

  tabListener() {
    tabController = TabController(length: 10, vsync: this);
    // tabController.addListener(() {
    // allowScroll = tabController.index==0;
    // update(["property_form"]);
    // });
  }

  // saveProperty(){
  //   switch(tabController.index){
  //     case 0: {
  //       nextLocation();
  //     }
  //     break;
  //     case 1:{
  //       submitNoOfRooms();
  //     }
  //     break;
  //     case 2:{
  //       submitAmenities();
  //     }
  //     break;
  //     case 3:{
  //       submitPhotos();
  //     }
  //     break;
  //     case 4:{
  //       submitName();
  //     }
  //     break;
  //     case 5:{
  //       submitDescription();
  //     }
  //     break;
  //     case 6:{
  //       submitBasics();
  //     }
  //     break;
  //     case 7:{
  //       submitPrice();
  //     }
  //     break;
  //     case 8:{
  //       submitMinMax();
  //     }
  //     break;
  //     case 9:{
  //       submitQuestion();
  //     }
  //     break;
  //   }
  //
  // }
  listFillColor() {
    if (listStep < 10) {
      listStep++;
    }
  }

  submitPlaceKind({isExit = false}) {
    // first check if no place type selected then retuen with message
    if (placeKind.value == null) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>()
              .translations
              .listing
              .propertyValidation!,
          keyword: DialogKeyword.warning);
      return;
    }

    // loader start
    isLoading.value = true;

    // API Hit to submit selected property type
    ApiServices.postApi("v1/property/addplace",
            body: {"property_type_id": placeKind.value?.id}, isAuth: true)
        .then((ResponseModel response) {
      // after response loader end
      isLoading.value = false;

      // if user want to save and exit then will go in this
      if (isExit) {
        submitAndExit();
      }
      // or if going next then will go in this
      else if (response.status) {

        Get.to(() => const AddProperty2())!.then((_) {
          spaceType.value = null;
        });
        spaceTypes.value = SearchHelper.c.filters.value!.spaceType
            .where((item) => item.propertyType == placeKind.value?.id)
            .toList();
        try {
          spaceTypes.sort((a, b) => b.priority!.compareTo(a.priority!));
        } catch (e) {
          print("Error $e");
        }

        listPropertyId = response.data['propertyid'];
        unitCode = response.data['property_code'];
      }
      // at last a web engage event hit for marketing
      WebEngagePlugin.trackEvent('Property Type', {
        'User': isHost ? 'Host' : 'Customer',
        'Value Selected': placeKind.value?.name
      });
    });
  }

  submitSpaceType({isExit = false}) {
    if (spaceTypes.isNotEmpty && spaceType.value == null) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.listing.spaceValidation!,
          keyword: DialogKeyword.warning);
      return;
    }
    isLoading.value = true;
    ApiServices.postApi("v1/listing/$listPropertyId/spacetype",
            body: {
              "space_type": spaceType.value?.id,
              "no_of_appartment": noOfApartments.value
            },
            isAuth: true)
        .then((ResponseModel response) {
      isLoading.value = false;
      if (isExit) {
        submitAndExit();
      } else if (response.status && !isEdit) {
        // getCurrentLocation(isFirst: true);
        Get.to(() => AddProperty3());
      }
      WebEngagePlugin.trackEvent('Property Category', {
        'User': isHost ? 'Host' : 'Customer',
        'Value Selected': spaceType.value?.name
      });
      WebEngagePlugin.trackEvent('Number of Apartments', {
        'User': isHost ? 'Host' : 'Customer',
        'Value Selected': noOfApartments.value
      });
    });
  }

  backFromMap() {
    //marker.isNotEmpty && isLoading.isFalse
    // if(marker.isNotEmpty){
    // isLoading.value = true;
    address1.clear();
    address2.clear();
    country.clear();
    city.clear();
    state.clear();
    code.clear();
    ViewsCommon.clearPriceControllers();
    mapController = Completer();
    //   Future.delayed(const Duration(milliseconds: 1500)).then((value){
    //     isLoading.value = false;
    //     mapController = Completer();
    //     Get.back();
    //   });
    // }
  }

  nextLocation() async {
    isLoading.value = true;
    final location = await GeocodingPlatform.instance!.placemarkFromCoordinates(
        marker.first.position.latitude, marker.first.position.longitude);
    country.text = location[0].country ?? "";
    address1.text = propertyAddress.value ?? "";
    city.text = location[0].locality ?? location[0].subAdministrativeArea ?? "";
    state.text = location[0].administrativeArea ?? "";
    if (location[0].postalCode != null && location[0].postalCode!.isNotEmpty) {
      code.text = location[0].postalCode!;
    }
    isLoading.value = false;
    Get.to(() => const ConfirmAddress());
    WebEngagePlugin.trackEvent('Location', {
      'User': isHost ? 'Host' : 'Customer',
      'Value Selected':
          '${marker.first.position.latitude}, ${marker.first.position.longitude}'
    });
  }

  submitLocation({isExit = false}) async {
    if (locationForm.currentState?.validate() ?? false) {
      isLoading.value = true;
      Map formData = {
        'address_line_1': address1.text,
        'address_line_2': address2.text,
        'country': country.text,
        'city': city.text,
        'state': state.text,
        'district': district.text,
        'latitude': marker.first.position.latitude,
        'longitude': marker.first.position.longitude,
        'postal_code': code.text
      };
      try {
        final response = await ApiServices.postApi(
            isEdit
                ? "v1/update/property/$listPropertyId?step=location"
                : "v1/listing/$listPropertyId/location",
            body: formData,
            isAuth: true);
        // name.text = response.data['title'];
        isLoading.value = false;
        if (isExit) {
          submitAndExit();
        } else if (response.status) {
          if (isEdit) {
            Get.until((r) => r.settings.name == Routes.editMain);
          } else {
            licenseMandatory = response.data['license_mandatory'] == true;
            Get.to(() => const AddProperty4());
          }
        }
        ListingHelper.updatePropertyEventCalled("location");
        WebEngagePlugin.trackEvent('Address Entered', {
          'User': isHost ? 'Host' : 'Customer',
          'Value Selected': address1.text
        });
      } catch (e) {
        isLoading.value = false;
      }
    }
  }

  submitBasics({isExit = false}) {
    isLoading.value = true;
    Map formData = {"adult": adultCount.value, "children": childrenCount.value};
    try {
      ApiServices.postApi("v1/listing/$listPropertyId/basic",
              body: formData, isAuth: true)
          .then((ResponseModel response) {
        if (isExit) {
          submitAndExit();
        } else if (response.status && !isEdit) {
          Get.to(() => const AddProperty10());
        }
        isLoading.value = false;
        WebEngagePlugin.trackEvent('Family', {
          'User': isHost ? 'Host' : 'Customer',
          'Value Selected': adultCount.value + childrenCount.value
        });
      });
    } catch (e) {
      isLoading.value = false;
    }
  }

  submitNoOfRooms({isExit = false}) async {
    int beds = singleBedCount.value + doubleBedCount.value;
    if (bedroomsCount.value > 0 && beds <= 0) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.listing.bedValidation!,
          keyword: DialogKeyword.warning);
      return;
    }
    isLoading.value = true;
    Map formData = {
      "bedroom": bedroomsCount.value,
      "single_beds": bedroomsCount.value > 0 ? singleBedCount.value : 0,
      "double_beds": bedroomsCount.value > 0 ? doubleBedCount.value : 0,
      "beds": bedroomsCount.value > 0 ? beds : 0,
      "bathroom": bathroomsCount.value,
    };
    try {
      final response = await ApiServices.postApi(
          "v1/listing/$listPropertyId/numberofRoom",
          body: formData,
          isAuth: true);
      if (response.status) {
        if (isExit) {
          submitAndExit();
        } else if (response.status && !isEdit) {
          Get.to(() => const StepMain(stepNo: 2));
        }

        WebEngagePlugin.trackEvent('Single Beds', {
          'User': isHost ? 'Host' : 'Customer',
          'Value Selected': bedroomsCount.value > 0 ? singleBedCount.value : 0
        });
        WebEngagePlugin.trackEvent('Double Beds', {
          'User': isHost ? 'Host' : 'Customer',
          'Value Selected': bedroomsCount.value > 0 ? doubleBedCount.value : 0
        });
        WebEngagePlugin.trackEvent('Bathrooms', {
          'User': isHost ? 'Host' : 'Customer',
          'Value Selected': bathroomsCount > 0 ? bathroomsCount.value : 0
        });
      }
      isLoading.value = false;
    } catch (e) {
      isLoading.value = false;
    }
  }

  submitAmenities({isExit = false}) {
    List selectedAmenities = [];
    List<String> amenities = [],
        safetyItems = [],
        houseRules = [],
        customHouseRules = [];
    for (Amenity item in SearchHelper.c.filters.value!.amenities) {
      if (item.isChecked) {
        selectedAmenities.add(item.id);
        amenities.add(item.title ?? '');
      }
    }
    for (Amenity item in SearchHelper.c.filters.value!.safetyAmenities) {
      if (item.isChecked) {
        selectedAmenities.add(item.id);
        safetyItems.add(item.title ?? '');
      }
    }
    for (Amenity item in SearchHelper.c.filters.value!.houseRules) {
      if (item.isChecked) {
        selectedAmenities.add(item.id);
        houseRules.add(item.title ?? '');
      }
    }
    for (Amenity item in SearchHelper.c.customHouseRules) {
      customHouseRules.add(item.title ?? '');
    }
    if (selectedAmenities.isEmpty) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.listing.amenityValidation!,
          keyword: DialogKeyword.warning);
      return;
    }
    isLoading.value = true;
    Map formData = {
      "amenities": selectedAmenities,
      // will enable when API is ready
      "custom_amenities": customHouseRules
    };
    ApiServices.postApi("v1/listing/$listPropertyId/amenities",
            body: formData, isAuth: true)
        .then((ResponseModel response) {
      if (isExit) {
        submitAndExit();
      } else if (response.status && !isEdit) {
        Get.to(() => const AddProperty6());
      }
      isLoading.value = false;
      WebEngagePlugin.trackEvent('Premium Amenities Selected', {
        'User': isHost ? 'Host' : 'Customer',
        'Value Selected': amenities.join(", ")
      });
      WebEngagePlugin.trackEvent('Safety Items', {
        'User': isHost ? 'Host' : 'Customer',
        'Value Selected': safetyItems.join(", ")
      });
      WebEngagePlugin.trackEvent('House Rules', {
        'User': isHost ? 'Host' : 'Customer',
        'Value Selected': houseRules.join(", ")
      });
    });
  }

  submitName({isExit = false}) {
    titleError.value = name.text.trim().isEmpty
        ? Get.find<TranslationHelper>().translations.jqueryValidation.required
        : !GlobalHelper.checkStringIsEnglishUsingReg(name.text)
            ? Get.find<TranslationHelper>()
                .translations
                .jqueryValidation
                .textInEnglish
            : null;
    titleErrorAr.value = nameAr.text.trim().isEmpty
        ? Get.find<TranslationHelper>().translations.jqueryValidation.required
        : !GlobalHelper.checkStringIsArabicUsingReg(nameAr.text)
            ? Get.find<TranslationHelper>()
                .translations
                .jqueryValidation
                .textInArabic
            : null;
    if (titleError.value == null && titleErrorAr.value == null) {
      isLoading.value = true;
      ApiServices.postApi("v2/listing/$listPropertyId/title",
              body: {
                "title": name.text,
                "title_ar": nameAr.text,
                "edit": isEdit ? 1 : 0
              },
              isAuth: true)
          .then((ResponseModel response) {
        GlobalHelper.removeFocus();
        if (isExit) {
          submitAndExit();
        } else if (response.status && !isEdit) {
          Get.to(() => const AddProperty8());
        }
        isLoading.value = false;
      });
    }
  }

  submitDescription({isExit = false}) {
    descriptionError.value = summary.text.trim().isEmpty
        ? Get.find<TranslationHelper>().translations.jqueryValidation.required
        : summary.text.trim().length < 20
            ? Get.find<TranslationHelper>()
                .translations
                .listing
                .descriptionMinValidation
            : !GlobalHelper.checkStringIsEnglishUsingReg(summary.text)
                ? Get.find<TranslationHelper>()
                    .translations
                    .jqueryValidation
                    .textInEnglish
                : null;
    descriptionErrorAr.value = summaryAr.text.trim().isEmpty
        ? Get.find<TranslationHelper>().translations.jqueryValidation.required
        : !GlobalHelper.checkStringIsArabicUsingReg(summaryAr.text)
            ? Get.find<TranslationHelper>()
                .translations
                .jqueryValidation
                .textInArabic
            : null;
    if (descriptionError.value == null && descriptionErrorAr.value == null) {
      isLoading.value = true;
      ApiServices.postApi("v2/listing/$listPropertyId/description",
              body: {
                "summary": summary.text.trim(),
                "summary_ar": summaryAr.text.trim(),
                "edit": isEdit ? 1 : 0
              },
              isAuth: true)
          .then((ResponseModel response) {
        GlobalHelper.removeFocus();
        if (isExit) {
          submitAndExit();
        } else if (response.status && !isEdit) {
          Get.to(() => const StepMain(stepNo: 3));
          //Get.to(() => const AddProperty9());
        }
        isLoading.value = false;
      });
    }
  }

  submitPrice({isExit = false}) {
    for (String key in ViewsCommon.priceControllers.keys) {
      int? number = int.tryParse(ViewsCommon.priceControllers[key]!.text);
      if (number == null || number < 50) {
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>()
                .translations
                .listing
                .minPriceValidation!,
            keyword: DialogKeyword.warning);
        return;
      }
    }
    // if (int.parse(minNights.text) > int.parse(maxNights.text)) {
    //    ViewsCommon.showSnackbar(translateKeywords[Get.locale?.languageCode??"en"]!.listing.minMaxNightsValidation!,keyword: DialogKeyword.warning);
    //    return;
    //  }
    isLoading.value = true;
    Map formData = {};
    for (String fieldName in ViewsCommon.priceControllers.keys) {
      formData[fieldName == "price" ? fieldName : "price_$fieldName"] =
          ViewsCommon.priceControllers[fieldName]?.text ?? "0";
    }
    ApiServices.postApi("v1/listing/$listPropertyId/price",
            body: formData, isAuth: true)
        .then((ResponseModel response) {
      if (isExit) {
        ViewsCommon.clearPriceControllers();
        submitAndExit();
      } else if (response.status && !isEdit) {
        if (userModel.value?.bank != null) {
          Get.to(() => const AddProperty11());
        } else {
          Get.to(() => const BankAccount(fromListing: true));
        }
      }
      isLoading.value = false;
    });
  }

  submitMinMax({isExit = false}) async {
    if (int.parse(minNights.text) > int.parse(maxNights.text)) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>()
              .translations
              .listing
              .minMaxNightsValidation!,
          keyword: DialogKeyword.warning);
      return;
    }
    String checkInS =
        "${GlobalHelper.twoNumberFormat(checkIn.value.inHours)}:${GlobalHelper.twoNumberFormat(checkIn.value.inMinutes % 60)}:00";
    String checkOutS =
        "${GlobalHelper.twoNumberFormat(checkOut.value.inHours)}:${GlobalHelper.twoNumberFormat(checkOut.value.inMinutes % 60)}:00";
    isLoading.value = true;
    //old - "${GlobalHelper.twoNumberFormat(checkIn.value.hour)}:${GlobalHelper.twoNumberFormat(checkIn.value.minute)}:00"
    Map formData = {
      "min_nights": minNights.text,
      "max_nights": maxNights.text,
      "checkinTime": checkInS,
      "checkoutTime": checkOutS,
    };
    final response = await ApiServices.postApi(
        "v1/listing/$listPropertyId/nightsandtime",
        body: formData,
        isAuth: true);
    if (isExit) {
      ViewsCommon.clearPriceControllers();
      submitAndExit();
    } else if (response.status && !isEdit) {
      Get.to(() => const AddProperty12());
    }
    isLoading.value = false;
  }

  submitQuestion({isExit = false}) {
    // removing on 26 jul because from now on uploading license and cr number on publish listing screen done
    // if(userModel.value?.document == 0 && !removeDocument.value){
    //     Get.dialog(SaudiLicenseDialogue());
    //     return;
    // }
    isLoading.value = true;
    ApiServices.postApi("v1/listing/$listPropertyId/question",
            body: {"booking_type": hostingType.value}, isAuth: true)
        .then((ResponseModel response) {
      if (isExit) {
        submitAndExit();
      } else if (!isEdit) {
        //Preferred Contact
        Get.to(() => CalendarImport(listingId: listPropertyId));
      }
      isLoading.value = false;
    });
  }

  submitPreferredContactType(dynamic contactTypes, {isExit = false}) {
    isLoading.value = true;
    List<String> commMethod = [];
    for (var item in contactTypes) {
      if (item.isChecked) {
        commMethod.add(item.name ?? '');
      }
    }
    if (commMethod.isEmpty) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>()
                  .translations
                  .usersProfile
                  .selectAtleast1 ??
              "Please select at least 1 contact method",
          keyword: DialogKeyword.warning);
      isLoading.value = false;
      return;
    }
    ApiServices.postApi("v1/listing/$listPropertyId/communicationMethod",
            body: {"comm_method": commMethod}, isAuth: true)
        .then((ResponseModel response) {
      userModel.value!.communicationPrefs = commMethod.join(",");
      userModel.value!.contactMethodAdded = true;
      GlobalHelper.storageBox.write('user', userModel.value?.toJson());
      if (isExit) {
        submitAndExit();
      } else if (!isEdit) {
        userModel.value?.yaqeenVerified == false
            ? Get.to(() => const AddProperty14())
            : Get.to(() => const PublishListing());
      }
      isLoading.value = false;
    });
  }

  submitIlmYaqeen({isExit = false}) {
    if (userModel.value?.yaqeenVerified == false) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>()
              .translations
              .hostDashboard
              .verifyIdentity!,
          keyword: DialogKeyword.warning);
      return;
    }
    if (isExit) {
      submitAndExit();
    } else {
      Get.to(() => const PublishListing());
    }
  }

  final licenseForm = GlobalKey<FormState>();
  TextEditingController cr = TextEditingController();
  TextEditingController license = TextEditingController();
  TextEditingController coName = TextEditingController();
  final licenseExpiry = Rx<DateTime>(DateTime.now());
  // final licenseDoc = Rxn<XFile>();

  final documentUploaded = RxBool(false);
  final isCompany = RxBool(false);
  final licenseStep = RxInt(1);
  // FilePickerResult? licensePath;

  // clearLicensePath(){
  //   licensePath = null;
  // }
  // pickLicenseFile() async {
  //   clearLicensePath();
  //   try{
  //     var image =await FilePicker.platform.pickFiles(
  //     compressionQuality: 30,
  //     type: FileType.custom,
  //     allowMultiple: false,
  //     onFileLoading: (FilePickerStatus status) => debugPrint(status.toString()),
  //       allowedExtensions: ['pdf', 'doc', 'txt''jpeg','jpg','png'],
  //   );
  //     if(image != null){
  //       licensePath = image;
  //     }
  //   }catch(e){
  //   debugPrint("getting error while picking file $e");
  //   clearLicensePath();
  //   isLoading.value= false;
  //   debugPrint(e.toString());
  //   }
  // }
  moveLicenseBack() {
    licenseStep.value = 1;
    coName.clear();
    licenseExpiry.value = DateTime.now();
    // licenseDoc.value = null;
  }

  void clearLicenseFields({bool shouldDeleteController = false}) {
    licenseStep.value = 1;
    license.clear();
    cr.clear();
    coName.clear();
    licenseExpiry.value = DateTime.now();
    // licenseDoc.value = null;
    if (shouldDeleteController) {
      Get.delete<PropertyController>();
    }
  }

  // licenseDocument(){
  //   FilePicker.platform.pickFiles(allowedExtensions:['pdf','docx', 'png', 'jpg', 'jpeg'],type: FileType.custom).then((value){
  //     if(value?.files.isNotEmpty==true){
  //       licenseDoc.value  = value!.files.first.xFile;
  //     }
  //   });
  //   GlobalHelper.removeFocus();
  // }
  uploadDocument([dwelling]) async {
    if (licenseForm.currentState?.validate() == true) {
      try {
        // if (licensePath != null) {
        isLoading.value = true;
        if (licenseStep.value == 2) {
          // await submitLicenseDocument(dwelling);
        } else {
          Map<String, String> mapDocument = {
            // 'step':licenseStep.value.toString(),
            "cr_no": cr.text,
            "license_no": license.text,
            'license_is_company': '${isCompany.value ? 1 : 0}',
            'license_company_name': coName.text,
            'license_expiry': formDateFormat.format(licenseExpiry.value),
          };
          // var response = await ApiServices.postApi(
          //     "v2/upload-license/${dwellingId??listPropertyId}",
          //     body: mapDocument,
          //     isAuth: true
          // );
          final res = await ApiServices.postApi(
              'v3/upload-license/${dwellingId ?? listPropertyId}',
              body: mapDocument,
              isAuth: true);
          //imagePath: licenseDoc.value?.path, fileKeyName: 'license_document'
          if (res.status) {
            //licenseStep.value = 2;
            documentUploaded.value = true;
            if (forLicense) {
              Get.until((route) => route.isFirst);
              if (dwelling != null) {
                dwelling.licenseVerified = true;
                ListingHelper.c.dwellings.refresh();
              }
            } else {
              Get.back();
            }
          }
          ViewsCommon.showSnackbar(res.message, displayTime: 750);
        }
        isLoading.value = false;
        // }
        // isLoading.value = false;
        // ViewsCommon.showSnackbar("يرجى إرفاق ترخيص/تصريح وزارة السياحة",displayTime: 750);
      } catch (e) {
        debugPrint("getting error while uploadDocument $e");
        isLoading.value = false;
        debugPrint(e.toString());
        return false;
      }
    }
  }

  // submitLicenseDocument([dwelling])async{
  //   Map<String,String> form = {
  //     'step':licenseStep.value.toString(),
  //     'license_is_company':'${isCompany.value?1:0}',
  //     'license_company_name':coName.text,
  //     'license_expiry':formDateFormat.format(licenseExpiry.value),
  //   };
  //   final res = await ApiServices.imageUpload('v2/upload-license/${dwellingId??listPropertyId}',body: form,fileKeyName: 'license_document',imagePath: licenseDoc.value?.path,displayDialog:false);
  //   if(res.status){
  //     documentUploaded.value = true;
  //     // userModel.value!.document = 1;
  //     // userModel.refresh();
  //     // storageBox.write('user', userModel.value?.toJson());
  //     //ViewsCommon.showSnackbar("تم تقديم الترخيص بنجاح",displayTime: 750);
  //     if(forLicense){
  //       Get.until((route) => route.isFirst);
  //       if(dwelling!=null){
  //         dwelling.licenseVerified = true;
  //         ListingHelper.c.dwellings.refresh();
  //       }
  //     }else{
  //       Get.back();
  //     }
  //   }
  //   ViewsCommon.showSnackbar(res.message,keyword: res.status?DialogKeyword.success:DialogKeyword.info);
  // }
  bool isComplete = false;
  String? unitCode;
  openConfirmationDialogue() {
    isComplete = true;
    Get.dialog(WarningDialog(
            confirmText:
                Get.find<TranslationHelper>().translations.listing.propertyOk,
            largeText: true,
            isLargeIcon: true,
            title:
                "${Get.find<TranslationHelper>().translations.listing.propertySuccessful!}✨",
            description: Get.find<TranslationHelper>()
                .translations
                .listing
                .messageRecieveShortly!,
            onConfirmed: () async {
              Get.back();
            },
            keyword: DialogKeyword.success))
        .then((value) {
      submitAndExit();
    });
  }

  publishListing() async {
    if (!isEdit && !hostAgreed.value && !forLicense) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.listing.hostAgree!,
          keyword: DialogKeyword.warning);
      return;
    }
    if (referralController.text.isNotEmpty &&
        (referralController.text.length != 10)) {
      referralError.value = Get.find<TranslationHelper>()
          .translations
          .hostListing
          .referralCodeLimitError;
      return;
    } else {
      referralError.value = null;
    }
    try {
      if (documentUploaded.value || !licenseMandatory) {
        isLoading.value = true;
        final response = await ApiServices.getApi(
            "v1/listing/$listPropertyId/reviewListing?ref_code=${referralController.text}",
            isAuth: true);
        if (response.status) {
          openConfirmationDialogue();
         // Track host property listing completed event with WebEngage (existing)
          WebEngagePlugin.trackEvent('Host Registration Completed', {
            'User': isHost ? 'Host' : 'Customer',
          });

          // Track host property listing completed event with Analytics Manager
          Get.find<AnalyticsManager>().trackEvent(
            AnalyticsEvents.hostPropertyListingCompleted,
            eventAttributes: {
              AnalyticsAttributes.hostId: userModel.value?.id?.toString(),
              AnalyticsAttributes.propertyId: listPropertyId?.toString(),
              AnalyticsAttributes.timestamp: DateTime.now().toIso8601String(),
              AnalyticsAttributes.platform: 'mobile_app',
            },
          );
        }
        isLoading.value = false;
      } else {
        ViewsCommon.showSnackbar('Please your upload License first !',
            keyword: DialogKeyword.info);
      }
    } catch (e) {
      isLoading.value = false;
    }
  }

  @override
  void onInit() {
    tabListener();
    setAmenities();
    super.onInit();
    getCurrentLocation(isFirst: true);
    WebEngagePlugin.trackEvent('Host Registration Started', {
      'User': isHost ? 'Host' : 'Customer',
    });

    // Track host property listing started event with Analytics Manager
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.hostPropertyListingStarted,
      eventAttributes: {
        AnalyticsAttributes.hostId: userModel.value?.id?.toString(),
        AnalyticsAttributes.timestamp: DateTime.now().toIso8601String(),
        AnalyticsAttributes.platform: 'mobile_app',
      },
    );
  }

  setAmenities() {
    SearchHelper.c.customHouseRules.clear();
    for (var item in SearchHelper.c.filters.value?.amenities ?? []) {
      item.isChecked = false;
    }
    for (var item in SearchHelper.c.filters.value?.safetyAmenities ?? []) {
      item.isChecked = false;
    }
    for (var item in SearchHelper.c.filters.value?.houseRules ?? []) {
      item.isChecked = false;
    }
  }

  void getCurrentLocation({bool isFirst = false}) async {
    if (LocationHelper.currentLocation != null ||
        await LocationHelper.getCurrentLocation(showWarnings: !isFirst)) {
      onCameraChanged(LocationHelper.currentLocation!);
    } else if (isFirst) {
      onCameraChanged(propertyLocation);
    }
  }

  onCameraChanged(LatLng value, {fromMarker = false, String? location}) async {
    final Uint8List markIcon = await getImage();
    if (location == null) {
      final result = await http.get(Uri.parse(
          'https://maps.google.com/maps/api/geocode/json?key=$mapKey&latlng=${value.latitude},${value.longitude}'));
      if (result.statusCode == 200) {
        final response = jsonDecode(result.body);
        if (response['status'] == 'OK') {
          location = response['results'].first['formatted_address'];
        }
      }
    }
    if (!fromMarker) {
      marker.value = {
        Marker(
          draggable: true,
          markerId: const MarkerId(""),
          icon: BitmapDescriptor.bytes(markIcon),
          position: value,
          onDragEnd: (value) => onCameraChanged(value, fromMarker: true),
        )
      };
    }
    propertyAddress.value = location;
    if (!mapController.isCompleted) {
      mapController = Completer<GoogleMapController>();
    }
    mapController.future.then((c) {
      c.animateCamera(CameraUpdate.newLatLng(value));
      propertyLocation = value;
    });
    searchLocation.text = location ?? "";
  }

  Future<Uint8List> getImage() async {
    ByteData data = await rootBundle.load("assets/icons/pin.png");
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetHeight: 70);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  bathroomMinus() {
    if (bathroomsCount.value == 1) {
      bathroomsCount.value = 0.5;
    } else if (bathroomsCount.value > 1) {
      bathroomsCount.value--;
    }
  }

  bathroomPlus() {
    if (bathroomsCount.value == 0.5) {
      bathroomsCount.value = 1;
    } else {
      bathroomsCount.value++;
    }
  }

  minusWithValidation(val, {minAmount = 1}) {
    if (val.value > minAmount) {
      val.value--;
    } else if (minAmount == 5) {
      //price k lye
      Get.closeAllSnackbars();
      Get.snackbar("Warning", "The price must be at least 5");
      return;
    }
  }

  plus(val) {
    val.value++;
  }

  plusPrice(TextEditingController c) {
    c.text = "${int.parse(c.text) + 1}";
  }

  minusPrice(TextEditingController c, {minAmount = 1}) {
    int price = int.parse(c.text);
    if (price > minAmount) {
      c.text = "${int.parse(c.text) - 1}";
    }
  }

  reArrangeList(int oldIndex, int newIndex) {
    final temp = propertyImages[oldIndex];
    propertyImages[oldIndex] = propertyImages[newIndex];
    propertyImages[newIndex] = temp;
    propertyImages.refresh();
  }

  removeImage(index) {
    if (propertyImages[index].id != 0) {
      isLoading.value = true;
      int removeId = propertyImages[index].id;
      ApiServices.postApi("v1/delete_photo",
              body: {"photoid": removeId}, isAuth: true)
          .then((ResponseModel response) {
        if (response.status) {
          propertyImages.removeAt(index);
          if (removeId == coverImage.value?.id &&
              propertyImages.isNotEmpty &&
              propertyImages[0].id != 0) {
            coverImage.value = propertyImages[0];
          }
          if (propertyImages.isEmpty || propertyImages[0].id == 0) {
            // selectedCoverImageI.value = null;
            coverImage.value = null;
          }
        }
        isLoading.value = false;
      });
    } else {
      propertyImages.removeAt(index);
    }
  }

  changeCover(i) async {
    if (propertyImages[i].image != coverImage.value?.image) {
      isLoading.value = true;
      if (propertyImages[0].id != 0) {
        await ApiServices.postApi("v1/editlisting/updatecover",
            body: {
              "property_id": listPropertyId,
              "photo_id": coverImage.value!.id
            },
            isAuth: true);
        coverImage.value = propertyImages[i];
      } else {
        final cover = propertyImages[i];
        propertyImages.removeAt(i);
        propertyImages.insert(0, cover);
        coverImage.value = propertyImages[0];
      }
      isLoading.value = false;
    }
  }

  // coverPhotoDialog(){
  //   List properties = propertyImages.where((item)=>item.id!=0).toList();
  //    Get.dialog(Dialog(
  //      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(23)),
  //      child: Container(
  //        height: heightSpace(80),
  //        padding: EdgeInsets.symmetric(horizontal: widthSpace(4), vertical: widthSpace(2)),
  //        child: Obx(()=>Column(children: [
  //            AppBar(
  //                leadingWidth: widthSpace(8),
  //                leading: InkWell(
  //                    onTap: Get.back,
  //                    child: const Icon(Icons.cancel_outlined,size: 30, color: Colors.black)),
  //                centerTitle: true,
  //                title: CustomText(translateKeywords[Get.locale?.languageCode??"en"]!.listing.selectCover,size: 2.3,weight: FontWeight.w500)),
  //            SizedBox(height:heightSpace(3)),
  //            Expanded(
  //              child: properties.isEmpty?const Center(child: CustomText("No property found")):GridView(gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 2,mainAxisSpacing: 6),
  //                  children:List.generate(properties.length, (index){
  //                    bool isSelected = selectedCoverImageI.value == index;
  //                    return
  //                      InkWell(
  //                        onTap: (){
  //                                selectedCoverImageI.value = index;
  //                              },
  //                              child: Container(
  //                                  alignment: Alignment.topCenter,
  //                                  margin: const EdgeInsets.all(3),
  //                                  decoration: BoxDecoration(
  //                                    border: isSelected?Border.all(width: 0.5):null,
  //                                      borderRadius: BorderRadius.circular(5),
  //                                      image: DecorationImage(
  //                                          colorFilter: isSelected? ColorFilter.mode(Colors.black.withOpacity(0.4), BlendMode.dstATop):null,
  //                                          image: properties[index].image.contains("/property") ==true
  //                                              ? NetworkImage("$baseUrl/${properties[index].image}") as ImageProvider
  //                                              : FileImage(File(properties[index].image)),
  //                                          fit: BoxFit.cover)),child: isSelected?Center(child: Icon(Icons.check,size: widthSpace(7))):null),
  //                            );
  //                          }),),
  //            ),
  //            Align(alignment:Alignment.centerRight,child: CommonButton(title: translateKeywords[Get.locale?.languageCode??"en"]!.utility.submit, isLoading: isImgLoading.value,
  //                onPressed:properties.isEmpty?null:changeCover))
  //          ]),
  //        ),
  //      ),
  //    ));
  // }
  // submitPhotos({isExit = false})async {
  //   if(isLoading.value){
  //     return;
  //   }
  //   if (propertyImages.length < 5) {
  //    ViewsCommon.showSnackbar(translateKeywords[Get.locale?.languageCode??"en"]!.listing.minImagesValidation!,keyword: DialogKeyword.warning);
  //    return;
  //   }
  //   if(isEdit){
  //     isLoading.value = true;
  //     Future.delayed(const Duration(milliseconds: 600)).then((value){
  //       isLoading.value = false;
  //     });
  //   }else if(isExit){
  //     uploadImages(isExit:true);
  //   }else{
  //     if(Get.currentRoute=='/ArrangeImages'){
  //       uploadImages();
  //     }else{
  //       Get.to(() => ArrangeImages());
  //     }
  //   }
  // }
  sortImages({bool isExit = false}) async {
    uploading.value = true;
    final list = propertyImages.map((e) => e.id).toList();
    final r = await ApiServices.postApi("v1/change-photo-order/$listPropertyId",
        body: {'sort_images': list}, isAuth: true);
    if (r.status) {
      if (isExit) {
        submitAndExit();
      } else {
        Get.to(() => const AddProperty7());
      }
    }
    uploading.value = false;
  }

  uploadImages({isExit = false}) async {
    if (propertyImages.length < 5) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>()
              .translations
              .listing
              .minImagesValidation!,
          keyword: DialogKeyword.warning);
      return;
    }
    if (propertyImages.length > 20) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>()
              .translations
              .listing
              .maxImagesValidation!,
          keyword: DialogKeyword.warning);
      return;
    }
    final files = propertyImages
        .where((item) => item.id == 0)
        .map((item) => item.image)
        .toList();
    if (files.isNotEmpty) {
      uploading.value = true;
      final response = await ApiServices.imageUpload(
          "v2/listing/$listPropertyId/photos",
          body: {'edit': isEdit ? '1' : '0'},
          fileKeyName: "file[]",
          imagePaths: files);
      uploading.value = false;
      if (response.status) {
        propertyImages.removeWhere((item) => item.id == 0);
        for (int i = 0; i < response.data['photosid'].length; i++) {
          if (propertyImages.isEmpty) {
            propertyImages.add(PropertyPhoto(
                response.data['photosid'][i], files[i],
                coverPhoto: 1));
            coverImage.value = propertyImages.first;
          } else {
            propertyImages.add(PropertyPhoto(
                response.data['photosid'][i], files[i],
                coverPhoto: 0));
          }
        }
      } else {
        return;
      }
    }
    if (isExit) {
      submitAndExit();
    } else if (files.isNotEmpty || propertyImages.isNotEmpty) {
      print('Navigating..');
      Get.to(() => ArrangeImages());
    }
    WebEngagePlugin.trackEvent('Upload Photos',
        {'User': isHost ? 'Host' : 'Customer', 'Value Selected': true});
  }

  pickImage() async {
    if (propertyImages.length >= 20) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>()
              .translations
              .listing
              .maxImagesValidation!,
          keyword: DialogKeyword.warning);
      return;
    }
    try {
      var imagesMultiple =
          await ImagePicker().pickMultiImage(requestFullMetadata: false);
      if (imagesMultiple.isNotEmpty) {
        uploading.value = true;
        int maxImages = 20 - propertyImages.length;
        if (imagesMultiple.length > maxImages) {
          imagesMultiple = imagesMultiple.sublist(0, maxImages);
        }
        propertyImages
            .addAll(imagesMultiple.map((item) => PropertyPhoto(0, item.path)));
        if (propertyImages.length == imagesMultiple.length) {
          propertyImages[0].coverPhoto = 1;
          coverImage.value = propertyImages[0];
        }
        uploading.value = false;
      }
    } catch (err) {
      uploading.value = false;
      err as PlatformException;
      if (err.code == "multiple_request") {
        return;
      }
      var status = await Permission.photos.status;
      if (status.isDenied || status.isPermanentlyDenied) {
        final request = await Permission.photos.request();
        if (request.isPermanentlyDenied) {
          Get.dialog(WarningDialog(
              title: "Permission Denied",
              description: "Please grant gallery permission to insert image.",
              onConfirmed: () async {
                final settings = await openAppSettings();
                if (settings) {
                  pickImage();
                }
              },
              keyword: DialogKeyword.info));
        }
      } else {
        pickImage();
      }
    }
  }

  selectHosingType(String value) {
    hostingType.value = value;
  }

  checkHaveAny(value) {
    value['isChecked'] = !(value['isChecked'] == true);
    haveAny.refresh();
  }

  agreeHost() {
    hostAgreed.value = !hostAgreed.value;
  }

  translate(String target, TextEditingController controller,
      TextEditingController targetController, RxInt count) async {
    if (controller.text.isNotEmpty) {
      // if(target=="ar"){
      //   if(summary.text.isEmpty){
      //
      //   }
      // }else{
      //   if(summaryAr.text.isEmpty){
      //     return;
      //   }
      // }
      http
          .get(Uri.parse(
              "https://translation.googleapis.com/language/translate/v2?target=$target&key=$mapKey&q=${controller.text}"))
          .then((value) {
        if (value.statusCode == 200) {
          Map response = json.decode(value.body);
          // if(target=="ar"){
          if (response['data']['translations'].isNotEmpty) {
            targetController.text = response['data']['translations']
                .first['translatedText']
                .replaceAll('&#39;', "'")
                .replaceAll('&quot;', '"');
            (target == 'en' ? characterCount : characterCountArabic).value =
                min(targetController.text.length, 50);
          }
          // }else if(response['data']['translations'].isNotEmpty){
          //     summary.text = response['data']['translations'].first['translatedText'];
          //     desCharCount.value = summary.text.length;
          //   }
        }
      });
    }
  }

  submitAndExit() {
    Get.until((route) => route.isFirst);
    SearchHelper.c.changeIndex(3);
    // String r = fromDwelling?Routes.dwellings:Routes.hostHome;
    // Get.until((route) => route.settings.name?.contains(r)==true);
  }
}
