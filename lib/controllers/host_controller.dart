// import 'package:darent/controllers/dashboard_controller.dart';
// import 'package:darent/helperMethods/globalHelpers.dart';
// import 'package:darent/models/calenderPrice.dart';
// import 'package:darent/screens/listing_journey/add_property3.dart';
// import 'package:darent/screens/listing_journey/add_property5.dart';
// import 'package:darent/screens/listing_journey/edit_property.dart';
// import 'package:darent/screens/listing_journey/publish_listing.dart';
// import 'package:darent/screens/promotion/promo_code.dart';
// import 'package:darent/screens/promotion/promotions.dart';
// import 'package:flutter_colorpicker/flutter_colorpicker.dart';
// import 'package:google_maps_flutter/google_maps_flutter.dart';
//
// import '../components/common_button.dart';
// import '../components/common_checkbox.dart';
// import '../components/custom_text.dart';
// import '../components/custom_textfield.dart';
// import '../controllers/property_controller.dart';
// import 'package:darent/models/bookingHistory.dart';
// import 'package:darent/models/bookingModel.dart';
// import 'package:darent/models/propertyModel.dart';
// import '../models/promoModel.dart';
// import '../screens/listing_journey/add_property2.dart';
// import '../screens/listing_journey/add_property4.dart';
// import '../screens/listing_journey/add_property6.dart';
// import '../screens/listing_journey/add_property7.dart';
// import '../screens/listing_journey/add_property8.dart';
// import '../screens/listing_journey/add_property9.dart';
// import '../screens/listing_journey/add_property10.dart';
// import '../screens/listing_journey/add_property11.dart';
// import '../screens/listing_journey/add_property12.dart';
// import '../utils/api_service.dart';
// import '../utils/constants.dart';
// import '../utils/sizeconfig.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:intl/intl.dart';
// import '../components/warning_dialog.dart';
//
// class HostController extends SuperController{
//   int index = 0;
//   HostController({this.index=0});
//
//   ScrollController scrollController = ScrollController();
//   ScrollController completeScrollController = ScrollController();
//   ScrollController inCompleteScrollController = ScrollController();
//   final myCompletedProperties = RxList<Property>();
//   final inCompleteProperties = RxList<Property>();
//   //import calender
//     final reportFormKey = GlobalKey<FormState>();
//     final calenderName = TextEditingController();
//     final calenderUrl = TextEditingController();
//     final pickerCustomColor = Rx<Color>(Colors.white);
//     final calenderCustomColor = Rxn<Color>();
//     final calendarColorError = false.obs;
//     final calenderType = RxnString();
//     final calenderError = RxnString();
//
//
//
//   // promotion
//   int? promoId;
//   TextEditingController propertySearch = TextEditingController();
//   final propertySearchKey = "".obs;
//   final promotions = RxList<PromoModel>();
//   final promoFormKey = GlobalKey<FormState>();
//   final selectedProps = RxnString();
//   final promoTitle = TextEditingController();
//   final promoDiscount = TextEditingController();
//   final promoCode = TextEditingController();
//   final promoUpto = TextEditingController();
//   final promoMaxUsage = TextEditingController();
//   final promoUserUsage = TextEditingController();
//   final promoDescription = TextEditingController();
//   final promoStart = Rx(DateTime.now());
//   final promoEnd = Rx(DateTime.now().add(Duration(days: 1)));
//   final forNewUser = false.obs;
//   final promoProperties = RxList<Property>();
//
//   // final promoStartError = RxnString();
//   // final promoEndError = RxnString();
//   final selectPropsError = RxnString();
//   int completePage = 1 ;
//   int incompletePage = 1;
//
//   int completeTotalPage = 1;
//   int incompleteTotalPage = 1;
//   int promotionPage = 1;
//   int lastPromotionPage = 1;
//   final promotionLazyLoader = false.obs;
//   final completedLoader = false.obs;
//   final inCompleteLoader = false.obs;
//
//   List calenderPrice = <CalenderPrice>[];
//
//   TextEditingController optionalMessage = TextEditingController();
//   TextEditingController otherReason = TextEditingController();
//   final blockCalendar = false.obs;
//   final declineReason= RxnString();
//   final declineReasons = [
//     "dates_not_available","not_comfortable","not_a_good_fit" ,"waiting_for_better_reservation" ,"different_dates_than_selected" ,"spam","other"];
//   final declineError = RxnString();
//   final otherDeclineError = RxnString();
//
//   final requests = RxList<BookingModel>();
//   final history = RxList<BookingHistory>();
//   final cancelledBooking = RxList<BookingHistory>();
//   final ongoingBooking = RxList<BookingHistory>();
//   final expiredBooking = RxList<BookingHistory>();
//
//
//   int upcomingPage = 1;
//   int upcomingLastPage = 1;
//
//   int historyPage = 1;
//   int historyLastPage = 1;
//
//   int ongoingPage = 1;
//   int ongoingLastPage = 1;
//
//   int cancelledPage = 1;
//   int cancelledLastPage = 1;
//   int expiredPage = 1;
//   int expiredLastPage = 1;
//   final bookingLazyLoader = false.obs;
//
//   final bookingTab = "coming".obs;
//   int requestsPage = 1,requestsPageLimit=10,totalPages = 1;
//
//   TextEditingController ratingControllerHost = TextEditingController();
//   final  ratingImageHost = RxnString();
//   dynamic  ratingHost = 0.0.obs;
//
//   final dateFormat = DateFormat("MM-dd-yyyy");
//   final startDate = DateTime.now().obs;
//   final  endDate = DateTime.now().obs;
//   final dateError = RxnString();
//   TextEditingController price = TextEditingController(text: "5");
//   TextEditingController minStay = TextEditingController(text: "1");
//   final isAvailable = true.obs,nationalID = false.obs;
//   final List<DateTime> priceAdjustDates= [];
//
//   final selectedPolicy = "Flexible".obs;
//   final policiesShortTerm = [
//     {
//       "title": "Flexible",
//       "detail": "Full refund 1 day prior to arrival"
//     },
//     {
//       "title": "Moderate",
//       "detail": "Full refund 5 day prior to arrival"
//     },
//     {
//       "title": "Firm",
//       "detail":
//       "Full refund for cancellations up to 30 days before check-in. If booked fewer than 30 days before check-in, a full refund for cancellations made within 48 hours of booking and at least 14 days before check-in. After that, a 50% refund up to 7 days before check-in. No refund after that.",
//     },
//     {
//       "title": "Strict",
//       "detail":
//       "Full refund for cancellations made within 48 hours of booking. if the check-in date is at least 14 days away. 50% refund for cancellations made at least 7 days before check-in. No refunds for cancellations made within 7 days of check-in.",
//     },
//   ];
//   final isLoading = true.obs;
//   getData(){
//     ApiServices.getApi("v1/host-data?size=12",isAuth:true).then((response){
//       if(response.status){
//         myCompletedProperties.value = response.data["complete"]['data'].map<Property>((item) => Property.fromJson(item)).toList();
//         completeTotalPage = response.data["complete"]['total_pages'];
//         updatePromoProperties();
//         update(['calendar']);
//
//         inCompleteProperties.value = response.data["incomplete"]['data'].map<Property>((item) => Property.fromJson(item)).toList();
//         incompleteTotalPage = response.data["incomplete"]['total_pages'];
//
//         requests.value = response.data['upcoming']['data'].map<BookingModel>((item)=>BookingModel.fromJson(item)).toList();
//         upcomingLastPage = response.data['upcoming']['total_pages'];
//
//         history.value = response.data['history']['data'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)).toList();
//         historyLastPage = response.data['history']['total_pages'];
//
//         cancelledBooking.value = response.data['cancelled']['data'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)).toList();
//         cancelledLastPage = response.data['cancelled']['total_pages'];
//
//         // expiredBooking.value = response.data['expired']['data'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)).toList();
//         // expiredLastPage = response.data['expired']['total_pages'];
//
//         ongoingBooking.value = response.data['ongoing']['data'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)).toList();
//         ongoingLastPage = response.data['ongoing']['total_pages'];
//
//         promotions.value = response.data['promocode']['data'].map<PromoModel>((item) =>PromoModel.fromJson(item)).toList();
//         lastPromotionPage = response.data['promocode']['total_pages'];
//         isLoading.value = false;
//       }
//     });
//   }
//   @override
//   void onInit() {
//     if(isUser){
//       getData();
//       completeScrollController.addListener(() {
//         if(completeScrollController.position.pixels==completeScrollController.position.maxScrollExtent && completePage<completeTotalPage){
//           completedLoader.value = true;
//           completePage++;
//           getCompleteProperties();
//         }
//       });
//       inCompleteScrollController.addListener(() {
//         if(inCompleteScrollController.position.pixels==inCompleteScrollController.position.maxScrollExtent && incompletePage<incompleteTotalPage){
//           inCompleteLoader.value = true;
//           incompletePage++;
//           getIncompleteProperties(paginate:true);
//         }
//       });
//     }
//     super.onInit();
//   }
//   changeIndex(int i) {
//     index = i;
//     update();
//   }
//   getMyProperties() {
//     getCompleteProperties();
//     getIncompleteProperties();
//     }
//   getIncompleteProperties({paginate=false}){
//     if(!paginate){
//       isLoading.value = true;
//       incompletePage = 1;
//     }
//     ApiServices.getApi("v2/incomplete-properties?page=$incompletePage&size=12", isAuth: true).then((response) {
//       if (response.status) {
//         if(incompletePage>1){
//           inCompleteProperties.addAll(response.data["IncompleteProperties"].map<Property>((item)=>Property.fromJson(item)).toList());
//           inCompleteProperties.refresh();
//           }else{
//           inCompleteProperties.value = response.data["IncompleteProperties"].map<Property>((item) => Property.fromJson(item)).toList();
//         }
//         incompleteTotalPage = response.data['pagination']['total_pages'];
//       }
//       if(paginate){
//         inCompleteLoader.value = false;
//       }
//       isLoading.value = false;
//     });
//   }
//   getCompleteProperties({refresh=false}){
//     int size = 12,page = completePage;
//     if(refresh){
//       size = completePage * 12;
//       page = 1;
//     }
//     ApiServices.getApi("v2/complete-properties?page=$page&size=$size", isAuth:true).then((response) {
//       if (response.status) {
//         if(completePage>1 && !refresh){
//           myCompletedProperties.addAll(response.data["CompleteProperties"].map<Property>((item)=>Property.fromJson(item)));
//           myCompletedProperties.refresh();
//           completedLoader.value = false;
//           update(['calendar']);
//         }else{
//           myCompletedProperties.value = response.data["CompleteProperties"].map<Property>((item) => Property.fromJson(item)).toList();
//           isLoading.value = false;
//         }
//         completeTotalPage = response.data['pagination']['total_pages'];
//         updatePromoProperties();
//       }
//     });
//   }
//   updatePromoProperties(){
//     promoProperties.value = myCompletedProperties.where((item)=>item.visibility==1 && item.status=="Listed").toList();
//     if(promoProperties.isNotEmpty){
//       promoProperties.insert(0, Property(id: 0,name: "Add All"));
//     }
//   }
//   getCalenderPrice(id){
//     try{
//       ApiServices.getApi("v2/bookingcalender/$id", isAuth: true).then((response) {
//         print(response.status);
//         if (response.status) {
//           calenderPrice = response.data.map<CalenderPrice>((item)=>CalenderPrice.fromJson(item)).toList();
//         }
//         isLoading.value = false;
//         update(['calendar']);
//       });
//     }catch(e){
//       isLoading.value = false;
//       update(['calendar']);
//     }
//   }
//   syncCalendar(id)async{
//     isLoading.value = true;
//     final response = await ApiServices.getApi("v1/icalendar/synchronization/$id",isAuth:true,showDialog: false);
//     if(response.status){
//       calenderPrice = response.data.map<CalenderPrice>((item)=>CalenderPrice.fromJson(item)).toList();
//       update(['calendar']);
//     }
//     isLoading.value = false;
//   }
//   pickCustomColor(){
//     Get.dialog(AlertDialog(
//         title: const Text('Pick a color!'),
//         content: SingleChildScrollView(
//           child: ColorPicker(pickerColor:pickerCustomColor.value, onColorChanged: (value) {
//             pickerCustomColor.value = value;
//
//     })),
//     actions: [
//       ElevatedButton(
//         child: const Text('Got it'),
//         onPressed: () {
//           calenderCustomColor.value = pickerCustomColor.value;
//           Get.back();
//         },
//       )
//     ],
//     ));
//   }
//   importCalendar(id)async{
//     if(Get.context!=null){
//       GlobalHelper.removeFocus();
//     }
//     if(calenderType.value==null){
//       calenderError.value = "Please Select Your Color";
//     }
//     if(calenderType.value=="custom" && calenderCustomColor.value==null){
//       calendarColorError.value = true;
//     }
//     if((reportFormKey.currentState?.validate()??false) && calenderError.value==null && calendarColorError.isFalse){
//       isLoading.value = true;
//       Map formData = {
//         "name" : calenderName.text,
//         "url":calenderUrl.text,
//         "color" : calenderType.value,
//         "customcolor" :calenderType.value!="custom"?"none":"#${(colorToHex(calenderCustomColor.value!))}",
//         "property_id" : id
//       };
//       final response = await ApiServices.postApi("v1/icalender-import",body: formData,isAuth: true);
//       if(response.status){
//         Get.back();
//         Get.dialog(WarningDialog(title: "success".tr,keyword: DialogKeyword.success,description: "Import Calender successfully!"));
//         clearCalenderImport();
//         getCalenderPrice(id);
//
//       }
//       isLoading.value = false;
//     }
//   }
//   toggleProperty(index) async {
//     myCompletedProperties[index].visibility = myCompletedProperties[index].visibility == 1 ? 0 : 1;
//     myCompletedProperties.refresh();
//     Map formData = {
//       "propertyid": myCompletedProperties[index].id,
//       "checkbox_value": myCompletedProperties[index].visibility == 1,
//     };
//     ApiServices()
//         .postApi("v1/check_list", body: formData, isAuth: true)
//         .then((response) {
//       if (response.status) {
//         updatePromoProperties();
//       }
//     });
//   }
//   deletePromo(index){
//   Get.dialog(WarningDialog(title: 'warning'.tr, description: "Are you sure you want to delete this Promotion?",onConfirmed: (){
//     ApiServices.getApi("v1/promocode/delete/${promotions[index].id}" , isAuth: true).then((response){
//       if(response.status){
//         promotions.removeAt(index);
//         promotions.refresh();
//         Get.back();
//       }
//     });
//   }));
// }
//   getPromoCode({refresh = false}) async {
//     if(refresh){
//       promotionPage = 1;
//     }
//     ResponseModel responseModel = await ApiServices.getApi("v1/get/promocode?page=$promotionPage&size=12" , isAuth: true);
//     if(responseModel.status){
//       if (promotionPage > 1) {
//         promotions.addAll(responseModel.data['collection'].map<PromoModel>((item) => PromoModel.fromJson(item)));
//         promotionLazyLoader.value = false;
//         promotions.refresh();
//       } else {
//         promotions.value = responseModel.data['collection'].map<PromoModel>((item) =>PromoModel.fromJson(item)).toList();
//       }
//       lastPromotionPage = responseModel.data['pagination']['total_pages'];
//
//     }
//   }
//
//   completeListing(index,{isCompleted=false}){
//     DashboardController dashC = Get.put(DashboardController());
//     Get.delete<PropertyController>();
//     PropertyController properC = Get.put(PropertyController(isEdit : isCompleted));
//     if(isCompleted){
//       properC.listPropertyId = myCompletedProperties[index].id;
//       properC.spaceType.value =  myCompletedProperties[index].spaceType;
//       properC.adultCount.value = myCompletedProperties[index].adults??0;
//       properC.childrenCount.value = myCompletedProperties[index].children??0;
//       properC.bedroomsCount.value = myCompletedProperties[index].bedrooms!;
//       properC.bathroomsCount.value = myCompletedProperties[index].bathrooms!;
//       properC.noOfApartments.value = myCompletedProperties[index].noOfApartments!;
//       properC.characterCount.value = myCompletedProperties[index].name!.length;
//       properC.name.text = myCompletedProperties[index].name!;
//       properC.summary.text = myCompletedProperties[index].summary!;
//       properC.summaryAr.text = myCompletedProperties[index].summaryAr!;
//       properC.desCharCount.value = myCompletedProperties[index].summary!.length;
//       properC.desCharCountArabic.value = myCompletedProperties[index].summaryAr!.length;
//       for(String key in myCompletedProperties[index].specialDaysPrice!.keys){
//         ViewsCommon.priceControllers[key]!.text = myCompletedProperties[index].specialDaysPrice![key].toString();
//       }
//       ViewsCommon.priceControllers['price']!.text = myCompletedProperties[index].price.toString();
//       properC.maxNights.text = myCompletedProperties[index].maxNights.toString();
//       properC.minNights.text = myCompletedProperties[index].minNights.toString();
//       // properC.selectHosingType(myCompletedProperties[index].bookingType);
//       properC.propertyImages.value = myCompletedProperties[index].propertyPhotos??[];
//
//       properC.coverImage.value = myCompletedProperties[index].propertyPhotos?.firstWhereOrNull((item) => item.coverPhoto==1);
//       if(properC.coverImage.value==null && properC.propertyImages.isNotEmpty){
//         properC.coverImage.value = properC.propertyImages[0];
//       }
//
//       properC.singleBedCount.value = myCompletedProperties[index].singleBeds??0;
//       properC.doubleBedCount.value = myCompletedProperties[index].doubleBeds??0;
//       if(myCompletedProperties[index].checkInTime!=null && myCompletedProperties[index].checkInTime!.isNotEmpty){
//         List checkIn = myCompletedProperties[index].checkInTime!.split(":");
//         properC.checkIn.value = TimeOfDay(hour:int.tryParse(checkIn[0])??0,minute: int.tryParse(checkIn[1])??0);
//       }
//       if(myCompletedProperties[index].checkOutTime!=null && myCompletedProperties[index].checkOutTime!.isNotEmpty){
//         List checkOut = myCompletedProperties[index].checkOutTime!.split(":");
//         properC.checkOut.value = TimeOfDay(hour:int.tryParse(checkOut[0])??0,minute:int.tryParse(checkOut[1])??0);
//       }
//       properC.code.text =myCompletedProperties[index].postalCode??"";
//       properC.district.text =myCompletedProperties[index].district??"";
//       properC.onCameraChanged(LatLng(double.parse(myCompletedProperties[index].latitude!),double.parse(myCompletedProperties[index].longitude!)),
//           location: myCompletedProperties[index].addressLine);
//       for (var amenity in dashC.filters.value!.amenities) {
//         amenity.isChecked = myCompletedProperties[index].amenities!.contains("${amenity.id}");
//       }
//       for (var safetyAmenity in dashC.filters.value!.safetyAmenities) {
//         safetyAmenity.isChecked = myCompletedProperties[index].amenities!.contains("${safetyAmenity.id}");
//       }
//       for (var houseRule in dashC.filters.value!.houseRules) {
//         houseRule.isChecked = myCompletedProperties[index].amenities!.contains("${houseRule.id}");
//       }
//           Get.to(()=>const EditProperty())!.then((value){
//             getCompleteProperties(refresh: true);
//             Get.delete<PropertyController>();
//           });
//           return;
//     }else{
//       properC.getCurrentLocation(isFirst: true);
//       properC.listPropertyId = inCompleteProperties[index].id;
//       properC.name.text = inCompleteProperties[index].name??"";
//       if(inCompleteProperties[index].propertyPhotos?.isNotEmpty??false){
//         properC.coverImage.value = inCompleteProperties[index].propertyPhotos?.firstWhereOrNull((item) => item.coverPhoto==1);
//         properC.coverImage.value ??= inCompleteProperties[index].propertyPhotos![0];
//       }
//       if(inCompleteProperties[index].price!=0){
//         ViewsCommon.priceControllers['price']!.text = inCompleteProperties[index].price.toString();
//       }
//     }
//     switch(inCompleteProperties[index].missedStep){
//       case "spacetype":{//spaceType
//         Get.to(()=>const AddProperty2())!.then((value){
//           getIncompleteProperties();
//         });
//         return;
//       }
//       case "location":
//       case "confirmLocation":{//location
//         Get.to(()=>const AddProperty3())!.then((value){
//           getIncompleteProperties();
//         });
//         return;
//       }
//       case "numberofRoom":{
//         Get.to(()=>const AddProperty4())!.then((value){
//           getIncompleteProperties();
//         });
//         return;
//       }
//       case "amenities":{
//         Get.to(()=>const AddProperty5())!.then((value){
//           getIncompleteProperties();
//         });
//         return;
//       }
//       case "photos":
//       case "setCover":{
//         Get.to(()=>const AddProperty6())!.then((value){
//           getIncompleteProperties();
//         });
//         return;
//       }
//       case "title":{
//         Get.to(()=>const AddProperty7())!.then((value){
//           getIncompleteProperties();
//         });
//         return;
//       }
//       case "description":{
//         Get.to(()=>const AddProperty8())!.then((value){
//           getIncompleteProperties();
//         });
//         return;
//       }
//       case "basics":
//       case "basic":{
//         Get.to(()=>const AddProperty9())!.then((value){
//           getIncompleteProperties();
//         });
//         return;
//       }
//       case "pricing":
//       case "price":{
//         Get.to(()=>const AddProperty10())!.then((value){
//           getIncompleteProperties();
//         });
//         return;
//       }
//       case "nightsandtime":{
//         Get.to(()=>const AddProperty11())!.then((value){
//           getIncompleteProperties();
//         });
//         return;
//       }
//       case "booking":
//       case "question":{
//         Get.to(()=>const AddProperty12())!.then((value){
//           getMyProperties();
//         });
//         return;
//       }
//       case "reviewListing":{
//         Get.to(()=>const PublishListing())!.then((value){
//           getMyProperties();
//         });
//         return;
//       }}
//   }
//
//   setBookingTab(val) {
//     if (bookingTab.value != val) {
//       bookingTab.value = val;
//     }
//   }
//   getMyBookings({refresh=false}){
//     if(refresh){
//       upcomingPage = 1;
//     }
//     ApiServices.getApi("v2/my-bookings?page=$upcomingPage&size=12",isAuth:true).then((response){
//      if (response.status) {
//       if(!refresh && upcomingPage>1){
//         requests.addAll(response.data['bookings'].map<BookingModel>((item)=>BookingModel.fromJson(item)));
//         requests.refresh();
//         bookingLazyLoader.value = false;
//       }else{
//         requests.value = response.data['bookings'].map<BookingModel>((item)=>BookingModel.fromJson(item)).toList();
//       }
//       upcomingLastPage = response.data['pagination']['total_pages'];
//     }
//     });
//   }
//   getBookingsHistory({refresh=false}){
//     if(refresh){
//       historyPage = 1;
//     }
//     ApiServices.getApi("v1/booking_history?page=$historyPage&size=12",isAuth:true).then((response){
//       if (response.status) {
//       if(!refresh && historyPage>1){
//         history.addAll(response.data['BookingsHistory'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)));
//         history.refresh();
//         bookingLazyLoader.value = false;
//       }else{
//         history.value = response.data['BookingsHistory'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)).toList();
//       }
//       historyLastPage = response.data['pagination']['total_pages'];
//     }
//     });
//   }
//   getCancelledHostBooking({refresh=false}){
//     if(refresh){
//       cancelledPage = 1;
//     }
//     ApiServices.getApi("v1/host/cancelled/reservation?page=$cancelledPage&size=12",isAuth:true).then((response){
//      if (response.status) {
//       if(!refresh && cancelledPage>1){
//         cancelledBooking.addAll(response.data['BookingsHistory'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)));
//         cancelledBooking.refresh();
//         bookingLazyLoader.value = false;
//       }else{
//         cancelledBooking.value = response.data['BookingsHistory'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)).toList();
//       }
//       cancelledLastPage = response.data['pagination']['total_pages'];
//     }
//
//
//     });
//     }
//
//     getExpiredHostBooking({refresh=false}){
//     if(refresh){
//       expiredPage = 1;
//     }
//     ApiServices.getApi("v1/host/expired/reservation?page=$expiredPage&size=12",isAuth:true).then((response){
//      if (response.status) {
//       if(!refresh && expiredPage>1){
//         expiredBooking.addAll(response.data['BookingsHistory'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)));
//         expiredBooking.refresh();
//         bookingLazyLoader.value = false;
//       }else{
//         expiredBooking.value = response.data['BookingsHistory'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)).toList();
//       }
//       expiredLastPage = response.data['pagination']['total_pages'];
//     }
//
//
//     });
//     }
//     getOngoingBooking({refresh=false}){
//     if(refresh){
//       ongoingPage = 1;
//     }
//     ApiServices.getApi("v1/host/current/reservation?page=$ongoingPage&size=12",isAuth:true).then((response){
//      if (response.status) {
//       if(!refresh && ongoingPage>1){
//         ongoingBooking.addAll(response.data['BookingsHistory'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)));
//         ongoingBooking.refresh();
//         bookingLazyLoader.value = false;
//       }else{
//         ongoingBooking.value = response.data['BookingsHistory'].map<BookingHistory>((item)=>BookingHistory.fromJson(item)).toList();
//       }
//       ongoingLastPage = response.data['pagination']['total_pages'];
//     }
//     });
//     }
//   acceptBooking(index){
//     isLoading.value =false;
//     Get.dialog(
//      Dialog(
//        insetPadding: EdgeInsets.symmetric(horizontal: widthSpace(10)),
//        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(23)),
//        child: Padding(
//          padding: EdgeInsets.symmetric(horizontal: widthSpace(9), vertical: widthSpace(viewPadding)),
//          child: Column(mainAxisSize: MainAxisSize.min, children: [
//            const CustomTextField(hint:"Write a message to Guest (Optional)"),
//            SizedBox(height: heightSpace(2)),
//            SizedBox(
//                width: double.maxFinite,
//                child: Obx(()=>CommonButton(title: "Confirm",isLoading: isLoading.isTrue,onPressed: (){
//                    isLoading.value =true;
//                    ApiServices.postApi("v1/booking/accept/${requests[index].id}",body: {"message":optionalMessage.text},isAuth: true).then((response){
//                      isLoading.value =false;
//                      if(response.status){
//                        requests[index].status ="Processing";
//                        requests.refresh();
//                        Get.back();
//                      }
//                    });
//                  }),
//                ))
//          ]),
//        ),
//      ),
//     ).then((value){
//       optionalMessage.clear();
//     });
//   }
//   submitRatingHost(reviewid){
//     if(ratingHost>0 && ratingControllerHost.text.isNotEmpty){
//       Map<String, String> formData = { 'rating' : ratingHost.toString(), 'message' : ratingControllerHost.text , 'reviewid' : reviewid.toString() };
//       ApiServices().imageUpload("v1/hostrating",imagePaths: ratingImageHost.value!=null?[ratingImageHost.value!]:null , body: formData ).then((value) {
//         Get.back();
//         ratingControllerHost.clear();
//         ratingImageHost.value = null;
//         ratingHost = 0.0;
//         Get.dialog(WarningDialog(title: "success".tr,keyword: DialogKeyword.success,description: "Your Rating Is Done"));
//       });
//     }else{
//       Get.dialog(WarningDialog(
//           title: "warning".tr,
//           description: ratingHost==0?"Please rate this listing":"Write a message also"));
//     }
//   }
//   pickImage() async {
//     try {
//       var image = await ImagePicker().pickImage(source: ImageSource.gallery,requestFullMetadata: false);
//       if (image != null) {
//         ratingImageHost.value =  image.path;
//       }
//     } catch (e) {
//       debugPrint(e.toString());
//     }
//    }
//   declineBooking(index){
//     DateTime start = DateTime.parse(requests[index].startDate??"");
//     DateTime end = DateTime.parse(requests[index].endDate??"");
//     optionalMessage.clear();
//     Get.dialog(
//       Dialog(
//         insetPadding: EdgeInsets.symmetric(horizontal: widthSpace(5)),
//         shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(23)),
//         child: SingleChildScrollView(
//           padding: EdgeInsets.symmetric(horizontal: widthSpace(9), vertical: widthSpace(viewPadding)),
//           child: Obx(()=>Column(mainAxisSize: MainAxisSize.min,crossAxisAlignment: CrossAxisAlignment.start, children: [
//               const CustomText("Help us improve your experience.Please write down the main reason for\nCancelling this Booking",
//               lineSpacing: 1.5,
//               color: Color(greyText)),
//               SizedBox(height: heightSpace(2.5)),
//               const CustomText("Your response will not be shared with the host",size: 2.1,color: Color(greyText),weight: FontWeight.bold),
//               SizedBox(height: heightSpace(3.5)),
//               Container(
//                 padding:const EdgeInsets.symmetric(horizontal: 25),
//                 decoration: BoxDecoration(
//                     borderRadius: BorderRadius.circular(50),
//                     border: Border.all(color: const Color(greyBorder))),
//                 child: DropdownButtonHideUnderline(
//                   child: DropdownButton<String>(
//                     isExpanded: true,
//                     value: declineReason.value,
//                     icon: Image.asset(
//                         "assets/icons/arrow_down.png"),
//                     elevation: 16,
//                     hint: CustomText(declineReason.value?.tr?? "Why are you declining"),
//                     style: const TextStyle(fontSize: 16, color: Colors.black),
//                     onChanged: (String? newValue) {
//                       declineReason.value = newValue;
//                       declineError.value=null;
//                       otherDeclineError.value=null;
//                     },
//                     items: declineReasons.map<DropdownMenuItem<String>>((String value) {
//                           return DropdownMenuItem<String>(
//                             value: value,
//                             child: CustomText(value.tr,size: 1.8),
//                           );
//                         }).toList(),
//                   ),
//                 ),
//               ),
//               if(declineError.value!=null)Padding(
//                 padding: const EdgeInsets.only(left:18.0,top: 5),
//                 child: CustomText("Please select decline reason",size: 1.55,color: Colors.red[600]),
//               ),
//               if(declineReason.value=="other")...[
//               SizedBox(height: heightSpace(3.5)),
//               const CustomText("Why are you declining?",weight: FontWeight.w500),
//               SizedBox(height: heightSpace(1.5)),
//               CustomTextField(controller:otherReason,hint:"Other reason",errorText: otherDeclineError.value,isRoundedBorder: true),
//               ],
//
//               SizedBox(height: heightSpace(3.5)),
//               const CustomText("Write optional message to guest",weight: FontWeight.w500),
//               SizedBox(height: heightSpace(1.5)),
//             CustomTextField(controller: optionalMessage,hint:"message",isRoundedBorder: true),
//               SizedBox(height: heightSpace(3.5)),
//               CommonCheckBox(
//                   isSelected: blockCalendar.isTrue,
//                   onPressed: (){
//                   blockCalendar.value = !blockCalendar.value;
//                 }, title: "Block my calendar from ${months[start.month-1]} ${start.year} to ${months[end.month-1]} ${end.year}",isExpanded:true),
//               SizedBox(height: heightSpace(4.5)),
//
//         Row(
//             mainAxisAlignment: MainAxisAlignment.end,
//             children: [
//               CommonButton(
//                               fontSize: 1.8,
//                               title: "Close".tr,onPressed: ()=>Get.back(), backgroundBg: Color(warningColor), ),
//              // CommonButton(title: "Close",isBorder: true, buttonThemeColor:const Color(warningColor),onPressed: ()=>Get.back()),
//               SizedBox(width: widthSpace(2)),
//               CommonButton(onPressed:isLoading.isTrue?null: (){
//                 if(declineReason.value==null){
//                   declineError.value = "";
//                   return;
//                 }else{
//                   if(declineReason.value=="other"){
//                     if(otherReason.text.isEmpty){
//                       otherDeclineError.value = 'emptyError'.tr;
//                       return;
//                     }else{
//                       otherDeclineError.value = null;
//                     }
//                   }
//                 }
//                 Map formData = {
//                   "decline_reason" : declineReason.value,
//                   "decline_reason_other" : otherReason.text,
//                   "block_calendar" : blockCalendar.isTrue?"yes":"no",
//                   "message" : optionalMessage.text
//                 };
//                 isLoading.value =true;
//                 ApiServices.postApi("v1/booking/decline/${requests[index].id}",body:formData,isAuth: true).then((response){
//                   isLoading.value =true;
//                   if(response.status){
//                     requests.removeAt(index);
//                     getCancelledHostBooking();
//                     isLoading.value =false;
//                     Get.back();
//                   }
//                 });
//               }, title: "Decline",isLoading: isLoading.isTrue,
//
//                   ),
//             ],
//         )]),
//           )))).then((value){
//             declineReason.value= null;
//             declineError.value = null;
//             otherReason.clear();
//             optionalMessage.clear();
//             blockCalendar.value =false;
//     });
//   }
//   plusOnEdit(TextEditingController c){
//     c.text = "${int.parse(c.text)+1}";
//   }
//   minusOnEdit(TextEditingController c,{minAmount = 5}){
//     int num = int.parse(c.text);
//     if(num>minAmount){
//       c.text = "${num-1}";
//     }
//   }
//   submitEditProperty(index)async{
//     Map formData = {
//       "start_date"   : dateFormat.format(startDate.value),
//       "end_date" : dateFormat.format(endDate.value),
//       "status" : isAvailable.value?"Available":"Not Available",
//       "property_id" : myCompletedProperties[index].id,
//       "price" : price.text,
//       "min_stay" : minStay.text
//     };
//     print(formData);
//     if(double.parse(price.text) < 5) {
//       Get.dialog(WarningDialog(
//           title: "error".tr,
//           description: "Price cannot be less than 5"));
//     } else {
//       isLoading.value = true;
//       ResponseModel response = await ApiServices.postApi("v1/setcalendarprice",body: formData,isAuth:true);
//       if(response.status){
//        getCalenderPrice(myCompletedProperties[index].id);
//      }
//       Get.back();
//       isLoading.value = false;
//     }
//   }
//   setCancelPolicy(value) {
//     selectedPolicy.value = value;
//     ApiServices.postApi("v1/store-cancellationpolicy",body: {"cancel_policy":value},isAuth:true);
//   }
//   toggleLegalNic() {
//     nationalID.value = !nationalID.value;
//     ApiServices.postApi("v1/store-guestrequirement",body: {"guest_req":nationalID.value?"on":null},isAuth:true);
//   }
//   generateCode(){
//     ApiServices.getApi("v1/promo/generate").then((response){
//       if(response.status){
//         promoCode.text = response.data;
//       }
//     });
//   }
//   createPromo(){
//     final selectedProperties =myCompletedProperties.where((item) => item.isChecked==true);
//     if(selectedProps.value==null || selectedProperties.isEmpty){
//       selectPropsError.value = "Select properties to offer discount";
//     }
//     if(promoFormKey.currentState!.validate() && selectPropsError.value==null){
//       isLoading.value = true;
//       Map formData = {
//         "title" : promoTitle.text,
//         "promo_code":promoCode.text,
//         "description" : promoDescription.text,
//         "percentage" : promoDiscount.text,
//         "discount_upto":promoUpto.text,
//         "max_usage":promoMaxUsage.text,
//         "per_user_usage":promoUserUsage.text,
//         "for_new_customer":forNewUser.value,
//         "startdate":formDateFormat.format(promoStart.value),
//         "enddate":formDateFormat.format(promoEnd.value),
//         "property_id" : []
//       };
//       for(final item in selectedProperties){
//         formData['property_id'].add(item.id);
//       }
//       ApiServices.postApi("v1/promocode/${promoId!=null?"update/$promoId":"insert"}",body: formData,isAuth: true).then((response){
//         if(response.status){
//           getPromoCode(refresh: true);
//           Get.dialog(WarningDialog(title: "success".tr,keyword: DialogKeyword.success,description: "Promo code has been ${promoId!=null?"updated":"created"} successfully!",confirmText: 'okay'.tr,onConfirmed: () {
//             Get.close(2);
//             Get.to(()=>const Promotions());
//           }));
//           clearPromoFields();
//         }
//         isLoading.value = false;
//       });
//     }
//   }
//
//   promoOptionSelected(String value,index){
//     if(value=="edit"){
//       promoId = promotions[index].id;
//       promoCode.text = promotions[index].code??"";
//       promoTitle.text = promotions[index].title??"";
//       promoDescription.text = promotions[index].description??"";
//       promoDiscount.text = "${promotions[index].percentage??" "}";
//       promoUpto.text = "${promotions[index].discountUpto??""}";
//       promoMaxUsage.text = "${promotions[index].maxUsage??""}";
//       promoUserUsage.text = "${promotions[index].perUserUsage??""}";
//       forNewUser.value = promotions[index].forNewCustomer==1;
//       if(promotions[index].startDate!=null){
//         promoStart.value = DateTime.parse(promotions[index].startDate!);
//       }
//       if(promotions[index].expiryDate!=null){
//         promoEnd.value = DateTime.parse(promotions[index].expiryDate!);
//       }
//       for(var item in promoProperties){
//         item.isChecked = promotions[index].codeProperties?.firstWhereOrNull((e) => e.property==item.id)!=null;
//       }
//       final temp = promoProperties.where((item) => item.isChecked && item.id!=0).map((filter) => filter.name);
//       selectedProps.value = temp.join(", ");
//       Get.to(()=>const PromoCode());
//     }else if(value == "delete"){
//       deletePromo(index);
//     }
//   }
//   clearCalenderImport(){
//     calenderName.clear();
//     calenderUrl.clear();
//     calenderType.value = null;
//     calenderCustomColor.value = null;
//     calendarColorError.value = false;
//     calenderError.value = null;
//    }
//
//   clearPromoFields(){
//     promoId = null;
//     promoTitle.clear();
//     promoDescription.clear();
//     promoDiscount.clear();
//     promoCode.clear();
//     promoUpto.clear();
//     promoMaxUsage.clear();
//     promoUserUsage.clear();
//     forNewUser.value = false;
//     promoStart.value = DateTime.now();
//     promoEnd.value = DateTime.now().add(const Duration(days: 1));
//     propertySearchKey.value = "";
//     for(var item in promoProperties){
//       item.isChecked = false;
//     }
//     selectedProps.value = null;
//
//   }
//   toggleCheckAll(){
//     for(var item in promoProperties){
//       item.isChecked = promoProperties[0].isChecked;
//     }
//     promoProperties.refresh();
//
//   }
//   @override
//   void onDetached() {
//   }
//   @override
//   void onInactive() {
//   }
//   @override
//   void onPaused() {
//   }
//
//   @override
//   void onResumed() {
//     if(isUser){
//       if(index==2){
//         if(bookingTab.value=="coming"){
//           getMyBookings(refresh: true);
//         }else if(bookingTab.value=="history"){
//           getBookingsHistory(refresh: true);
//         }else{
//           getCancelledHostBooking(refresh: true);
//         }
//       }
//     }
//   }
// }