import 'package:darent/models/ticketDetailModel.dart';
import 'package:darent/utils/api_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

class TicketDetailController extends GetxController{
  final String ticketNumber;
  TicketDetailController(this.ticketNumber);
  final data = Rxn<TicketDetailModel>();
  TextEditingController comment = TextEditingController();
  final isLoading = true.obs;
  @override
  void onInit() {
    getData();
    super.onInit();
  }
  getData()async{
    final response = await ApiServices.getApi("v1/ticket/details/$ticketNumber",isAuth:true);
    print(response.data);
    if(response.status){
      data.value = TicketDetailModel.fromJson(response.data[0]);
    }
    isLoading.value = false;
  }
  submitComment()async{
    if(comment.text.isEmpty){
      if(!Get.isSnackbarOpen){
        Get.snackbar('warning'.tr, "Please enter some text!");
      }
      return;
    }
    isLoading.value = true;
    Map formData = {
      "description": comment.text,
      "ticket_number":ticketNumber
    };

    FocusScope.of(Get.context!).requestFocus(FocusNode());
    final response = await ApiServices.postApi("v1/ticket/details/$ticketNumber",body: formData,isAuth: true);
    if(response.status){
      data.value!.comments!.add(Comments.fromJson(response.data));
      comment.clear();
    }
    isLoading.value = false;
  }
}