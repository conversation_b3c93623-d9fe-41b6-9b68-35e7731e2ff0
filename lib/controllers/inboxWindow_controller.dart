// import 'package:darent/models/chatWindowModel.dart';
// import 'package:darent/utils/api_service.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';
//
// import '../helperMethods/search_helper.dart';
//
// class InboxWindowController extends SuperController {
//   final int hostId,propertyId;
//   Property? property;
//   InboxWindowController({required this.hostId,required this.propertyId});
//
//   TextEditingController msgC = TextEditingController();
//   RxList<ChatWindow> data = RxList<ChatWindow>();
//   late Receiver otherPerson;
//   DateFormat formatter = DateFormat.yMEd().add_jms();
//   final isLoading = true.obs;
//
//   ScrollController scrollController   = ScrollController();
//   @override
//   void onInit() {
//     getData();
//     super.onInit();
//   }
//   sendMessage()async{
//     if(msgC.text.isNotEmpty){
//       isLoading.value = true;
//       ResponseModel response = await ApiServices.postApi("v1/messaging/reply",body:{
//         "property_id" : propertyId,
//         "receiver_id" : hostId,
//         "msg" :msgC.text,
//       },isAuth: true);
//       if(response.status){
//         getData();
//         msgC.clear();
//         // SearchHelper.c.getInbox();
//       }
//     }
//   }
//
//   @override
//   void onResumed()async {
//     getData();
//   }
//   getData()async{
//     ResponseModel response = await ApiServices.postApi("v2/window/$hostId?propertyid=$propertyId",isAuth: true);
//     if(response.status){
//       data.value = response.data['messages'].map<ChatWindow>((item)=>ChatWindow.fromJson(item)).toList();
//       property = Property.fromJson(response.data['property']);
//       otherPerson = Receiver.fromJson(response.data['receiver']);
//       isLoading.value = false;
//       WidgetsBinding.instance
//           .addPostFrameCallback((_){
//             if(scrollController.hasClients){
//               scrollController.jumpTo(scrollController.position.maxScrollExtent+500);
//             }
//       });
//     }
//   }
// }