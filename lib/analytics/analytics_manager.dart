import 'package:flutter/foundation.dart';
import 'analytics_provider.dart';

/// A manager class that handles multiple analytics providers
class AnalyticsManager {
  static final AnalyticsManager _instance = AnalyticsManager._internal();

  /// Factory constructor to return the singleton instance
  factory AnalyticsManager() => _instance;

  /// Private constructor for singleton pattern
  AnalyticsManager._internal();

  /// List of registered analytics providers
  final List<AnalyticsProvider> _providers = [];

  /// Register a provider with the manager
  void registerProvider(AnalyticsProvider provider) {
    if (!_providers.any((p) => p.providerName == provider.providerName)) {
      _providers.add(provider);
      if (kDebugMode) {
        print('Registered analytics provider: ${provider.providerName}');
      }
    }
  }

  /// Initialize all registered providers
  Future<void> initializeProviders() async {
    try {
         for (var provider in _providers) {
      if (provider.isEnabled) {
        await provider.initialize();
      }
    }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing providers: $e');
      }
      
    }
 
  }

  /// Identify a user with all providers
  Future<void> identifyUser(String userId) async {
    try {
      for (var provider in _providers) {
        if (provider.isEnabled) {
          await provider.identifyUser(userId);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error identifying user: $e');
      }
    }
  }

  /// Set user attributes with all providers
  Future<void> setUserAttributes({
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? gender,
    DateTime? birthday,
    Map<String, dynamic>? customAttributes,
  }) async {
    try {
      for (var provider in _providers) {
        if (provider.isEnabled) {
          await provider.setUserAttributes(
            firstName: firstName,
            lastName: lastName,
            email: email,
            phoneNumber: phoneNumber,
            gender: gender,
            birthday: birthday,
            customAttributes: customAttributes,
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error setting user attributes: $e');
      }
    }
  }

  /// Track an event with all providers
  Future<void> trackEvent(String eventName,
      {Map<String, dynamic>? eventAttributes}) async {
    try {
      for (var provider in _providers) {
        if (provider.isEnabled) {
          await provider.trackEvent(eventName,
              eventAttributes: eventAttributes);
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error tracking event: $e');
      }
    }
  }

  /// Reset user with all providers
  Future<void> resetUser() async {
    try {
      for (var provider in _providers) {
        if (provider.isEnabled) {
          await provider.resetUser();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error resetting user: $e');
      }
    }
  }

  /// Get a specific provider by name
  AnalyticsProvider? getProvider(String providerName) {
    try {
      return _providers.firstWhere(
        (provider) => provider.providerName == providerName,
      );
    } catch (e) {
      return null;
    }
  }

  /// Enable or disable a specific provider
  void setProviderEnabled(String providerName, bool enabled) {
    final provider = getProvider(providerName);
    if (provider != null) {
      provider.isEnabled = enabled;
    }
  }

  /// Get all registered providers
  List<AnalyticsProvider> get providers => List.unmodifiable(_providers);
}
