import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:moengage_flutter/moengage_flutter.dart';
import '../analytics_provider.dart';

/// MoEngage implementation of the AnalyticsProvider interface
class MoEngageProvider implements AnalyticsProvider {
  static final MoEngageProvider _instance = MoEngageProvider._internal();

  /// Factory constructor to return the singleton instance
  factory MoEngageProvider() => _instance;

  // MoEngage Workspace ID
  static const String _workspaceId = 'O1ZAC6O2X0IHDHVKG0JR1KEC';

  // MoEngage plugin instance
  late MoEngageFlutter _moengagePlugin;

  bool _isEnabled = true;

  /// Private constructor for singleton pattern
  MoEngageProvider._internal() {
    _moengagePlugin = MoEngageFlutter(_workspaceId);
  }

  @override
  Future<void> initialize() async {
    try {
      // Initialize MoEngage with the appropriate configuration
      _moengagePlugin.initialise();
      logDebug('SDK initialized successfully');

      try {
        _moengagePlugin
            .registerForProvisionalPush(); // for MoEngage Push Registration
      } catch (e) {
        logDebug('Error registerForProvisionalPush: $e');
      }

      // Enable push notifications
      if (Platform.isAndroid || Platform.isIOS) {
        _moengagePlugin.registerForPushNotification();
      }
    } catch (e) {
      logDebug('Error initializing SDK: $e');
    }
  }

  @override
  Future<void> identifyUser(String userId) async {
    try {
      _moengagePlugin.identifyUser(userId);
      logDebug('User identified: $userId');
    } catch (e) {
      logDebug('Error identifying user: $e');
    }
  }

  @override
  Future<void> setUserAttributes({
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? gender,
    DateTime? birthday,
    Map<String, dynamic>? customAttributes,
  }) async {
    try {
      if (firstName != null) {
        _moengagePlugin.setFirstName(firstName);
      }

      if (lastName != null) {
        _moengagePlugin.setLastName(lastName);
      }

      if (email != null) {
        _moengagePlugin.setEmail(email);
      }

      if (phoneNumber != null) {
        _moengagePlugin.setPhoneNumber(phoneNumber);
      }

      if (gender != null) {
        _moengagePlugin.setUserAttribute("gender", gender);
      }

      if (birthday != null) {
        String isoDate = birthday.toIso8601String();
        _moengagePlugin.setBirthDate(isoDate);
      }

      if (customAttributes != null && customAttributes.isNotEmpty) {
        for (var entry in customAttributes.entries) {
          _moengagePlugin.setUserAttribute(entry.key, entry.value);
        }
      }

      logDebug('User attributes set successfully');
    } catch (e) {
      logDebug('Error setting user attributes: $e');
    }
  }

  @override
  Future<void> trackEvent(String eventName,
      {Map<String, dynamic>? eventAttributes}) async {
    try {
      MoEProperties? properties;

      if (eventAttributes != null && eventAttributes.isNotEmpty) {
        properties = MoEProperties();

        for (var entry in eventAttributes.entries) {
          final key = entry.key;
          final value = entry.value;

          if (value is String) {
            properties.addAttribute(key, value);
          } else if (value is int) {
            properties.addAttribute(key, value);
          } else if (value is double) {
            properties.addAttribute(key, value);
          } else if (value is bool) {
            properties.addAttribute(key, value);
          } else if (value is DateTime) {
            properties.addAttribute(key, value.toIso8601String());
          } else if (value is List) {
            properties.addAttribute(key, value);
          } else if (value is Map) {
            properties.addAttribute(key, value);
          } else {
            // For any other type, convert to string
            properties.addAttribute(key, value.toString());
          }
        }
      }

      _moengagePlugin.trackEvent(eventName, properties);

      logDebug('Event tracked: $eventName with attributes: $eventAttributes');
    } catch (e) {
      logDebug('Error tracking event: $e');
    }
  }

  @override
  Future<void> resetUser() async {
    try {
      _moengagePlugin.logout();
      logDebug('User reset/logged out');
    } catch (e) {
      logDebug('Error resetting user: $e');
    }
  }

  @override
  String get providerName => 'moengage';

  @override
  bool get isEnabled => _isEnabled;

  @override
  set isEnabled(bool value) {
    _isEnabled = value;
  }

  @override
  void logDebug(String message) {
    if (kDebugMode) {
      print('[MoEngage] $message');
    }
  }
}
