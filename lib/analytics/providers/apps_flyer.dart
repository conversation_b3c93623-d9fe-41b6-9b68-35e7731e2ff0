// lib/analytics/providers/appsflyer_provider.dart
import 'package:flutter/foundation.dart';
import 'package:appsflyer_sdk/appsflyer_sdk.dart';
import '../analytics_provider.dart';

class AppsFlyerProvider implements AnalyticsProvider {
  static final AppsFlyerProvider _instance = AppsFlyerProvider._internal();
  
  factory AppsFlyerProvider() => _instance;
  
  // AppsFlyer Dev Key
  static const String _devKey = 'V5Bjqdw7MSjNxmByJEEfLF';
  static const String _appId = '**********'; // Only for iOS
  
  late AppsflyerSdk _appsflyerSdk;
  bool _isEnabled = true;
  String? _userId;
  
  AppsFlyerProvider._internal() {
    final Map<String, dynamic> appsFlyerOptions = {
      "afDevKey": _devKey,
      "afAppId": _appId,
      "isDebug": kDebugMode,
    };
    
    _appsflyerSdk = AppsflyerSdk(appsFlyerOptions);
  }
  
  @override
  Future<void> initialize() async {
    try {
      await _appsflyerSdk.initSdk(
        registerConversionDataCallback: true,
        registerOnAppOpenAttributionCallback: true,
        registerOnDeepLinkingCallback: true,
      );
      
      if (kDebugMode) {
        print('AppsFlyer SDK initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing AppsFlyer SDK: $e');
      }
    }
  }
  
  @override
  Future<void> identifyUser(String userId) async {
    try {
      _userId = userId;
       _appsflyerSdk.setCustomerUserId(userId);
      
      if (kDebugMode) {
        print('AppsFlyer: User identified: $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AppsFlyer: Error identifying user: $e');
      }
    }
  }
  
  @override
  Future<void> setUserAttributes({
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? gender,
    DateTime? birthday,
    Map<String, dynamic>? customAttributes,
  }) async {
    try {
      // AppsFlyer doesn't have direct methods for setting user attributes
      // We can use custom events to store user data
      final Map<String, dynamic> userAttributes = {};
      
      if (firstName != null) userAttributes['first_name'] = firstName;
      if (lastName != null) userAttributes['last_name'] = lastName;
      if (email != null) userAttributes['email'] = email;
      if (phoneNumber != null) userAttributes['phone'] = phoneNumber;
      if (gender != null) userAttributes['gender'] = gender;
      if (birthday != null) userAttributes['birthday'] = birthday.toIso8601String();
      
      if (customAttributes != null) {
        userAttributes.addAll(customAttributes);
      }
      
      // Only track if we have attributes to track
      if (userAttributes.isNotEmpty) {
        await _appsflyerSdk.logEvent('user_attributes_updated', userAttributes);
      }
      
      if (kDebugMode) {
        print('AppsFlyer: User attributes set successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AppsFlyer: Error setting user attributes: $e');
      }
    }
  }
  
  @override
  Future<void> trackEvent(String eventName, {Map<String, dynamic>? eventAttributes}) async {
    try {
      await _appsflyerSdk.logEvent(eventName, eventAttributes ?? {});
      
      if (kDebugMode) {
        print('AppsFlyer: Event tracked: $eventName with attributes: $eventAttributes');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AppsFlyer: Error tracking event: $e');
      }
    }
  }
  
  @override
  Future<void> resetUser() async {
    try {
      // AppsFlyer doesn't have a direct logout method
      // We can set the customer user ID to null or a guest ID
       _appsflyerSdk.setCustomerUserId('');
      _userId = null;
      
      if (kDebugMode) {
        print('AppsFlyer: User reset/logged out');
      }
    } catch (e) {
      if (kDebugMode) {
        print('AppsFlyer: Error resetting user: $e');
      }
    }
  }
  
  @override
  String get providerName => 'appsflyer';
  
  @override
  bool get isEnabled => _isEnabled;
  
  @override
  set isEnabled(bool value) {
    _isEnabled = value;
  }
  
  @override
  void logDebug(String message) {
    //
  }
}