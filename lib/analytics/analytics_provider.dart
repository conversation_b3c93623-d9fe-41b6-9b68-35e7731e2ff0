import 'package:flutter/foundation.dart';

/// Abstract class that defines the interface for all analytics providers
abstract class AnalyticsProvider {
  /// Initialize the analytics provider
  Future<void> initialize();

  /// Identify a user with a unique ID
  Future<void> identifyUser(String userId);

  /// Set user attributes
  Future<void> setUserAttributes({
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? gender,
    DateTime? birthday,
    Map<String, dynamic>? customAttributes,
  });

  /// Track an event with optional attributes
  Future<void> trackEvent(String eventName,
      {Map<String, dynamic>? eventAttributes});

  /// Reset user (logout)
  Future<void> resetUser();

  /// Get the provider name
  String get providerName;

  /// Check if the provider is enabled
  bool get isEnabled;

  /// Enable or disable the provider
  set isEnabled(bool value);

  /// Log debug information
  void logDebug(String message) {
    if (kDebugMode) {
      print('[$providerName] $message');
    }
  }
}
