/// Constants for analytics providers
class AnalyticsProviders {
  static const String moengage = 'moengage';
  static const String webengage = 'webengage';
  static const String firebase = 'firebase';
  static const String appsflyer = 'appsflyer';
  static const String hotjar = 'hotjar';
}

/// Constants for common analytics events
/// These event names will be used across all providers
/// Event names match the Google Sheets specification exactly
class AnalyticsEvents {
  // Shared Events (Guest & Host) - from Google Sheets
  static const String bookingConfirmed = 'booking.confirmed';
  static const String bookingInitiated = 'booking.initiated';
  static const String bookingDeclined = 'booking.Declined';
  static const String startBooking = 'start.booking';
  static const String confirmBookingPaymentSuccess =
      'confirm.booking';
  static const String paymentCompleted = 'payment.completed';
  static const String paymentFailed = 'payment.failed';
  static const String paymentInitiated = 'payment.initiated ';
  static const String bookingCancelled = 'booking.cancelled';
  static const String submitReview = 'submit.review';
  static const String couponApplied = 'coupon.applied';

  // Guest-Specific Events - from Google Sheets
  static const String guestAppInstalled = 'guest.app.installed';
  static const String userRegister = 'user.register';
  static const String userLogin = 'user.login';
  static const String appOpen = 'app.open';
  static const String viewPropertyDetails = 'view.property_details';
  static const String guestReferralSent = 'guest.referral.sent';
  static const String guestReferralSuccess = 'guest.referral.success';
  static const String guestSessionEnd = 'guest.session.end';
  static const String guestPushClicked = 'guest.push.clicked';
  static const String saveFavorite = 'save.favorite';
  static const String guestInactive = 'guest.inactive';
  static const String userProfileUpdate = 'guest.user.profile.Update';
  static const String reviewPublished = 'review.published';
  static const String ratingSubmitted = 'rating.submitted';
  static const String propertySearched = 'property.searched';
  static const String searchResults = 'property.search.results';
  static const String propertyShare = 'property.share';

  // Host-Specific Events - from Google Sheets
  static const String hostPropertyListingStarted =
      'host.property.listing.started';
  static const String hostPropertyListingCompleted =
      'host.property.listing.completed';
  static const String hostPropertyApproved = 'host.property.approved';
  static const String hostPropertyRejected = 'host.property.rejected';
  static const String hostCalendarUpdated = 'host.calendar.updated';
  static const String hostPayoutSuccessful = 'host.payout.successful';//
  static const String hostGuestReviewPublished = 'host.guest.review_published';
  static const String hostDashboardOpened = 'host.dashboard.opened';
  static const String hostLowBookingActivity = 'host.low.booking.activity';
  static const String hostLicenseExpiryNotification =
      'host.license.expiry.notification';
}

/// Constants for common analytics attributes
class AnalyticsAttributes {
  // User attributes
  static const String userId = 'user_id';
  static const String userType = 'user_type';
  static const String userLanguage = 'user_language';
  static const String userEmail = 'user_email';
  static const String userPhone = 'user_phone';

  // Property attributes
  static const String propertyId = 'property_id';
  static const String propertyType = 'property_type';
  static const String propertyPrice = 'property_price';
  static const String propertyLocation = 'property_location';
  static const String propertyName = 'property_name';
  static const String propertyHostId = 'property_host_id';
  static const String propertyRating = 'property_rating';
  static const String city = 'city';
  static const String priceRange = 'price_range';

  // Booking attributes
  static const String bookingId = 'booking_id';
  static const String guestId = 'guest_id';
  static const String hostId = 'host_id';
  static const String checkInDate = 'check_in_date';
  static const String checkOutDate = 'check_out_date';
  static const String bookingStage = 'booking_stage';
  static const String paymentStatus = 'payment_status';
  static const String amountPaid = 'amount_paid';
  static const String cancellationReason = 'cancellation_reason';
  static const String declinedReason = 'declined_reason';
  static const String hostCancellationPolicy = 'host_cancellation_policy';
  static const String bookingDates = 'booking_dates';
  static const String bookingNights = 'booking_nights';
  static const String bookingGuests = 'booking_guests';
  static const String bookingAmount = 'booking_amount';
  static const String bookingStatus = 'booking_status';

  // Device attributes
  static const String deviceType = 'device_type';
  static const String deviceLanguage = 'device_language';
  static const String guestDeviceType = 'guest_device_type';
  static const String guestOsVersion = 'guest_os_version';
  static const String guestAppVersion = 'guest_app_version';
  static const String osVersion = 'os_version';
  static const String appVersion = 'app_version';

  // Session attributes
  static const String sessionTimestamp = 'session_timestamp';
  static const String timestamp = 'timestamp';
  static const String platform = 'platform';
  static const String guestSessionId = 'guest_session_id';
  static const String guestSessionDuration = 'guest_session_duration';
  static const String guestLastInteractionType = 'guest_last_interaction_type';

  // Guest-specific attributes
  static const String guestRegistrationChannel = 'guest_registration_channel';
  static const String guestType = 'guest_type';
  static const String guestLocation = 'guest_location';
  static const String guestReferralLink = 'guest_referral_link';
  static const String guestReferralChannel = 'guest_referral_channel';
  static const String guestReferrerId = 'guest_referrer_id';
  static const String guestPushId = 'guest_push_id';
  static const String guestDaysSinceLastSession =
      'guest_days_since_last_session';
  static const String guestDaysSinceLastBooking =
      'guest_days_since_last_booking';

  // Host-specific attributes
  static const String hostListingProgressStatus =
      'host_listing_progress_status';
  static const String hostApprovalStatus = 'host_approval_status';
  static const String hostRejectionReason = 'host_rejection_reason';
  static const String hostDaysSinceLastUpdate = 'host_days_since_last_update';
  static const String calendarSyncStatus = 'calendar_sync_status';
  static const String hostPayoutAmount = 'host_payout_amount';
  static const String hostPayoutStatus = 'host_payout_status';
  static const String guestReviewScore = 'guest_review_score';
  static const String guestReviewText = 'guest_review_text';
  static const String hostBookingDropRate = 'host_booking_drop_rate';
  static const String hostDaysSinceLastBooking = 'host_days_since_last_booking';
  static const String hostLicenseId = 'host_license_id';
  static const String hostLicenseExpiryDate = 'host_license_expiry_date';
  static const String daysToExpiry = 'days_to_expiry';

  // Review attributes
  static const String reviewScore = 'review_score';
  static const String reviewText = 'review_text';
  static const String ratingValue = 'rating_value';
  static const String reviewCategory = 'review_category';

  // Search attributes
  static const String searchQuery = 'search_query';
  static const String searchLocation = 'search_location';
  static const String searchDates = 'search_dates';
  static const String searchGuests = 'search_guests';
  static const String searchFilters = 'search_filters';
  static const String searchResultsCount = 'search_results_count';

  // Payment attributes
  static const String paymentMethod = 'payment_method';
  static const String paymentAmount = 'payment_amount';
  static const String paymentCurrency = 'payment_currency';
  static const String couponCode = 'coupon_code';
  static const String discountAmount = 'discount_amount';
  static const String price = 'price';

  // Campaign attributes
  static const String campaignId = 'campaign_id';

  // Screen attributes
  static const String screenName = 'screen_name';
  static const String previousScreen = 'previous_screen';

  // Category attributes
  static const String categoryName = 'category_name';
  static const String categoryId = 'category_id';
}
