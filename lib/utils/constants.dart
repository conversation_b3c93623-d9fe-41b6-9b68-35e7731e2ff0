import 'dart:math';
import 'package:darent/models/userModel.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:fpjs_pro_plugin/result.dart';
import 'package:intl/intl.dart';
import 'package:get/get.dart';

import '../models/listing_prefs_model.dart';

const viewPadding = 5.5;

enum DialogKeyword {
  success,
  warning,
  info,
}

// Map<String,TranslationModel> translateKeywords = <String,TranslationModel>{};
Map<String, ListingPrefsModel> listPrefs = <String, ListingPrefsModel>{};
List<BankModel> banksList = [];
// Map<String,dynamic> translateOfflineKeywords = {};
FirebaseAnalytics analytics = FirebaseAnalytics.instance;

purchaseEvent(
    String currency,
    String bookingId,
    total,
    double tax,
    String transactionType,
    String reservationMonth,
    int reservationDuration,
    AnalyticsEventItem item,
    {String coupon = ''}) {
  analytics.logPurchase(
      items: [item],
      value: total is int ? total.toDouble() : (total ?? 0),
      tax: tax,
      currency: currency,
      transactionId: bookingId,
      coupon: coupon,
      parameters: {
        'transaction_type': transactionType,
        'reservation_month': reservationMonth,
        'reservation_duration': reservationDuration
      });
}

final randomInstance = Random();
final formDateFormat = DateFormat("yyyy-MM-dd");
final formDateFormatCservice = DateFormat("MM/dd/yyyy");
final formatter = DateFormat("dd-MM-yyyy");
final alphaNumericRegex8 = RegExp(r'^[a-zA-Z0-9]+$');

bool isUser = false;
final userModel = Rxn<UserModel>();
bool isHost = false;
bool displayPromo = false;
// List<CountryCode> countryCodes = [];

String get baseUrl => dotenv.get('BASE_URL');

const defaultImage = "images/default-image.png";

bool get forceUpdateIgnoreCondition => baseUrl.contains("dev") || baseUrl.contains("staging");

String get apiBaseUrl => "$baseUrl/api/";

String get propertyLicenseLink => dotenv.get('PROPERTY_LICENSE_LINK') ?? '';

// const myFatoorahLive = "FYVSuYvxJgsd_m5DjUf3i3i94To4xwYyCrZCu2o7-DIGnifna_ecTsx6RIbotTvCfIRylbu8IA6NfceFXVBCFS6kLXnyCtyK63yu2tFyoUBsIa2toMBsls15zDn1WoEy8LT_bjFr8FsK-rmUWooomvuNQCNwMsbLkhubOVHnOvdAfK1japp0q6piwNNB9sw9Z99nt-r26TNddyIrbJWFK7luIK33SzVkc_1mp_Yizf9sXyIghL2_NhHRdZX9ODiUrGzDqWYOw9RZalpiq4mz6T_yDNdpu8H4idr30CP6dAl-Z3vYlUpoJa7OqMSzioEqfEnxzCt5XAEgjgzydU8oC8p9g915IgAe0avFKmpi_somgw7_kbmiPFWyFMTb_yEx5Lj6lW02GULYXQN-93ewNG-1WNRUToXz8R-xkRBA5fOEBF5OHiHPo6z4Kl3lS33gBa4croEPBT3BdUE95s6r8XztETlbKax33xcOyPG5LBtWgY8pHEavHpHEZX6ay9R_N-P8ij947MUx-t_rEP5_Fp-npwlQ0Y377vnj9kqVXeeVWTnqKkUmvbG-c8XcEgfBbteKrm3NEkNQyU7FLM_Dlr-LVQX8I0CKIhO6JCSO3gtNd1iSU-3MucukEiuyphSAKzqVovkQyoMjWIeppeSl85Z8BKTd6b4FSwt6g-q5GSIAokMkVgC2L8la2v8Z74lPjEXPrR05u1ATxVj5lmFTYyHLlK0";
// const myFatoorahDemo = "rLtt6JWvbUHDDhsZnfpAhpYk4dxYDQkbcPTyGaKp2TYqQgG7FGZ5Th_WD53Oq8Ebz6A53njUoo1w3pjU1D4vs_ZMqFiz_j0urb_BH9Oq9VZoKFoJEDAbRZepGcQanImyYrry7Kt6MnMdgfG5jn4HngWoRdKduNNyP4kzcp3mRv7x00ahkm9LAK7ZRieg7k1PDAnBIOG3EyVSJ5kK4WLMvYr7sCwHbHcu4A5WwelxYK0GMJy37bNAarSJDFQsJ2ZvJjvMDmfWwDVFEVe_5tOomfVNt6bOg9mexbGjMrnHBnKnZR1vQbBtQieDlQepzTZMuQrSuKn-t5XZM7V6fCW7oP-uXGX-sMOajeX65JOf6XVpk29DP6ro8WTAflCDANC193yof8-f5_EYY-3hXhJj7RBXmizDpneEQDSaSz5sFk0sV5qPcARJ9zGG73vuGFyenjPPmtDtXtpx35A-BVcOSBYVIWe9kndG3nclfefjKEuZ3m4jL9Gg1h2JBvmXSMYiZtp9MR5I6pvbvylU_PP5xJFSjVTIz7IQSjcVGO41npnwIxRXNRxFOdIUHn0tjQ-7LwvEcTXyPsHXcMD8WtgBh-wxR8aKX7WPSsT1O8d8reb2aR7K3rkV3K82K_0OgawImEpwSvp9MNKynEAJQS6ZHe_J_l77652xwPNxMRTMASk1ZsJL";
//Tabby Test credentials

String get tabbyTestAPIKey => dotenv.get('TABBY_API_KEY') ?? '';

// Tabby Live credentials
//  const public= 'pk_995021d7-c53e-409f-916d-30246f2e191f';
//  const secret= 'sk_4e1f2951-ca9b-48fd-887c-7a988810d9ca';

// Moyasar Test credentials
String get moyasarTestAPIKey => dotenv.get('MOYASAR_TEST_API_KEY') ?? '';

// Moyasar Live credentials
String get moyasarLiveAPIKey => dotenv.get('MOYASAR_LIVE_API_KEY') ?? '';

Map? tawuniya;

const themeColor = 0xffF5C33E;
//fdb040
const lightBg = 0xffF7F7F7;
const greyBorder = 0xffE2E2E2;
const greyText = 0xff909090;
const successColor = 0xff23BC4C;
const successDialogColor = 0xff00AC65;
const warningColor = 0xffF45E5E;
const calenderDisableColor = 0xff5a409a;
int lightBlack = 0xff010101;
const List<double> grayscaleMatrix = [
  0.2126, 0.7152, 0.0722, 0, 0, // Red channel
  0.2126, 0.7152, 0.0722, 0, 0, // Green channel
  0.2126, 0.7152, 0.0722, 0, 0, // Blue channel
  0,      0,      0,      1, 0, // Alpha channel
];

double checkAndGetDouble(value) {
  if (value is int) {
    return value.toDouble();
  } else if (value is String) {
    return double.parse(value);
  } else if (value is double) {
    return value;
  } else {
    return 0.0;
  }
}

const List<String> menuNames = [
  "Home",
  "Wishlist",
  "Bookings",
  "Index",
  "Account"
];

List<Map> colors = [
  {"value": "#7FFFD4", "name": "Aquamarine", "name_ar": "زبرجد"},
  {"value": "#0000FF", "name": "Blue", "name_ar": "أزرق"},
  {"value": "#000080", "name": "Navy", "name_ar": "القوات البحرية"},
  {"value": "#800080", "name": "Purple", "name_ar": "أرجواني"},
  {"value": "#FF1493", "name": "DeepPink", "name_ar": "زهري غامق"},
  {"value": "#EE82EE", "name": "Violet", "name_ar": "البنفسجي"},
  {"value": "#FFC0CB", "name": "Pink", "name_ar": "لون القرنفل"},
  {"value": "#006400", "name": "DarkGreen", "name_ar": "أخضر غامق"},
  {"value": "#008000", "name": "Green", "name_ar": "أخضر"},
  {"value": "#9ACD32", "name": "YellowGreen", "name_ar": "الأخضر الأصفر"},
  {"value": "#FFFF00", "name": "Yellow", "name_ar": "أصفر"},
  {"value": "#FFA500", "name": "Orange", "name_ar": "البرتقالي"},
  {"value": "#FF0000", "name": "Red", "name_ar": "أحمر"},
  {"value": "#A52A2A", "name": "Brown", "name_ar": "بني"},
  {"value": "#DEB887", "name": "BurlyWood", "name_ar": "خشب قوي البنية"},
  // { "value":"custom", "name":"Custom","name_ar":"مخصص"}
];
Map<String, List<String>> months = {
  'en': [
    'Jan',
    'Feb',
    'March',
    'April',
    'May',
    'June',
    'July',
    'Aug',
    'Sept',
    'Oct',
    'Nov',
    'Dec'
  ],
  'ar': [
    'يناير',
    'فبراير',
    'مارس',
    'ابريل ',
    'مايو',
    ' يونيو',
    ' يوليو',
    ' أغسطس',
    ' سبتمبر',
    ' أكتوبر',
    ' نوفمبر',
    ' ديسمبر'
  ]
};
List<String> days = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday'
];
List<String> monthOfNo = [
  '01',
  '02',
  '03',
  '04',
  '05',
  '06',
  '07',
  '08',
  '09',
  '10',
  '11',
  '12',
];
List noOfCounts = ["0", "1", "2", "3", "4", "5", "6", "7", "8"];
List oneTo10 = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"];
List oneTo5 = ["1", "2", "3", "4", "5"];
// const mapKey = "AIzaSyCZlKun3AkG-gLCKfl9MEsaVBJizAzfP_k";
//AIzaSyCOAu5HQQ-B3GbvATl_LRtxd30dZ45ISX4
String mapKey = GetPlatform.isAndroid
    ? "AIzaSyBwsMnL9BeaD5OWxE3AyuvZlGniO8as6w8"
    : "AIzaSyBlsO3-QsNeKNFIf1fllhl9i29kJ0azdWo";
const apiError =
    "An error occurred while processing your request.\nCheck if you have an active Internet connection.";
const apiErrorInternal =
    "Please check if you have an active Internet connection.";
//convert numbers to arabic
String convertToAabic(String input) {
  const english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
  const farsi = ['۰', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  for (int i = 0; i < english.length; i++) {
    input = input.replaceAll(english[i], farsi[i]);
  }
  return input;
}

String visitorId = '';
FingerprintJSProResponse? deviceData;
String requestId = '';
