import 'dart:async';
import 'package:darent/components/warning_dialog.dart';
import 'package:get/get.dart';

class FailedRequestDialog {
  FailedRequestDialog() : _isPresented = false;

  late bool _isPresented;

  Future<void> show(String title, String description) async {
    if (_isPresented) return;
    _isPresented = true;
    await Get.dialog(WarningDialog(title: title, description: description));
    _isPresented = false;
  }
}
