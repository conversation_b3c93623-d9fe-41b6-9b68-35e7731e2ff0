import 'package:darent/screens/authentication/force_update.dart';
import 'package:darent/screens/authentication/promotionBanner.dart';
import 'package:darent/screens/customer_services.dart';
import 'package:darent/screens/new_host/new_edit/edit_main.dart';
import 'package:darent/screens/profile.dart';
import 'package:darent/screens/wallet_payments.dart';
import 'package:get/get.dart';
import '../screens/authentication/splash.dart';
import '../screens/dashboard.dart';
import '../screens/host/dwellings.dart';
import '../screens/inbox_window/inbox_window.dart';
import '../screens/new_host/host.dart';
import '../screens/new_host/host_reservations.dart';
import '../screens/reservations/reservation_detail.dart';

class Routes {
  static const splash = "/splash";
  static const home = "/";
  static const hostHome = "/host-home";
  static const inbox = "/property_inbox";
  static const reservationDetail = "/reservation";
  static const hostReservations = "/host_reservations";
  static const walletPaymentRoute = "/wallet_payment";
  static const dwellings = "/dwellings";
  // static const editProperty1 = "/edit_property1";
  // static const editProperty2 = "/edit_property2";
  static const editMain = "/edit_main";
  static const propertySingle = "PropertyDetail"; //properties
  static const properties = "properties";
  static const profile = "/profile";
  static const customerServices = "/customer-services";

  static const promotion = "/promotion";
  static const forceUpdate = "/force-update";

  static List<GetPage> getPages = [
    GetPage(
      name: splash,
      page: () {
        return const SplashScreen();
      },
    ),
    GetPage(
        name: home,
        page: () {
          return const Dashboard();
        },
        popGesture: false),
    GetPage(
        name: profile,
        page: () {
          return const Profile();
        }),
    GetPage(
        name: hostHome,
        page: () {
          int i = int.tryParse(Get.parameters['index'] ?? '0') ?? 0;
          String? bookingTab = Get.arguments?['booking_tab'];
          return Host(index: i, bookingTab: bookingTab);
        }),
    //https://staging4.darent.com/en/properties/testing-to-check-license-HK9-430?code=HK9-430-1769
    // GetPage(name: '/$propertySingle', page: (){
    //   return PropertyDetail(slug: Get.parameters['slug']??'');
    // },popGesture: false),
    GetPage(
        name: Routes.inbox,
        page: () {
          return const InboxWindow();
        }),
    GetPage(
        name: '$reservationDetail/:code',
        page: () {
          return ReservationDetail(
              code: Get.parameters['code']!,
              forReview: Get.parameters['review'] == 'true');
        }),
    GetPage(
        name: hostReservations,
        page: () {
          return HostReservations(); //currentTab:Get.parameters['tab']
        }),
    GetPage(
        name: walletPaymentRoute,
        page: () {
          return const WalletPayments();
        }),
    GetPage(
        name: dwellings,
        page: () {
          return const Dwellings();
        }),
    // GetPage(name: editProperty1, page: (){
    //   return const EditProperty1();
    // }),
    // GetPage(name: editProperty2, page: (){
    //   return const EditProperty2();
    // }),
    GetPage(
        name: editMain,
        page: () {
          return const EditMain();
        }),
    GetPage(
        name: customerServices,
        page: () {
          return const CustomerServices();
        }),

    GetPage(
        name: promotion,
        page: () {
          return PromotionBanner();
        }),

    GetPage(
        name: forceUpdate,
        page: () {
          return const ForceUpdate();
        }),
  ];
}
