import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/failed_request_dialog.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;

import '../helperMethods/authHelper.dart';
import '../helperMethods/globalHelpers.dart';
import 'api_constants.dart';

class ResponseModel {
  bool status = false;
  var data, message;
  ResponseModel(this.status, this.message, this.data);
  Map toJson() => {
        'status': status,
        'data': data,
        'message': message,
      };
  ResponseModel.fromJson(Map<String, dynamic> json) {
    status = json['message'] == "Success" ||
        json['message'] == "success" ||
        json['status'] == "success" ||
        json['status'] == 200 ||
        json['success'] == true;
    data = json['data'];
    message = json['message'];
  }
}

class ApiServices {
  static final _instance = ApiServices._();
  ApiServices._();
  factory ApiServices() {
    return _instance;
  }
  static int seconds = 0;
  static Timer? timer;
  static final http.Client httpClient = http.Client();

  static void Function() closeClient = httpClient.close;

  static Future<ResponseModel> getApi(String url,
      {bool isAuth = true,
      bool fullUrl = false,
      bool showDialog = true}) async {
    Map<String, String>? headers;
    if (isAuth && GlobalHelper.storageBox.hasData("user")) {
      var userObj = GlobalHelper.storageBox.read('user');
      headers = {
        'Authorization': "Bearer ${userObj['token']}",
        "Accept": "application/json",
        'Content-Type': 'application/json',
        'X-Fingerprint-Request-Id': requestId,
        // 'X-Fingerprint-Visitor-Id': visitorId,
      };
    }
    try {
      return httpClient
          .get(Uri.parse(fullUrl ? url : apiBaseUrl + url), headers: headers)
          .then((value) async {
        if (url.contains('performance-report')) {}
        if (kDebugMode) {
          print(fullUrl ? url : apiBaseUrl + url);
          print(headers);
          print("Status code = ${value.statusCode}");
          print(value.body);
        }
        if (value.statusCode == 200) {
          return ResponseModel.fromJson(jsonDecode(value.body));
        } else if (value.statusCode == 403 ||
            value.statusCode == 422 ||
            value.statusCode == 401) {
          // await throughSentryException("On status code: ${value.statusCode} in Get $url", getErrorString(jsonDecode(value.body), url: url,showDialog: showDialog),);
          return ResponseModel.fromJson({
            "status": false,
            "message": getErrorString(jsonDecode(value.body),
                status: value.statusCode, url: url, showDialog: showDialog)
          });
        } else {
          // await throughSentryException("On status code: ${value.statusCode} in Get $url", getErrorString(jsonDecode(value.body), url: url,showDialog: showDialog),);
          if (!url.contains('apiLogs')) {
            logApi(url, value.body);
          }
          return ResponseModel.fromJson({
            "status": false,
            "message": getErrorString(jsonDecode(value.body),
                status: value.statusCode, url: url)
          });
        }
      }).onError((error, stackTrace) async {
        if (!url.contains('apiLogs')) {
          logApi(url, 'Error:$error\nStacktrace:$stackTrace', type: 'mobile');
        }
        return ResponseModel.fromJson({
          "status": false,
          "message": getErrorString(
              {'error': "Check if you have an active Internet connection."},
              showDialog: showDialog && !url.contains('apiLogs'))
        });
      });
    } on SocketException catch (e, stackTrace) {
      // await throughSentryException("On SocketException in Get ${fullUrl ? url : apiBaseUrl + url} \n $e", stackTrace);
      logApi(url, 'Error:$e', type: 'mobile');
      return ResponseModel.fromJson({
        "status": false,
        "message": getErrorString(url: url, {
          'error': "Please check if you have an active Internet connection."
        })
      });
    }
  }

  static Future<ResponseModel> postApi(url,
      {body, allowDialog = true, isAuth = false, bool fullUrl = false}) async {
    Map<String, String> headers = {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'X-Fingerprint-Request-Id': requestId,
      'X-Fingerprint-Visitor-Id': visitorId,
    };
    if (isAuth && GlobalHelper.storageBox.hasData('user')) {
      var userObj = GlobalHelper.storageBox.read('user');
      headers['Authorization'] = "Bearer ${userObj['token']}";
    }
    if(!isUser){
      if(url.contains(ApiConstants.searchResult) || url.contains(ApiConstants.propertySingle) || url.contains(ApiConstants.sendOtpPhone)){
        headers['X-Guest-UUID'] = AuthHelper.uuid??'';
      }
    }
    try {
      return httpClient
          .post(Uri.parse(fullUrl ? url : apiBaseUrl + url),
              body: jsonEncode(body), headers: headers)
          .then((value) async {
        if (kDebugMode) {
          print("data sent = $body");
          print(fullUrl ? url : apiBaseUrl + url);
          print(headers);
          print("Status code = ${value.statusCode}");
          print(value.body);
        }
        if (value.statusCode == 200) {
          return ResponseModel.fromJson(jsonDecode(value.body));
        } else if (value.statusCode == 403 ||
            value.statusCode == 422 ||
            value.statusCode == 401) {
          // await throughSentryException("On status code: ${value.statusCode} in Post $url", getErrorString(jsonDecode(value.body), url: url,showDialog: allowDialog),);
          return ResponseModel.fromJson({
            "status": false,
            "message": getErrorString(jsonDecode(value.body),
                url: url, showDialog: allowDialog, status: value.statusCode)
          });
        } else {
          // await throughSentryException("On status code: ${value.statusCode} in Post $url", getErrorString(jsonDecode(value.body), url: url,showDialog: allowDialog),);
          if (!url.contains('apiLogs')) {
            logApi(url, value.body);
          }
          return ResponseModel.fromJson({
            "status": false,
            "message": getErrorString(jsonDecode(value.body),
                url: url, showDialog: allowDialog)
          });
        }
        // }
      }).onError((error, stackTrace) async {
        // await throughSentryException("On Error in Post ${fullUrl ? url : apiBaseUrl + url} \n $error", stackTrace,);
        if (!url.contains('apiLogs')) {
          logApi(url, 'Error:$error\nStacktrace:$stackTrace', type: 'mobile');
        }
        return ResponseModel.fromJson({
          "status": false,
          "message": getErrorString({'error': apiErrorInternal},
              url: url, showDialog: allowDialog && !url.contains('apiLogs'))
        });
      });
    } on SocketException catch (e, stackTrace) {
      // await throughSentryException("On SocketException in Post ${fullUrl ? url : apiBaseUrl + url} \n $e", stackTrace,);
      return ResponseModel.fromJson({
        "status": false,
        "message": getErrorString(url: url, {'error': apiErrorInternal})
      });
    }
  }

  static Future<ResponseModel> imageUpload(
    url, {
    String? imagePath,
    Map<String, String>? body,
    String fileKeyName = 'file',
    bool displayDialog = true,
    List<String> imagePaths = const [],
  }) async {
    final userObj = GlobalHelper.storageBox.read('user');

    try {
      final responseData = await ImageUploadRequest(
        url: url,
        body: body ?? {},
        fileKeyName: fileKeyName,
        headers: {
          'Accept': "application/json",
          'Authorization': "Bearer ${userObj['token']}",
        },
        images: [
          if (imagePath != null) imagePath,
          ...imagePaths,
        ],
      ).send();

      final response = json
          .decode(String.fromCharCodes(await responseData.stream.toBytes()));
      if (kDebugMode) {
        print("Bearer ${userObj['token']}");
        print(Uri.parse(apiBaseUrl + url));
        print(body);
        print("response: ${responseData.statusCode}, $response");
      }
      if (responseData.statusCode == 200) {
        return ResponseModel.fromJson(response);
      } else {
        // await throughSentryException("On status code: ${responseData.statusCode} in imageUpload $url", getErrorString(url: url,response is Map?response:jsonDecode(response)),);
        return ResponseModel.fromJson({
          "status": false,
          "message": getErrorString(
              url: url,
              response is Map ? response : jsonDecode(response),
              status: responseData.statusCode,
              showDialog: displayDialog)
        });
      }
    } on SocketException catch (e, stackTrace) {
      // await throughSentryException("On SocketException in imageUpload $url\n $e", stackTrace);
      logApi(url, e.toString(), type: 'mobile');
      return ResponseModel.fromJson({
        "status": false,
        "message": getErrorString(
            url: url, {'error': apiErrorInternal}, showDialog: displayDialog)
      });
    } catch (e) {
      // await throughSentryException( "On catch in imageUpload $url\n $e", stackTrace);
      logApi(url, e.toString(), type: 'mobile');
      return ResponseModel.fromJson({
        "status": false,
        "message": getErrorString(
            url: url, {'error': apiErrorInternal}, showDialog: displayDialog)
      });
    }
  }

  static logApi(String endPoint, String response, {String type = 'api'}) {
    if (kReleaseMode) {
      if (timer?.isActive != true) {
        Map formData = {
          "response": response,
          "endpoint": endPoint,
          "type": type
        };
        if (userModel.value != null) {
          formData['user_id'] = userModel.value!.id;
        }
        ApiServices.postApi('v1/apiLogs', body: formData, allowDialog: false);
        if (seconds == 0) {
          seconds = 3;
        } else {
          timer = Timer(const Duration(seconds: 3), () {
            if (seconds > 0) {
              seconds--;
            } else {
              timer!.cancel();
            }
            print("Seconds: $seconds");
          });
        }
      }
    }
  }

  // throughSentryException(String title, dynamic stackTrace) async {
  //   if(kReleaseMode){
  //     await Sentry.captureException(
  //       title,
  //       stackTrace: stackTrace,
  //     );
  //   }
  // }
  static String getErrorString(body, {url, showDialog = true, int? status}) {
    String? msg;
    msg = getError(body['error']);
    msg ??= getError(body['message']);
    msg ??= body['message'] ?? apiErrorInternal;
    String title;
    try {
      title = Get.find<TranslationHelper>().translations.propertySingle.error ??
          'Error'; //baseUrl.contains("dev")? (url ?? "Error"):
    } catch (e) {
      title = "Error";
    }
    if (showDialog && (Get.isDialogOpen == false || baseUrl.contains("dev"))) {
      Get.find<FailedRequestDialog>().show(title, msg!).then((_) {
        if (status == 401) AuthHelper.c.onLogout();
      });
    }
    return msg!;
  }

  static String? getError(body) {
    if (body is Map) {
      List errors = [];
      for (var errorKey in body.keys) {
        errors.add(body[errorKey][0]);
      }
      return errors.join("\n");
    } else {
      return body;
    }
  }
}

class ImageUploadRequest extends http.MultipartRequest {
  ImageUploadRequest({
    required String url,
    required String fileKeyName,
    Map<String, String> body = const {},
    Map<String, String> headers = const {},
    List<String> images = const [],
  })  : _body = body,
        _images = images,
        _headers = headers,
        _fileKeyName = fileKeyName,
        super('POST', Uri.parse(apiBaseUrl + url));

  final String _fileKeyName;
  final List<String> _images;
  final Map<String, String> _body;
  final Map<String, String> _headers;

  @override
  Map<String, String> get fields => _body;

  @override
  Map<String, String> get headers => _headers;

  @override
  Future<http.StreamedResponse> send() async {
    files.addAll(await Future.wait(
      _images.map((file) => http.MultipartFile.fromPath(_fileKeyName, file)),
    ));
    return await super.send();
  }
}
