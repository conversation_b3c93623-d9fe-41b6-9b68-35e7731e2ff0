import 'package:darent/utils/constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:fpjs_pro_plugin/fpjs_pro_plugin.dart';
import 'package:fpjs_pro_plugin/region.dart';

class FingerPrintProps {
  FingerPrintProps() : _isInitialized = false;

  late bool _isInitialized;

  Future<void> load() async {
    try {
      await _tryLoad();
    } catch (error) {
      if (kDebugMode) print(error.toString());
      rethrow;
    }
  }

  Future<void> _tryLoad() async {
    await _initialize();
    await _loadVisitorId();
    await _loadRequestId();
  }

  Future<void> _initialize() async {
    if (_isInitialized) return;
    await FpjsProPlugin.initFpjs(
      dotenv.get('FPJS_PRO_API_KEY'),
      region: Region.ap,
    );
    _isInitialized = true;
  }

  Future<void> _loadRequestId() async {
    if (requestId.isNotEmpty) return;
    requestId = (await FpjsProPlugin.getVisitorData()).requestId;
  }

  Future<void> _loadVisitorId() async {
    if (visitorId.isNotEmpty && visitorId != 'Unknown') return;
    visitorId = (await FpjsProPlugin.getVisitorId()) ?? 'Unknown';
  }
}
