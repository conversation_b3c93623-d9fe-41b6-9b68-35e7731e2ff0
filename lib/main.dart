import 'dart:async';
import 'dart:convert' show json;
import 'dart:io';
import 'dart:ui';
import 'package:darent/analytics/analytics.dart';
import 'package:darent/analytics/providers/apps_flyer.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/screens/property_single/property_details.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/failed_request_dialog.dart';
import 'package:darent/utils/routes.dart';
import 'package:darent/utils/translations.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tabby_flutter_inapp_sdk/tabby_flutter_inapp_sdk.dart';
import 'package:upgrader/upgrader.dart';
import './controllers/dashboard_controller.dart';
import './utils/constants.dart';
import './utils/sizeconfig.dart';
import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show SystemUiOverlayStyle;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'helperMethods/authHelper.dart';
import 'helperMethods/chat_helper.dart';
import 'helperMethods/remote_config.dart';
import 'helperMethods/update_helper.dart';
import 'utils/finger_print_props.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  await notificationPermission();
  // Load env file first
  await dotenv.load(fileName: '.env');
  _injectRequiredDependencies();
  intFCMService();
}

notificationPermission() async {
  final status = await Permission.notification.isGranted;
  if (status) {
    return;
  } else {
    Permission.notification.request();
    return;
  }
}
late AndroidNotificationChannel channel;
FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

Future listenForMessages(RemoteMessage message) async {
  DashboardController dashbC = Get.find();
  dashbC.updateOnNotify(message.data);
  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true, badge: true, sound: true);
  dashbC.setApplicationBadgeCount();
  var androidPlatformChannelSpecifics = AndroidNotificationDetails(
      "channel_ID3", message.notification?.body ?? "",
      importance: Importance.max,
      sound: const RawResourceAndroidNotificationSound('cute_notification'),
      showProgress: true,
      channelShowBadge: true,
      priority: Priority.high,
      number: dashbC.finalCount.value,
      ticker: 'test ticker');

  final iOSPlatformChannelSpecifics = DarwinNotificationDetails(
      sound: 'assets/cute_notification', // Sound file in the app's bundle
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      badgeNumber: dashbC.finalCount.value);
  var platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics);
  if (GetPlatform.isAndroid) {
    flutterLocalNotificationsPlugin.show(
        randomInstance.nextInt(100),
        message.notification?.title,
        message.notification?.body,
        platformChannelSpecifics,
        payload: json.encode(message.data));
  }
}

intFCMService() async {
  await FirebaseMessaging.instance.requestPermission(
    sound: true,
    badge: true,
    alert: true,
    announcement: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
  );

  await FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
      alert: true, badge: true, sound: true);

  if (GetPlatform.isIOS) {
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()!
        .requestPermissions(alert: true, badge: true, sound: true);
  }
  const AndroidInitializationSettings initializationSettingsAndroid =
      AndroidInitializationSettings('@mipmap/launcher_icon');
  const InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: DarwinInitializationSettings(
        requestAlertPermission: true,
        requestSoundPermission: true,
        defaultPresentSound: false,
      ));
  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveBackgroundNotificationResponse: onDidNotificationResponse,
    onDidReceiveNotificationResponse: onDidNotificationResponse,
  );
  FirebaseMessaging.onMessage.listen(listenForMessages);
}

onDidNotificationResponse(details) {
  if (details.payload != null) {
    final data = json.decode(details.payload!);

    // Track guest push clicked event with Analytics Manager
    if (GlobalHelper.storageBox.hasData('user') &&
        !(GlobalHelper.storageBox.read("isHost") ?? false)) {
      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.guestPushClicked,
        eventAttributes: {
          AnalyticsAttributes.guestId:
              GlobalHelper.storageBox.read('user')['id']?.toString(),
          AnalyticsAttributes.guestPushId:
              data['notification_id']?.toString() ?? 'unknown',
          AnalyticsAttributes.sessionTimestamp:
              DateTime.now().toIso8601String(),
          'push_type': data['type'] ?? 'unknown',
          'push_title': data['title'] ?? '',
          'push_body': data['body'] ?? '',
        },
      );
    }

    DashboardController dashboardC = Get.find();
    dashboardC.handleMessage(data);
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  await dotenv.load(fileName: '.env');
  await GetStorage.init();
  await _injectRequiredDependencies();

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  if (kReleaseMode) {
    FlutterError.onError = (errorDetails) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    };
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };
  }

  // Initialize Analytics Manager
  unawaited(Get.find<AnalyticsManager>().initializeProviders());

  try {
    GlobalHelper.tabbySdk.setup(
        withApiKey: tabbyTestAPIKey, environment: Environment.production);
  } catch (e) {
    if (kDebugMode) {
      print("Error in tabbySdk setup: $e");
    }
  }

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);



  // Track app opened event with Analytics Manager (using Google Sheets event name)
  unawaited(Get.find<AnalyticsManager>().trackEvent(
    AnalyticsEvents.appOpen,
    eventAttributes: {
      AnalyticsAttributes.guestId: GlobalHelper.storageBox.hasData('user')
          ? GlobalHelper.storageBox.read('user')['id']?.toString()
          : null,
      AnalyticsAttributes.sessionTimestamp: DateTime.now().toIso8601String(),
      AnalyticsAttributes.deviceType: Platform.isAndroid ? 'android' : 'ios',
      AnalyticsAttributes.deviceLanguage:
          Get.deviceLocale?.languageCode ?? 'en',
    },
  ));

  // Track guest app installed event with Analytics Manager (first time only)
  if (!GlobalHelper.storageBox.hasData('app_installed_tracked')) {
    unawaited(Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.guestAppInstalled,
      eventAttributes: {
        AnalyticsAttributes.guestId: GlobalHelper.storageBox.hasData('user')
            ? GlobalHelper.storageBox.read('user')['id']?.toString()
            : null,
        AnalyticsAttributes.sessionTimestamp: DateTime.now().toIso8601String(),
        AnalyticsAttributes.deviceType: Platform.isAndroid ? 'android' : 'ios',
        AnalyticsAttributes.deviceLanguage:
            Get.deviceLocale?.languageCode ?? 'en',
        AnalyticsAttributes.appVersion: '1.0.0',
        'install_source': Platform.isAndroid ? 'google_play' : 'app_store',
      },
    ));
    GlobalHelper.storageBox.write('app_installed_tracked', true);
  }

  // Track app open with Firebase Analytics (keeping for backward compatibility)
  analytics.logAppOpen();

  await Upgrader.clearSavedSettings();
  final fbEvents = FacebookAppEvents();
  fbEvents.setAdvertiserTracking(enabled: true);
  fbEvents.setAutoLogAppEventsEnabled(true);
  await intFCMService();
  await _loadFingerPrintProps();
  runApp(const MyApp());
}

Future _injectRequiredDependencies() async{
  Get.put(FingerPrintProps(), permanent: true);
  Get.put(TranslationHelper(), permanent: true);
  Get.put(FailedRequestDialog(), permanent: true);
  await Get.putAsync(
    () async => await RemoteConfig.instance(
      remoteConfig: FirebaseRemoteConfig.instance,
      onRemoteConfigUpdated: Get.forceAppUpdate,
    ),
    permanent: true,
  );

  // Initialize the Analytics Manager and register providers
  final analyticsManager = AnalyticsManager();
  Get.put(analyticsManager, permanent: true);

  // Register MoEngage provider
  analyticsManager.registerProvider(MoEngageProvider());
  // Register AppsFlyer provider
  analyticsManager.registerProvider(AppsFlyerProvider());
}

Future<void> _loadFingerPrintProps() async {
  await Get.find<FingerPrintProps>().load().catchError((e, _) {
    if (kDebugMode) print('Error loading FingerPrintProps: $e');
  });
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    if (Get.find<TranslationHelper>().translateKeywords.isNotEmpty) {
      Get.find<TranslationHelper>()
          .setTranslations(Get.locale?.languageCode ?? 'en');
      if (kDebugMode) {
        print('lang changed: ${Get.locale?.languageCode ?? 'en'}');
      }
    }
    return LayoutBuilder(
      builder: (context, constraints) {
        SizeConfig.init(constraints);
        ErrorWidget.builder = (FlutterErrorDetails details) {
          return MaterialApp(
            home: Scaffold(
              backgroundColor: Colors.red,
              body: Center(
                child: SingleChildScrollView(
                  child: Text(
                    'Error: ${details.exceptionAsString()}\n\nStack Trace:\n${details.stack}',
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
              ),
            ),
          );
        };
        return GetMaterialApp(
          title: 'Darent',
          translationsKeys: translationKeys,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('en'), Locale('ar')],
          debugShowCheckedModeBanner: false,
          initialBinding: InitialBinding(),
          onGenerateRoute: (settings) {
            if (settings.name?.contains(Routes.properties) == true) {
              return MaterialPageRoute(
                  builder: (_) =>
                      PropertyDetailScreen(slug: Get.parameters['slug'] ?? ''));
            }
            return null;
          },
          navigatorObservers: [DeepLinkNavigatorObserver()],
          getPages: Routes.getPages,
          locale: Locale(GlobalHelper.storageBox.read('user')?['lang'] ??
              (Get.deviceLocale?.languageCode == "en" ? "en" : "ar")),
          transitionDuration: Duration.zero,
          theme: ThemeData(
            useMaterial3: false,
            scaffoldBackgroundColor: Colors.grey[50],
            // fontFamily: Get.locale?.languageCode == 'ar' ? null :"PingAR+LT",
            dividerColor: Colors.black12,
            dividerTheme: const DividerThemeData(thickness: 0.8),
            splashFactory: InkSparkle.constantTurbulenceSeedSplashFactory,
            appBarTheme: AppBarTheme(
                systemOverlayStyle: const SystemUiOverlayStyle(
                    statusBarBrightness: Brightness.light),
                backgroundColor: Colors.grey[200],
                shadowColor: Colors.black26,
                centerTitle: true,
                elevation: 0,
                iconTheme: const IconThemeData(color: Colors.black),
                titleTextStyle: TextStyle(
                    color: Color(lightBlack),
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    // fontFamily: Get.locale?.languageCode == 'ar' ? null :"PingAR+LT"
                )),
            primaryColor: const Color(themeColor),
            colorScheme: ColorScheme.fromSwatch().copyWith(
              secondary: const Color(themeColor),
              primary: const Color(themeColor),
            ),
          ),
          initialRoute: Routes.splash, //InitialNavigation.initRoute,
        );
      },
    );
  }
}

class DeepLinkNavigatorObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    final routeName = route.settings.name;
    if (routeName != null && routeName.contains(Routes.properties)) {
      Uri uri = Uri.parse(routeName);
      String slug = uri.pathSegments.last;
      // Defer navigation to avoid conflict
      scheduleMicrotask(() {
        if(Get.previousRoute.contains(Routes.properties) || Get.previousRoute.contains(Routes.propertySingle)){
          Get.off(() => PropertyDetailScreen(slug: slug));
        }else if(Get.previousRoute.contains(Routes.home)){
          Get.to(() => PropertyDetailScreen(slug: slug));
        }else{
          Get.offAll(() => PropertyDetailScreen(slug: slug));
        }
      });
    }
  }
}

class InitialBinding implements Bindings {
  @override
  void dependencies() async {
    UpdateHelper();

    if (GlobalHelper.storageBox.hasData("user")) {
      // var userObj = storageBox.read('user');
      isHost = GlobalHelper.storageBox.read("isHost") ?? false;
      final lang = GlobalHelper.storageBox.read('user')['lang'] ?? 'en';

      // Set user attributes in Analytics Manager
      final userData = GlobalHelper.storageBox.read('user');
      if (userData != null && Get.isRegistered<AnalyticsManager>()) {
        final analyticsManager = Get.find<AnalyticsManager>();

        // Identify user with unique ID
        analyticsManager.identifyUser(userData['id'].toString());

        // Set user attributes
        analyticsManager.setUserAttributes(
          firstName: userData['first_name'],
          lastName: userData['last_name'],
          email: userData['email'],
          phoneNumber: userData['phone'],
          customAttributes: {
            AnalyticsAttributes.userType: isHost ? 'Host' : 'Customer',
            AnalyticsAttributes.userLanguage: lang,
          },
        );
      }

      Future.delayed(const Duration(seconds: 1), () {
        Get.updateLocale(Locale(lang));
      });
      ApiServices.postApi("v1/set-language",
          body: {"lang": lang}, isAuth: true);
      // final headers = {
      //   "Authorization" : "Bearer ${userObj['token']}"
      // };
      // await post(Uri.parse("${apiBaseUrl}v1/set-language"),body: {"lang":Get.deviceLocale?.languageCode??"en"}, headers: headers);
    }
    Get.put(DashboardController());
    ChatHelper();
    AuthHelper();
    // Get.lazyPut(() => ChatController());
  }
}
