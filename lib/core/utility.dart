import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';



class Utility {
  static Future<void> appLaunchUrl(String url) async {
    final Uri url0 = Uri.parse(url);

    if (!await launchUrl(url0)) {
      throw Exception('Could not launch $url0');
    }
  }

  static Future<void> launchWhatsApp({
    required String phoneNumber,
    required String message,
  }) async {
    final String encodedMessage = Uri.encodeComponent(message);
    final String whatsappUrl = 'https://wa.me/$phoneNumber?text=$encodedMessage';
    
    try {
      await appLaunchUrl(whatsappUrl);
    } catch (e) {
      // show error 
      // throw Exception('Could not launch WhatsApp: $e');
    }
  }

  static String generateReservationWhatsAppMessage({
    required String propertyId,
    required String propertyCode,
    required String propertyName,
    required String checkIn,
    required String checkOut,
  }) {
    return 'Hi Darent Team:\nI want to book this property\nProperty Details:\nID: $propertyId\nCode: $propertyCode\nName: $propertyName\nCheck-in: $checkIn\nCheck-out: $checkOut';
  }

  static String? validatorGeneral(value) {
    if (value!.isEmpty) {
      return "filedNullError".tr;
    }
    return null;
  }

  static String? validatorPassword(value) {
    if (value!.isEmpty) {
      return "filedNullError".tr;
    } else if (value.length < 9) {
      return "password at least 9 characters".tr;
    }
    return null;
  }

  static String? validatorEmail(value) {
    if (value!.isEmpty) {
      return "filedNullError".tr;
    } else if (!GetUtils.isEmail(value)) {
      return "invalidEmailError".tr;
    }
    return null;
  }
}
