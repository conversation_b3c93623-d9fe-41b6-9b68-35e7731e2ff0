import 'package:darent/helperMethods/update_helper.dart';
import 'package:get/get.dart';

import '../utils/routes.dart';
import 'globalHelpers.dart';

class InitialNavigation {
  static final InitialNavigation _singleton = InitialNavigation._internal();
  static String? initRoute;

  factory InitialNavigation() {
    initLogic();
    return _singleton;
  }

  InitialNavigation._internal();
  static Future? initLogic()async{
    await UpdateHelper.init();
    if(UpdateHelper.updateApp){
      initRoute = Routes.forceUpdate;
    }else{
      if(!Get.currentRoute.contains(Routes.propertySingle)){
        if (!GlobalHelper.storageBox.hasData('notFirstTime')) {
          initRoute = Routes.promotion;
        }
        else{
          //initRoute = checkUserNavigate();
          checkUserNavigate();
        }
      }else{
        initRoute = Get.currentRoute;
      }
    }
    return initRoute;
  }
  static checkUserNavigate({bool fromForceUpdate=false}){
    //c.userModel.value?.userVerification?.email == "no"
    // if(storageBox.hasData('user')){
    //   final lang = storageBox.read('user')['lang']??'en';
    //   Get.updateLocale(Locale(lang));
    //   ApiServices.postApi("v1/set-language",body: {"lang":lang},isAuth:true);
    // }
    if(GlobalHelper.storageBox.read('isHost')??false){
      initRoute = Routes.hostHome;
      //Get.offAllNamed(Routes.hostHome);
    }else{
      if (false) {
        // Get.to(() => const OtpVerification(isEmailOtp: true));
        // c.secondsRemaining.value = 60;
        // c.enableResend.value = false;
        // c.startResendTimer();
      } else {
        initRoute = Routes.home;
        //Get.offAllNamed(Routes.home);
      }
    }
    if(fromForceUpdate){
      Get.offAllNamed(initRoute??'/');
    }
  }
}