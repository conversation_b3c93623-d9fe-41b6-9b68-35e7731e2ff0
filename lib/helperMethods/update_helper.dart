import 'package:upgrader/upgrader.dart';

class UpdateHelper{

  static String appVersion = '';
  static Upgrader packageInfo = Upgrader();
  static bool updateApp = false;

  static final UpdateHelper _instance = UpdateHelper._();
  factory UpdateHelper(){
    init();
    return _instance;
  }
  UpdateHelper._();

  static init()async{
    await packageInfo.initialize();
    appVersion = "${packageInfo.currentInstalledVersion}";
    updateApp = packageInfo.isUpdateAvailable();
  }
}