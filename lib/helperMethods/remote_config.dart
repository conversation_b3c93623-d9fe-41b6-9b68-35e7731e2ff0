import 'package:darent/utils/constants.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:get/get.dart';
import 'package:intl_phone_field/countries.dart';

abstract class RemoteConfig {
  factory RemoteConfig.fake() = _DefaultRemoteConfig;

  static Future<RemoteConfig> instance({
    required FirebaseRemoteConfig remoteConfig,
    required void Function() onRemoteConfigUpdated,
  }) async =>
      _RemoteConfigImpl.instance(
        remoteConfig: remoteConfig,
        onRemoteConfigUpdated: onRemoteConfigUpdated,
      );

  //values
  int get dobGapeYears;
  String get imagesBaseUrl;
  String get allowedCountries;

  //flags
  bool get tamayouzEnabled;
  bool get licenseMandatory;
  bool get enableNoOfApartments;
  bool get enableForceUpdate;

  List<Country> get fieldCountries;

  //method to setCountries from RemoteCnfig or initialData
  void setCountries(List<dynamic> jsonCountries);
}

class _FirebaseRemoteConfig implements RemoteConfig {
  final List<Country> _countries=[];
  _FirebaseRemoteConfig(
    FirebaseRemoteConfig remoteConfig,
  ) : _firebaseRemoteConfig = remoteConfig;

  final FirebaseRemoteConfig _firebaseRemoteConfig;

  @override
  String get allowedCountries =>
      _firebaseRemoteConfig.getString('allowedCountries');

  @override
  int get dobGapeYears => _firebaseRemoteConfig.getInt('dobGapeYears');

  @override
  bool get enableNoOfApartments =>
      _firebaseRemoteConfig.getBool('enableNoOfApartments');

  @override
  String get imagesBaseUrl => _firebaseRemoteConfig.getString('imagesBaseUrl');

  @override
  bool get licenseMandatory =>
      _firebaseRemoteConfig.getBool('licenseMandatory');

  @override
  bool get tamayouzEnabled => _firebaseRemoteConfig.getBool('tamayouzEnabled');

  @override
  bool get enableForceUpdate => _firebaseRemoteConfig.getBool('enableForceUpdate');

  @override
  List<Country> get fieldCountries => _countries;

  @override
  void setCountries(List jsonCountries) {

  }
}

class _DefaultRemoteConfig implements RemoteConfig {
  List<Country> _countries=[];
  @override
  int get dobGapeYears => 15;

  @override
  String get imagesBaseUrl => baseUrl;

  @override
  String get allowedCountries => '';

  @override
  bool get tamayouzEnabled => false;

  @override
  bool get licenseMandatory => false;

  @override
  bool get enableNoOfApartments => true;

  @override
  bool get enableForceUpdate => true;

  @override
  List<Country> get fieldCountries => _countries;

  @override
  void setCountries(List jsonCountries) {

  }
}

class _RemoteConfigImpl implements RemoteConfig {
  List<Country> _countries = [];
  static Future<RemoteConfig> instance({
    required FirebaseRemoteConfig remoteConfig,
    required void Function() onRemoteConfigUpdated,
  }) async {
    try {
      return await _tryCreateInstance(
        remoteConfig: remoteConfig,
        onRemoteConfigUpdated: onRemoteConfigUpdated,
      );
    } catch (_, __) {
      return _DefaultRemoteConfig();
    }
  }

  @override
  setCountries(List jsonCountries){
    if(allowedCountries.isNotEmpty){
      Iterable<Country> countriesList = countries.where((e)=>allowedCountries.contains(e.code));
      if(countriesList.isNotEmpty){
        _countries = countriesList.toList();
      }
    }else{
      for(final jsonCountry in jsonCountries){
        final country = countries.firstWhereOrNull((c)=>c.code==jsonCountry['short_name']);
        if(country!=null){
          _countries.add(country);
        }
      }
    }
    if(_countries.isEmpty){
      _countries = countries.where((i)=>i.code=='SA').toList();
    }
  }
  static Future<RemoteConfig> _tryCreateInstance({
    required FirebaseRemoteConfig remoteConfig,
    required void Function() onRemoteConfigUpdated,
  }) async {
    await remoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(minutes: 1),
      minimumFetchInterval: const Duration(seconds: 5),
    ));
    await remoteConfig.fetchAndActivate();
    return _RemoteConfigImpl(
      remoteConfig: remoteConfig,
      onRemoteConfigUpdated: onRemoteConfigUpdated,
      defaultRemoteConfig: _FirebaseRemoteConfig(remoteConfig),
    );
  }

  _RemoteConfigImpl({
    required RemoteConfig defaultRemoteConfig,
    required FirebaseRemoteConfig remoteConfig,
    required void Function() onRemoteConfigUpdated,
  })  : _isListenedToRemoteConfig = false,
        _remoteConfig = defaultRemoteConfig,
        _firebaseRemoteConfig = remoteConfig,
        _onRemoteConfigUpdated = onRemoteConfigUpdated;

  final RemoteConfig _remoteConfig;
  late bool _isListenedToRemoteConfig;
  final void Function() _onRemoteConfigUpdated;
  final FirebaseRemoteConfig _firebaseRemoteConfig;

  @override
  List<Country> get fieldCountries => _countries;

  @override
  String get allowedCountries {
    _listenToRemoteConfig();
    return _remoteConfig.allowedCountries;
  }

  @override
  int get dobGapeYears {
    _listenToRemoteConfig();
    return _remoteConfig.dobGapeYears;
  }

  @override
  bool get enableNoOfApartments {
    _listenToRemoteConfig();
    return _remoteConfig.enableNoOfApartments;
  }

  @override
  String get imagesBaseUrl {
    _listenToRemoteConfig();
    return _remoteConfig.imagesBaseUrl;
  }

  @override
  bool get licenseMandatory {
    _listenToRemoteConfig();
    return _remoteConfig.licenseMandatory;
  }

  @override
  bool get tamayouzEnabled {
    _listenToRemoteConfig();
    return _remoteConfig.tamayouzEnabled;
  }

  @override
  bool get enableForceUpdate {
    _listenToRemoteConfig();
    return _remoteConfig.enableForceUpdate;
  }
  void _listenToRemoteConfig() {
    if (_isListenedToRemoteConfig) return;
    _isListenedToRemoteConfig = true;
    _firebaseRemoteConfig.onConfigUpdated.listen((_) async {
      if (await _firebaseRemoteConfig.activate()) {
        return _onRemoteConfigUpdated();
      }
    });
  }
}