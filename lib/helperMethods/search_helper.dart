import 'package:darent/controllers/dashboard_controller.dart';
import 'package:darent/utils/constants.dart';
import 'package:syncfusion_flutter_sliders/sliders.dart';
import 'package:get/get.dart';

import 'globalHelpers.dart';

class SearchHelper {
  static late DashboardController c;
  static const maxPrice = 5000;
  SearchHelper(DashboardController cont) {
    c = cont;
  }
  static Map defaultFilterForm = {
    "min_price": 1,
    "max_price": 9999,
    "checkin": formDateFormatCservice.format(DateTime.now()),
    "checkout": formDateFormatCservice
        .format(DateTime.now().add(const Duration(days: 1))),
  };
  static bool planTripCleared = true, filterCleared = true;
  static Map filterForm = defaultFilterForm;

  static setFormBeforeApi({planTripValue}) {
    if (c.unitCode.text.isNotEmpty) {
      SearchHelper.filterCleared = false;
    }
    if (planTripCleared && filterCleared) {
      filterForm['properties'] = "recommended";
      c.searcText.value = null;
    } else {
      List searchText = [];
      if (planTripValue != null && planTripValue.isNotEmpty) {
        if (planTripValue['checkin'] != null &&
            planTripValue['checkout'] != null) {
          c.checkin = planTripValue['checkin'];
          c.checkout = planTripValue['checkout'];
        }
        if ((planTripValue['location'] ?? '').isNotEmpty) {
          c.locationText = (Get.locale?.languageCode ?? 'en') == 'en'
              ? c.selectedCity.value?.name
              : c.selectedCity.value?.nameAr;
        }
        filterForm['location'] = planTripValue['location'];
        // analytics.logEvent(
        //   name: "city_selection",
        //   parameters: {
        //     'city_name': c.locationText??''});
        c.adults = planTripValue['adult_guest'] ?? 1;
        c.children = planTripValue['child_guest'] ?? 1;
        filterForm.addAll(planTripValue);
      }
      filterForm['checkin'] = formDateFormatCservice.format(c.checkin);
      filterForm['checkout'] = formDateFormatCservice.format(c.checkout);
      // if (c.adults != null && c.children != null) {
      //   searchText.add("${c.adults! + c.children!} ${Get.find<TranslationHelper>().translations.header.guest}");
      // }
      searchText.add(
          "${c.checkin.day}/${c.checkin.month} - ${c.checkout.day}/${c.checkout.month}");
      c.searcText.value = searchText.join(", ");
    }
  }
  static resetCheckInOut() {
    c.checkin = GlobalHelper.getAbsoluteNowDate();
    c.checkout = c.checkin.add(const Duration(days: 1));
  }
  static prepareFiltersForm({bool toApplyFilter=false}){
    SearchHelper.filterForm.remove("properties");
    // List propertyTypeIds = [];
    List spaceTypeIds = c.filters.value!.spaceType
        .where((e) => e.isChecked)
        .map((e) => e.id)
        .toList();
    List amenitiesIds = c.filters.value!.amenities
        .where((e) => e.isChecked)
        .map((e) => e.id)
        .toList();
    List amenitiesTypeIDs = c.filters.value!.safetyAmenities
        .where((e) => e.isChecked)
        .map((e) => e.id)
        .toList();
    amenitiesTypeIDs.addAll(c.filters.value!.houseRules
        .where((e) => e.isChecked)
        .map((e) => e.id)
        .toList());
    List districtss =
    c.districts.where((e) => e.isChecked).map((e) => e.id).toList();
    c.backupPropertyType = c.selectedPropertyType.value;

    // for (var item in c.filters.value!.property_type) {
    //   if (item.isChecked) {
    //     propertyTypeIds.add(item.id);
    //     // analytics.logEvent(
    //     //     name: "unit_event",
    //     //     parameters: {
    //     //       'id' : item?.id??'',
    //     //       'event_name' : item?.name??'',
    //     //     });
    //   }
    // }

    // for (var item in c.filters.value!.spaceType) {
    //   if (item.isChecked) {
    //     spaceTypeIds.add(item.id);
    //   }
    // }
    // for (var item in c.filters.value!.amenities) {
    //   if (item.isChecked) {
    //     amenitiesIds.add(item.id);
    //   }
    // }
    // for (var item in c.filters.value!.safetyAmenities) {
    //   if (item.isChecked) {
    //     amenitiesTypeIDs.add(item.id);
    //   }
    // }
    // for (var item in c.filters.value!.houseRules) {
    //   if (item.isChecked) {
    //     amenitiesTypeIDs.add(item.id);
    //   }
    // }
    var formData = {
      'min_price': c.priceRange.value.start > maxPrice
          ? maxPrice
          : c.priceRange.value.start,
      'max_price':
      c.priceRange.value.end > maxPrice ? maxPrice : c.priceRange.value.end,
    };
    if (c.backupPropertyType != null) {
      formData['property_type'] = c.selectedPropertyType.value;
    } else {
      SearchHelper.filterForm.remove("property_type");
    }
    if (spaceTypeIds.isNotEmpty) {
      formData['space_type'] = spaceTypeIds.join(",");
    } else {
      SearchHelper.filterForm.remove("space_type");
    }
    if (!toApplyFilter) {
      if (districtss.isNotEmpty) {
        formData['districts'] = districtss;
      } else {
        SearchHelper.filterForm.remove('districts');
      }
    }
    if (c.unitCode.text.trim().isNotEmpty) {
      formData['property_code'] = c.unitCode.text.trim();
    } else {
      SearchHelper.filterForm.remove('property_code');
    }

    if (amenitiesIds.isNotEmpty) {
      formData['amenities'] = amenitiesIds.join(",");
    } else {
      SearchHelper.filterForm.remove("amenities");
    }
    if (amenitiesTypeIDs.isNotEmpty) {
      formData['amenities_type'] = amenitiesTypeIDs.join(",");
    } else {
      SearchHelper.filterForm.remove("amenities_type");
    }
    if (c.selectedBookingType.value != null) {
      formData['booking_type'] =
      c.selectedBookingType.value == "instantBook" ? "instant" : "request";
    } else {
      SearchHelper.filterForm.remove('booking_type');
    }
    SearchHelper.filterForm['adult_guest'] = c.guestsNo.value;
    SearchHelper.filterForm['bedrooms'] =
    c.bedRoomsNo.value == "0" ? "Any" : c.bedRoomsNo.value;
    SearchHelper.filterForm['beds'] =
    c.bedsNo.value == '0' ? 'Any' : c.bedsNo.value;
    SearchHelper.filterForm['bathrooms'] =
    c.bathroomsNo.value == '0' ? 'Any' : c.bathroomsNo.value;
    SearchHelper.filterForm.addAll(formData);
  }
  static applyFilter({bool toApplyFilter = false}) {
    prepareFiltersForm(toApplyFilter:toApplyFilter);
    c.dataPage = 1;
    // sending selected city in plan trip value so that our city name overrides
    if (c.selectedCity.value != null) {
      c.getData(
          planTripValue: {"location": c.selectedCity.value!.id.toString()});
      c.destinationSelectedEventCalled();
    } else {
      c.getData();
    }
    Get.back();
  }

  static planTripSearch(value) {
    if (value != null) {
      filterForm.remove("districts");
      // c.selectedCity.value = null;
      // for (var item in c.cities) {
      //   item['isChecked'] = false;
      // }
      // c.selectedDistricts.value=null;
      if (value.isEmpty) {
        clearFilter();
      } else {
        filterForm.remove("properties");
        planTripCleared = false;
      }
      // else{
      //   if(value['location']!=null && value['location'].isNotEmpty){
      //     locationText = value['location'];
      //   }else{
      //     filterFormData.remove("location");
      //   }
      //   if(value['guest']!=null){
      //     searchText.add("${value['guest']} Guests");
      //   }
      // }
      // filterFormData.addAll(value);
      c.dataPage = 1;
      c.dataSize = 8;
      c.getData(planTripValue: value);
    }
  }

  static onSortChanged(String? value) async {
    if (value != null) {
      filterForm['sortBy'] = value;
      filterForm.remove("properties");
      filterForm['extra'] = "";
      c.dataPage = 1;
      if (value == 'nearestToCity') {}

      c.getData();
    }
  }

  // static clearPlanTrip(){
  //   c.locationText = null;
  //   c.searcText.value = null;
  //   filterForm.remove("location");
  //   filterForm.remove("adult_guest");
  //   filterForm.remove("child_guest");
  //   filterForm.remove("sortBy");
  //   filterForm.remove("extra");
  //   planTripCleared = true;
  // }
  static clearPropertyType() {
    c.selectedPropertyType.value = null;
    c.backupPropertyType = null;
  }

  static clearFilter({bool applyFilter = false}) {
    c.totalAvailablePages.value = 0;
    c.showResultsText.value = null;
    c.rangeError.value = null;
    c.priceRange.value = SfRangeValues(c.filters.value?.min_price ?? 5,
        c.filters.value?.max_price ?? maxPrice);
    c.minController.text = c.priceRange.value.start > maxPrice
        ? "$maxPrice+"
        : c.priceRange.value.start.toString();
    c.maxController.text = c.priceRange.value.end > maxPrice
        ? "$maxPrice+"
        : c.priceRange.value.end.toString();

    clearPropertyType();

    // for (var item in c.filters.value?.property_type ?? const []) {
    //   item.isChecked = false;
    // }
    for (var item in c.filters.value?.spaceType ?? const []) {
      item.isChecked = false;
    }
    for (var item in c.filters.value?.recommendedAmenities ?? const []) {
      item.isChecked = false;
    }
    bool containsTrue = SearchHelper.c.cities.any((e) => e.isChecked);
    if (containsTrue) {
      for (var city in SearchHelper.c.cities) {
        city.isChecked = false;
      }
    }
    for (var item in c.filters.value?.amenities ?? const []) {
      item.isChecked = false;
    }
    for (var item in c.filters.value?.safetyAmenities ?? const []) {
      item.isChecked = false;
    }
    for (var item in c.filters.value?.houseRules ?? const []) {
      item.isChecked = false;
    }
    for (var item in c.cities) {
      item.isChecked = false;
    }
    for (var item in c.districts) {
      item.isChecked = false;
    }
    resetCheckInOut();
    c.districts.clear();
    c.selectedCity.value = null;
    c.selectedDistricts.clear();
    c.unitCode.clear();
    c.guestsNo.value = 1;
    c.bedRoomsNo.value = "0";
    c.bedsNo.value = "0";
    c.bathroomsNo.value = "0";
    c.selectedBookingType.value = null;
    c.filters.refresh();
    filterForm['min_price'] = c.priceRange.value.start;
    filterForm['max_price'] = c.priceRange.value.end;
    c.locationText = null;
    c.searcText.value = null;
    filterForm.remove("location");
    filterForm.remove("child_guest");
    filterForm.remove("sortBy");
    filterForm.remove("extra");
    filterForm.remove("bedrooms");
    filterForm.remove("adult_guest");
    filterForm.remove("beds");
    filterForm.remove("bathrooms");
    filterForm.remove("property_type");
    filterForm.remove("amenities");
    filterForm.remove("amenities_type");
    filterForm.remove("space_type");
    filterForm.remove('districts');
    filterForm.remove('property_code');
    filterForm.remove('checkin');
    filterForm.remove('checkout');
    c.dataTotalPages = 1;
    filterCleared = true;
    planTripCleared = true;
    if (applyFilter) {
      Get.back();
      c.getData();
    }
  }
}
