import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/controllers/property_controller.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:darent/screens/listing_journey/new_property_manage_screens/landing_property_manage.dart';
import 'package:flutter/foundation.dart';
import 'package:webengage_flutter/webengage_flutter.dart';
import '../../helperMethods/translation_helper.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/models/host/dwelling_detail.dart';
import 'package:darent/models/host/dwelling_model.dart';
import 'package:darent/models/host/property_photo.dart';
import 'package:darent/screens/listing_journey/add_property10.dart';
import 'package:darent/screens/listing_journey/add_property11.dart';
import 'package:darent/screens/listing_journey/add_property12.dart';
import 'package:darent/screens/listing_journey/add_property2.dart';
import 'package:darent/screens/listing_journey/add_property3.dart';
import 'package:darent/screens/listing_journey/add_property4.dart';
import 'package:darent/screens/listing_journey/add_property5.dart';
import 'package:darent/screens/listing_journey/add_property6.dart';
import 'package:darent/screens/listing_journey/add_property7.dart';
import 'package:darent/screens/listing_journey/add_property8.dart';
import 'package:darent/screens/listing_journey/add_property9.dart';
import 'package:darent/screens/listing_journey/publish_listing.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:get/get.dart';

import '../../models/pagination_model.dart';
import '../../screens/listing_journey/add_property.dart';
import '../../screens/listing_journey/add_property13.dart';
import '../../screens/listing_journey/add_property14.dart';
import '../../utils/routes.dart';

class ListingHelper {
  static late HostDashboardController c;
  ListingHelper(controller) {
    c = controller;
  }
  static uploadPhotos() async {
    List<String> files =
        c.tempPhotos.where((item) => item.id == 0).map((e) => e.image).toList();
    List permanentDeleted = c.dwellingDetail!.propertyPhotos!
        .where((e) => e.softDelete)
        .map((e) => e.id)
        .toList();
    List tempDeleted =
        c.tempPhotos.where((e) => e.softDelete).map((e) => e.id).toList();
    int remains = (c.dwellingDetail!.propertyPhotos!.length + files.length) -
        permanentDeleted.length;
    if (remains < 5) {
      ViewsCommon.showSnackbar('Please upload ${5 - remains} more image(s).',
          keyword: DialogKeyword.info);
      return;
    }
    if (files.isNotEmpty ||
        tempDeleted.isNotEmpty ||
        permanentDeleted.isNotEmpty) {
      c.isLoading.value = true;
      Map<String, String> formData = {
        'edit': 1.toString(),
      };
      if (permanentDeleted.isNotEmpty) {
        formData['permanentDeleteFile[]'] = permanentDeleted.join(',');
      }
      if (tempDeleted.isNotEmpty) {
        formData['tempDeleteFile[]'] = tempDeleted.join(',');
      }
      final response = await ApiServices.imageUpload(
          "v3/listing/${c.dwellingDetail?.id}/photos",
          body: formData,
          fileKeyName: "file[]",
          imagePaths: files);
      if (response.status) {
        for (var item in c.dwellingDetail!.propertyPhotos!) {
          if (item.softDelete) {
            c.dwellingDetail!.tempPhotos!
                .add(PropertyPhoto(item.id, item.image, remove: '1'));
          }
        }

        c.dwellingDetail!.propertyPhotos!
            .removeWhere((e) => e.softDelete || e.id == 0);
        c.dwellingDetail!.tempPhotos!
            .removeWhere((e) => e.softDelete || e.id == 0);

        for (int i = 0; i < response.data['photosid'].length; i++) {
          (c.dwellingDetail!.status == 'Listed'
                  ? c.tempPhotos
                  : c.dwellingDetail!.propertyPhotos!)
              .add(PropertyPhoto(response.data['photosid'][i], files[i]));
        }
        c.coverIndex = c.dwellingDetail!.tempPhotos
                ?.indexWhere((e) => e.id == response.data['cover_photo']) ??
            0;
        ViewsCommon.showSnackbar(Get.find<TranslationHelper>()
            .translations
            .hostListing
            .imagesUploadMsg!);
      }
    }
    c.isLoading.value = false;
  }

  static setPrice() {
    if (c.dwellingDetail != null) {
      for (String key
          in c.dwellingDetail!.propertyPrice!.specialDaysPrice!.keys) {
        ViewsCommon.priceControllers[key]!.text =
            (c.dwellingDetail!.propertyPrice!.specialDaysPrice![key] ?? 50)
                .toString();
      }
      ViewsCommon.priceControllers['price']!.text =
          c.dwellingDetail!.propertyPrice!.price.toString();
    }
  }

  static submitPrice() async {
    for (String key in ViewsCommon.priceControllers.keys) {
      int? number = int.tryParse(ViewsCommon.priceControllers[key]!.text);
      if (number == null || number < 50) {
        ViewsCommon.showSnackbar(
            Get.find<TranslationHelper>()
                .translations
                .listing
                .minPriceValidation!,
            keyword: DialogKeyword.warning);
        return;
      }
    }
    c.isLoading.value = true;
    Map formData = {};
    for (String fieldName in ViewsCommon.priceControllers.keys) {
      formData[fieldName == 'price' ? fieldName : 'price_$fieldName'] =
          ViewsCommon.priceControllers[fieldName]?.text ?? '0';
    }
    updateProperty("price", formData).then((status) {
      if (status && c.dwellingDetail != null) {
        for (String fieldName in ViewsCommon.priceControllers.keys) {
          c.dwellingDetail!.propertyPrice!.specialDaysPrice![fieldName] =
              ViewsCommon.priceControllers[fieldName]?.text;
        }
        c.dwellingDetail!.propertyPrice!.price =
            int.parse(ViewsCommon.priceControllers['price']?.text ?? "50");
      }
    });
  }

  static submitExtraPrice(key, extraPrice) async {
    if (c.extraPriceKey.currentState?.validate() ?? false) {
      c.isLoading.value = true;

      updateProperty('$key', {'$key': c.extraPrice.text}).then((status) {
        if (status) {
          Get.until((r) => Get.isBottomSheetOpen == false);
          if (key == 'cleaning_fee') {
            c.dwellingDetail?.propertyPrice?.cleaningFee =
                int.parse(c.extraPrice.text);
          } else {
            c.dwellingDetail?.propertyPrice?.securityFee =
                int.parse(c.extraPrice.text);
          }
          c.update(['detail']);
        }
      });
    }
  }

  static submitQuestion() async {
    c.isLoading.value = true;
    final response = await ApiServices.postApi(
        "v1/listing/${c.dwellingDetail?.id}/question",
        body: {"booking_type": c.instantBook.value},
        isAuth: true);
    c.isLoading.value = false;
    if (response.status) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.listing.successUpdated!);
      c.dwellingDetail!.bookingType = c.instantBook.value;
      c.update(['detail']);
    }
  }

  static resetHouseRules(_) {
    List amenities = c.dwellingDetail!.amenities!.split(',');
    for (var item in SearchHelper.c.filters.value!.houseRules) {
      item.isChecked = amenities.contains('${item.id}');
    }
  }

  static resetAmenities(_) {
    List amenities = c.dwellingDetail!.amenities!.split(',');
    for (var item in SearchHelper.c.filters.value!.amenities) {
      item.isChecked = amenities.contains('${item.id}');
    }
    for (var item in SearchHelper.c.filters.value!.safetyAmenities) {
      if (item.isChecked) {
        item.isChecked = amenities.contains('${item.id}');
      }
    }
  }

  static submitAmenities() {
    List selectedAmenities = [];
    List selectedHouseRules = [];
    List customHouseRules = [];
    for (var item in SearchHelper.c.filters.value!.amenities) {
      if (item.isChecked) {
        selectedAmenities.add(item.id);
      }
    }
    for (var item in SearchHelper.c.filters.value!.safetyAmenities) {
      if (item.isChecked) {
        selectedAmenities.add(item.id);
      }
    }
    for (var item in SearchHelper.c.filters.value!.houseRules) {
      if (item.isChecked) {
        selectedHouseRules.add(item.id);
      }
    }
    for (var item in SearchHelper.c.customHouseRules) {
      customHouseRules.add(item.title);
    }
    if (Get.currentRoute == '/EditFeatures' && selectedAmenities.isEmpty) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.listing.amenityValidation!,
          keyword: DialogKeyword.warning);
      return;
    }
    c.isLoading.value = true;
    List finalSelected = [...selectedAmenities, ...selectedHouseRules];
    updateProperty("amenities", {
      "amenities": finalSelected,
      // will enable when API is ready
      "custom_amenities": customHouseRules
    }).then((value) {
      c.dwellingDetail!.amenities = finalSelected.join(',');
      c.update(['detail']);
    });
  }

  static submitBasic() async {
    c.isLoading.value = true;
    updateProperty(
            "basic", {"adult": c.adults.value, "children": c.children.value})
        .then((status) {
      if (status) {
        c.dwellingDetail!.adultGuest = c.adults.value;
        c.dwellingDetail!.childrenGuest = c.children.value;
        SearchHelper.c.filters.refresh();
      }
    });
  }

  static submitCancellation() async {
    c.isLoading.value = true;
    updateProperty("cancellation", {"cancellation": c.cancellation.value})
        .then((status) {
      if (status) {
        c.dwellingDetail!.cancellation = c.cancellation.value;
        c.update(['detail']);
      }
    });
  }

  static void changeCancellation(String? value) {
    c.cancellation.value = value!;
  }

  static calendarExport(id) async {
    c.isLoading.value = true;
    String? link;
    final response =
        await ApiServices.getApi('v1/icalender-export/$id', isAuth: true);
    if (response.status) {
      link = response.data['link'];
    }
    c.isLoading.value = false;
    return link;
  }

  static calendarImport({IcalImport? item, int? listingId}) async {
    if (c.importCalendarKey.currentState?.validate() ?? false) {
      c.isLoading.value = true;
      String endPoint = item != null
          ? 'v1/calendar-urls-update/${item.id}'
          : 'v1/icalender-import';
      Map formData = {
        "name": c.calendarName.text,
        "url": c.calendarUrl.text,
        "color": c.calenderColor.value,
        "customcolor": "none"
      };
      if (item == null) {
        formData['property_id'] = listingId ?? c.dwellingDetail?.id;
      }
      final response =
          await ApiServices.postApi(endPoint, body: formData, isAuth: true);
      if (response.status) {
        if (listingId != null) {
          goFromImport();
        } else {
          if (item != null) {
            item.icalendarName = ListingHelper.c.calendarName.text;
            item.icalendarUrl = ListingHelper.c.calendarUrl.text;
          } else {
            c.dwellingDetail!.icalImports.insert(
                0,
                IcalImport(
                    response.data['icalImport'],
                    ListingHelper.c.calendarUrl.text,
                    ListingHelper.c.calendarName.text));
          }
          c.dwellingDetail!.icalImports.refresh();
          Get.back();
        }
      }
      c.isLoading.value = false;
      ViewsCommon.showSnackbar(item != null
          ? 'Calendar updated successfully'
          : Get.find<TranslationHelper>()
              .translations
              .listing
              .calenderImported!);
    }
  }

  static goFromImport() {
    if (userModel.value?.contactMethodAdded == false) {
      Get.to(() => const AddProperty13());
    } else if (userModel.value?.yaqeenVerified == false && kReleaseMode) {
      Get.to(() => const AddProperty14());
    } else {
      Get.to(() => const PublishListing());
    }
  }

  static deleteIcal(int i) {
    ApiServices.postApi(
            'v1/calendar-urls-remove/${c.dwellingDetail!.icalImports[i].id}',
            isAuth: true)
        .then((response) {
      print(response.data);
      if (response.status) {
        c.dwellingDetail!.icalImports.removeAt(i);
      }
    });
  }

  static clearImportFields() {
    c.calendarName.clear();
    c.calendarUrl.clear();
    c.calenderColor.value = null;
  }

  static submitWeeklyDiscount({bool getPrice = false}) async {
    c.isLoading.value = true;
    if (getPrice) {
      updatePropertyToGetPrice(
              "weekly_discount", {"weekly_discount": c.weeklyDiscount.text})
          .then((value) {
        if (value != null) {
          c.dwellingDetail?.propertyPrice?.weeklyDiscount =
              int.parse(c.weeklyDiscount.text);
          c.priceBeforeDiscount.value =
              checkAndGetDouble(value['weekly_amount'] ?? 0.0);
          c.priceAfterDiscount.value =
              checkAndGetDouble(value['weekly_amount_with_discount'] ?? 0.0);
          c.update(['detail']);
        }
      });
    } else {
      updateProperty(
              "weekly_discount", {"weekly_discount": c.weeklyDiscount.text})
          .then((status) {
        if (status) {
          c.dwellingDetail?.propertyPrice?.weeklyDiscount =
              int.parse(c.weeklyDiscount.text);
        }
      });
    }
  }

  static submitMonthlyDiscount({bool getPrice = false}) async {
    c.isLoading.value = true;
    if (getPrice) {
      updatePropertyToGetPrice(
              "monthly_discount", {"monthly_discount": c.monthlyDiscount.text})
          .then((value) {
        if (value != null) {
          c.dwellingDetail?.propertyPrice?.monthlyDiscount =
              int.parse(c.monthlyDiscount.text);
          c.priceBeforeDiscount.value =
              checkAndGetDouble(value['monthly_amount'] ?? 0.0);
          c.priceAfterDiscount.value =
              checkAndGetDouble(value['monthly_amount_with_discount'] ?? 0.0);
          c.update(['detail']);
        }
      });
    } else {
      updateProperty(
              "monthly_discount", {"monthly_discount": c.monthlyDiscount.text})
          .then((status) {
        if (status) {
          c.dwellingDetail?.propertyPrice?.monthlyDiscount =
              int.parse(c.monthlyDiscount.text);
        }
      });
    }
  }

  static submitTime() async {
    c.isLoading.value = true;
    String checkIn =
        "${GlobalHelper.twoNumberFormat(c.checkIn.value.inHours)}:${GlobalHelper.twoNumberFormat(c.checkIn.value.inMinutes % 60)}:00";
    String checkOut =
        "${GlobalHelper.twoNumberFormat(c.checkOut.value.inHours)}:${GlobalHelper.twoNumberFormat(c.checkOut.value.inMinutes % 60)}:00";
    updateProperty("nightsandtime", {
      "checkinTime": checkIn,
      "checkoutTime": checkOut,
    }).then((status) {
      if (status) {
        c.dwellingDetail!.checkinTime = checkIn;
        c.dwellingDetail!.checkoutTime = checkOut;
        c.update(['detail']);
      }
    });
  }

  static setCoverPhoto() async {
    final photo = c.dwellingDetail!.propertyPhotos!
        .firstWhereOrNull((e) => e.coverPhoto == 1);
    if (photo?.id != c.dwellingDetail!.propertyPhotos![c.coverIndex].id) {
      c.isLoading.value = true;
      await ApiServices.postApi("v1/editlisting/updatecover",
          body: {
            "property_id": c.dwellingDetail?.id,
            "photo_id": c.dwellingDetail!.propertyPhotos![c.coverIndex].id
          },
          isAuth: true);
      final coverI = c.dwellingDetail!.propertyPhotos!
          .firstWhereOrNull((e) => e.coverPhoto == 1);
      if (coverI != null) {
        coverI.coverPhoto = 0;
      }
      c.dwellingDetail!.propertyPhotos![c.coverIndex].coverPhoto = 1;
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.listing.successUpdated!);
      c.isLoading.value = false;
    }
  }

  static toggleVisibility() async {
    if (c.dwellingTab.value != (c.status.value ? "Listed" : "Unlisted")) {
      c.isLoading.value = true;
      int index = c.dwellings.indexWhere((e) => e.id == c.dwellingDetail!.id);
      DwellingModel dwelling = c.dwellings[index];
      c.dwellings.removeAt(index);
      c.dwellingDetail!.visibility = c.status.value ? 1 : 0;
      if (c.dwellings.isEmpty) {
        c.dwellings.insert(0, dwelling);
      }
      updateProperty("status", {
        "status": c.dwellingDetail!.visibility == 1 ? "Listed" : "Unlisted",
      }).then((status) {
        if (status) {
          Get.until((route) => route.isFirst);
          ViewsCommon.showSnackbar(Get.find<TranslationHelper>()
              .translations
              .listing
              .successUpdated!);
          c.update(['detail']);
          c.allProperties();
        }
      });
    }
  }

  static setLocation() {
    completeListing(c.dwellingDetail!, step: "location");
  }

  static Future<bool> updateProperty(String step, Map form,
      {bool isSubmitted = false}) async {
    final response = await ApiServices.postApi(
        "v1/update/property/${(c.dwellingDetail?.id) ?? c.selectedDwelling.value}?step=$step",
        body: form,
        isAuth: true);
    if (response.status) {
      updatePropertyEventCalled(step);

      ViewsCommon.showSnackbar(isSubmitted
          ? Get.find<TranslationHelper>().translations.listing.successSubmit!
          : Get.find<TranslationHelper>().translations.listing.successUpdated!);
    }
    c.isLoading.value = false;
    return response.status;
  }

  static Future<dynamic> updatePropertyToGetPrice(String step, Map form,
      {bool isSubmitted = false}) async {
    final response = await ApiServices.postApi(
        "v1/update/property/${c.dwellingDetail?.id}?step=$step",
        body: form,
        isAuth: true);
    if (response.status) {
      updatePropertyEventCalled(step);
      ViewsCommon.showSnackbar(isSubmitted
          ? Get.find<TranslationHelper>().translations.listing.successSubmit!
          : Get.find<TranslationHelper>().translations.listing.successUpdated!);
    }
    c.isLoading.value = false;
    return response.data;
  }

  static updatePropertyEventCalled(String step) async {
    String? houseRules = [
      ...SearchHelper.c.filters.value!.houseRules.map((e) => e.title),
      ...SearchHelper.c.customHouseRules.map((e) => e.title),
    ].join(",");

    String photos = c.dwellingDetail?.images?.join(",") ?? '';

    String calendar = "{$baseUrl/icalender/export/${c.dwellingDetail?.id}.ics}";

    Map<String, dynamic> dataMap = {
      "Unit code": c.dwellingDetail?.propertyCode,
      "Price": c.dwellingDetail?.propertyPrice?.price ?? 0,
      "Calendar": calendar,
      "Photos": photos,
      "Checkin Date": c.dwellingDetail?.checkinTime ?? "",
      "Checkout Date": c.dwellingDetail?.checkoutTime ?? "",
      "Checkin Time": c.dwellingDetail?.checkinTime ?? "",
      "Checkout Time": c.dwellingDetail?.checkoutTime ?? "",
      "Title": c.dwellingDetail?.name ?? "",
      "Description": c.dwellingDetail?.propertyDescription?.summary.value ?? "",
      "Home rule": houseRules,
      "Cleaning fee": c.dwellingDetail?.propertyPrice?.cleaningFee ?? "",
      "Bank account details":
          c.dwellingDetail?.propertyPrice?.weeklyDiscount ?? "",
      "Create a weekly and monthly discount":
          "${c.dwellingDetail?.propertyPrice?.weeklyDiscount ?? ""}, ${c.dwellingDetail?.propertyPrice?.monthlyDiscount ?? ""}",
      "Update type": step,
      "Cancellation Policy": c.cancellation.value,
    };
    await WebEngagePlugin.trackEvent("Host Edit property", dataMap);
  }

  static refreshDwellings() {
    if (Get.isRegistered<PropertyController>()) {
      PropertyController propC = Get.find();
      if (!propC.isComplete) {
        WebEngagePlugin.trackEvent('Host property data incomplete', {
          'Host ID': userModel.value?.id,
          'Unit Code': propC.unitCode,
          'Rejection Date': formDateFormat.format(DateTime.now()),
        });
      }
      Get.delete<PropertyController>(force: true);
    }
    // c.dwellings.clear();
    // if(c.dwellingTab.value=='Processing'){
    //   c.dwellings.value['Unlisted'] = null;
    // }else if(c.dwellingTab.value=='Unlisted'){
    //   c.dwellings.value['Processing'] = null;
    // }else{
    //   c.dwellings.value['Unlisted'] = null;
    //   c.dwellings.value['Processing'] = null;
    // }
    c.getDwellings(refresh: true);
    c.getCalendarProperties();
  }

  static completeListing(DwellingDetail dwelling, {String? step}) {
    if (step != null) {
      dwelling.missedStep = step;
    }
    Get.delete<PropertyController>(force: true);
    PropertyController properC =
        Get.put(PropertyController(isEdit: step != null));
    properC.unitCode = dwelling.propertyCode;
    properC.listPropertyId = dwelling.id;
    properC.spaceTypes.value = SearchHelper.c.filters.value!.spaceType
        .where((item) => item.propertyType == dwelling.propertyType)
        .toList();
    properC.spaceType.value =
        properC.spaceTypes.firstWhereOrNull((i) => i.id == dwelling.spaceType);

    properC.licenseMandatory = dwelling.licenseMandatory == true;

    properC.name.text = dwelling.name ?? "";
    properC.nameAr.text = dwelling.nameAr ?? "";

    properC.summary.text = dwelling.propertyDescription?.summary.value ?? "";
    properC.summaryAr.text =
        dwelling.propertyDescription?.summaryAr.value ?? "";
    properC.documentUploaded.value = dwelling.licenseVerifiedAt != null;

    if (dwelling.propertyPhotos?.isNotEmpty ?? false) {
      properC.propertyImages.value = dwelling.propertyPhotos!;
      properC.coverImage.value = dwelling.propertyPhotos!
          .firstWhereOrNull((item) => item.coverPhoto == 1);
      properC.coverImage.value ??= dwelling.propertyPhotos!.first;
      // properC.documentUploaded.value = dwelling
    }

    List amenities = dwelling.amenities?.split(',') ?? [];
    List amen = [];
    List houseRules = [];
    for (var item in SearchHelper.c.filters.value?.amenities ?? []) {
      item.isChecked = amenities.contains("${item.id}");
      if (amenities.contains("${item.id}")) {
        amen.add(item.id);
      }
    }
    for (var item in SearchHelper.c.filters.value?.houseRules ?? []) {
      item.isChecked = amenities.contains("${item.id}");
      if (amenities.contains("${item.id}")) {
        houseRules.add(item.id);
      }
    }

    setPrice();
    properC.minNights.text = dwelling.minNights?.toString() ?? '1';
    properC.maxNights.text = dwelling.maxNights?.toString() ?? '30';
    if (dwelling.bookingType != null) {
      properC.hostingType.value = dwelling.bookingType!;
    }
    // if (dwelling.propertyPrice?.price != 0) {
    //   ViewsCommon.priceControllers['price']!.text =
    //       dwelling.propertyPrice!.price.toString();
    // }

    properC.getCurrentLocation(isFirst: true);
    _navigation(dwelling.missedStep);
  }

  static _navigation(String? missedStep) {
    List screens = [];
    switch (missedStep) {
      case "spacetype":
        {
          // Get.to(() => const AddProperty2())!.then((value) {
          //   refreshUnlisted();
          // });
          screens.add(AddProperty2());
          break;
        }
      case "location":
      case "confirmLocation":
        {
          // Get.to(() => AddProperty3(),popGesture: false)?.then((value) {
          //   if (step != null) {
          //     c.getDwellingDetail(c.dwellingDetail?.slug);
          //   } else {
          //     refreshUnlisted();
          //   }
          // });
          screens = [AddProperty2(), AddProperty3()];
          break;
        }
      case "numberofRoom":
        {
          // Get.to(() => const AddProperty4())!.then((value) {
          //   refreshUnlisted();
          // });
          screens = [AddProperty2(), AddProperty3(), AddProperty4()];
          break;
        }
      case "amenities":
        {
          // Get.to(() => const AddProperty5())!.then((value) {
          //   refreshUnlisted();
          // });
          screens = [
            AddProperty2(),
            AddProperty3(),
            AddProperty4(),
            AddProperty5()
          ];
          break;
        }
      case "photos":
      case "setCover":
        {
          // Get.to(() => const AddProperty6())!.then((value) {
          //   refreshUnlisted();
          // });
          screens = [
            AddProperty2(),
            AddProperty3(),
            AddProperty4(),
            AddProperty5(),
            AddProperty6()
          ];
          break;
        }
      case "title":
        {
          // Get.to(() => const AddProperty7())!.then((value) {
          //   refreshUnlisted();
          // });
          screens = [
            AddProperty2(),
            AddProperty3(),
            AddProperty4(),
            AddProperty5(),
            AddProperty6(),
            AddProperty7()
          ];
          break;
        }
      case "description":
        {
          Get.to(() => const AddProperty8())!.then((value) {
            refreshUnlisted();
          });
          screens = [
            AddProperty2(),
            AddProperty3(),
            AddProperty4(),
            AddProperty5(),
            AddProperty6(),
            AddProperty7(),
            AddProperty8()
          ];
          break;
        }
        break;
      case "basics":
      case "basic":
        {
          // Get.to(() => const AddProperty9())!.then((value) {
          //   refreshUnlisted();
          // });
          screens = [
            AddProperty2(),
            AddProperty3(),
            AddProperty4(),
            AddProperty5(),
            AddProperty6(),
            AddProperty7(),
            AddProperty8(),
            AddProperty9()
          ];
          break;
        }
      case "pricing":
      case "price":
        {
          //   Get.to(() => const AddProperty10())!.then((value) {
          //     refreshUnlisted();
          //   });
          screens = [
            AddProperty2(),
            AddProperty3(),
            AddProperty4(),
            AddProperty5(),
            AddProperty6(),
            AddProperty7(),
            AddProperty8(),
            AddProperty9(),
            AddProperty10()
          ];
          break;
        }
      case "nightsandtime":
        {
          // Get.to(() => const AddProperty11())!.then((value) {
          //   refreshUnlisted();
          // });
          screens = [
            AddProperty2(),
            AddProperty3(),
            AddProperty4(),
            AddProperty5(),
            AddProperty6(),
            AddProperty7(),
            AddProperty8(),
            AddProperty9(),
            AddProperty10(),
            AddProperty11()
          ];
          break;
        }
      case "booking":
      case "question":
        {
          // Get.to(() => const AddProperty12())!.then((value) {
          //   refreshUnlisted();
          // });
          screens = [
            AddProperty2(),
            AddProperty3(),
            AddProperty4(),
            AddProperty5(),
            AddProperty6(),
            AddProperty7(),
            AddProperty8(),
            AddProperty9(),
            AddProperty10(),
            AddProperty11(),
            AddProperty12()
          ];
          break;
        }
      case "reviewListing":
        {
          if (userModel.value?.contactMethodAdded == false) {
            // Get.to(() => const AddProperty13())?.then((value) {
            //   refreshUnlisted();
            // });
            screens = [
              AddProperty2(),
              AddProperty3(),
              AddProperty4(),
              AddProperty5(),
              AddProperty6(),
              AddProperty7(),
              AddProperty8(),
              AddProperty9(),
              AddProperty10(),
              AddProperty11(),
              AddProperty12()
            ];
          } else if (userModel.value?.yaqeenVerified == false) {
            // Get.to(() => const AddProperty14())!.then((value) {
            //   refreshUnlisted();
            // });
            screens = [
              AddProperty2(),
              AddProperty3(),
              AddProperty4(),
              AddProperty5(),
              AddProperty6(),
              AddProperty7(),
              AddProperty8(),
              AddProperty9(),
              AddProperty10(),
              AddProperty11(),
              AddProperty12(),
              AddProperty14()
            ];
          } else {
            // Get.to(() => const PublishListing())?.then((value) {
            //   refreshUnlisted();
            //   c.getCalendarProperties();
            // });
            screens = [
              AddProperty2(),
              AddProperty3(),
              AddProperty4(),
              AddProperty5(),
              AddProperty6(),
              AddProperty7(),
              AddProperty8(),
              AddProperty9(),
              AddProperty10(),
              AddProperty11(),
              AddProperty12(),
              PublishListing()
            ];
          }
          break;
        }
    }
    for (var item in screens) {
      Get.to(item)?.then((_) {
        if (Get.currentRoute.contains(Routes.hostHome)) {
          refreshUnlisted();
        }
        if (Get.previousRoute == '/PublishListing') {
          c.getCalendarProperties();
        }
      });
    }
  }

  static refreshUnlisted() {
    Get.delete<PropertyController>(force: true);
    c.dwellings.clear();
    // c.dwellings.value['Unlisted'] = null;
    c.getDwellings(refresh: true);
  }

  static gotoDuplicationCheckScreen() async {
    ListingHelper.c.isBtnLoading.value = true;
    Map form = {"page": 1, "size": 100, "property_status": "Processing"};
    final res = await ApiServices.postApi('v1/host/all-listing',
        body: form, isAuth: true);
    if (res.status) {
      c.inProgressDuplicationPagination =
          Pagination.fromJson(res.data['pagination']);
      c.inProgressDuplication.value = res.data['hostListing']
          .map<DwellingModel>((item) => DwellingModel.fromJson(item))
          .toList();
      c.isPropertyManageByDarentEnabled.value =
          res.data['auto_manage'] ?? false;
    }
    ListingHelper.c.isBtnLoading.value = false;
  }

  static goToDarentManagement() {
    Get.to(() => const LandingPageOfPropertyManage())?.then((_) {
      ListingHelper.refreshDwellings();
    });
  }

  static Future<bool> submitDarStay({required formData}) async {
    ListingHelper.c.isDarStayLoading.value = true;
    var response = await ApiServices.postApi('v1/host/request-management',
        body: formData, isAuth: true);
    if (response.status) {
      ViewsCommon.showSnackbar(
          Get.find<TranslationHelper>().translations.account.submitRequest,
          keyword: DialogKeyword.success);
    } else {
      ViewsCommon.showSnackbar(response.message,
          keyword: DialogKeyword.warning);
    }
    ListingHelper.c.isDarStayLoading.value = false;
    return response.status;
  }

  static gotoNewListing() {
    Get.to(() => const AddProperty())?.then((_) {
      ListingHelper.refreshDwellings();
    });
  }
}
