import 'package:darent/controllers/account_controller.dart';
// import 'package:flutter/material.dart' show DraggableScrollableController;
import 'package:get/get.dart';

import 'globalHelpers.dart';

class AuthHelper {
  static late AccountController c;
  static String? uuid;
  // static final homeAuthSheet = DraggableScrollableController();
  // static final detailAuthSheet = DraggableScrollableController();

  AuthHelper() {
    c = Get.put(AccountController());
  }
  // static bottomSheetListener(DraggableScrollableController controller) {
  //   if(controller.isAttached){
  //     try{
  //       if(controller.size<.145){
  //         GlobalHelper.removeFocus();
  //         c.clearLoginFields();
  //       }
  //     }catch(e){
  //       print(e);
  //     }
  //   }
  // }

  static checkAndSetGuesUuid(String? uuidApi) {
    if (GlobalHelper.storageBox.hasData('uuid')) {
      uuid = GlobalHelper.storageBox.read('uuid');
    } else {
      uuid = uuidApi;
    }
  }

  static removeGuestUuid() {
    uuid = null;
    GlobalHelper.storageBox.remove('uuid');
  }
}
