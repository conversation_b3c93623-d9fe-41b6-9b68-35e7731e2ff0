// class ConnectivityHelper{
//   static bool isConnected = false;
//   static Future<bool> checkInternet() async {
//     try {
//       final connectivityResult = await Connectivity().checkConnectivity();
//       if(connectivityResult == ConnectivityResult.mobile || connectivityResult == ConnectivityResult.wifi){
//         final result = await InternetAddress.lookup('google.com');
//         isConnected =  result.isNotEmpty && result[0].rawAddress.isNotEmpty;
//       }else{
//         isConnected =  false;
//       }
//     } catch (_) {
//       isConnected =  false;
//     }
//     return isConnected;
//   }
// }
