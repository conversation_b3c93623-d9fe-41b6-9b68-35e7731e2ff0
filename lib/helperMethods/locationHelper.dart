import 'package:darent/components/warning_dialog.dart';
import 'package:darent/utils/constants.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:location/location.dart' as location_package;
import 'package:permission_handler/permission_handler.dart';
import 'package:get/route_manager.dart';

class LocationHelper {
  static LatLng? currentLocation;
  static Future<bool> getCurrentLocation({bool showWarnings = true}) async {
    if (await Geolocator.isLocationServiceEnabled()) {
      final permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.always || permission == LocationPermission.whileInUse) {
        final position = await Geolocator.getCurrentPosition();
        currentLocation = LatLng(position.latitude, position.longitude);
        return true;
      } else {
        if (showWarnings) {
          Get.dialog(WarningDialog(
              title: "Permission Denied",
              description:"To use this feature, you need to enable location services.",
              keyword: DialogKeyword.info,
              confirmText: "Goto Settings",
              onConfirmed: () async {
                Get.back();
                openAppSettings();
              }));
        }
        return false;
      }
    } else {
      location_package.Location location = location_package.Location();
      final serviceStatus = await location.requestService();
      if (serviceStatus) {
        return await getCurrentLocation();
      }
      return false;
    }
  }
}