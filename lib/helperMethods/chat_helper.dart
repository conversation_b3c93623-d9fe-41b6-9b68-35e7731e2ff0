import 'package:darent/components/warning_dialog.dart';
import 'package:darent/controllers/chat_controller.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:flutter/material.dart';
import '../../helperMethods/translation_helper.dart';
import 'package:darent/models/chatHeadModel.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:get/get.dart';
import 'package:webengage_flutter/webengage_flutter.dart';

import '../utils/routes.dart';

class ChatHelper {
  static late ChatController c;
  ChatHelper() {
    c = Get.put(ChatController(), permanent: true);
  }
  static contactHost(int propertyId, start, end, int adults, int children,
      {int? chatHeadId, String? inquiryMessage}) async {
    if (chatHeadId != null) {
      await gotoChats(chatHeadId, isFirst: false);
    } else {
      Map formData = {
        "start_date": start is String ? start : formDateFormat.format(start),
        "end_date": end is String ? end : formDateFormat.format(end),
        "guest_adult": adults.toString(),
        "guest_child": children.toString()
      };
      if (inquiryMessage != null) {
        formData['message'] = inquiryMessage;
      }
      final response = await ApiServices.postApi(
          "v3/properties/$propertyId/inquiry",
          body: formData,
          isAuth: true);
      if (response.data['status'] == 'block') {
        handleBlockMsg(response.data['blocked_word']);
      } else if (response.status) {
        await gotoChats(response.data['chat_head_id']);
        return response.data['chat_head_id'];
      } else if (response.data['chat_head_id'] != null) {
        await gotoChats(response.data['chat_head_id']);
      }
    }
  }

  static gotoChats(headId,
      {isFirst = true, Map<String, dynamic>? webEngageData}) async {
    if (isFirst) {
      // chatC = Get.put(ChatController());
      c.selectedChatHead = await getChatHead(headId);
      SearchHelper.c.loadingNotify = null;
      SearchHelper.c.notifyList.refresh();
      if (c.selectedChatHead != null) {
        ChatHelper.c.scrollController = ScrollController();
        c.getChats(headId);
        Get.toNamed(Routes.inbox)?.then((value) {
          ChatHelper.c.clearMessages();
          ChatHelper.c.scrollController.dispose();
          ChatHelper.c.isLazyLoading.value = false;
        });
        c.getChatHeads();
      }
    } else {
      // c.isLoading.value = true;
      c.initWithInquiry(headId);
    }
    if (webEngageData != null) {
      WebEngagePlugin.trackEvent('Message the Host', webEngageData);
    }
  }

  static Future<ChatHeadModel?> getChatHead(id) async {
    final res = await ApiServices.getApi(
        "v3/${isHost ? "host" : "guest"}/properties/chats/$id",
        isAuth: true);
    if (res.status) {
      return ChatHeadModel.fromJson(res.data['chat_head']);
    }
    return null;
  }

  static handleBlockMsg(List blocked) {
    final tr = Get.find<TranslationHelper>().translations.hostDashboard;
    Get.dialog(WarningDialog(
      isIcon: false,
      title: tr.sorryCantSendMsg!,
      description:
          '${tr.linksCantShare}\n\n${tr.removeInfo}\n\n${blocked.join(', ')}',
      keyword: DialogKeyword.warning,
      confirmText: tr.editMessage,
      largeText: true,
    ));
  }
}
