import 'dart:async';
import 'package:app_links/app_links.dart';
class DeepLinkHelper{
  static String? code;
  static Uri? deepLink;
  static final AppLinks _appLinks = AppLinks();
  static StreamSubscription<Uri>? _linkSubscription;
  DeepLinkHelper(){
    _linkSubscription = _appLinks.uriLinkStream.listen((uri) {
      code = uri.queryParameters['code'];
      // if(uri.path.contains(Routes.properties)){
      //   print('Going to tohe Deep link?');
      //   Get.to(()=>PropertyDetail(slug: uri.pathSegments.last),preventDuplicates: false);
      // }
    });
  }
  static retrieveDynamicLink() async {
    var appLink = await _appLinks.getInitialAppLink();

    if (appLink != null) {
      code = appLink.queryParameters['code'];

      // final languageCode = appLink.pathSegments.firstWhere(
      //       (segment) => segment == 'ar' || segment == 'en',
      //   orElse: () => 'en',
      // );

      deepLink = appLink;

      // Update app locale
      // await Get.updateLocale(Locale(languageCode));
      // deepLink = appLink.path.replaceFirst('/$languageCode', '').replaceAll(baseUrl, '');

      // Timer(const Duration(milliseconds: 2500), () {
      //   if(Get.isRegistered<PropertyDetailController>()){
      //     Get.until((route) => route.isFirst);
      //   };
      //   Get.toNamed(appLink.toString().replaceAll(baseUrl, ""));
      // });
    }
  }
  static dispose(){
    if(_linkSubscription!=null){
      _linkSubscription!.cancel();
    }
  }
}