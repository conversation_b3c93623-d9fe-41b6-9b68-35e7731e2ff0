import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/utils/constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:http/http.dart' as http;
import 'package:tabby_flutter_inapp_sdk/tabby_flutter_inapp_sdk.dart';

class GlobalHelper {
  static final tabbySdk = TabbySDK();
  static final storageBox = GetStorage();
  static removeFocus() {
    if (FocusManager.instance.primaryFocus?.hasFocus ?? false) {
      FocusManager.instance.primaryFocus!.unfocus();
    }
  }
  static DateTime getAbsoluteNowDate(){
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);

  }
  static bool checkStringIsArabicUsingReg(String text) {
    final RegExp arabicRegExp =
        RegExp(r'''^[\u0600-\u06FF0-9\s/'‘’“”"():,.?!`&-]+$''');
    return arabicRegExp.hasMatch(text.trim());
  }

  static bool checkStringIsEnglishUsingReg(String text) {
    final RegExp englishRegExp = RegExp(r"^[a-zA-Z0-9\s/'‘’“”():,.?!`&-]+$");
    return englishRegExp.hasMatch(text);
  }

  static twoNumberFormat(int num) {
    return num > 9 ? num : "0$num";
  }

  static to12Hours(int num) {
    return num == 0
        ? 12
        : num > 12
            ? num - 12
            : num;
  }

  static to12HoursString(String timeString) {
    List list = timeString.split(":");
    int tempHour = int.tryParse(list[0]) ?? 0;
    int hour = 0;
    int minutes = int.tryParse(list[1]) ?? 0;
    late String timePeriod;
    if (tempHour == 0) {
      hour = 12;
      timePeriod = "am";
    } else if (tempHour == 12) {
      hour = 12;
      timePeriod = "pm";
    } else if (tempHour > 12) {
      hour = tempHour - 12;
      timePeriod = "pm";
    } else if (tempHour == 12) {
      hour = tempHour;
      timePeriod = "pm";
    } else {
      hour = tempHour;
      timePeriod = "am";
    }
    return "${twoNumberFormat(hour)}:${twoNumberFormat(minutes)} $timePeriod";
  }

  static bool isBetweenDates(DateTime date, DateTime start, DateTime end) {
    return date.isAfter(start.subtract(const Duration(days: 1))) &&
        date.isBefore(end.add(const Duration(days: 1)));
  }

  static List<String> daysInBetween(DateTime startDate, DateTime endDate) {
    List<String> days = [];
    for (int i = 0; i <= endDate.difference(startDate).inDays; i++) {
      days.add(formDateFormat.format(startDate.add(Duration(days: i))));
    }
    return days;
  }

  static Offset getWidgetOffset(GlobalKey key) {
    final RenderBox renderBox =
        key.currentContext!.findRenderObject() as RenderBox;
    final Offset offset = renderBox.localToGlobal(Offset.zero);
    return offset;
  }
  static scrollToTarget(controller,targetKey) {
    final RenderBox? renderBox = targetKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      final position = renderBox.localToGlobal(Offset.zero).dy;
      controller.animateTo(
        position-100,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  static getBookingIndex(String val) {
    return val == 'pending-bookings'
        ? 0
        : val == 'upcoming-bookings'
            ? 1
            : val == 'ongoing-bookings'
                ? 2
                : val == 'history-bookings'
                    ? 3
                    : val == 'cancelled-bookings'
                        ? 4
                        : 5;
    //val=='expired-bookings'?5:6
  }

  static getHostReviewIndex(String val) {
    return val == 'receiver_id' ? 0 : 1;
  }

  static String resolveImageUrl(String imageUrl) {
    if (imageUrl.isEmpty || imageUrl == '/') {
      return '';
    }

    final baseUrl = (Get.find<RemoteConfig>().imagesBaseUrl ?? '').replaceFirst(RegExp(r'/$'), '');
    if (imageUrl.startsWith('/')) {
      final cleanedUrl = imageUrl.replaceFirst(RegExp(r'^/+'), '');
      final finalUrl = '$baseUrl/$cleanedUrl';
      // if (kDebugMode) print('Resolved relative image URL: $finalUrl');
      return finalUrl;
    }

    final uri = Uri.tryParse(imageUrl);
    if (uri?.host == 'static.darent.com' ||
        uri?.host == 'dev.darent.com' ||
        uri?.host == 'darent.com') {
      return imageUrl;
    }
    return '';
  }

  // Helper function to display the Network Image Provider
  static ImageProvider buildNetworkImageProvider({
    required String url,
    ImageProvider defaultOption = const AssetImage("assets/default-image.png"),
  }) {
    try {
      final imageUrl = resolveImageUrl(url);
      final uri = Uri.tryParse(imageUrl);

      if (imageUrl.isNotEmpty &&
          uri != null &&
          uri.hasScheme &&
          uri.host.isNotEmpty) {
        return NetworkImage(imageUrl);
      } else {
        // if (kDebugMode) {
        //   print("Invalid or empty URL after resolution: $imageUrl (original: $url)");
        // }
      }
    } catch (e, stack) {
      if (kDebugMode) {
        print("Error loading image: $e\nStack trace: $stack");
      }
    }
    return defaultOption;
  }

  // Cache for storing fetched SVG content
  static final Map<String, String> _svgCache = {};
  // Cache for storing Futures to prevent duplicate requests
  static final Map<String, Future<http.Response>> _futureCache = {};

  static String resolveSVGImageUrl(String imageUrl) {
    if (imageUrl.isEmpty || imageUrl == '/') {
      return '';
    }
    final baseUrl = (Get.find<RemoteConfig>().imagesBaseUrl ?? '').replaceFirst(RegExp(r'/$'), '');
    if (Uri.tryParse(imageUrl)?.host == 'static.darent.com' ||
        Uri.tryParse(imageUrl)?.host == 'dev.darent.com' ||
        Uri.tryParse(imageUrl)?.host == 'darent.com') {
      return imageUrl;
    }
    if (imageUrl.startsWith('/')) {
      final cleanedUrl = imageUrl.replaceFirst(RegExp(r'^/+'), '');
      return cleanedUrl.contains(".svg") ? '$baseUrl/icons/$cleanedUrl' : '$baseUrl/icons/$cleanedUrl.svg';
    }
    else{
      final cleanedUrl = imageUrl.replaceFirst(RegExp(r'^/+'), '');
      return cleanedUrl.contains(".svg") ? '$baseUrl/$cleanedUrl' : '$baseUrl/$cleanedUrl.svg';
    }
  }

  static Widget buildNetworkSvgWidget({
    required String url,
    double? width,
    double? height,
    Color? myColor,
    BoxFit fit = BoxFit.contain,
    Widget? defaultOption,
  }) {
    final svgUrl = resolveSVGImageUrl(url);
    // if (kDebugMode) {
    //   print("url = $url and svg url $svgUrl");
    // }

    if (svgUrl.isEmpty) {
      return defaultOption ??
          Container(
            width: width,
            height: height,
            color: Colors.grey[200],
            child: const Icon(Icons.image_not_supported),
          );
    }

    // Check if SVG content is already cached
    if (_svgCache.containsKey(svgUrl)) {
      return SvgPicture.string(
        _svgCache[svgUrl]!,
        width: width,
        height: height,
        fit: fit,
        colorFilter: myColor != null ? ColorFilter.mode(myColor, BlendMode.srcIn) : null,
        semanticsLabel: 'SVG Image',
      );
    }

    // Check if a Future is already in progress for this URL
    if (!_futureCache.containsKey(svgUrl)) {
      _futureCache[svgUrl] = http.get(Uri.parse(svgUrl));
    }

    return FutureBuilder<http.Response>(
      future: _futureCache[svgUrl],
      builder: (context, snapshot) {

        if (snapshot.connectionState == ConnectionState.waiting) {
          return defaultOption ?? Image.asset("assets/default-image.png", height: height, width: width);
        }

        if (snapshot.hasData) {
          final response = snapshot.data!;
          final body = response.body;

          if (response.statusCode == 200 && body.contains('<svg') && !body.contains('<Error')) {
            if (body.contains('font-weight="none"')) {
              if (kDebugMode) {
                print("Invalid font-weight detected in SVG for $svgUrl");
              }
            } else {
              _svgCache[svgUrl] = body;
              return SvgPicture.string(
                body,
                width: width,
                height: height,
                fit: fit,
                colorFilter: myColor != null ? ColorFilter.mode(myColor, BlendMode.srcIn) : null,
                semanticsLabel: 'SVG Image',
              );
            }
          }
        }

        // In case of invalid response or SVG content
        return defaultOption ??
            Container(
              width: width,
              height: height,
              color: Colors.grey[200],
              child: const Icon(Icons.image_not_supported),
            );
      },
    );
  }

  // Optional: Clear cache if needed (e.g., to refresh icons or free memory)
  static void clearSvgCache() {
    _svgCache.clear();
    _futureCache.clear();
  }
}
