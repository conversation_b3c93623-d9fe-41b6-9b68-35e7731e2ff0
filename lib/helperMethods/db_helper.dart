// import 'package:darent/models/homeProperty.dart';
// import 'package:flutter/material.dart';
// import 'package:sqflite/sqflite.dart';
// import 'package:path/path.dart';
//
//
// class DbHelper{
//   static Database? _database;
//
//   static init()async{
//     _database = await openDatabase(
//         join(await getDatabasesPath(), 'darent.db'),
//         onCreate: (db, version) {
//           db.execute(
//               "CREATE TABLE properties("
//                   "id INTEGER PRIMARY KEY,"
//                   "title TEXT,"
//                   "slug TEXT,"
//                   "photo TEXT,"
//                   "summary TEXT,"
//                   "rating TEXT,"
//                   "rating_count TEXT,"
//                   "price INTEGER,"
//                   "wishlist INTEGER,"
//                   "latitude REAL,"
//                   "longitude REAL,"
//                   "discount INTEGER,"
//                   "discounted_amount REAL,"
//                   "views TEXT,"
//                   "images TEXT,"
//                   "unit_code TEXT,"
//                   "location TEXT"
//                   ")"
//           );
//
//           db.execute(
//               "CREATE TABLE translation("
//                   "wishlist TEXT,"
//                   "inbox TEXT,"
//                   "home TEXT,"
//                   "booking TEXT,"
//                   "account TEXT,"
//                   "no_result_found TEXT,"
//                   "location TEXT,"
//                   "empty_wishlist_title TEXT,"
//                   "empty_inbox TEXT,"
//                   "no_notification TEXT,"
//                   "upcoming TEXT,"
//                   "ongoing TEXT,"
//                   "history TEXT,"
//                   "cancelled TEXT,"
//                   "no_upcoming_bookings TEXT,"
//                   "today TEXT,"
//                   "in_days TEXT,"
//                   "in_day TEXT,"
//                   "property_location TEXT,"
//                   "chat_with_the_host TEXT,"
//                   "view_profile TEXT,"
//                   "show_your_house_on_darent TEXT,"
//                   "show_house_on_darent_description TEXT,"
//                   "setting TEXT,"
//                   "account_info TEXT,"
//                   "account_manager TEXT,"
//                   "login_security TEXT,"
//                   "wallet TEXT,"
//                   "hosting TEXT,"
//                   "switch_to_hosting TEXT,"
//                   "pages TEXT,"
//                   "about_us TEXT,"
//                   "terms_and_conditions TEXT,"
//                   "privacy_policy TEXT,"
//                   "support TEXT,"
//                   "faq TEXT,"
//                   "customer_services TEXT,"
//                   "create_ticket TEXT,"
//                   "ticket_listing TEXT,"
//                   "language TEXT,"
//                   "browse_in TEXT,"
//                   "wishlist_subtitle TEXT,"
//                   "wishlist_desc TEXT,"
//                   "reservation_subtitle TEXT,"
//                   "reservation_desc TEXT,"
//                   "inbox_subtitle TEXT,"
//                   "inbox_desc TEXT,"
//                   "profile_subtitle TEXT,"
//                   "profile_desc TEXT,"
//                   "logout TEXT,"
//                   "button_text TEXT"
//                   ")"
//           );
//         },version: 1);
//   }
//
//   static insertPropertiesToDb(List<HomeProperty> properties)async{
//     int success = 0;
//     try {
//       if (await tableCount("properties") <= 0) {
//         await _database!.delete("properties");
//         for (HomeProperty property in properties) {
//           success = await _database!.insert(
//               "properties", HomeProperty().toJson(property));
//         }
//       }
//     }catch(e){
//       debugPrint("Getting Error while inert to DB ${e.toString()}");
//     }
//     return success==1;
//   }
//
//   static insertTranslationToDb(List<Map<String, dynamic>> translations)async{
//     int success = 0;
//     try {
//       if (await tableCount("translation") <= 0) {
//         await _database!.delete("translation");
//         for(var translation in translations)
//           {
//             success = await _database!.insert("translation", translation);
//           }
//       }
//     }catch(e){
//       debugPrint("Getting Error while inert translation to DB ${e.toString()}");
//     }
//     return success==1;
//   }
//
//   static tableCount(String tableName)async{
//     int? count = 0;
//     try {
//       count = Sqflite.firstIntValue(await _database!.rawQuery('SELECT COUNT(*) FROM $tableName'));
//     } catch (e) {
//       debugPrint("getting error while getting table count ${e.toString()}");
//     }
//     debugPrint("table count is = ${count.toString()} and condition is ${count! <= 0}");
//     return count;
//   }
//
//   static Future<List<HomeProperty>> getHomePageOfflineData()async{
//     List properties =[];
//     try {
//       properties = await _database!.query("properties");
//     }catch(e){
//       debugPrint("getting error while getting offline data ${e.toString()}");
//     }
//     return properties.map<HomeProperty>((item) => HomeProperty.fromDb(item)).toList();
//   }
//
//   static Future<List<Map<String, dynamic>>> getTranslationOfflineData()async{
//     List<Map<String, dynamic>> translation =[];
//     try {
//       translation = await _database!.query("translation");
//     }catch(e){
//       debugPrint("getting error while getting offline data ${e.toString()}");
//     }
//     return translation;
//   }
//
//   static bool get checkDb=>_database!=null && _database!.isOpen;
//   static Database? get getDb=>_database;
// }
//
//
//
//
