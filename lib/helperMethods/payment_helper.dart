import 'dart:io';

class PaymentMethod {
  int id;
  String name;
  PaymentMethod(this.id, this.name);
}

class PaymentHelper {
  static String? paymentGateway;
  static final PaymentHelper _singleton = PaymentHelper._internal(); //singleton
  factory PaymentHelper() {
    return _singleton;
  }
  PaymentHelper._internal();

  // new payment method in case whole payment with wallet will send this id
  static var walletPaymentMethod = PaymentMethod(44, 'Wallet');

  static final paymentMethods = [
    PaymentMethod(2, 'card'),
    PaymentMethod(12, 'stcPay'),
    PaymentMethod(101, 'mada'),
    PaymentMethod(100, 'Pay with Tabby'),
    if (Platform.isIOS) PaymentMethod(11, 'Apple Pay')
  ];
}
