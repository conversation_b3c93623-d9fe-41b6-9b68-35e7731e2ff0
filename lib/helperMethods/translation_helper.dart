import '../models/translationModel.dart';

class TranslationHelper{
  final Map<String,TranslationModel> translateKeywords = <String,TranslationModel>{};
  late TranslationModel translations;

  void setKeywords(Map data,String lang){
    translateKeywords['en'] =  TranslationModel.fromJson(data['en']);
    translateKeywords['ar'] =  TranslationModel.fromJson(data['ar']);
    setTranslations(lang);
  }

  void setTranslations(String lang) {
    translations = translateKeywords[lang]!;
  }
}