import 'package:darent/components/views_common.dart';
import 'package:darent/components/warning_dialog.dart';
import 'package:darent/controllers/dashboard_controller.dart';
import 'package:darent/controllers/property_detail_controller.dart';
import 'package:darent/controllers/wishlistController.dart';
import 'package:darent/analytics/analytics.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:darent/models/homeProperty.dart';
import 'package:darent/models/wishlist/wishlist_group_model.dart';
import 'package:darent/screens/authentication/login.dart';
import 'package:darent/screens/wishlist/add_wishlist_group.dart';
import 'package:darent/screens/wishlist/select_wishlist.dart';
import 'package:darent/utils/api_service.dart';
import 'package:darent/utils/constants.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:webengage_flutter/webengage_flutter.dart';
import '../utils/routes.dart';
import 'debouncer.dart';
import 'translation_helper.dart';

class WishlistHelper {
  static int wishlistGroupPage = 1, wishlistGroupTotal = 1;
  static late DashboardController c;
  static Debouncer debouncer = Debouncer();
  WishlistHelper(dC) {
    c = dC;
    getWishlistGroup();
  }
  static clearForm({String? name}) {
    c.wishlistNameController.text = name ?? "";
    c.isBtnLoading.value = false;
    c.isButtonDisabled.value = name == null;
  }

  static clearWishlist() {
    c.wishlistGroups.clear();
    wishlistGroupPage = 1;
    wishlistGroupTotal = 1;
  }

  static share(code) {
    ViewsCommon.share(
        '$baseUrl/${Get.locale?.languageCode ?? 'en'}/wishlists/$code',
        title: 'Wishlist',
        itemId: code);
  }

  static invite(code) {
    ApiServices.getApi("v1/collaborator?code=$code", isAuth: true);
    share(code);
  }

  static checkWishlist(HomeProperty item) async {
    if (isUser) {
      if (item.wishlist) {
        removeFromWishlist(item);
      } else {
        if (c.wishlistGroups.isNotEmpty) {
          Get.bottomSheet(SelectWishlist(item: item));
        } else {
          clearForm();
          Get.dialog(AddWishlistListGroup(property: item));
        }
      }
    } else {
      Get.to(const Login());
    }
  }

  static changeWishlistName(index) async {
    try {
      c.isBtnLoading.value = true;
      Map formData = {
        "id": c.wishlistGroups[index].id,
        "name": c.wishlistNameController.text
      };
      final response = await ApiServices.postApi("v1/update/wishlist",
          body: formData, isAuth: true);
      if (response.status) {
        Get.back();
        c.wishlistGroups[index].name = c.wishlistNameController.text;
        c.wishlistGroups.refresh();
        clearForm();
      }
      c.isBtnLoading.value = false;
    } catch (e) {
      c.isBtnLoading.value = false;
    }
  }

  static getWishlistGroup({refresh = false}) {
    if (refresh) {
      wishlistGroupPage = 1;
      c.dataLoading.value = true;
    } else {
      c.lazyLoader.value = true;
    }
    ApiServices.getApi("v1/all/group/wishlist?size=10&page=$wishlistGroupPage")
        .then((response) {
      if (response.status) {
        if (wishlistGroupPage > 1) {
          c.wishlistGroups.addAll(response.data['wishlist_groups']
              .where((i) => i['wishlist_properties'].isNotEmpty == true)
              .map<WishlistGroupModel>(
                  (item) => WishlistGroupModel.fromJson(item)));
        } else {
          c.wishlistGroups.value = (response.data['wishlist_groups'] as List)
              .where((i) => i['wishlist_properties'].isNotEmpty == true)
              .map<WishlistGroupModel>(
                  (item) => WishlistGroupModel.fromJson(item))
              .toList();
        }
        wishlistGroupTotal = response.data['pagination']['total_pages'];
      }
      (refresh ? c.dataLoading : c.lazyLoader).value = false;
    });
  }

  static removeFromWishlist(HomeProperty item) async {
    debouncer.run(() async {
      final response = await ApiServices.postApi("v1/toggle/wishlist",
          body: {"property_id": item.id}, isAuth: true);
      if (response.status) {
        ViewsCommon.showSnackbar(Get.find<TranslationHelper>()
            .translations
            .success
            .favouriteRemoveSuccess);
        item.wishlist = false;
        if (Get.currentRoute == "/" || Get.currentRoute == "/Dashboard") {
          c.data.refresh();
        } else if (Get.currentRoute.contains(Routes.propertySingle)) {
          updatePropertySingle(false);
          updateHome(item);
          c.data.refresh();
        } else if (Get.currentRoute == "/Wishlist") {
          updateWishlist(item);
        }
        getWishlistGroup();
      }
    });
  }

  static deleteWishlist(index) {
    Get.back();
    Get.dialog(WarningDialog(
        title: Get.find<TranslationHelper>().translations.modal.areYouSure,
        description: Get.find<TranslationHelper>()
            .translations
            .jqueryValidation
            .areYouSureToDelete,
        keyword: DialogKeyword.info,
        onConfirmed: () async {
          final response = await ApiServices.getApi(
              "v1/delete/wishlist/${c.wishlistGroups[index].id}",
              isAuth: true);
          Get.until((route) => route.isFirst);
          if (response.status) {
            c.wishlistGroups.removeAt(index);
            c.wishlistGroups.refresh();
            c.getData();
          }
        }));
  }

  static toggleWishlist(HomeProperty item, int wishlistGroupIndex) {
    debouncer.run(() async {
      try {
        c.wishlistLoading.value = item.slug;
        c.data.refresh();
        Map formData = {
          "wishlist_name_id": c.wishlistGroups[wishlistGroupIndex].id,
          "property_id": item.id
        };
        ResponseModel response = await ApiServices.postApi(
            "v1/add/property/wishlist",
            body: formData,
            isAuth: true);
        if (response.status) {
          ViewsCommon.showSnackbar(Get.find<TranslationHelper>()
              .translations
              .success
              .favouriteAddSuccess);
          item.wishlist = true;
          placeWishListEventCalled(item);
          if (Get.currentRoute.contains(Routes.propertySingle)) {
            updatePropertySingle(true);
            updateHome(item);
          }
          c.data.refresh();
        }
        getWishlistGroup();
        c.wishlistLoading.value = null;
        if (item.wishlist == true) {
          addToWishlistEvent(item);
        }
      } catch (e) {
        c.wishlistLoading.value = null;
      }
    });
  }

  static placeWishListEventCalled(HomeProperty item) async {
    Map<String, dynamic> mapData = {
      "Name": item.title,
      "Unit Code": item.unitCode,
      "Cost Per Night": "${item.price ?? 0}",
      "Category Name": item.propertyTypeName,
      "User": isHost ? "Host" : "Customer"
    };
    if (kDebugMode) {
      print("Mark as wishlist event wishlist $mapData");
    }

    // Track wishlist event with WebEngage (existing)
    await WebEngagePlugin.trackEvent('Mark as wishlist', mapData);
    await WebEngagePlugin.trackEvent('Place - Mark in Wishlitst', mapData);

    // Track wishlist event with Analytics Manager (using Google Sheets event name)
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.saveFavorite,
      eventAttributes: {
        AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
        AnalyticsAttributes.propertyId: item.id?.toString(),
        AnalyticsAttributes.propertyType: item.propertyTypeName,
        AnalyticsAttributes.city: item.location ?? "",
        AnalyticsAttributes.priceRange: item.price?.toString(),
        AnalyticsAttributes.timestamp: DateTime.now().toIso8601String(),
      },
    );
  }

  static addToWishlistEvent(item) {
    bool isHome = Get.currentRoute == Routes.home;
    int i = SearchHelper.c.data.indexWhere((item) => item.id == item.id);
    analytics.logAddToWishlist(parameters: {
      'value': item.price ?? 0,
      'currency': 'SAR',
      'item_type': item.propertyTypeName ?? '',
      //'item_city_name':'${isHome?SearchHelper.c.selectedCity.value?.name:' '}',
      'item_host_id': item.hostId ?? 0,
      'total_price': isHome
          ? '${SearchHelper.c.checkout.difference(SearchHelper.c.checkin).inDays * item.price!}'
          : item.price ?? 0
    }, items: [
      AnalyticsEventItem(
        itemId: item.id.toString(),
        affiliation: '',
        discount: item.discountedAmount ?? 0,
        index: i + 1,
        price: (item.price) / 1000000,
        quantity: isHome
            ? SearchHelper.c.checkout.difference(SearchHelper.c.checkin).inDays
            : 1,
      )
    ]);
  }

  static addWishlistGroup(HomeProperty property) async {
    try {
      c.isBtnLoading.value = true;
      Map formData = {
        "name": c.wishlistNameController.text,
        "property_id": property.id
      };
      final response = await ApiServices.postApi("v1/create/wishlist",
          body: formData, isAuth: true);
      if (response.status) {
        Get.back();
        ViewsCommon.showSnackbar(Get.find<TranslationHelper>()
            .translations
            .success
            .favouriteAddSuccess);
        placeWishListEventCalled(property);
        if (Get.currentRoute.contains(Routes.propertySingle)) {
          updatePropertySingle(true);
        }
        property.wishlist = true;
        updateHome(property);
        c.data.refresh();
        getWishlistGroup(refresh: true);
      }
      c.isBtnLoading.value = false;
    } catch (e) {
      c.isBtnLoading.value = false;
    }
  }

  static updateWishlist(HomeProperty item) {
    WishlistController wishlistC = Get.find();
    wishlistC.wishlist
        .removeWhere((wishlistItem) => wishlistItem.id == item.id);
    wishlistC.wishlist.refresh();
    updateHome(item);
  }

  static updateHome(HomeProperty property) {
    int index = c.data.indexWhere((item) => item.id == property.id);
    if (index > -1) {
      c.data[index].wishlist = property.wishlist;
      c.data.refresh();
    }
  }

  static updatePropertySingle(bool boolSet) {
    PropertyDetailController propDC = Get.find();
    propDC.data.value!.wishlist = boolSet;
    propDC.data.refresh();
  }
}
