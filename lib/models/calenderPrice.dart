class CalenderPrice{
  int? id;
  int? property_id;
  String? status;
  int? price;
  int? min_stay;
  int? min_day;
  String? date;
  String? type;
  String? color;
  CalenderPrice.fromJson(Map json){
    id = json['id'];
    property_id = json['property_id'];
    status = json['status'];
    price = json['price'];
    min_stay = json['min_stay'];
    min_day = json['min_day'];
    date = json['date'];
    type = json['type'];
    color = json['color'];
  }
}