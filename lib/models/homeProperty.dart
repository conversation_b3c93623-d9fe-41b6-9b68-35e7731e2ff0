import 'dart:convert';

import 'package:darent/models/propertyCheckoutPrice.dart';

class HomeProperty {
  int? id;
  // String? name;
  String? title;
  String? spaceTypeLang;
  String? slug;
  String? photo;
  String? summary;
  String? rating;
  String? ratingCount;
  double? price;
  bool wishlist;
  double? latitude;
  double? longitude;
  int? discount;
  double? discountedAmount;
  String hostProfile;
  String? views;
  List<PropertyPhotos>? propertyPhotos;
  String? unitCode;
  String? location;
  int currentSliderId;
  String? propertyTypeName;
  int? hostId;
  double? totalNightPrice;
  int? numberOfDays;
  double? dayPrice;
  double? totalPrice;
  double? beforeDiscount;
  bool available = true;
  bool exclusive = true;
  bool isNewLable = true;
  String? daysDiscount;
  // int? daysDiscountValue;
  String? badge;
  int? beds;
  int? bedroom;
  num? bathrooms;
  String? priceError;
  PropertyCheckoutPrice? propertyPrice;
  bool? hasDailyDiscount;

  HomeProperty(
      {this.id,
      this.title,
      this.spaceTypeLang,
      this.slug,
      this.photo,
      this.summary,
      this.rating,
      this.ratingCount,
      this.price,
      this.wishlist = false,
      this.latitude,
      this.longitude,
      this.discount,
      this.discountedAmount,
      this.hostProfile = "",
      this.views,
      this.propertyPhotos = const [],
      this.unitCode,
      this.location,
      this.currentSliderId = 0,
      this.propertyTypeName,
      this.hostId,
      this.totalNightPrice,
      this.numberOfDays,
      this.dayPrice,
      this.totalPrice,
      this.beforeDiscount,
      this.available = true,
      this.exclusive = false,
      this.isNewLable = false,
      this.daysDiscount,
      // this.daysDiscountValue,
      this.beds,
        this.badge,
        this.bedroom,
      this.bathrooms,this.hasDailyDiscount});

  factory HomeProperty.fromJson(Map<String, dynamic> json) {
    // String? tempPhoto;
    // if(json['photo']!=null){
    //   tempPhoto = json['photo'];
    // }else{
    //   Map? images = json['images'].firstWhere((item)=>item['cover_photo']==1,orElse:()=>null);
    //   if(images!=null){
    //     tempPhoto = images['photo'];
    //   }
    // }
    return HomeProperty(
      id: json['id'],
      title: json['property_title'],
      spaceTypeLang: json['space_type_lang'] ?? "",
      slug: json['slug'],
      photo: json['photo'],
      summary: json['summary'],
      rating: json['rating'] is int
          ? json['rating'].toString()
          : (json['rating'] ?? "0"),
      ratingCount: json['rating_count'].toString(),
      price: json['price'] is int ? (json['price']).toDouble() : json['price'],
      wishlist: json['wishlist_added'] ?? false,
      latitude: double.parse(json['latitude'] ?? "0"),
      longitude: double.parse(json['longitude'] ?? "0"),
      discount: json['discount'],
      discountedAmount: json['discounted_amount'] is int
          ? json['discounted_amount'].toDouble()
          : json['discounted_amount'],
      hostProfile: json['host_profile'] ?? "",
      views: json['views'] ?? "0",
      propertyPhotos: (json['images'] ?? [])
          .map<PropertyPhotos>((item) => PropertyPhotos.fromJson(item))
          .toList(),
      unitCode: json['unit_code'],
      location: json['location'],
      currentSliderId:
          (json['images'] ?? []).isNotEmpty ? json['images'][0]['id'] : 0,
      propertyTypeName: json['property_type_name'],
      hostId: json['host_id'] is String
          ? int.parse(json['host_id'])
          : json['host_id'],
      totalNightPrice: json['total_price'] is int
          ? (json['total_price']).toDouble()
          : json['total_price'],
      numberOfDays: json['number_of_days'],
      dayPrice: json['day_price'] is int
          ? json['day_price'].toDouble()
          : json['day_price'] is String
              ? double.parse(json['day_price'])
              : json['day_price'],
      totalPrice: json['total_price'] is int
          ? json['total_price'].toDouble()
          : json['total_price'] is String
              ? double.parse(json['total_price'])
              : json['total_price'],
      beforeDiscount: json['before_discount'] is int
          ? json['before_discount'].toDouble()
          : json['before_discount'] is String
              ? double.parse(json['before_discount'])
              : json['before_discount'],
      available: json['available'] != false,
      exclusive: json['exclusive'] == true,
      isNewLable: json['is_new_lable'] == true,
      daysDiscount: json['days_discount'],
      // daysDiscountValue: json['days_discount_value'],
      badge: json['badge'] ?? "none",
      beds: json['beds'],
      bedroom: json['bedrooms'],
      bathrooms: json['bathrooms'],
      hasDailyDiscount: json['has_daily_discount'],
    );
  }

  factory HomeProperty.fromDetail(data) {
    return HomeProperty(
      id: data.id,
      title: data.name,
      unitCode: data.propertyCode,
      propertyTypeName: data.propertyTypeName,
      discountedAmount: data.discountedAmount,
      hostId: data.hostId,
      wishlist: data.wishlist,
      slug: data.slug,
      beds: data.beds,
      bedroom: data.bedrooms,
      bathrooms: data.bathrooms,
      // daysDiscount: data.daysDiscount,
      // daysDiscountValue: data.daysDiscount,
    );
  }

  factory HomeProperty.fromDb(Map<String, dynamic> json) {
    List thisImages = jsonDecode(json['images']) ?? [];
    return HomeProperty(
        id: json['id'],
        title: json['title'],
        spaceTypeLang: json['space_type_lang'] ?? "",
        slug: json['slug'],
        photo: json['photo'],
        summary: json['summary'],
        rating: json['rating'],
        ratingCount: json['rating_count'].toString(),
        price: json['price'],
        wishlist: json['wishlist'] == 1,
        latitude: json['latitude'],
        longitude: json['longitude'],
        discount: json['discount'],
        discountedAmount: json['discounted_amount'],
        views: json['views'] ?? "0",
        propertyPhotos: thisImages
            .map<PropertyPhotos>((item) => PropertyPhotos.fromJson(item))
            .toList(),
        unitCode: json['unit_code'],
        location: json['location'],
        currentSliderId: thisImages.isNotEmpty ? thisImages[0]['id'] : 0);
  }

  Map<String, Object?> toJson(HomeProperty property) {
    return {
      "id": property.id,
      "title": property.title,
      "space_type_lang": property.spaceTypeLang,
      "slug": property.slug,
      "photo": property.photo,
      "summary": property.summary,
      "rating": property.rating,
      "rating_count": property.ratingCount,
      "price": property.price,
      "wishlist": property.wishlist ? 1 : 0,
      "latitude": property.latitude,
      "longitude": property.longitude,
      "discount": property.discount,
      "discounted_amount": property.discountedAmount,
      "views": property.views ?? "0",
      "images": jsonEncode(
          (property.propertyPhotos ?? []).map((e) => e.toJson()).toList()),
      "unit_code": property.unitCode,
      "location": property.location
    };
  }
}

class PropertyPhotos {
  int? id;
  String? photo;
  String? message;
  int? current;

  PropertyPhotos({this.id, this.photo, this.message, this.current});

  PropertyPhotos.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    photo = json['photo'];
    message = json['message'];
    current = 0;
  }

  toJson() {
    return {
      "id": id,
      "photo": photo,
      "message": message,
    };
  }
}
