class InboxModel {
  int? propertyId;
  int? receiverId;
  int? senderId;
  bool? read;
  String? message;
  String? createdAt;
  String? propertyName;
  int? hostId;
  String? senderFirstName;
  String? senderLastName;
  String? senderProfile;
  String? coverPhoto;
  String? receiverFirstName;
  String? receiverLastName;
  String? receiverProfile;

  InboxModel(
      {this.propertyId,
        this.receiverId,
        this.senderId,
        this.read,
        this.message,
        this.createdAt,
        this.propertyName,
        this.hostId,
        this.senderFirstName,
        this.senderLastName,
        this.senderProfile,
        this.coverPhoto,
        this.receiverFirstName,
        this.receiverLastName,
        this.receiverProfile});

  InboxModel.fromJson(Map<String, dynamic> json) {
    propertyId = json['property_id'];
    receiverId = json['receiver_id'];
    senderId = json['sender_id'];
    read = json['read']==1;
    message = json['message'];
    createdAt = json['created_at'];
    propertyName = json['property_name'];
    hostId = json['host_id'];
    senderFirstName = json['sender_first_name'];
    senderLastName = json['sender_last_name'];
    senderProfile = json['sender_profile'];
    coverPhoto = json['cover_photo'];
    receiverFirstName = json['receiver_first_name'];
    receiverLastName = json['receiver_last_name'];
    receiverProfile = json['receiver_profile'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['property_id'] = propertyId;
    data['receiver_id'] = receiverId;
    data['sender_id'] = senderId;
    data['message'] = message;
    data['created_at'] = createdAt;
    data['property_name'] = propertyName;
    data['host_id'] = hostId;
    data['sender_first_name'] = senderFirstName;
    data['sender_last_name'] = senderLastName;
    data['sender_profile'] = senderProfile;
    data['cover_photo'] = coverPhoto;
    data['receiver_first_name'] = receiverFirstName;
    data['receiver_last_name'] = receiverLastName;
    data['receiver_profile'] = receiverProfile;
    return data;
  }
}
class NotifyModel{
  String? message;
  DateTime? readAt;
  Map? data;
  String? createdAt;
  NotifyModel.fromJson(Map json){
    message = json['message'];
    readAt = readAt!=null?DateTime.parse(json['read_at']):null;
    data = json['data'];
    createdAt = json['created_at'];
  }
}
