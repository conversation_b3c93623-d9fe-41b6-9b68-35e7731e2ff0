class PropertyCheckoutPrice {
  PropertyDefault? propertyDefault;
  List<DateWithPrice> dateWithPrice=[];
  double? totalNightPrice;
  double? totalNightPriceAfterDiscount;
  double? discount;
  double? propertyPrice;
  int? totalNights;
  double? serviceFee;
  double? serviceFeeWithDiscount;
  double? serviceFeeDiscount;
  double? hostFee;
  double? ivaTax;
  double? accomodationTax;
  int? additionalGuest;
  int? securityFee;
  double? serviceFeeSecurity;
  int? cleaningFee;
  double? serviceFeeCleaning;
  double? subtotal;
  String? yousaved;
  double? total;
  double? totalWithDiscount;
  double? totalDiscount;
  double? yousavedWithoutSymbol;
  String? totalNightPriceWithSymbol;
  String? serviceFeeWithSymbol;
  String? totalWithSymbol;
  String? totalWithDiscountWithSymbol;
  String? totalNightPriceAfterDiscountWithSymbol;
  String? ivaTaxWithSymbol;
  String? accomodationTaxWithSymbol;
  String? additionalGuestFeeWithSymbol;
  String? securityFeeWithSymbol;
  String? cleaningFeeWithSymbol;
  String? perNightPriceWithSymbol;
  String? discountWithSymbol;
  String? currency;
  String? paymentGetway;
  double? valueOfApplyingCoupon;
  double? dailyDiscountAmount;
  int? monthlyWeeklyDiscountPercentage;
  int? dailyDiscountPercent;

  PropertyCheckoutPrice(
      {this.propertyDefault,
        this.dateWithPrice=const [],
        this.totalNightPrice,
        this.totalNightPriceAfterDiscount,
        this.discount,
        this.propertyPrice,
        this.totalNights,
        this.serviceFee,
        this.serviceFeeWithDiscount,
        this.hostFee,
        this.ivaTax,
        this.accomodationTax,
        this.additionalGuest,
        this.securityFee,
        this.serviceFeeSecurity,
        this.cleaningFee,
        this.serviceFeeCleaning,
        this.subtotal,
        this.yousaved,
        this.total,
        this.totalWithDiscount,
        this.yousavedWithoutSymbol,
        this.totalNightPriceWithSymbol,
        this.serviceFeeWithSymbol,
        this.totalWithSymbol,
        this.totalWithDiscountWithSymbol,
        this.totalNightPriceAfterDiscountWithSymbol,
        this.ivaTaxWithSymbol,
        this.accomodationTaxWithSymbol,
        this.additionalGuestFeeWithSymbol,
        this.securityFeeWithSymbol,
        this.cleaningFeeWithSymbol,
        this.perNightPriceWithSymbol,
        this.discountWithSymbol,
        this.currency,this.paymentGetway,this.valueOfApplyingCoupon,this.dailyDiscountAmount,this.monthlyWeeklyDiscountPercentage,this.dailyDiscountPercent});

  PropertyCheckoutPrice.fromJson(Map<String, dynamic> json) {
    propertyDefault = json['property_default'] != null
        ? PropertyDefault.fromJson(json['property_default'])
        : null;
    if (json['date_with_price'] != null) {
      dateWithPrice = json['date_with_price'].map<DateWithPrice>((item)=>DateWithPrice.fromJson(item)).toList();
    }
    totalNightPrice = json['total_night_price'] is int ?json['total_night_price'].toDouble():(json['total_night_price']??0.0);
    totalNightPriceAfterDiscount = json['total_night_price_after_discount'] is int ?json['total_night_price_after_discount'].toDouble():(json['total_night_price_after_discount']??0.0);
    discount = json['discount'] is int?json['discount'].toDouble():json['discount'];
    propertyPrice = json['property_price'] is String?double.parse(json['property_price']):json['property_price'] is int?json['property_price'].toDouble():json['property_price'];
    totalNights = json['total_nights'];
    serviceFee = json['service_fee'] is int?json['service_fee'].toDouble():(json['service_fee']??0.0);
    serviceFeeWithDiscount = json['service_fee_with_discount'] is int?json['service_fee_with_discount'].toDouble():(json['service_fee_with_discount']??0.0);
    hostFee = json['host_fee'] is int ?json['host_fee'].toDouble():(json['host_fee']??0.0);
    ivaTax = json['iva_tax'] is int ?json['iva_tax'].toDouble():(json['iva_tax']??0.0);
    accomodationTax = json['accomodation_tax'] is int?json['accomodation_tax'].toDouble():json['accomodation_tax'];
    additionalGuest = json['additional_guest'];
    securityFee = json['security_fee'];
    serviceFeeSecurity = json['service_fee_security'] is int?json['service_fee_security'].toDouble():json['service_fee_security'];
    cleaningFee = json['cleaning_fee'];
    serviceFeeCleaning = json['service_fee_cleaning'] is int?json['service_fee_cleaning'].toDouble():json['service_fee_cleaning'];
    subtotal = json['subtotal'] is int ?json['subtotal'].toDouble():(json['subtotal'])??0.0;
    yousaved = json['yousaved'];
    total = json['total'] is int? json['total'].toDouble():(json['total']??0.0);
    totalWithDiscount = json['total_with_discount'] is int? json['total_with_discount'].toDouble():(json['total_with_discount']??0.0);
    yousavedWithoutSymbol = json['yousaved_without_symbol'] is int? json['yousaved_without_symbol'].toDouble():(json['yousaved_without_symbol']??0.0);
    totalNightPriceWithSymbol = json['total_night_price_with_symbol'];
    serviceFeeWithSymbol = json['service_fee_with_symbol'];
    totalWithSymbol = json['total_with_symbol'];
    totalWithDiscountWithSymbol = json['total_with_discount_with_symbol'];
    totalNightPriceAfterDiscountWithSymbol =
    json['total_night_price_after_discount_with_symbol'];
    ivaTaxWithSymbol = json['iva_tax_with_symbol'];
    accomodationTaxWithSymbol = json['accomodation_tax_with_symbol'];
    additionalGuestFeeWithSymbol = json['additional_guest_fee_with_symbol'];
    securityFeeWithSymbol = json['security_fee_with_symbol'];
    cleaningFeeWithSymbol = json['cleaning_fee_with_symbol'];
    perNightPriceWithSymbol = json['per_night_price_with_symbol'];
    discountWithSymbol = json['discount_with_symbol'];
    currency = json['currency'];
    paymentGetway = json['payment_getway'];
    valueOfApplyingCoupon = json['value_of_applying_coupon'] is int? json['value_of_applying_coupon'].toDouble():(json['value_of_applying_coupon']??0.0);
    dailyDiscountAmount = json['daily_discount_amount'] is int? json['daily_discount_amount'].toDouble():(json['daily_discount_amount']??0.0);
    monthlyWeeklyDiscountPercentage = json['monthly_weekly_discount_percentage'] is double? json['monthly_weekly_discount_percentage'].toInt():(json['monthly_weekly_discount_percentage']??0.0);
    dailyDiscountPercent = json['daily_discount_percent'] is double? json['daily_discount_percent'].toInt():(json['daily_discount_percent']??0.0);
  }
}

class PropertyDefault {
  int? price;
  String? currencyCode;
  String? symbol;
  String? rate;
  int? localToPropertyRate;

  PropertyDefault(
      {this.price,
        this.currencyCode,
        this.symbol,
        this.rate,
        this.localToPropertyRate});

  PropertyDefault.fromJson(Map<String, dynamic> json) {
    price = json['price'];
    currencyCode = json['currency_code'];
    symbol = json['symbol'];
    rate = json['rate'];
    localToPropertyRate = json['local_to_propertyRate'];
  }
}

class DateWithPrice {
  String? price;
  double? originalPrice;
  String? date;

  DateWithPrice({this.price, this.originalPrice, this.date});

  DateWithPrice.fromJson(Map<String, dynamic> json) {
    price = json['price'];
    originalPrice = json['original_price'] is String?double.parse(json['original_price']):json['original_price'] is int?json['original_price'].toDouble():json['original_price'];
    date = json['date'];
  }
}