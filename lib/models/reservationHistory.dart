class ReservationHistory {
  int? id;
  String? bookingNumber;
  int? propertyId;
  int? hostId;
  String? startDate;
  String? endDate;
  String? propertyName;
  String? propertyAddress;
  String? propertyDescription;
  String? coverPhoto;
  bool? rated = false;
  String? slug;
  String? cancelledBy;
  bool? visibility;

  ReservationHistory(
      {this.id,
        this.bookingNumber,
        this.propertyId,
        this.hostId,
        this.startDate,
        this.endDate,
        this.propertyName,
        this.propertyAddress,
        this.propertyDescription,this.coverPhoto,
        this.rated = false,
        this.slug,this.cancelledBy,this.visibility=true});

  ReservationHistory.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    bookingNumber = json['booking_number'];
    propertyId = json['property_id'];
    hostId = json['host_id'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    propertyName = json['property_name'];
    propertyAddress = json['address'];
    propertyDescription = json['property_description'];
    coverPhoto = json['property_cover_photo'];
    rated = json['rated'];
    slug = json['slug'];
    cancelledBy = json['cancelled_by'];
    visibility = json['visibility']==1;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['property_id'] = propertyId;
    data['host_id'] = hostId;
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    data['property_name'] = propertyName;
    data['address'] = propertyAddress;
    data['property_description'] = propertyDescription;
    data['property_cover_photo'] = coverPhoto;
    data['slug'] = slug;
    return data;
  }
}