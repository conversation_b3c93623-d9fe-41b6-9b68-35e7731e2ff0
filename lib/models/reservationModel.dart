class ReservationModel {
  String? bookingCode;
  String? userName;
  String? startDate;
  String? endDate;
  String? propertyName;
  String? phone;
  String? city;
  String? country;
  String? coverPhoto;
  String? latitude;
  String? longitude;
  String? address;
  int? upcomingDays;
  int? chatHeadId;
  bool? visibility;
  int? propertyId;
  int? adultGuest;
  int? childGuest;
  String? propertyDescription;
  String? unitCode;
  String? perNight;
  String? propertyType;
  String? status;

  ReservationModel(
      {this.bookingCode,
        this.userName,
        this.startDate,
        this.endDate,
        this.propertyName,
        this.phone,
        this.city,
        this.country,
        this.coverPhoto,
        this.latitude,
        this.longitude,
        this.address,
        this.upcomingDays,
        this.chatHeadId,
        this.visibility=false,
        this.propertyId,
        this.adultGuest,
        this.childGuest,
        this.propertyDescription,this.unitCode,this.perNight,this.propertyType,this.status});

  ReservationModel.fromJson(Map<String, dynamic> json) {
    bookingCode = json['booking_code'];
    userName = json['user_name'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    propertyName = json['property_name'];
    phone = json['phone'];
    city = json['city'];
    country = json['country'];
    coverPhoto = json['cover_photo'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    address = json['address']??'';
    upcomingDays = json['upcoming_days'];
    chatHeadId = json['chat_head_id'];
    visibility = json['visibility']==1;
    propertyId = json['property_id'];
    adultGuest = json['adult_guest'];
    childGuest = json['child_guest'];
    propertyDescription = json['property_description'];
    unitCode = json['unit_code'];
    perNight = json['per_night'] is String?json['per_night']:json['per_night'].toString();
    propertyType = json['property_type'];
    status = json['status'];
  }
}