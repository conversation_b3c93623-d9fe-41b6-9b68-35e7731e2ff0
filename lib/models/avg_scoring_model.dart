class AvgScoringModel{
  int score;
  String? name,nameAr,description,descriptionAr;
  AvgScoringModel({this.score=0,this.name,this.nameAr,this.description,this.descriptionAr});
  factory AvgScoringModel.fromJson(Map json){
    return AvgScoringModel(
      score: json['score'],
      name: json['name'],
      nameAr: json['name_ar'],
      description: json['description'],
      descriptionAr: json['description_ar']
    );
  }
}