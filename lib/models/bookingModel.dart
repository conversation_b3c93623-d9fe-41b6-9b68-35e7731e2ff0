import 'package:darent/utils/constants.dart';
class BookingModel{
  int? id;
  String? bookingCode;
  String? propertyCode;
  int? propertyId;
  String? slug;
  String? condition;
  int? guest;
  double? total;
  double? basePrice;
  double? cleaningFee;
  double? serviceCharge;
  double? hostFee;
  double? hostTotal;
  DateTime? startDate;
  DateTime? endDate;
  String? checkinTime;
  String? checkoutTime;
  String? userName;
  String? profile;
  int? joined;
  String? propertyName;
  String? phone;
  String? coverPhoto;
  String? userRating;
  int? chatHeadId;
  int? adultGuest;
  int? totalNights;
  bool? idAchieved;
  String? userAddress;
  String? propertyDetails;
  int? adults;
  int? children;
  String? departure;
  String? status;
  bool? isUserDelete;
  double? securityFee;
  double? hostCommission;
  double? totalDiscount;
  double? totalAccomodation;
  double? insurancePercent;
  String? discountType;
  num? youSaved;
  String? promoCodeCreatedBy;
  BookingModel(
      {this.id,
        this.bookingCode,
        this.propertyCode,
        this.propertyId,
        this.slug,
        this.condition,
        this.guest,
        this.total,
        this.startDate,
        this.endDate,
        this.userName,
        this.profile,
        this.joined,
        this.propertyName,
        this.phone,
        this.coverPhoto,
        this.userRating,
        this.chatHeadId,
        this.totalNights,
        this.idAchieved,
        this.userAddress,
        this.propertyDetails,
        this.adults,
        this.children,
        this.checkinTime,
        this.checkoutTime,
        this.departure,
        this.basePrice,
        this.cleaningFee,
        this.serviceCharge,
        this.hostFee,
        this.hostTotal,this.status, this.isUserDelete,this.securityFee,this.hostCommission,this.totalDiscount,this.totalAccomodation,this.insurancePercent,this.discountType,this.youSaved,
  this.promoCodeCreatedBy});

  BookingModel.fromJson(Map<String, dynamic> json) {
    DateTime start = formDateFormat.parse(json['start_date']);
    DateTime end = formDateFormat.parse(json['end_date']);
    id = json['id'];
    bookingCode = json['booking_code']??json['code'];
    propertyCode = json['property_code'];
    propertyId = json['property_id'];
    slug = json['slug'];
    condition = json['condition'];
    total = checkAndGetDouble(json['total']);
    startDate = start;
    endDate = end;
    userName = json['user_name'];
    profile = json['profile'];
    joined = json['joined'];
    propertyName = json['property_name'];
    phone = json['phone'];
    coverPhoto = json['cover_photo'];
    userRating = json['user_rating'].toString();
    chatHeadId = json['chat_head_id'];
    totalNights = json['total_nights'];
    idAchieved = json['id_achieved']??true;
    userAddress = json['user_address'];
    propertyDetails = json['property_address']??"An apartment with a simple and calm character"
        " (an apartment characterized by the character of the root in- the noun Simple and integrated) a";
    adults = json['adults']??json['guest_adult']??1;
    children = json['children']??json['guest_child']??0;
    guest = json['guest']??(adults!+children!);
    checkinTime = json['checkin_time']??0;
    checkoutTime = json['checkin_time']??0;
    departure = json['departure']??"Today";
    basePrice = (json['base_price']) is int?json['base_price'].toDouble():json['base_price'];
    cleaningFee = json['cleaning_fee'] is int?json['cleaning_fee'].toDouble():json['cleaning_fee'];
    serviceCharge = json['service_charge'] is int?json['service_charge'].toDouble():json['service_charge'];
    hostFee = json['host_fee'] is int?json['host_fee'].toDouble():json['host_fee'];
    hostTotal = json['host_total'] is int?json['host_total'].toDouble():json['host_total'];
    status = json['status'];
    isUserDelete = json['user_delete'];
    securityFee = json['security_fee'] is int?json['security_fee'].toDouble():json['security_fee'] is String?double.parse(json['security_fee']):json['security_fee'];
    hostCommission = json['host_commission'] is int?json['host_commission'].toDouble():json['host_commission'] is String?double.parse(json['host_commission']):json['host_commission'];
    totalDiscount = json['total_discount'] is int?json['total_discount'].toDouble():json['total_discount'] is String?double.parse(json['total_discount']):json['total_discount'];
    totalAccomodation = json['total_accomodation'] is int?json['total_accomodation'].toDouble():json['total_accomodation'] is String?double.parse(json['total_accomodation']):json['total_accomodation'];
    insurancePercent = json['insurancePercent'] is int?json['insurancePercent'].toDouble():json['insurancePercent'] is String?double.parse(json['insurancePercent']):json['insurancePercent'];
    discountType = json['discount_type'];
    youSaved = json['you_saved'] is String? num.parse(json['you_saved']):json['you_saved'];
    promoCodeCreatedBy = json['promo_code_created_by'];
  }
  BookingModel.fromChatJson(Map<String, dynamic> json) {
    DateTime start = formDateFormat.parse(json['start_date']);
    DateTime end = formDateFormat.parse(json['end_date']);
    id = json['id'];
    bookingCode = json['code'];
    total = json['total'] is int?json['total'].toDouble():json['total'];
    startDate = start;
    endDate = end;
    userName = json['user']?['name'];
    profile = json['user']?['image'];
    joined = json['user']?['created_at'];
    phone =  json['user']?['phone'];
    userRating = json['user']?['avg_rating'].toString();
    idAchieved = json['id_achieved']??true;
    totalNights = json['total_night'];
    userAddress = json['user']?['location'];
    adults = json['guest_adult']??1;
    children = json['guest_child']??0;
    guest = json['guest']??(adults!+children!);
    checkinTime = json['checkin_time']??0;
    checkoutTime = json['checkin_time']??0;
    basePrice = (json['base_price']) is int?json['base_price'].toDouble():json['base_price'] is String?double.parse(json['base_price']):json['base_price'];
    cleaningFee = json['cleaning_charge'] is int?json['cleaning_charge'].toDouble():json['cleaning_charge'];
    serviceCharge = json['service_charge'] is int?json['service_charge'].toDouble():json['service_charge'];
    hostFee = json['host_fee'] is int?json['host_fee'].toDouble():json['host_fee'];
    hostTotal = (json['host_total']??0.0) is int?json['host_total'].toDouble():json['host_total'] is String?double.parse(json['host_total']):json['host_total'];
    status = json['status'];
    securityFee = json['security_money'] is int?json['security_money'].toDouble():json['security_money'] is String?double.parse(json['security_money']):json['security_money'];
    hostCommission = json['host_fee_percent'] is int?json['host_fee_percent'].toDouble():json['host_fee_percent'] is String?double.parse(json['host_fee_percent']):json['host_fee_percent'];
    totalDiscount = json['total_discount'] is int?json['total_discount'].toDouble():json['total_discount'] is String?double.parse(json['total_discount']):(json['total_discount']??0);
    totalAccomodation = json['total_accomodation'] is int?json['total_accomodation'].toDouble():json['total_accomodation'] is String?double.parse(json['total_accomodation']):json['total_accomodation'];
    discountType = json['discount_type'];
    youSaved = json['you_saved'];
  }
}
