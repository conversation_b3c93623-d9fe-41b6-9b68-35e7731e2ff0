import './host/property_photo.dart';

class Property {
  int? id;
  String? slug;
  String? name;
  String? summary;
  String? propertyCode;
  String? summaryAr;
  int? reviewsCount;
  bool canEdit = false;
  String? status;
  int? visibility;
  int? spaceType;
  int? adults;
  int? children;
  int? beds;
  int? bedrooms;
  double? bathrooms;
  int? noOfApartments;
  int? price;
  int? maxNights;
  int? minNights;
  String? bookingType;
  List<PropertyPhoto>? propertyPhotos;
  String? coverPhoto;
  String? addressLine;
  String? latitude;
  String? longitude;
  String? amenities;
  String? stepsRemaining;
  String? missedStep;
  String? averageRating;
  Map? specialDaysPrice;
  int? singleBeds;
  int? doubleBeds;
  String? checkInTime;
  String? checkOutTime;
  String? postalCode;
  String? district;
  bool isChecked = false;

  Property({
    this.id,
    this.name,
    this.slug,
    this.bedrooms,
    this.beds,
    this.bathrooms,
    this.amenities,
    this.spaceType,
    this.bookingType,
    this.status,
    this.visibility,
    this.reviewsCount,
    this.canEdit = false,
    this.summary,
    this.propertyCode,
    this.summaryAr,
    this.noOfApartments,
    this.maxNights,
    this.minNights,
    this.propertyPhotos,
    this.addressLine,
    this.stepsRemaining,
    this.missedStep,
    this.averageRating,
    this.specialDaysPrice, this.singleBeds, this.doubleBeds, this.checkInTime, this.checkOutTime, this.postalCode
  });

  Property.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return;
    }
    id = json['id'];
    slug = json['slug'];
    name = json['name'];
    summary = json['summary'];
    propertyCode = json['property_code'];
    summaryAr = json['summary_ar'] ?? json['summary'];
    reviewsCount = json['reviews_count'];
    canEdit = json['can_edit'];
    status = json['status'];
    visibility = json['visibility'];
    spaceType = json['space_type'];
    adults = json['adults'];
    children = json['children'];
    beds = json['beds'];
    bedrooms = json['bedrooms'];
    bathrooms =
    json['bathrooms'] is int ? json['bathrooms'].toDouble() : json['bathrooms'];
    noOfApartments = json['no_of_apartments'];
    price = json['price'];
    maxNights = json['max_nights'];
    minNights = json['min_nights'];
    bookingType = json['booking_type'];
    if (json['photos'].isNotEmpty) {
      propertyPhotos = json['photos']
          .map<PropertyPhoto>((item) =>
          PropertyPhoto(item['id'], item['photo'],coverPhoto:item['cover_photo']))
          .toList();
      coverPhoto = (propertyPhotos!.firstWhere((item) => item.coverPhoto == 1,
          orElse: () => propertyPhotos!.first)).image;
    }
    latitude = json['latitude'];
    longitude = json['longitude'];
    addressLine = json['address_line'];
    amenities = json['amenities'];
    stepsRemaining = json['steps_remaining'];
    missedStep = json['missed_step'];
    averageRating = json['average_rating'] ?? "0";
    specialDaysPrice = json['special_days_price'] ?? {};
    singleBeds = json['single_beds'] is String
        ? int.tryParse(json['single_beds']) ?? 0
        : json['single_beds'];
    doubleBeds = json['double_beds'] is String
        ? int.tryParse(json['double_beds']) ?? 0
        : json['double_beds'];
    checkInTime = json['checkinTime'];
    checkOutTime = json['checkoutTime'];
    postalCode = json['postal_code'];
    district = json['district'];
  }
}
