class CardModel{
  int id=0;
  String cardName="";
  String cardNumber="";
  String cardMonth="";
  String cardYear="";
  String cardCvv="";
  bool isDefault=false;
  CardModel(this.id,this.cardName,this.cardNumber,this.cardMonth,this.cardYear,this.cardCvv,this.isDefault);
  factory CardModel.fromJson(Map<String,dynamic> json){
    return CardModel(
        json['id'],
        json['card_name'],
        json['card_number'],
        json['card_month'],
        json['card_year'],
        json['card_cvv'],
        json['is_default']==1
    );
  }
}

class WalletModel {
  int? id = 0;
  int? userId = 0;
  double? balance = 0;
  int? isActive = 0;
  String? createdAt = "";
  String? updatedAt = "";
  dynamic deletedAt = "";

  WalletModel(this.id, this.userId, this.balance, this.isActive, this.createdAt,
      this.updatedAt, this.deletedAt);

  factory WalletModel.fromJson(Map<String, dynamic> json) {
    return WalletModel(
      json['id'],
      json['user_id'],
      json['balance'] is double
          ? json['balance']
          : json['balance'] is String
              ? double.parse(json['balance'])
              : json['balance'] is int
                  ? double.parse(json['balance'].toString())
                  : json['balance'],
      json['is_active'],
      json['created_at'],
      json['updated_at'],
      json['deleted_at'],
    );
  }
}
