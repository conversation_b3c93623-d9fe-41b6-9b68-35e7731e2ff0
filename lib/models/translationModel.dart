import 'package:get/get.dart';

import '../helperMethods/translation_helper.dart';

class TranslationModel {
  TranslationModel({
    required this.header,
    required this.footer,
    required this.sidenav,
    required this.success,
    required this.error,
    required this.home,
    required this.login,
    required this.forgotPass,
    required this.usersDashboard,
    required this.usersShow,
    required this.usersProfile,
    required this.usersMedia,
    required this.signUp,
    required this.property,
    required this.propertySingle,
    required this.listingSidebar,
    required this.listingBasic,
    required this.listingDescription,
    required this.listingLocation,
    required this.listingPrice,
    required this.listingBook,
    required this.listingCalendar,
    required this.accountSidenav,
    required this.accountPreference,
    required this.accountTransaction,
    required this.accountSecurity,
    required this.bookingMy,
    required this.bookingDetail,
    required this.bookingRequest,
    required this.tripsActive,
    required this.tripsReceipt,
    required this.previousTrips,
    required this.payment,
    required this.paymentStripe,
    required this.utility,
    required this.feeds,
    required this.profile,
    required this.upload,
    required this.notification,
    required this.managePhoto,
    required this.purchase,
    required this.settings,
    required this.account,
    required this.withdraw,
    required this.photoDetails,
    required this.search,
    required this.earning,
    required this.message,
    required this.modal,
    required this.staticPages,
    required this.reviews,
    required this.filter,
    required this.ical,
    required this.emailTemplate,
    required this.favourite,
    required this.general,
    required this.jqueryValidation,
    required this.homepage,
    required this.becomeHost,
    required this.wishlist,
    required this.reservation,
    required this.listing,
    required this.sort,
    required this.wallet,
    required this.hostDashboard,
    required this.scoringPointExplain,
    required this.hostListing,
    required this.hostReservation,
    required this.accountMobile,
    required this.promocode,
    required this.contactHost,
    required this.coHost,
    required this.hostReview,
  });

  Header header;
  Footer footer;
  Sidenav sidenav;
  Success success;
  Error error;
  Home home;
  Login login;
  ForgotPass forgotPass;
  UsersDashboard usersDashboard;
  UsersShow usersShow;
  UsersProfile usersProfile;
  UsersMedia usersMedia;
  SignUp signUp;
  PropertyTranslation property;
  PropertySingle propertySingle;
  ListingSidebar listingSidebar;
  ListingBasic listingBasic;
  ListingDescription listingDescription;
  ListingLocation listingLocation;
  ListingPrice listingPrice;
  ListingBook listingBook;
  ListingCalendar listingCalendar;
  AccountSidenav accountSidenav;
  AccountPreference accountPreference;
  AccountTransaction accountTransaction;
  AccountSecurity accountSecurity;
  BookingMy bookingMy;
  BookingDetail bookingDetail;
  BookingRequest bookingRequest;
  TripsActive tripsActive;
  TripsReceipt tripsReceipt;
  PreviousTrips previousTrips;
  Payment payment;
  PaymentStripe paymentStripe;
  Utility utility;
  Feeds feeds;
  ProfileTranslation profile;
  Upload upload;
  Notification notification;
  ManagePhoto managePhoto;
  Purchase purchase;
  Settings settings;
  Account account;
  Withdraw withdraw;
  PhotoDetails photoDetails;
  Search search;
  Earning earning;
  Message message;
  Modal modal;
  StaticPages staticPages;
  Reviews reviews;
  Filter filter;
  Ical ical;
  EmailTemplate emailTemplate;
  Favourite favourite;
  General general;
  JqueryValidation jqueryValidation;
  Homepage homepage;
  BecomeHost becomeHost;
  Wishlist wishlist;
  Reservation reservation;
  Listing listing;
  Sort sort;
  Wallet wallet;
  HostDashboard hostDashboard;
  ScoringPointsExplain scoringPointExplain;
  HostListing hostListing;
  HostReservation hostReservation;
  AccountMobile accountMobile;
  PromoCode promocode;
  ContactHost contactHost;
  CoHost coHost;
  HostReview hostReview;

  factory TranslationModel.fromJson(Map json) => TranslationModel(
        header: Header.fromJson(json["header"]),
        footer: Footer.fromJson(json["footer"]),
        sidenav: Sidenav.fromJson(json["sidenav"]),
        success: Success.fromJson(json["success"]),
        error: Error.fromJson(json["error"]),
        home: Home.fromJson(json["home"]),
        login: Login.fromJson(json["login"]),
        forgotPass: ForgotPass.fromJson(json["forgot_pass"]),
        usersDashboard: UsersDashboard.fromJson(json["users_dashboard"]),
        usersShow: UsersShow.fromJson(json["users_show"]),
        usersProfile: UsersProfile.fromJson(json["users_profile"]),
        usersMedia: UsersMedia.fromJson(json["users_media"]),
        signUp: SignUp.fromJson(json["sign_up"]),
        property: PropertyTranslation.fromJson(json["property"]),
        propertySingle: PropertySingle.fromJson(json["property_single"]),
        listingSidebar: ListingSidebar.fromJson(json["listing_sidebar"]),
        listingBasic: ListingBasic.fromJson(json["listing_basic"]),
        listingDescription:
            ListingDescription.fromJson(json["listing_description"]),
        listingLocation: ListingLocation.fromJson(json["listing_location"]),
        listingPrice:
            ListingPrice.fromJson(json["listing_price"], json['listingprice']),
        listingBook: ListingBook.fromJson(json["listing_book"]),
        listingCalendar: ListingCalendar.fromJson(json["listing_calendar"]),
        accountSidenav: AccountSidenav.fromJson(json["account_sidenav"]),
        accountPreference:
            AccountPreference.fromJson(json["account_preference"]),
        accountTransaction:
            AccountTransaction.fromJson(json["account_transaction"]),
        accountSecurity: AccountSecurity.fromJson(json["account_security"]),
        bookingMy: BookingMy.fromJson(json["booking_my"]),
        bookingDetail: BookingDetail.fromJson(json["booking_detail"]),
        bookingRequest: BookingRequest.fromJson(json["booking_request"]),
        tripsActive: TripsActive.fromJson(json["trips_active"]),
        tripsReceipt: TripsReceipt.fromJson(json["trips_receipt"]),
        previousTrips: PreviousTrips.fromJson(json["previous_trips"]),
        payment: Payment.fromJson(json["payment"]),
        paymentStripe: PaymentStripe.fromJson(json["payment_stripe"]),
        utility: Utility.fromJson(json["utility"]),
        feeds: Feeds.fromJson(json["feeds"]),
        profile: ProfileTranslation.fromJson(json["profile"]),
        upload: Upload.fromJson(json["upload"]),
        notification: Notification.fromJson(json["notification"]),
        managePhoto: ManagePhoto.fromJson(json["manage_photo"]),
        purchase: Purchase.fromJson(json["purchase"]),
        settings: Settings.fromJson(json["settings"]),
        account: Account.fromJson(json["account"]),
        withdraw: Withdraw.fromJson(json["withdraw"]),
        photoDetails: PhotoDetails.fromJson(json["photo_details"]),
        search: Search.fromJson(json["search"]),
        earning: Earning.fromJson(json["earning"]),
        message: Message.fromJson(json["message"]),
        modal: Modal.fromJson(json["modal"]),
        staticPages: StaticPages.fromJson(json["static_pages"]),
        reviews: Reviews.fromJson(json["reviews"]),
        filter: Filter.fromJson(json["filter"]),
        ical: Ical.fromJson(json["ical"]),
        emailTemplate: EmailTemplate.fromJson(json["email_template"]),
        favourite: Favourite.fromJson(json["favourite"]),
        general: General.fromJson(json["general"]),
        jqueryValidation: JqueryValidation.fromJson(json["jquery_validation"]),
        homepage: Homepage.fromJson(json["homepage"]),
        becomeHost: BecomeHost.fromJson(json["become-host"]),
        wishlist: Wishlist.fromJson(json["wishlist"]),
        reservation: Reservation.fromJson(json["reservation"]),
        listing: Listing.fromJson(json["listing"]),
        sort: Sort.fromJson(json["sort"]),
        wallet: Wallet.fromJson(json["wallet"]),
        hostDashboard: HostDashboard.fromJson(json["host_dashboard"]),
        scoringPointExplain:
            ScoringPointsExplain.fromJson(json['scoring_points_explain']),
        hostListing: HostListing.fromJson(json["host_listing"]),
        hostReservation: HostReservation.fromJson(json["host_reservation"]),
        accountMobile: AccountMobile.fromJson(json["account_mobile"]),
        promocode: PromoCode.fromJson(json['promocode']),
        contactHost: ContactHost.fromJson(json['contact_host']),
        coHost: CoHost.fromJson(json['cohost']),
        hostReview: HostReview.fromJson(json['host_review']),
      );
}

class Account {
  Account({
    required this.arrivalGuide,
    required this.yourPoints,
    required this.points,
    required this.currentBalance,
    required this.viewMore,
    required this.upcomingReserv,
    required this.weekReport,
    required this.weekRating,
    required this.basedOn,
    required this.previous,
    required this.accountSetting,
    required this.helpSupport,
    required this.legalDocument,
    required this.hostAgreement,
    required this.account,
    required this.currentlyHave,
    required this.activeReservations,
    required this.youHave,
    required this.avrageRating,
    required this.verified,
    required this.unVerified,
    required this.identity,
    required this.joinedSince,
    required this.paymentDetails,
    required this.email,
    required this.notChangeLeaveEmpty,
    required this.previousPassword,
    required this.customerServices,
    required this.deleteAcc,
    required this.deleteAccNote,
    required this.guestMode,
    required this.hostMode,
    required this.feedback,
    required this.submitRequest,
  });

  String arrivalGuide;
  String yourPoints;
  String points;
  String currentBalance;
  String viewMore;
  String upcomingReserv;
  String weekReport;
  String weekRating;
  String basedOn;
  String previous;
  String accountSetting;
  String helpSupport;
  String legalDocument;
  String hostAgreement;
  String account;
  String currentlyHave;
  String activeReservations;
  String youHave;
  String avrageRating;
  String verified;
  String unVerified;
  String identity;
  String joinedSince;
  String paymentDetails;
  String email;
  String notChangeLeaveEmpty;
  String previousPassword;
  String customerServices;
  String deleteAcc;
  String deleteAccNote;
  String guestMode;
  String hostMode;
  String feedback;
  String submitRequest;

  factory Account.fromJson(Map<String, dynamic> json) => Account(
        arrivalGuide: json["arrival_guide"] ?? "Arrival Guidance",
        yourPoints: json["your_points"] ?? "Your Points",
        points: json["points"] ?? "Points",
        currentBalance: json["current_balance"] ?? "Current Balance",
        viewMore: json["view_more"] ?? "View More",
        upcomingReserv: json['upcoming_reserv'] ?? "Upcoming Reservations",
        weekReport: json["view_week_report"] ?? "View the week's Report",
        weekRating: json['weekRating'] ?? "This Week Ratings",
        basedOn: json["based_on"] ?? "Based on \$number Customers Reviews",
        previous: json["previous"] ?? "Previous",
        accountSetting: json["account_setting"] ?? "Account Settings",
        helpSupport: json["help_support"] ?? "Help & support",
        legalDocument: json["legal_document"] ?? "Legal Document",
        hostAgreement: json["host_agreement"] ?? "Host Agreement",
        currentlyHave: json["currently_have"] ?? "You currently have",
        youHave: json["you_have"] ?? "You have",
        activeReservations:
            json["active_reservations"] ?? "active reservations",
        account: json["account"] ?? "My Account",
        avrageRating: json["average_rating"] ?? "Average Rating",
        verified: json["verified"] ?? "Verified",
        unVerified: json["unverified"] ?? "Unverified",
        identity: json["identity"] ?? "Identity",
        joinedSince: json["joined_since"] ?? "Joined Since",
        paymentDetails: json["payment_details"] ?? "Payment Details",
        email: json["email"],
        notChangeLeaveEmpty: json["not_change_leave_empty"],
        previousPassword: json["previous_password"],
        customerServices: json["customer_services"],
        deleteAcc: json["delete_acc"],
        deleteAccNote: json["delete_acc_note"] ??
            "This will permanently delete your account and your data in accordance with applicable law",
        guestMode: json["guest_mode"],
        hostMode: json["host_mode"],
        feedback: json["feedback"],
      submitRequest: json["submit_request"]
      );
}

class AccountPreference {
  AccountPreference({
    required this.addPayout,
    required this.address,
    required this.address2,
    required this.city,
    required this.stateProvince,
    required this.postalCode,
    required this.country,
    required this.paymentData1,
    required this.sendMoney,
    required this.sendMoney2,
    required this.payoutMethod,
    required this.processingTime,
    required this.additionalFee,
    required this.currency,
    required this.datail,
    required this.paypal,
    required this.businessDay,
    required this.none,
    required this.eur,
    required this.businessDayData,
    required this.save,
    required this.paypalEmailId,
    required this.accountMethod,
    required this.accountMethodData,
    required this.method,
    required this.detail,
    required this.status,
    required this.accountPreferenceDefault,
    required this.ready,
    required this.option,
    required this.remove,
    required this.setDefault,
    required this.depositPaypal,
    required this.payoutRequest,
    required this.bankName,
    required this.bankHolder,
    required this.branchName,
    required this.bankAccountNum,
    required this.branchCity,
    required this.branchAddress,
    required this.swiftCode,
    required this.routingNo,
    required this.banks,
    required this.addNewBank,
    required this.editBank,
    required this.logo,
    required this.logoLimit,
    required this.empty,
    required this.accountIban,
    required this.submitBankDetails,
  });

  String addPayout;
  String address;
  String address2;
  String city;
  String stateProvince;
  String postalCode;
  String country;
  String paymentData1;
  String sendMoney;
  String sendMoney2;
  String payoutMethod;
  String processingTime;
  String additionalFee;
  String currency;
  String datail;
  String paypal;
  String businessDay;
  String none;
  String eur;
  String businessDayData;
  String save;
  String paypalEmailId;
  String accountMethod;
  String accountMethodData;
  String method;
  String detail;
  String status;
  String accountPreferenceDefault;
  String ready;
  String option;
  String remove;
  String setDefault;
  String depositPaypal;
  String payoutRequest;
  String bankName;
  String bankHolder;
  String branchName;
  String bankAccountNum;
  String branchCity;
  String branchAddress;
  String swiftCode;
  String routingNo;
  String banks;
  String addNewBank;
  String editBank;
  String logo;
  String logoLimit;
  String empty;
  String accountIban;
  String submitBankDetails;

  factory AccountPreference.fromJson(Map<String, dynamic> json) =>
      AccountPreference(
        addPayout: json["add_payout"],
        address: json["address"],
        address2: json["address_2"],
        city: json["city"],
        stateProvince: json["state_province"],
        postalCode: json["postal_code"],
        country: json["country"],
        paymentData1: json["payment_data_1"],
        sendMoney: json["send_money"],
        sendMoney2: json["send_money_2"],
        payoutMethod: json["payout_method"],
        processingTime: json["processing_time"],
        additionalFee: json["additional_fee"],
        currency: json["currency"],
        datail: json["datail"],
        paypal: json["paypal"],
        businessDay: json["business_day"],
        none: json["none"],
        eur: json["eur"],
        businessDayData: json["business_day_data"],
        save: json["save"],
        paypalEmailId: json["paypal_email_id"],
        accountMethod: json["account_method"],
        accountMethodData: json["account_method_data"],
        method: json["method"],
        detail: json["detail"],
        status: json["status"],
        accountPreferenceDefault: json["default"],
        ready: json["ready"],
        option: json["option"],
        remove: json["remove"],
        setDefault: json["set_default"],
        depositPaypal: json["deposit_paypal"],
        payoutRequest: json["payout_request"],
        bankName: json["bank_name"],
        bankHolder: json["bank_holder"],
        branchName: json["branch_name"],
        bankAccountNum: json["bank_account_num"],
        branchCity: json["branch_city"],
        branchAddress: json["branch_address"],
        swiftCode: json["swift_code"],
        routingNo: json["routing_no"],
        banks: json["banks"],
        addNewBank: json["add_new_bank"],
        editBank: json["edit_bank"],
        logo: json["logo"],
        logoLimit: json["logo_limit"],
        empty: json["empty"],
        accountIban: json["account_iban"] ?? "Account IBAN",
        submitBankDetails: json["submit_bank_details"] ?? "Submit Bank Details",
      );
}

class AccountSecurity {
  AccountSecurity({
    required this.changePassword,
    required this.oldPassword,
    required this.newPassword,
    required this.confirmPass,
    required this.updatePass,
  });

  String changePassword;
  String oldPassword;
  String newPassword;
  String confirmPass;
  String updatePass;

  factory AccountSecurity.fromJson(Map<String, dynamic> json) =>
      AccountSecurity(
        changePassword: json["change_password"],
        oldPassword: json["old_password"],
        newPassword: json["new_password"],
        confirmPass: json["confirm_pass"],
        updatePass: json["update_pass"],
      );
}

class AccountSidenav {
  AccountSidenav({
    required this.accountPreference,
    required this.transactionHistory,
    required this.security,
  });

  String accountPreference;
  String transactionHistory;
  String security;

  factory AccountSidenav.fromJson(Map<String, dynamic> json) => AccountSidenav(
        accountPreference: json["account_preference"],
        transactionHistory: json["transaction_history"],
        security: json["security"],
      );
}

class AccountTransaction {
  AccountTransaction({
    required this.transaction,
    required this.date,
    required this.type,
    required this.detail,
    required this.amount,
    required this.status,
  });

  String transaction;
  String date;
  String type;
  String detail;
  String amount;
  String status;

  factory AccountTransaction.fromJson(Map<String, dynamic> json) =>
      AccountTransaction(
        transaction: json["transaction"],
        date: json["date"],
        type: json["type"],
        detail: json["detail"],
        amount: json["amount"],
        status: json["status"],
      );
}

class BecomeHost {
  BecomeHost({
    required this.keepTheTravelingExperience,
    required this.howDoWeHelpHost,
    required this.startHosting,
    required this.seeListingAroundYou,
    required this.presentPropertyEffectively,
    required this.helpHostToRaiseProperty,
    required this.damageAssurance,
    required this.forSafetyOfHostProperty,
    required this.weTakeCareOfMarketing,
    required this.bestMarketingMethod,
    required this.customeDashboardMakeEasy,
  });

  String keepTheTravelingExperience;
  String howDoWeHelpHost;
  String startHosting;
  String seeListingAroundYou;
  String presentPropertyEffectively;
  String helpHostToRaiseProperty;
  String damageAssurance;
  String forSafetyOfHostProperty;
  String weTakeCareOfMarketing;
  String bestMarketingMethod;
  String customeDashboardMakeEasy;

  factory BecomeHost.fromJson(Map<String, dynamic> json) => BecomeHost(
        keepTheTravelingExperience: json["keep_the_traveling_experience"],
        howDoWeHelpHost: json["how_do_we_help_host"],
        startHosting: json["start_hosting"],
        seeListingAroundYou: json["see_listing_around_you"],
        presentPropertyEffectively: json["present_property_effectively"],
        helpHostToRaiseProperty: json["help_host_to_raise_property"],
        damageAssurance: json["damage_assurance"],
        forSafetyOfHostProperty: json["for_safety_of_host_property"],
        weTakeCareOfMarketing: json["we_take_care_of_marketing"],
        bestMarketingMethod: json["best_marketing_method"],
        customeDashboardMakeEasy: json["custome_dashboard_make_easy"],
      );
}

class BookingDetail {
  BookingDetail({
    required this.requestBooking,
    required this.expireIn,
    required this.property,
    required this.viewProperty,
    required this.checkIn,
    required this.viewCalender,
    required this.adjustPriceDates,
    required this.checkOut,
    required this.night,
    required this.guest,
    required this.ratePerNight,
    required this.cleanningFee,
    required this.additionalGuestFee,
    required this.securityFee,
    required this.subtotal,
    required this.hostFee,
    required this.totalPayout,
    required this.aboutTheGuest,
    required this.memberSince,
    required this.age,
    required this.viewMessHistory,
    required this.acceptRequest,
    required this.expireInData,
    required this.accept,
    required this.rateYourGuest,
    required this.decline,
    required this.acceptThisRequest,
    required this.optionalMessageRequest,
    required this.checkBoxAgree,
    required this.guaranteeTermCondition,
    required this.refundPolicyTerm,
    required this.and,
    required this.termOfService,
    required this.close,
    required this.cancelThisBooking,
    required this.improveExperience,
    required this.whatReasonCancelling,
    required this.responseNotShared,
    required this.whyDeclining,
    required this.dateAreNotAvialable,
    required this.notFeelComfortableGuest,
    required this.listingIsNotGood,
    required this.waitingMoreAttractive,
    required this.differentDateOneSelected,
    required this.spamMessage,
    required this.other,
    required this.blockCalender,
    required this.through,
    required this.discussThisRequest,
    required this.acceptOrDecline,
    required this.messagingOrContactingData,
    required this.discountAmount,
    required this.depositAmount,
    required this.depositAmountWallet,
    required this.depositMessage,
    required this.walletDeposit,
  });

  String requestBooking;
  String expireIn;
  String property;
  String viewProperty;
  String checkIn;
  String viewCalender;
  String adjustPriceDates;
  String checkOut;
  String night;
  String guest;
  String ratePerNight;
  String cleanningFee;
  String additionalGuestFee;
  String securityFee;
  String subtotal;
  String hostFee;
  String totalPayout;
  String aboutTheGuest;
  String memberSince;
  String age;
  String viewMessHistory;
  String acceptRequest;
  String expireInData;
  String accept;
  String rateYourGuest;
  String decline;
  String acceptThisRequest;
  String optionalMessageRequest;
  String checkBoxAgree;
  String guaranteeTermCondition;
  String refundPolicyTerm;
  String and;
  String termOfService;
  String close;
  String cancelThisBooking;
  String improveExperience;
  String whatReasonCancelling;
  String responseNotShared;
  String whyDeclining;
  String dateAreNotAvialable;
  String notFeelComfortableGuest;
  String listingIsNotGood;
  String waitingMoreAttractive;
  String differentDateOneSelected;
  String spamMessage;
  String other;
  String blockCalender;
  String through;
  String discussThisRequest;
  String acceptOrDecline;
  String messagingOrContactingData;
  String discountAmount;
  String depositAmount;
  String depositAmountWallet;
  String depositMessage;
  String walletDeposit;

  factory BookingDetail.fromJson(Map<String, dynamic> json) => BookingDetail(
        requestBooking: json["request_booking"],
        expireIn: json["expire_in"],
        property: json["property"],
        viewProperty: json["view_property"],
        checkIn: json["check_in"],
        viewCalender: json["view_calender"],
        adjustPriceDates: json["adjust_price_dates"],
        checkOut: json["check_out"],
        night: json["night"],
        guest: json["guest"],
        ratePerNight: json["rate_per_night"],
        cleanningFee: json["cleanning_fee"],
        additionalGuestFee: json["additional_guest_fee"],
        securityFee: json["security_fee"],
        subtotal: json["subtotal"],
        hostFee: json["host_fee"],
        totalPayout: json["total_payout"],
        aboutTheGuest: json["about_the_guest"],
        memberSince: json["member_since"],
        age: json["age"],
        viewMessHistory: json["view_mess_history"],
        acceptRequest: json["accept_request"],
        expireInData: json["expire_in_data"],
        accept: json["accept"],
        rateYourGuest: json["rate_your_guest"],
        decline: json["decline"],
        acceptThisRequest: json["accept_this_request"],
        optionalMessageRequest: json["optional_message_request"],
        checkBoxAgree: json["check_box_agree"],
        guaranteeTermCondition: json["guarantee_term_condition"],
        refundPolicyTerm: json["refund_policy_term"],
        and: json["and"],
        termOfService: json["term_of_service"],
        close: json["close"],
        cancelThisBooking: json["cancel_this_booking"],
        improveExperience: json["improve_experience"],
        whatReasonCancelling: json["what_reason_cancelling"],
        responseNotShared: json["response_not_shared"],
        whyDeclining: json["why_declining"],
        dateAreNotAvialable: json["date_are_not_avialable"],
        notFeelComfortableGuest: json["not_feel_comfortable_guest"],
        listingIsNotGood: json["listing_is_not_good"],
        waitingMoreAttractive: json["waiting_more_attractive"],
        differentDateOneSelected: json["different_date_one_selected"],
        spamMessage: json["spam_message"],
        other: json["other"],
        blockCalender: json["block_calender"],
        through: json["through"],
        discussThisRequest: json["discuss_this_request"],
        acceptOrDecline: json["accept_or_decline"],
        messagingOrContactingData: json["messaging_or_contacting_data"],
        discountAmount: json["discount_amount"] ?? "Discount Amount",
        depositAmount: json["deposit_amount"] ?? "Deposit Amount",
        depositAmountWallet:
            json["deposit_amount_wallet"] ?? "Deposit Amount in Wallet",
        depositMessage:
            json['deposit_message'] ?? "deposit successfully in wallet.",
        walletDeposit: json['wallet_deposit'] ?? "Wallet Deposit",
      );

  toJson() => {
        'why_declining': whyDeclining,
        'date_are_not_avialable': dateAreNotAvialable,
        'not_feel_comfortable_guest': notFeelComfortableGuest,
        'listing_is_not_good': listingIsNotGood,
        'waiting_more_attractive': waitingMoreAttractive,
        'different_date_one_selected': differentDateOneSelected,
        'spam_message': spamMessage,
        'other': other,
      };
}

class BookingMy {
  BookingMy({
    required this.upcomingBooking,
    required this.bookingHistory,
    required this.noBooking,
    required this.addList,
    required this.booking,
    required this.status,
    required this.dateLocation,
    required this.guest,
    required this.detail,
    required this.sendMessage,
    required this.emailContact,
    required this.total,
    required this.cancel,
    required this.allBookingHistory,
    required this.upcomingBook,
  });

  String upcomingBooking;
  String bookingHistory;
  String noBooking;
  String addList;
  String booking;
  String status;
  String dateLocation;
  String guest;
  String detail;
  String sendMessage;
  String emailContact;
  String total;
  String cancel;
  String allBookingHistory;
  String upcomingBook;

  factory BookingMy.fromJson(Map<String, dynamic> json) => BookingMy(
        upcomingBooking: json["upcoming_booking"],
        bookingHistory: json["booking_history"],
        noBooking: json["no_booking"],
        addList: json["add_list"],
        booking: json["booking"],
        status: json["status"],
        dateLocation: json["date_location"],
        guest: json["guest"],
        detail: json["detail"],
        sendMessage: json["send_message"],
        emailContact: json["email_contact"],
        total: json["total"],
        cancel: json["cancel"],
        allBookingHistory: json["all_booking_history"],
        upcomingBook: json["upcoming_book"],
      );
}

class BookingRequest {
  BookingRequest({
    required this.requestHasSent,
    required this.notAConfirmedBooking,
    required this.hearBackWithin24,
    required this.notBeCharged,
    required this.accommodateStay,
    required this.getReady,
    required this.confirmedBooking,
    required this.emailedItinerary,
  });

  String requestHasSent;
  String notAConfirmedBooking;
  String hearBackWithin24;
  String notBeCharged;
  String accommodateStay;
  String getReady;
  String confirmedBooking;
  String emailedItinerary;

  factory BookingRequest.fromJson(Map<String, dynamic> json) => BookingRequest(
      requestHasSent: json["request_has_sent"],
      notAConfirmedBooking: json["not_a_confirmed_booking"],
      hearBackWithin24: json["hear_back_within_24"],
      notBeCharged: json["not_be_charged"],
      accommodateStay: json["accommodate_stay"],
      getReady: json["get_ready"],
      confirmedBooking: json["confirmed_booking"],
      emailedItinerary: json["emailed_itinerary"]);
}

class Earning {
  Earning({
    required this.yourSales,
    required this.anySalesShown,
    required this.managesMyPhotos,
  });

  String yourSales;
  String anySalesShown;
  String managesMyPhotos;

  factory Earning.fromJson(Map<String, dynamic> json) => Earning(
        yourSales: json["your_sales"],
        anySalesShown: json["any_sales_shown"],
        managesMyPhotos: json["manages_my_photos"],
      );

  Map<String, dynamic> toJson() => {
        "your_sales": yourSales,
        "any_sales_shown": anySalesShown,
        "manages_my_photos": managesMyPhotos,
      };
}

class EmailTemplate {
  EmailTemplate({
    required this.acceptDecline,
    required this.resetPassword,
    required this.confirmEmail,
    required this.addPaymentMethod,
    required this.transactionHistory,
  });

  String acceptDecline;
  String resetPassword;
  String confirmEmail;
  String addPaymentMethod;
  String transactionHistory;

  factory EmailTemplate.fromJson(Map<String, dynamic> json) => EmailTemplate(
        acceptDecline: json["accept_decline"],
        resetPassword: json["reset_password"],
        confirmEmail: json["confirm_email"],
        addPaymentMethod: json["add_payment_method"],
        transactionHistory: json["transaction_history"],
      );
}

class Error {
  Error({
    required this.oops,
    required this.unauthorizedAction,
    required this.errorData1,
    required this.errorData2,
    required this.bookingExpireError,
    required this.loginInfoError,
    required this.tokenExpireError,
    required this.propertyAvailableError,
    required this.paymentRequestError,
    required this.paymentProcessError,
    required this.imageBuyError,
    required this.bookingCancelError,
    required this.loginError,
    required this.payoutError,
    required this.payoutAccountError,
    required this.networkError,
    required this.currencyChange,
    required this.amountToLow,
  });

  String oops;
  String unauthorizedAction;
  String errorData1;
  String errorData2;
  String bookingExpireError;
  String loginInfoError;
  String tokenExpireError;
  String propertyAvailableError;
  String paymentRequestError;
  String paymentProcessError;
  String imageBuyError;
  String bookingCancelError;
  String loginError;
  String payoutError;
  String payoutAccountError;
  String networkError;
  String currencyChange;
  String amountToLow;

  factory Error.fromJson(Map<String, dynamic> json) => Error(
        oops: json["oops"],
        unauthorizedAction: json["unauthorized_action"],
        errorData1: json["error_data_1"],
        errorData2: json["error_data_2"],
        bookingExpireError: json["booking_expire_error"],
        loginInfoError: json["login_info_error"],
        tokenExpireError: json["token_expire_error"],
        propertyAvailableError: json["property_available_error"],
        paymentRequestError: json["payment_request_error"],
        paymentProcessError: json["payment_process_error"],
        imageBuyError: json["image_buy_error"],
        bookingCancelError: json["booking_cancel_error"],
        loginError: json["login_error"],
        payoutError: json["payout_error"],
        payoutAccountError: json["payout_account_error"],
        networkError:
            json["network_error"] ?? "Network error! Please try again.",
        currencyChange: json["currency_change"],
        amountToLow: json['low_amount'] ?? "Your order amount is too low",
      );

  Map<String, dynamic> toJson() => {
        "oops": oops,
        "unauthorized_action": unauthorizedAction,
        "error_data_1": errorData1,
        "error_data_2": errorData2,
        "booking_expire_error": bookingExpireError,
        "login_info_error": loginInfoError,
        "token_expire_error": tokenExpireError,
        "property_available_error": propertyAvailableError,
        "payment_request_error": paymentRequestError,
        "payment_process_error": paymentProcessError,
        "image_buy_error": imageBuyError,
        "booking_cancel_error": bookingCancelError,
        "login_error": loginError,
        "payout_error": payoutError,
        "payout_account_error": payoutAccountError,
        "network_error": networkError,
        "currency_change": currencyChange,
        "low_amount": amountToLow,
      };
}

class Favourite {
  Favourite({
    required this.add,
    required this.remove,
  });

  String add;
  String remove;

  factory Favourite.fromJson(Map<String, dynamic> json) => Favourite(
        add: json["add"],
        remove: json["remove"],
      );
}

class Feeds {
  Feeds({
    required this.photoUpload,
  });

  String photoUpload;

  factory Feeds.fromJson(Map<String, dynamic> json) => Feeds(
        photoUpload: json["photo_upload"],
      );

  Map<String, dynamic> toJson() => {
        "photo_upload": photoUpload,
      };
}

class Filter {
  Filter({
    required this.dateRange,
    required this.status,
    required this.pickDateRange,
    required this.all,
    required this.completed,
    required this.future,
    required this.filter,
    required this.reset,
    required this.typesOfPlace,
    required this.searchProperty,
    required this.nothingSelected,
    required this.any,
    required this.apply,
    required this.cancel,
    required this.showResults,
    required this.recommendedProperties,
    required this.propertyTypes,
    required this.minError,
    required this.maxError,
  });

  String dateRange;
  String status;
  String pickDateRange;
  String all;
  String completed;
  String future;
  String filter;
  String reset;
  String typesOfPlace;
  String searchProperty;
  String nothingSelected;
  String any;
  String apply;
  String cancel;
  String showResults;
  String recommendedProperties;
  String propertyTypes;
  String minError;
  String maxError;

  factory Filter.fromJson(Map<String, dynamic> json) => Filter(
        dateRange: json["date_range"],
        status: json["status"],
        pickDateRange: json["pick_date_range"],
        all: json["all"],
        completed: json["completed"],
        future: json["future"],
        filter: json["filter"],
        reset: json["reset"],
        typesOfPlace: json["types_of_place"],
        searchProperty: json["search_property"],
        nothingSelected: json["nothing_selected"],
        any: json["any"],
        apply: json["apply"],
        cancel: json["cancel"],
        showResults: json["show_results"],
        recommendedProperties: json["recommended_properties"]??'',
        propertyTypes: json["property_types"]??'',
        minError: json["min_error"]??'Minimum amount can not exceed the maximum amount.',
        maxError: json["max_error"]??'Maximum amount can not be less than minimum amount.',
      );
}

class Footer {
  Footer({
    required this.aboutUs,
    required this.contactUs,
    required this.contactGuest,
    required this.termsPrivacy,
    required this.becomeAHost,
    required this.hosting,
    required this.forYou,
    required this.support,
    required this.faq,
    required this.privacyPolicy,
    required this.darent,
    required this.home,
    required this.approximateLocation,
    required this.alert,
    required this.getDirections,
    required this.setting,
    required this.language,
    required this.wishlistSubTitle,
    required this.wishlistDescription,
    required this.inboxSubTitle,
    required this.inboxDescription,
    required this.reservationSubTitle,
    required this.reservationDescription,
    required this.profileSubTitle,
    required this.profileDescription,
    required this.properties,
    required this.myAccount,
  });

  String aboutUs;
  String contactUs;
  String termsPrivacy;
  String becomeAHost;
  String hosting;
  String contactGuest;
  String forYou;
  String support;
  String faq;
  String privacyPolicy;
  String darent;
  String home;
  String approximateLocation;
  String alert;
  String getDirections;
  String setting;
  String language;
  String wishlistSubTitle;
  String wishlistDescription;
  String inboxSubTitle;
  String inboxDescription;
  String reservationSubTitle;
  String reservationDescription;
  String profileSubTitle;
  String profileDescription;
  String properties;
  String myAccount;

  factory Footer.fromJson(Map<String, dynamic> json) => Footer(
      aboutUs: json["about_us"],
      contactUs: json["contact_us"],
      contactGuest: json["contact_guest"],
      termsPrivacy: json["terms_&_privacy"],
      becomeAHost: json["become_a_host"],
      hosting: json["hosting"],
      forYou: json['for_you'],
      support: json['support'],
      faq: json['faq'],
      privacyPolicy: json['privacy_policy'],
      darent: json['darent'],
      home: json['home'],
      approximateLocation: json["approximate_location"],
      alert: json["alert"],
      getDirections: json["get_directions"],
      setting: json["setting"],
      language: json["language"],
      wishlistSubTitle: json["wishlist_subtitle"],
      wishlistDescription: json["wishlist_desc"],
      inboxSubTitle: json["inbox_subtitle"],
      inboxDescription: json["inbox_desc"],
      reservationDescription: json["reservation_desc"],
      reservationSubTitle: json["reservation_subtitle"],
      profileDescription: json["profile_subtitle"],
      profileSubTitle: json["profile_desc"],
      properties: json["properties"] ?? "Properties",
      myAccount: json["my_account"] ?? "My Account");

  Map<String, dynamic> toJson() => {
        "about_us": aboutUs,
        "contact_us": contactUs,
        "contact_guest": contactGuest,
        "terms_&_privacy": termsPrivacy,
        "become_a_host": becomeAHost,
        "hosting": hosting,
        "for_you": forYou,
        "support": support,
        "faq": faq,
        "privacy_policy": privacyPolicy,
        "approximate_location": approximateLocation,
        "alert": alert,
        "get_directions": getDirections,
        "wishlist_subtitle": wishlistSubTitle,
        "wishlist_desc": wishlistDescription,
        "reservation_desc": reservationDescription,
        "reservation_subtitle": reservationSubTitle,
        "inbox_subtitle": inboxSubTitle,
        "inbox_desc": inboxDescription,
        "profile_subtitle": profileSubTitle,
        "profile_desc": profileDescription,
        "properties": properties,
        "my_account": myAccount
      };
}

class ForgotPass {
  ForgotPass({
    required this.pleaseEnterEmail,
    required this.resetPass,
    required this.resetLink,
    required this.resetPassword,
    required this.newPass,
    required this.confirmPass,
  });

  String pleaseEnterEmail;
  String resetPass;
  String resetLink;
  String resetPassword;
  String newPass;
  String confirmPass;

  factory ForgotPass.fromJson(Map<String, dynamic> json) => ForgotPass(
        pleaseEnterEmail: json["please_enter_email"],
        resetPass: json["reset_pass"],
        resetLink: json["reset_link"],
        resetPassword: json["reset_password"],
        newPass: json["new_pass"],
        confirmPass: json["confirm_pass"],
      );

  Map<String, dynamic> toJson() => {
        "please_enter_email": pleaseEnterEmail,
        "reset_pass": resetPass,
        "reset_link": resetLink,
        "reset_password": resetPassword,
        "new_pass": newPass,
        "confirm_pass": confirmPass,
      };
}

class General {
  General(
      {required this.blog,
      required this.walletPay,
      required this.yes,
      required this.no,
      required this.confirm,
      required this.attach,
      required this.image,
      required this.noData,
      required this.ifAny,
      required this.monday,
      required this.tuesday,
      required this.wednesday,
      required this.thursday,
      required this.friday,
      required this.saturday,
      required this.sunday,
      required this.search,
      required this.seeAll,
      required this.warning,
      required this.pleaseVerifyYourElmYaqeen,
      required this.verify,
      required this.sentReservationRequest,
      required this.reservationRequestSuggestion,
      required this.beforeCancelSeeCancelPolicy,
      required this.hostCancelPolicyDetermine,
      required this.yourReservationCancelSuccessfully,
      required this.wouldYouLikeToReserveMore,
      required this.ok,
      required this.verifyNow,
      required this.addLicense,
        required this.notAvailable,
        required this.highDemand,
        required this.topFeature,
      });

  String? blog;
  String? walletPay;
  String yes;
  String no;
  String confirm;
  String attach;
  String image;
  String noData;
  String? ifAny;
  String monday;
  String tuesday;
  String wednesday;
  String thursday;
  String friday;
  String saturday;
  String sunday;
  String search;
  String seeAll;
  String warning;
  String pleaseVerifyYourElmYaqeen;
  String verify;
  String sentReservationRequest;
  String reservationRequestSuggestion;
  String beforeCancelSeeCancelPolicy;
  String hostCancelPolicyDetermine;
  String yourReservationCancelSuccessfully;
  String wouldYouLikeToReserveMore;
  String ok;
  String verifyNow;
  String addLicense;
  String notAvailable;
  String highDemand;
  String topFeature;

  factory General.fromJson(Map<String, dynamic> json) => General(
        blog: json['blog'] ?? "Blog",
        walletPay: json["wallet_pay"] ?? "Wallet Pay",
        yes: json["yes"],
        no: json["no"],
        confirm: json["confirm"],
        attach: json["attach"],
        image: json["image"],
        noData: json["no_data"],
        ifAny: json["if_any"] ?? "If any",
        monday: json["monday"] ?? "Monday",
        tuesday: json["tuesday"] ?? "Tuesday",
        wednesday: json["wednesday"] ?? "Wednesday",
        thursday: json["thursday"] ?? "Thursday",
        friday: json["friday"] ?? "Friday",
        saturday: json["saturday"] ?? "Saturday",
        sunday: json["sunday"] ?? "Sunday",
        search: json['search'],
        seeAll: json['see_all'],
        warning: json['warning'],
        pleaseVerifyYourElmYaqeen: json['please_verify_your_elm_yaqeen'],
        verify: json['verify'],
        sentReservationRequest: json['sent_reservation_request'],
        reservationRequestSuggestion: json['reservation_request_suggestion'],
        beforeCancelSeeCancelPolicy: json['before_cancel_see_cancel_policy'],
        hostCancelPolicyDetermine: json['host_cancel_policy_determine'],
        yourReservationCancelSuccessfully:
            json['your_reservation_cancel_successfully'],
        wouldYouLikeToReserveMore: json['would_you_like_to_reserve_more'],
        ok: json['ok'],
        verifyNow: json['verify_now'] ?? 'Verify Now',
        addLicense: json['add_license'] ?? 'Add License',
        notAvailable: json['not_available'] ?? 'Not Available',
      highDemand:json['high_demand'] ?? 'High demand',
      topFeature:json['top_feature'] ?? 'Top Feature',
      );

  toJson() => {
        "monday": monday,
        "tuesday": tuesday,
        "wednesday": wednesday,
        "thursday": thursday,
        "friday": friday,
        "saturday": saturday,
        "sunday": sunday,
      };
}

class AccountMobile {
  String? viewProfile;
  String? profile;
  String? settings;
  String? accountInfo;
  String? loginAndSecurity;
  String? walletAndPayments;
  String? hosting;
  String? switchToHosting;
  String? switchToTravelling;
  String? pages;
  String? support;
  String? customerServices;
  String? termsAndConditions;
  String? browseIn;
  String? logOut;
  String? darentAgent;
  String? sendMsgAndOneOurGuestContact;
  String? enterYourMsgHere;
  String? appInfo;
  String? appVersion;

  AccountMobile(
      {this.viewProfile,
      this.profile,
      this.settings,
      this.accountInfo,
      this.loginAndSecurity,
      this.walletAndPayments,
      this.hosting,
      this.switchToHosting,
      this.switchToTravelling,
      this.pages,
      this.support,
      this.customerServices,
      this.termsAndConditions,
      this.browseIn,
      this.logOut,
      this.darentAgent,
      this.sendMsgAndOneOurGuestContact,
      this.enterYourMsgHere,
      this.appInfo,
      this.appVersion});

  AccountMobile.fromJson(Map<String, dynamic> json) {
    viewProfile = json['view_profile'];
    profile = json['profile'];
    settings = json['settings'];
    accountInfo = json['account_info'];
    loginAndSecurity = json['login_and_security'];
    walletAndPayments = json['wallet_and_payments'];
    hosting = json['hosting'];
    switchToHosting = json['switch_to_hosting'];
    switchToTravelling = json['switch_to_travelling'];
    pages = json['pages'];
    support = json['support'];
    customerServices = json['customer_services'];
    termsAndConditions = json['terms_and_conditions'];
    browseIn = json['browse_in'];
    logOut = json['log_out'];
    darentAgent = json['darent_agent'];
    sendMsgAndOneOurGuestContact = json['send_msg_and_one_our_guest_contact'];
    enterYourMsgHere = json['enter_your_msg_here'];
    appInfo = json['app_info'];
    appVersion = json['app_version'];
  }
}

class PromoCode {
  String promoMustBeEight;
  String promotions;
  String promotion;
  String createPromo;
  String promoCode;
  String deletePromo;
  String editPromo;
  String expireOn;
  String expireAt;
  String remainingUsage;
  String updatePromo;
  String updatePomocode;
  String noPromotionsAvailable;
  String title;
  String discount;
  String generateCode;
  String uptoSar;
  String maxUsage;
  String perUserUsage;
  String selectProperty;
  String selectAll;
  String description;
  String forNewUser;
  String promo;
  String discountUpto;
  String clickHere;
  String placeProper;
  String validUntil;

  PromoCode({
    required this.promoMustBeEight,
    required this.promotions,
    required this.promotion,
    required this.createPromo,
    required this.promoCode,
    required this.deletePromo,
    required this.editPromo,
    required this.expireOn,
    required this.expireAt,
    required this.remainingUsage,
    required this.updatePromo,
    required this.updatePomocode,
    required this.noPromotionsAvailable,
    required this.title,
    required this.discount,
    required this.generateCode,
    required this.uptoSar,
    required this.maxUsage,
    required this.perUserUsage,
    required this.selectProperty,
    required this.selectAll,
    required this.description,
    required this.forNewUser,
    required this.promo,
    required this.discountUpto,
    required this.clickHere,
    required this.placeProper,
    required this.validUntil,
  });

  factory PromoCode.fromJson(Map json) {
    return PromoCode(
      promoMustBeEight:
          json["code_must_be_eight"] ?? "Promo code must be 8 characters long",
      promotions: json['promotions'],
      promotion: json["promotion"],
      createPromo: json["create_promo"],
      promoCode: json["promocode"],
      deletePromo: json["delete_promo"],
      editPromo: json["edit_promo"],
      expireOn: json["expire_on"],
      expireAt: json["expire_at"],
      remainingUsage: json["remaining_usage"],
      updatePromo: json["update_promo"],
      updatePomocode: json["update_pomocode"],
      noPromotionsAvailable: json["no_promotions_available"],
      title: json["title"],
      discount: json["discount"],
      generateCode: json["generate_code"],
      uptoSar: json["upto_sar"],
      maxUsage: json["max_usage"],
      perUserUsage: json["per_user_usage"],
      selectProperty: json["select_property"],
      selectAll: json["select_all"],
      description: json["description"],
      forNewUser: json["for_new_user"],
      promo: json["promo"] ?? "PROMO",
      discountUpto: json["discount_upto"] ?? "Discount upto",
      clickHere: json["click_here"] ?? "Click here to select",
      placeProper: json["place_proper"] ?? "Place a proper description",
      validUntil: json["valid_until"] ?? "Valid Until",
    );
  }
}

class ContactHost {
  String typicallyResponseInHour;
  String contact;
  String checkinCheckoutBetween;
  String stillHaveQuestion;
  String typeHere;
  String checkAvailability;

  ContactHost({
    required this.typicallyResponseInHour,
    required this.contact,
    required this.checkinCheckoutBetween,
    required this.stillHaveQuestion,
    required this.typeHere,
    required this.checkAvailability,
  });

  factory ContactHost.fromJson(Map json) {
    return ContactHost(
      typicallyResponseInHour: json['typically_response_in_hour'],
      contact: json['contact'],
      checkinCheckoutBetween: json['checkin_checkout_between'],
      stillHaveQuestion: '${json['still_have']} ${json['question']}',
      typeHere: json['type_here'],
      checkAvailability: json['check_availability'] ?? 'Check Availibility',
    );
  }
}

class CoHost {
  String title;
  String cohostTitle;
  String getStarted;
  String startingDescription;
  String inviteCoHost;
  String inviteEmailPhone;
  String setPermission;
  String alwaysChange;
  String inviteReady;
  String reviewDetails;
  String inviteSentOn;
  String currentStatus;
  String permission;
  String submitAcknowledge;
  String cohostTerms;
  String fullAccessTitle;
  String fullAccessSubtitle;
  String primaryHost;

  CoHost({
    required this.title,
    required this.cohostTitle,
    required this.getStarted,
    required this.startingDescription,
    required this.inviteCoHost,
    required this.inviteEmailPhone,
    required this.setPermission,
    required this.alwaysChange,
    required this.inviteReady,
    required this.reviewDetails,
    required this.inviteSentOn,
    required this.currentStatus,
    required this.permission,
    required this.submitAcknowledge,
    required this.cohostTerms,
    required this.fullAccessTitle,
    required this.fullAccessSubtitle,
    required this.primaryHost,
  });

  factory CoHost.fromJson(Map json) {
    return CoHost(
      title: json["title"],
      cohostTitle: json["cohost_title"],
      getStarted: json["get_started"],
      startingDescription: json["starting_description"],
      inviteCoHost: json["invite_coHost"],
      inviteEmailPhone: json["invite_email_phone"],
      setPermission: json["set_permission"],
      alwaysChange: json["always_change"],
      inviteReady: json["invite_ready"],
      reviewDetails: json["review_details"],
      inviteSentOn: json["Invite_senton"],
      currentStatus: json["current_status"],
      permission: json["permission"],
      submitAcknowledge: json["submit_acknowledge"],
      cohostTerms: json["cohost_terms"],
      fullAccessTitle: json["full_access_title"],
      fullAccessSubtitle: json["full_access_subtitle"],
      primaryHost: json["primary_host"],
    );
  }
}

class HostReview {
  String writeGuestReview;
  String guestReviewInstruction;
  String cleanlinessTitle;
  String subtitle;
  String selectRating;
  String whatHappend;
  String selectOption;
  String cleanlinessOption1;
  String cleanlinessOption2;
  String cleanlinessOption3;
  String cleanlinessOption4;
  String cleanlinessOption5;
  String moreDetails;
  String blockUser;
  String blockInstruction;
  String communicationTitle;
  String communicationOption1;
  String communicationOption2;
  String communicationOption3;
  String communicationOption4;
  String communicationOption5;
  String overallTitle;
  String writeReview;
  String overallInstruction;
  String privateTitle;
  String privateSubtitle;
  String writePrivateMessage;

  HostReview({
    required this.writeGuestReview,
    required this.guestReviewInstruction,
    required this.cleanlinessTitle,
    required this.subtitle,
    required this.selectRating,
    required this.whatHappend,
    required this.selectOption,
    required this.cleanlinessOption1,
    required this.cleanlinessOption2,
    required this.cleanlinessOption3,
    required this.cleanlinessOption4,
    required this.cleanlinessOption5,
    required this.moreDetails,
    required this.blockUser,
    required this.blockInstruction,
    required this.communicationTitle,
    required this.communicationOption1,
    required this.communicationOption2,
    required this.communicationOption3,
    required this.communicationOption4,
    required this.communicationOption5,
    required this.overallTitle,
    required this.writeReview,
    required this.overallInstruction,
    required this.privateTitle,
    required this.privateSubtitle,
    required this.writePrivateMessage,
  });

  factory HostReview.fromJson(Map json) {
    return HostReview(
      writeGuestReview: json["write_guest_review"],
      guestReviewInstruction: json["guest_review_instruction"],
      cleanlinessTitle: json["cleanliness_title"],
      subtitle: json["subtitle"],
      selectRating: json["select_rating"],
      whatHappend: json["what_happend"],
      selectOption: json["select_option"],
      cleanlinessOption1: json["cleanliness_option1"],
      cleanlinessOption2: json["cleanliness_option2"],
      cleanlinessOption3: json["cleanliness_option3"],
      cleanlinessOption4: json["cleanliness_option4"],
      cleanlinessOption5: json["cleanliness_option5"],
      moreDetails: json["more_details"],
      blockUser: json["block_user"],
      blockInstruction: json["block_instruction"],
      communicationTitle: json["communication_title"],
      communicationOption1: json["communication_option1"],
      communicationOption2: json["communication_option2"],
      communicationOption3: json["communication_option3"],
      communicationOption4: json["communication_option4"],
      communicationOption5: json["communication_option5"],
      overallTitle: json["overall_title"],
      writeReview: json["write_review"],
      overallInstruction: json["overall_instruction"],
      privateTitle: json["private_title"],
      privateSubtitle: json["private_subtitle"],
      writePrivateMessage: json["write_private_message"],
    );
  }
}

class Header {
  Header({
    required this.edit,
    required this.inbox,
    required this.signin,
    required this.join,
    required this.feeds,
    required this.explore,
    required this.upload,
    required this.bestPhotos,
    required this.newestPhotos,
    required this.viewProfile,
    required this.managePhotos,
    required this.earning,
    required this.purchase,
    required this.settings,
    required this.logout,
    required this.filter,
    required this.allPhotos,
    required this.forSale,
    required this.allCategories,
    required this.listSpace,
    required this.home,
    required this.contactUs,
    required this.login,
    required this.dashboard,
    required this.yourListing,
    required this.propertyBooking,
    required this.yourTrip,
    required this.editProfile,
    required this.account,
    required this.checkIn,
    required this.checkOut,
    required this.guest,
    required this.findPlace,
    required this.whereAreYouGoing,
    required this.whereToGo,
    required this.selectLocation,
    required this.numberOfGuests,
    required this.adult,
    required this.children,
    required this.moreThan13Y,
    required this.lessThan13Y,
    required this.searchHere,
    required this.whereToQ,
    required this.whenQ,
    required this.guestQ,
    required this.camera,
    required this.gallery,
    required this.promoCreated,
    required this.promoUpdated,
    required this.sureToDelete,
  });

  String edit;
  String inbox;
  String signin;
  String join;
  String feeds;
  String explore;
  String upload;
  String bestPhotos;
  String newestPhotos;
  String viewProfile;
  String managePhotos;
  String earning;
  String purchase;
  String settings;
  String logout;
  String filter;
  String allPhotos;
  String forSale;
  String allCategories;
  String listSpace;
  String home;
  String contactUs;
  String login;
  String dashboard;
  String yourListing;
  String propertyBooking;
  String yourTrip;
  String editProfile;
  String account;
  String checkIn;
  String checkOut;
  String guest;
  String findPlace;
  String whereAreYouGoing;
  String whereToGo;
  String selectLocation;
  String numberOfGuests;
  String adult;
  String children;
  String moreThan13Y;
  String lessThan13Y;
  String searchHere;
  String whereToQ;
  String whenQ;
  String guestQ;
  String camera;
  String gallery;
  String promoCreated;
  String promoUpdated;
  String sureToDelete;

  factory Header.fromJson(Map<String, dynamic> json) => Header(
        edit: json["edit"],
        inbox: json["inbox"],
        signin: json["signin"],
        join: json["join"],
        feeds: json["feeds"],
        explore: json["explore"],
        upload: json["upload"],
        bestPhotos: json["best_photos"],
        newestPhotos: json["newest_photos"],
        viewProfile: json["view_profile"],
        managePhotos: json["manage_photos"],
        earning: json["earning"],
        purchase: json["purchase"],
        settings: json["settings"],
        logout: json["logout"],
        filter: json["filter"],
        allPhotos: json["all_photos"],
        forSale: json["for_sale"],
        allCategories: json["all_categories"],
        listSpace: json["list_space"],
        home: json["home"],
        contactUs: json["contact_us"],
        login: json["login"],
        dashboard: json["dashboard"],
        yourListing: json["your_listing"],
        propertyBooking: json["property_booking"],
        yourTrip: json["your_trip"],
        editProfile: json["edit_profile"],
        account: json["account"],
        checkIn: json["check_in"],
        checkOut: json["check_out"],
        guest: json["guest"],
        findPlace: json["find_place"],
        whereAreYouGoing: json["where_are_you_going"],
        whereToGo: json["where_to_go"],
        selectLocation: json["select_location"],
        numberOfGuests: json["number_of_guests"],
        adult: json["adult"],
        children: json["children"],
        moreThan13Y: json["more_than_13_y"],
        lessThan13Y: json["less_than_13_y"],
        searchHere: json["search_here"],
        whereToQ: json["where_to_q"],
        whenQ: json["when_q"],
        guestQ: json["guest_q"],
        camera: json["camera"] ?? "Camera",
        gallery: json["gallery"] ?? "Gallery",
        promoCreated: json["promo_created"] ??
            "Promo code has been created successfully!",
        promoUpdated: json["promo_updated"] ??
            "Promo code has been updated successfully!",
        sureToDelete: json["sure_to_delete"] ??
            "Are you sure you want to delete this Promotion?",
      );
}

class Home {
  Home(
      {required this.whereWantToGo,
      required this.guest,
      required this.search,
      required this.makeYourReservation,
      required this.topDestination,
      required this.popularLocation,
      required this.recommendedHome,
      required this.peopleSay,
      required this.chooseLanguage,
      required this.chooseCurrency,
      required this.allRightsReserved,
      required this.destinationSlogan,
      required this.recommendedSlogan,
      required this.sayAboutUs,
      required this.noDescription,
      required this.unitCode});

  String whereWantToGo;
  String guest;
  String search;
  String makeYourReservation;
  String topDestination;
  String popularLocation;
  String recommendedHome;
  String peopleSay;
  String chooseLanguage;
  String chooseCurrency;
  String allRightsReserved;
  String destinationSlogan;
  String recommendedSlogan;
  String sayAboutUs;
  String noDescription;
  String unitCode;

  factory Home.fromJson(Map<String, dynamic> json) => Home(
        whereWantToGo: json["where_want_to_go"],
        guest: json["guest"],
        search: json["search"],
        makeYourReservation: json["make_your_reservation"],
        topDestination: json["top_destination"],
        popularLocation: json["popular_location"],
        recommendedHome: json["recommended_home"],
        peopleSay: json["people_say"],
        chooseLanguage: json["choose_language"],
        chooseCurrency: json["choose_currency"],
        allRightsReserved: json["all_rights_reserved"],
        destinationSlogan: json["destination_slogan"],
        recommendedSlogan: json["recommended_slogan"],
        sayAboutUs: json["say_about_us"],
        noDescription: json["no_description"],
        unitCode: json["unit_code"],
      );
}

class Homepage {
  Homepage({
    required this.discover,
    required this.newWorld,
    required this.exploreNextVacation,
    required this.getFeature,
    required this.comingSoon,
    required this.termsCondition,
  });

  String discover;
  String newWorld;
  String exploreNextVacation;
  String getFeature;
  String comingSoon;
  String termsCondition;

  factory Homepage.fromJson(Map<String, dynamic> json) => Homepage(
        discover: json["discover"],
        newWorld: json["new_world"],
        exploreNextVacation: json["explore_next_vacation"],
        getFeature: json["get_feature"],
        comingSoon: json["coming_soon"],
        termsCondition: json["terms_condition"],
      );

  Map<String, dynamic> toJson() => {
        "discover": discover,
        "new_world": newWorld,
        "explore_next_vacation": exploreNextVacation,
        "get_feature": getFeature,
        "coming_soon": comingSoon,
        "terms_condition": termsCondition,
      };
}

class Ical {
  Ical(
      {required this.importCalendar,
      required this.syncWithOther,
      required this.exportCalendar,
      required this.importANew,
      required this.calendarAddress,
      required this.pasteCalendarAddress,
      required this.nameCalendar,
      required this.yourCalendarName,
      required this.colorOfCalendar,
      required this.setCustomColor,
      required this.visitWebsiteForCustomColor,
      required this.copyPasteLink,
      required this.pleaseSelect,
      required this.custom,
      required this.syncSuccessful,
      required this.status,
      required this.successMessage,
      required this.noSyncData,
      required this.needHelpFindingCalendar,
      required this.calendarName});

  String importCalendar;
  String syncWithOther;
  String exportCalendar;
  String importANew;
  String calendarAddress;
  String pasteCalendarAddress;
  String nameCalendar;
  String yourCalendarName;
  String colorOfCalendar;
  String setCustomColor;
  String visitWebsiteForCustomColor;
  String copyPasteLink;
  String pleaseSelect;
  String custom;
  String syncSuccessful;
  String status;
  String successMessage;
  String noSyncData;
  String needHelpFindingCalendar;
  String calendarName;

  factory Ical.fromJson(Map<String, dynamic> json) => Ical(
      importCalendar: json["import_calendar"],
      syncWithOther: json["sync_with_other"],
      exportCalendar: json["export_calendar"],
      importANew: json["import_a_new"],
      calendarAddress: json["calendar_address"],
      pasteCalendarAddress: json["paste_calendar_address"],
      nameCalendar: json["name_calendar"],
      yourCalendarName: json["your_calendar_name"],
      colorOfCalendar: json["color_of_calendar"],
      setCustomColor: json["set_custom_color"],
      visitWebsiteForCustomColor: json["visit_website_for_custom_color"],
      copyPasteLink: json["copy_paste_link"],
      pleaseSelect: json["please_select"],
      custom: json["custom"],
      syncSuccessful: json["sync_successful"],
      status: json["status"],
      successMessage: json["success_message"],
      noSyncData: json["no_sync_data"],
      needHelpFindingCalendar: json["need_help_finding_calendar"] ??
          "Need help finding your calendar link",
      calendarName: json['calendar_name'] ?? "Calendar Name");

  Map<String, dynamic> toJson() => {
        "import_calendar": importCalendar,
        "sync_with_other": syncWithOther,
        "export_calendar": exportCalendar,
        "import_a_new": importANew,
        "calendar_address": calendarAddress,
        "paste_calendar_address": pasteCalendarAddress,
        "name_calendar": nameCalendar,
        "your_calendar_name": yourCalendarName,
        "color_of_calendar": colorOfCalendar,
        "set_custom_color": setCustomColor,
        "visit_website_for_custom_color": visitWebsiteForCustomColor,
        "copy_paste_link": copyPasteLink,
        "please_select": pleaseSelect,
        "custom": custom,
        "sync_successful": syncSuccessful,
        "status": status,
        "success_message": successMessage,
        "no_sync_data": noSyncData,
      };
}

class JqueryValidation {
  JqueryValidation({
    required this.required,
    required this.email,
    required this.maxlength255,
    required this.maxlength30,
    required this.maxlength500,
    required this.minlength6,
    required this.max99,
    required this.min0,
    required this.min5,
    required this.url,
    required this.date,
    required this.dateIso,
    required this.number,
    required this.digits,
    required this.equalTo,
    required this.ageGreaterThan18,
    required this.emailExisted,
    required this.imageAccept,
    required this.oldPasswordDifferent,
    required this.emailNotExisted,
    required this.acceptTermsConditions,
    required this.choosePayoutMethod,
    required this.areYouSureToDelete,
    required this.cvvMust3,
    required this.invalidFormat,
    required this.cardMust16,
    required this.somethingMiss,
    required this.textInArabic,
    required this.textInEnglish,
    required this.selectPropertyDiscount,
    required this.specialCharacter,
    required this.zeroUsage,
  });

  String required;
  String email;
  String maxlength255;
  String maxlength30;
  String maxlength500;
  String minlength6;
  String max99;
  String min0;
  String min5;
  String url;
  String date;
  String dateIso;
  String number;
  String digits;
  String equalTo;
  String ageGreaterThan18;
  String emailExisted;
  String imageAccept;
  String oldPasswordDifferent;
  String emailNotExisted;
  String acceptTermsConditions;
  String choosePayoutMethod;
  String areYouSureToDelete;
  String cvvMust3;
  String invalidFormat;
  String cardMust16;
  String somethingMiss;
  String textInArabic;
  String textInEnglish;
  String selectPropertyDiscount;
  String specialCharacter;
  String zeroUsage;

  factory JqueryValidation.fromJson(Map<String, dynamic> json) =>
      JqueryValidation(
          required: json["required"],
          email: json["email"],
          maxlength255: json["maxlength255"],
          maxlength30: json["maxlength30"],
          maxlength500: json["maxlength500"],
          minlength6: json["minlength6"],
          max99: json["max99"],
          min0: json["min0"],
          min5: json["min5"],
          url: json["url"],
          date: json["date"],
          dateIso: json["dateISO"],
          number: json["number"],
          digits: json["digits"],
          equalTo: json["equalTo"],
          ageGreaterThan18: json["age_greater_than_18"],
          emailExisted: json["email_existed"],
          imageAccept: json["image_accept"],
          oldPasswordDifferent: json["old_password_different"],
          emailNotExisted: json["email_not_existed"],
          acceptTermsConditions: json["accept_terms_conditions"],
          choosePayoutMethod: json["choose_payout_method"],
          areYouSureToDelete: json["are_you_sure_to_delete"],
          cvvMust3: json["cvv_must3"] ?? "Cvv must be 3",
          invalidFormat: json["invalid_format"] ?? "Invalid Format",
          cardMust16: json["card_must16"] ?? "Card Must Be 16 Digits",
          somethingMiss: json["something_miss"] ?? "Something is missing",
          textInArabic:
              json["text_in_ar"] ?? "The text should be entered in Arabic",
          textInEnglish:
              json["text_in_eng"] ?? "The text should be entered in English",
          selectPropertyDiscount: json["select_property_discount"] ??
              "Select properties to offer discount",
          specialCharacter:
              json["special_character"] ?? "Avoid using special character",
          zeroUsage: json["zero_usage"] ?? "Can't Be Accepted 0 Usage");

  Map<String, dynamic> toJson() => {
        "required": required,
        "email": email,
        "maxlength255": maxlength255,
        "maxlength30": maxlength30,
        "maxlength500": maxlength500,
        "minlength6": minlength6,
        "max99": max99,
        "min0": min0,
        "min5": min5,
        "url": url,
        "date": date,
        "dateISO": dateIso,
        "number": number,
        "digits": digits,
        "equalTo": equalTo,
        "age_greater_than_18": ageGreaterThan18,
        "email_existed": emailExisted,
        "image_accept": imageAccept,
        "old_password_different": oldPasswordDifferent,
        "email_not_existed": emailNotExisted,
        "accept_terms_conditions": acceptTermsConditions,
        "choose_payout_method": choosePayoutMethod,
        "are_you_sure_to_delete": areYouSureToDelete,
        "cvv_must3": cvvMust3,
        "invalid_format": invalidFormat,
        "card_must16": cardMust16,
        "something_miss": somethingMiss,
        "text_in_eng": textInEnglish,
        "text_in_ar": textInArabic,
        "select_property_discount": selectPropertyDiscount,
        "special_character": specialCharacter,
        "zero_usage": zeroUsage
      };
}

class ListingBasic {
  ListingBasic({
    required this.roomBed,
    required this.bedroom,
    required this.bed,
    required this.bathroom,
    required this.bedType,
    required this.propertyType,
    required this.listing,
    required this.roomType,
    required this.accommodate,
    required this.next,
    required this.nights,
    required this.maximumNights,
    required this.minimumNights,
    required this.incompleteListing,
    required this.completeListing,
    required this.noIncompleteList,
    required this.noCompleteList,
    required this.skip,
  });

  String roomBed;
  String bedroom;
  String bed;
  String bathroom;
  String bedType;
  String propertyType;
  String listing;
  String roomType;
  String accommodate;
  String next;
  String nights;
  String maximumNights;
  String minimumNights;
  String incompleteListing;
  String completeListing;
  String noIncompleteList;
  String noCompleteList;
  String skip;

  factory ListingBasic.fromJson(Map<String, dynamic> json) => ListingBasic(
        roomBed: json["room_bed"],
        bedroom: json["bedroom"],
        bed: json["bed"],
        bathroom: json["bathroom"],
        bedType: json["bed_type"],
        propertyType: json["property_type"],
        listing: json["listing"],
        roomType: json["room_type"],
        accommodate: json["accommodate"],
        next: json["next"],
        nights: json["nights"],
        maximumNights: json["maximum_nights"],
        minimumNights: json["minimum_nights"],
        incompleteListing: json["incompleteListing"],
        completeListing: json["completeListing"],
        noIncompleteList: json["no_incomplete_list"],
        noCompleteList: json["no_complete_list"],
        skip: json["skip"] ?? 'Skip',
      );

  Map<String, dynamic> toJson() => {
        "room_bed": roomBed,
        "bedroom": bedroom,
        "bed": bed,
        "bathroom": bathroom,
        "bed_type": bedType,
        "property_type": propertyType,
        "listing": listing,
        "room_type": roomType,
        "accommodate": accommodate,
        "next": next,
        "nights": nights,
        "maximum_nights": maximumNights,
        "minimum_nights": minimumNights,
        "incompleteListing": incompleteListing,
        "completeListing": completeListing,
      };
}

class ListingBook {
  ListingBook({
    required this.bookingTitle,
    required this.bookingData,
    required this.bookingType,
    required this.reviewRequest,
    required this.guestInstant,
    required this.bookNow,
    required this.requestMessage,
    required this.hostCurrency,
  });

  String bookingTitle;
  String bookingData;
  String bookingType;
  String reviewRequest;
  String guestInstant;
  String bookNow;
  String requestMessage;
  String hostCurrency;

  factory ListingBook.fromJson(Map<String, dynamic> json) => ListingBook(
        bookingTitle: json["booking_title"],
        bookingData: json["booking_data"],
        bookingType: json["booking_type"],
        reviewRequest: json["review_request"],
        guestInstant: json["guest_instant"],
        bookNow: json["book_now"],
        requestMessage: json["request_message"],
        hostCurrency: json["host_currency"],
      );

  Map<String, dynamic> toJson() => {
        "booking_title": bookingTitle,
        "booking_data": bookingData,
        "booking_type": bookingType,
        "review_request": reviewRequest,
        "guest_instant": guestInstant,
        "book_now": bookNow,
        "request_message": requestMessage,
        "host_currency": hostCurrency,
      };
}

class ListingCalendar {
  ListingCalendar({
    required this.calendarTitle,
    required this.startDate,
    required this.endDate,
    required this.price,
    required this.submit,
    required this.open,
    required this.close,
    required this.yourList,
    required this.minimumStay,
    required this.available,
    required this.unAvailable,
  });

  String calendarTitle;
  String startDate;
  String endDate;
  String price;
  String submit;
  String open;
  String close;
  String yourList;
  String minimumStay;
  String? available;
  String? unAvailable;

  factory ListingCalendar.fromJson(Map<String, dynamic> json) =>
      ListingCalendar(
          calendarTitle: json["calendar_title"],
          startDate: json["start_date"],
          endDate: json["end_date"],
          price: json["price"],
          submit: json["submit"],
          open: json["open"],
          close: json["close"],
          yourList: json["your_list"],
          minimumStay: json["minimum_stay"],
          available: json["available"],
          unAvailable: json["unavailable"] ?? "Un Available");

  Map<String, dynamic> toJson() => {
        "calendar_title": calendarTitle,
        "start_date": startDate,
        "end_date": endDate,
        "price": price,
        "submit": submit,
        "close": close,
        "your_list": yourList,
        "minimum_stay": minimumStay,
        "available": available,
        "unavailable": unAvailable
      };
}

class ListingDescription {
  ListingDescription({
    required this.listingName,
    required this.summary,
    required this.addMore,
    required this.detail,
    required this.detailData,
    required this.back,
    required this.trip,
    required this.aboutPlace,
    required this.greatPlace,
    required this.guestAccess,
    required this.guestInteraction,
    required this.thingNote,
    required this.neighborhood,
    required this.overview,
    required this.gettingAround,
    required this.upload,
    required this.serial,
    required this.whatAreTheHighlight,
    required this.coverPhoto,
    required this.yes,
    required this.no,
  });

  String listingName;
  String summary;
  String addMore;
  String detail;
  String detailData;
  String back;
  String trip;
  String aboutPlace;
  String greatPlace;
  String guestAccess;
  String guestInteraction;
  String thingNote;
  String neighborhood;
  String overview;
  String gettingAround;
  String upload;
  String serial;
  String whatAreTheHighlight;
  String coverPhoto;
  String yes;
  String no;

  factory ListingDescription.fromJson(Map<String, dynamic> json) =>
      ListingDescription(
        listingName: json["listing_name"],
        summary: json["summary"],
        addMore: json["add_more"],
        detail: json["detail"],
        detailData: json["detail_data"],
        back: json["back"],
        trip: json["trip"],
        aboutPlace: json["about_place"],
        greatPlace: json["great_place"],
        guestAccess: json["guest_access"],
        guestInteraction: json["guest_interaction"],
        thingNote: json["thing_note"],
        neighborhood: json["neighborhood"],
        overview: json["overview"],
        gettingAround: json["getting_around"],
        upload: json["upload"],
        serial: json["serial"],
        whatAreTheHighlight: json["what_are_the_highlight"],
        coverPhoto: json["cover_photo"],
        yes: json["yes"],
        no: json["no"],
      );
}

class ListingLocation {
  ListingLocation({
    required this.country,
    required this.addressLine1,
    required this.addressLine2,
    required this.cityTownDistrict,
    required this.stateProvince,
    required this.zipPostalCode,
    required this.district,
  });

  String country;
  String addressLine1;
  String addressLine2;
  String cityTownDistrict;
  String stateProvince;
  String zipPostalCode;
  String district;

  factory ListingLocation.fromJson(Map<String, dynamic> json) =>
      ListingLocation(
        country: json["country"],
        addressLine1: json["address_line_1"],
        addressLine2: json["address_line_2"],
        cityTownDistrict: json["city_town_district"],
        stateProvince: json["state_province"],
        zipPostalCode: json["zip_postal_code"],
        district: json["district"] ?? "District",
      );

  Map<String, dynamic> toJson() => {
        "country": country,
        "address_line_1": addressLine1,
        "address_line_2": addressLine2,
        "city_town_district": cityTownDistrict,
        "state_province": stateProvince,
        "zip_postal_code": zipPostalCode,
      };
}

class ListingPrice {
  ListingPrice({
    required this.basePrice,
    required this.nightPrice,
    required this.currency,
    required this.accessOffer,
    required this.weekMonth,
    required this.price,
    required this.longTermPrice,
    required this.weekPrice,
    required this.weeklyPrice,
    required this.monthPrice,
    required this.monthlyPrice,
    required this.additionalPrice,
    required this.cleaningFee,
    required this.additionalGuest,
    required this.guestAfter,
    required this.weekendPrice,
    required this.securityDeposit,
    required this.weekend,
    required this.customPrice,
    required this.commonAmenities,
    required this.safetyAmenities,
    required this.customDiscount,
    required this.dailyDiscount,
  });

  String basePrice;
  String nightPrice;
  String currency;
  String accessOffer;
  String weekMonth;
  String price;
  String longTermPrice;
  String weekPrice;
  String weeklyPrice;
  String monthPrice;
  String monthlyPrice;
  String additionalPrice;
  String cleaningFee;
  String additionalGuest;
  String guestAfter;
  String weekendPrice;
  String securityDeposit;
  String weekend;
  String customPrice;
  String commonAmenities;
  String safetyAmenities;
  String customDiscount;
  String dailyDiscount;

  factory ListingPrice.fromJson(Map<String, dynamic> json, Map anotherJson) =>
      ListingPrice(
        basePrice: json["base_price"],
        nightPrice: json["night_price"],
        currency: json["currency"],
        accessOffer: json["access_offer"],
        weekMonth: json["week_month"],
        price: json["price"],
        longTermPrice: json["long_term_price"],
        weekPrice: json["week_price"],
        weeklyPrice: anotherJson["weekly_price"],
        monthPrice: anotherJson["month_price"],
        monthlyPrice: json["monthly_price"],
        additionalPrice: json["additional_price"],
        cleaningFee: json["cleaning_fee"],
        additionalGuest: json["additional_guest"],
        guestAfter: json["guest_after"],
        weekendPrice: json["weekend_price"],
        securityDeposit: json["security_deposit"],
        weekend: json["weekend"],
        customPrice: json["custom_price"],
        commonAmenities: json["common_amenities"],
        safetyAmenities: json["safety_amenities"],
        customDiscount: json["custom_discount"],
        dailyDiscount: json["daily_discount"],
      );

  Map<String, dynamic> toJson() => {
        "base_price": basePrice,
        "night_price": nightPrice,
        "currency": currency,
        "access_offer": accessOffer,
        "week_month": weekMonth,
        "price": price,
        "long_term_price": longTermPrice,
        "week_price": weekPrice,
        "monthly_price": monthlyPrice,
        "additional_price": additionalPrice,
        "cleaning_fee": cleaningFee,
        "additional_guest": additionalGuest,
        "guest_after": guestAfter,
        "weekend_price": weekendPrice,
        "security_deposit": securityDeposit,
        "weekend": weekend,
        "custom_price": customPrice,
        "common_amenities": commonAmenities,
        "safety_amenities": safetyAmenities,
      };
}

class ListingSidebar {
  ListingSidebar(
      {required this.basic,
      required this.description,
      required this.location,
      required this.amenities,
      required this.photos,
      required this.price,
      required this.booking,
      required this.calender,
      required this.guestRequirement,
      required this.updateProperty,
      required this.checkinCheckout,
      required this.thingsToKnow});

  String basic;
  String description;
  String location;
  String amenities;
  String photos;
  String price;
  String booking;
  String calender;
  String guestRequirement;
  String updateProperty;
  String checkinCheckout;
  String thingsToKnow;

  factory ListingSidebar.fromJson(Map<String, dynamic> json) => ListingSidebar(
      basic: json["basic"],
      description: json["description"],
      location: json["location"],
      amenities: json["amenities"],
      photos: json["photos"],
      price: json["price"],
      booking: json["booking"],
      calender: json["calender"],
      guestRequirement: json["guest_requirement"],
      updateProperty: json["update_property"],
      checkinCheckout: json["checkin_checkout"],
      thingsToKnow: json["things_to_know"]);
}

class Login {
  Login(
      {required this.name,
      required this.emailAddress,
      required this.password,
      required this.confirmPassword,
      required this.rememberMe,
      required this.forgotPwd,
      required this.login,
      required this.register,
      required this.resetPassword,
      required this.sendResetLink,
      required this.email,
      required this.or,
      required this.doNotHaveAnAccount,
      required this.invalidToken});

  String name;
  String emailAddress;
  String password;
  String confirmPassword;
  String rememberMe;
  String forgotPwd;
  String login;
  String register;
  String resetPassword;
  String sendResetLink;
  String email;
  String or;
  String doNotHaveAnAccount;
  String invalidToken;

  factory Login.fromJson(Map<String, dynamic> json) => Login(
        name: json["name"],
        emailAddress: json["email_address"],
        password: json["password"],
        confirmPassword: json["confirm_password"],
        rememberMe: json["remember_me"],
        forgotPwd: json["forgot_pwd"],
        login: json["login"],
        register: json["register"],
        resetPassword: json["reset_password"],
        sendResetLink: json["send_reset_link"],
        email: json["email"],
        or: json["or"],
        doNotHaveAnAccount: json["do_not_have_an_account"],
        invalidToken: json["invalid_token"],
      );
}

class ManagePhoto {
  ManagePhoto({
    required this.managePhoto,
    required this.photoUpload,
    required this.photoForSale,
    required this.editDetails,
    required this.deletePhoto,
    required this.notHaveImage,
    required this.deleteAnImage,
    required this.deleteLoss,
    required this.delete,
  });

  String managePhoto;
  String photoUpload;
  String photoForSale;
  String editDetails;
  String deletePhoto;
  String notHaveImage;
  String deleteAnImage;
  String deleteLoss;
  String delete;

  factory ManagePhoto.fromJson(Map<String, dynamic> json) => ManagePhoto(
        managePhoto: json["manage_photo"],
        photoUpload: json["photo_upload"],
        photoForSale: json["photo_for_sale"],
        editDetails: json["edit_details"],
        deletePhoto: json["delete_photo"],
        notHaveImage: json["not_have_image"],
        deleteAnImage: json["delete_an_image"],
        deleteLoss: json["delete_loss"],
        delete: json["delete"],
      );
}

class Message {
  Message({
    required this.bookingIsNotConfirmed,
    required this.bookingConfirmedPlace,
    required this.requestDeclined,
    required this.requestSent,
    required this.viewItinerary,
    required this.morePlacesAvailable,
    required this.keepSearching,
    required this.inquiryAbout,
    required this.youWillGet,
    required this.bookingDeclined,
    required this.bookingExpired,
    required this.emptyInbox,
    required this.emptyListing,
    required this.emptyTripts,
    required this.noPayoutSettings,
  });

  String bookingIsNotConfirmed;
  String bookingConfirmedPlace;
  String requestDeclined;
  String requestSent;
  String viewItinerary;
  String morePlacesAvailable;
  String keepSearching;
  String inquiryAbout;
  String youWillGet;
  String bookingDeclined;
  String bookingExpired;
  String emptyInbox;
  String emptyListing;
  String emptyTripts;
  String noPayoutSettings;

  factory Message.fromJson(Map<String, dynamic> json) => Message(
        bookingIsNotConfirmed: json["booking_is_not_confirmed"],
        bookingConfirmedPlace: json["booking_confirmed_place"],
        requestDeclined: json["request_declined"],
        requestSent: json["request_sent"],
        viewItinerary: json["view_itinerary"],
        morePlacesAvailable: json["more_places_available"],
        keepSearching: json["keep_searching"],
        inquiryAbout: json["inquiry_about"],
        youWillGet: json["you_will_get"],
        bookingDeclined: json["booking_declined"],
        bookingExpired: json["booking_expired"],
        emptyInbox: json["empty_inbox"],
        emptyListing: json["empty_listing"],
        emptyTripts: json["empty_tripts"],
        noPayoutSettings: json["no_payout_settings"],
      );

  Map<String, dynamic> toJson() => {
        "booking_is_not_confirmed": bookingIsNotConfirmed,
        "booking_confirmed_place": bookingConfirmedPlace,
        "request_declined": requestDeclined,
        "request_sent": requestSent,
        "view_itinerary": viewItinerary,
        "more_places_available": morePlacesAvailable,
        "keep_searching": keepSearching,
        "inquiry_about": inquiryAbout,
        "you_will_get": youWillGet,
        "booking_declined": bookingDeclined,
        "booking_expired": bookingExpired,
        "empty_inbox": emptyInbox,
        "empty_listing": emptyListing,
        "empty_tripts": emptyTripts,
        "no_payout_settings": noPayoutSettings,
      };
}

class Modal {
  Modal({
    required this.cancelThisBooking,
    required this.whatReasonCancelling,
    required this.whyAreYouCancelling,
    required this.placeNoLongerAvailable,
    required this.offerADifferentListing,
    required this.needMaintenance,
    required this.extenuatingCicumstance,
    required this.guestNeedsCancel,
    required this.other,
    required this.messsageGuest,
    required this.iAmUncomfortable,
    required this.areYouSure,
    required this.deleteMessage,
    required this.ok,
    required this.noPayoutSettings,
    required this.deleteWarn,
    required this.afterDelete,
    required this.logoutWarn,
    required this.haveCouponCode,
  });

  String cancelThisBooking;
  String whatReasonCancelling;
  String whyAreYouCancelling;
  String placeNoLongerAvailable;
  String offerADifferentListing;
  String needMaintenance;
  String extenuatingCicumstance;
  String guestNeedsCancel;
  String other;
  String messsageGuest;
  String iAmUncomfortable;
  String areYouSure;
  String deleteMessage;
  String ok;
  String noPayoutSettings;
  String deleteWarn;
  String afterDelete;
  String logoutWarn;
  String haveCouponCode;

  factory Modal.fromJson(Map<String, dynamic> json) => Modal(
        cancelThisBooking: json["cancel_this_booking"],
        whatReasonCancelling: json["what_reason_cancelling"],
        whyAreYouCancelling: json["why_are_you_cancelling"],
        placeNoLongerAvailable: json["place_no_longer_available"],
        offerADifferentListing: json["offer_a_different_listing"],
        needMaintenance: json["need_maintenance"],
        extenuatingCicumstance: json["extenuating_cicumstance"],
        guestNeedsCancel: json["guest_needs_cancel"],
        other: json["other"],
        messsageGuest: json["messsage_guest"],
        iAmUncomfortable: json["i_am_uncomfortable"],
        areYouSure: json["are_you_sure"],
        deleteMessage: json["delete_message"],
        ok: json["ok"],
        noPayoutSettings: json["no_payout_settings"],
        deleteWarn: json["delete_warn"],
        afterDelete: json["after_delete"],
        logoutWarn: json["logout_warn"],
        haveCouponCode: json["have_coupon_code"],
      );
}

class Notification {
  Notification(
      {this.notiTitle,
      this.notiSubtitle,
      this.notiDialogTitle,
      this.notiDialogSubtitle,
      required this.notification,
      required this.noNotification,
      required this.commented,
      required this.liked,
      required this.followed});

  String? notiTitle;
  String? notiSubtitle;
  String? notiDialogTitle;
  String? notiDialogSubtitle;

  String notification;
  String noNotification;
  String commented;
  String liked;
  String followed;

  factory Notification.fromJson(Map<String, dynamic> json) => Notification(
        notiTitle: json["noti_title"] ?? "Notification Permission Setting",
        notiSubtitle: json["noti_subtitle"] ??
            "Please enable notifications to ensure you can respond promptly as a host, which will help you earn higher ratings on Darent.",
        notiDialogTitle: json["noti_dialog_title"] ?? "تم رفض الإذن",
        notiDialogSubtitle: json["noti_dialog_subTitle"] ??
            "يرجى منح إذن الإخطار للحصول على الإخطار.",
        notification: json["notification"],
        noNotification: json["no_notification"],
        commented: json["commented"],
        liked: json["liked"],
        followed: json["followed"],
      );

  Map<String, dynamic> toJson() => {
        "noti_title": notiTitle,
        "noti_subtitle": notiSubtitle,
        "noti_dialog_title": notiDialogTitle,
        "noti_dialog_subTitle": notiDialogSubtitle,
        "notification": notification,
        "no_notification": noNotification,
        "commented": commented,
        "liked": liked,
        "followed": followed,
      };
}

class Payment {
  Payment({
    required this.payment,
    required this.makePayment,
    required this.country,
    required this.paymentType,
    required this.paypal,
    required this.stripe,
    required this.bank,
    required this.attach,
    required this.bankPay,
    required this.bankSelect,
    required this.uploadFailed,
    required this.redirectToPaypal,
    required this.tellAboutTrip,
    required this.helfulTripWrite,
    required this.whatBringTo,
    required this.whoJoin,
    required this.coordinateCheckPlan,
    required this.askRecomendationNeighbor,
    required this.welcomeTo,
    required this.welcomeData,
    required this.houseRule,
    required this.houseRuleData1,
    required this.clickOn,
    required this.agreeToTotalAmount,
    required this.includeServiceFee,
    required this.onTheRight,
    required this.termOfService,
    required this.cancelPolicy,
    required this.guestRefundPolicy,
    required this.paymentFor,
    required this.guest,
    required this.readPolicy,
    required this.night,
    required this.nights,
    required this.serviceFee,
    required this.additionalGuestFee,
    required this.securityDeposit,
    required this.cleaningFee,
    required this.total,
    required this.payingIn,
    required this.yourTotalCharge,
    required this.exchangeRateBooking,
    required this.cardNumber,
    required this.mm,
    required this.yy,
    required this.couponApplied,
    required this.removeCode,
    required this.discountType,
    required this.discountValue,
    required this.amountAfterDiscount,
    required this.cardInfo,
    required this.campaignCouponApplied,
  });

  String payment;
  String makePayment;
  String country;
  String paymentType;
  String paypal;
  String stripe;
  String bank;
  String attach;
  String bankPay;
  String bankSelect;
  String uploadFailed;
  String redirectToPaypal;
  String tellAboutTrip;
  String helfulTripWrite;
  String whatBringTo;
  String whoJoin;
  String coordinateCheckPlan;
  String askRecomendationNeighbor;
  String welcomeTo;
  String welcomeData;
  String houseRule;
  String houseRuleData1;
  String clickOn;
  String agreeToTotalAmount;
  String includeServiceFee;
  String onTheRight;
  String termOfService;
  String cancelPolicy;
  String guestRefundPolicy;
  String paymentFor;
  String guest;
  String readPolicy;
  String night;
  String nights;
  String serviceFee;
  String additionalGuestFee;
  String securityDeposit;
  String cleaningFee;
  String total;
  String payingIn;
  String yourTotalCharge;
  String exchangeRateBooking;
  String cardNumber;
  String mm;
  String yy;
  String couponApplied;
  String removeCode;
  String discountType;
  String discountValue;
  String amountAfterDiscount;
  String cardInfo;
  String campaignCouponApplied;

  factory Payment.fromJson(Map<String, dynamic> json) => Payment(
        payment: json["payment"],
        makePayment: json["make_payment"],
        country: json["country"],
        paymentType: json["payment_type"],
        paypal: json["paypal"],
        stripe: json["stripe"],
        bank: json["bank"],
        attach: json["attach"],
        bankPay: json["bank_pay"],
        bankSelect: json["bank_select"],
        uploadFailed: json["upload_failed"],
        redirectToPaypal: json["redirect_to_paypal"],
        tellAboutTrip: json["tell_about_trip"],
        helfulTripWrite: json["helful_trip_write"],
        whatBringTo: json["what_bring_to"],
        whoJoin: json["who_join"],
        coordinateCheckPlan: json["coordinate_check_plan"],
        askRecomendationNeighbor: json["ask_recomendation_neighbor"],
        welcomeTo: json["welcome_to"],
        welcomeData: json["welcome_data"],
        houseRule: json["house_rule"],
        houseRuleData1: json["house_rule_data_1"],
        clickOn: json["click_on"],
        agreeToTotalAmount: json["agree_to_total_amount"],
        includeServiceFee: json["include_service_fee"],
        onTheRight: json["on_the_right"],
        termOfService: json["term_of_service"],
        cancelPolicy: json["cancel_policy"],
        guestRefundPolicy: json["guest_refund_policy"],
        paymentFor: json["for"],
        guest: json["guest"],
        readPolicy: json["read_policy"],
        night: json["night"],
        nights: json["nights"],
        serviceFee: json["service_fee"],
        additionalGuestFee: json["additional_guest_fee"],
        securityDeposit: json["security_deposit"],
        cleaningFee: json["cleaning_fee"],
        total: json["total"],
        payingIn: json["paying_in"],
        yourTotalCharge: json["your_total_charge"],
        exchangeRateBooking: json["exchange_rate_booking"],
        cardNumber: json["card_number"],
        mm: json["mm"],
        yy: json["yy"],
        couponApplied: json["coupon_applied"],
        removeCode: json['remove_code'] ?? 'Remove Code',
        discountType: json['discount_type'],
        discountValue: json['discount_value'],
        amountAfterDiscount: json['amount_after_discount'],
        cardInfo: json['your_card_info_secure_no_one_reach'],
        campaignCouponApplied: json['campaign_coupon_applied'],
      );
}

class PaymentStripe {
  PaymentStripe({
    required this.creditDebitCard,
    required this.submitPayment,
  });

  String creditDebitCard;
  String submitPayment;

  factory PaymentStripe.fromJson(Map<String, dynamic> json) => PaymentStripe(
        creditDebitCard: json["credit_debit_card"],
        submitPayment: json["submit_payment"],
      );

  Map<String, dynamic> toJson() => {
        "credit_debit_card": creditDebitCard,
        "submit_payment": submitPayment,
      };
}

class PhotoDetails {
  PhotoDetails({
    required this.sellPhoto,
    required this.category,
    required this.title,
    required this.description,
    required this.location,
    required this.finishUpload,
  });

  String sellPhoto;
  String category;
  String title;
  String description;
  String location;
  String finishUpload;

  factory PhotoDetails.fromJson(Map<String, dynamic> json) => PhotoDetails(
        sellPhoto: json["sell_photo"],
        category: json["category"],
        title: json["title"],
        description: json["description"],
        location: json["location"],
        finishUpload: json["finish_upload"],
      );

  Map<String, dynamic> toJson() => {
        "sell_photo": sellPhoto,
        "category": category,
        "title": title,
        "description": description,
        "location": location,
        "finish_upload": finishUpload,
      };
}

class PreviousTrips {
  PreviousTrips({
    required this.previousTrip,
    required this.youHaveNoPreviousTrips,
  });

  String previousTrip;
  String youHaveNoPreviousTrips;

  factory PreviousTrips.fromJson(Map<String, dynamic> json) => PreviousTrips(
        previousTrip: json["previous_trip"],
        youHaveNoPreviousTrips: json["you_have_no_previous_trips"],
      );

  Map<String, dynamic> toJson() => {
        "previous_trip": previousTrip,
        "you_have_no_previous_trips": youHaveNoPreviousTrips,
      };
}

class ProfileTranslation {
  ProfileTranslation({
    required this.shop,
    required this.allPhotos,
    required this.followers,
    required this.following,
    required this.lovedPhotos,
    required this.photoViews,
    required this.joined,
    required this.profileUpdated,
    required this.currentVerifications,
    required this.youHaveConfirmedEmail,
    required this.emailVerified,
    required this.facebookVerification,
    required this.disconnect,
    required this.googleVerification,
    required this.linkedinVerification,
    required this.addMoreVerifications,
    required this.emailVerification,
    required this.connect,
    required this.connectedSuccessfully,
    required this.disconnectedSuccessfully,
    required this.emailConfirmed,
    required this.pwdNotCorrect,
    required this.newConfirmLinkSent,
  });

  String shop;
  String allPhotos;
  String followers;
  String following;
  String lovedPhotos;
  String photoViews;
  String joined;
  String profileUpdated;
  String currentVerifications;
  String youHaveConfirmedEmail;
  String emailVerified;
  String facebookVerification;
  String disconnect;
  String googleVerification;
  String linkedinVerification;
  String addMoreVerifications;
  String emailVerification;
  String connect;
  String connectedSuccessfully;
  String disconnectedSuccessfully;
  String emailConfirmed;
  String pwdNotCorrect;
  String newConfirmLinkSent;

  factory ProfileTranslation.fromJson(Map<String, dynamic> json) =>
      ProfileTranslation(
        shop: json["shop"],
        allPhotos: json["all_photos"],
        followers: json["followers"],
        following: json["following"],
        lovedPhotos: json["loved_photos"],
        photoViews: json["photo_views"],
        joined: json["joined"],
        profileUpdated: json["profile_updated"],
        currentVerifications: json["current_verifications"],
        youHaveConfirmedEmail: json["you_have_confirmed_email"],
        emailVerified: json["email_verified"],
        facebookVerification: json["facebook_verification"],
        disconnect: json["disconnect"],
        googleVerification: json["google_verification"],
        linkedinVerification: json["linkedin_verification"],
        addMoreVerifications: json["add_more_verifications"],
        emailVerification: json["email_verification"],
        connect: json["connect"],
        connectedSuccessfully: json["connected_successfully"],
        disconnectedSuccessfully: json["disconnected_successfully"],
        emailConfirmed: json["email_confirmed"],
        pwdNotCorrect: json["pwd_not_correct"],
        newConfirmLinkSent: json["new_confirm_link_sent"],
      );
}

class PropertyTranslation {
  PropertyTranslation({
    required this.listSpace,
    required this.propertyTitle,
    required this.homeType,
    required this.roomType,
    required this.accommodate,
    required this.city,
    required this.propertyContinue,
    required this.trustSafety,
    required this.trustSafetyData,
    required this.hostGuarantee,
    required this.hostGuaranteeData,
    required this.securePayment,
    required this.securePaymentData,
    required this.listed,
    required this.manageListCal,
    required this.stepListed,
    required this.unlisted,
    required this.stepToList,
    required this.pending,
    required this.continuE,
  });

  String listSpace;
  String propertyTitle;
  String homeType;
  String roomType;
  String accommodate;
  String city;
  String propertyContinue;
  String trustSafety;
  String trustSafetyData;
  String hostGuarantee;
  String hostGuaranteeData;
  String securePayment;
  String securePaymentData;
  String listed;
  String manageListCal;
  String stepListed;
  String unlisted;
  String stepToList;
  String pending;
  String continuE;

  factory PropertyTranslation.fromJson(Map<String, dynamic> json) =>
      PropertyTranslation(
        listSpace: json["list_space"],
        propertyTitle: json["property_title"],
        homeType: json["home_type"],
        roomType: json["room_type"],
        accommodate: json["accommodate"],
        city: json["city"],
        propertyContinue: json["continue"],
        trustSafety: json["trust_safety"],
        trustSafetyData: json["trust_safety_data"],
        hostGuarantee: json["host_guarantee"],
        hostGuaranteeData: json["host_guarantee_data"],
        securePayment: json["secure_payment"],
        securePaymentData: json["secure_payment_data"],
        listed: json["listed"],
        manageListCal: json["manage_list_cal"],
        stepListed: json["step_listed"],
        unlisted: json["unlisted"],
        stepToList: json["step_to_list"],
        pending: json["pending"],
        continuE: json["continue"],
      );
}

class PropertySingle {
  String? guest;
  String? joined;
  String? enterCoupon;
  String? couponCode;
  String? bed;
  String? perNight;
  String? sarPerNight;
  String? readMore;
  String? perMonth;
  String? checkIn;
  String? checkOut;
  String? night;
  String? nights;
  String? serviceFee;
  String? securityFee;
  String? additionalGuestFee;
  String? cleaningFee;
  String? total;
  String? dateNotAvailable;
  String? viewOtherList;
  String? requestBook;
  String? instantBook;
  String? reviewOfPay;
  String? aboutList;
  String? theSpace;
  String? bedType;
  String? noRooms;
  String? propertyType;
  String? accommodate;
  String? bedroom;
  String? bathroom;
  String? amenity;
  String? more;
  String? price;
  String? extraPeople;
  String? afterNight;
  String? guests;
  String? noCharge;
  String? weeklyPrice;
  String? week;
  String? monthlyPrice;
  String? month;
  String? description;
  String? aboutPlace;
  String? placeGreatFor;
  String? interactionGuest;
  String? aboutNeighborhood;
  String? guestAccess;
  String? getAround;
  String? other;
  String? safetyFeature;
  String? avialability;
  String? viewCalendar;
  String? noReviewYet;
  String? summary;
  String? aboutHost;
  String? trust;
  String? review;
  String? similarList;
  String? kilometerAway;
  String? weeklyDiscount;
  String? monthlyDiscount;
  String? viewAll;
  String? ivaTax;
  String? accommodatitonTax;
  String? youHaveBook;
  String? nightDates;
  String? addToWishlist;
  String? overveiw;
  String? location;
  String? locationOnMap;
  String? additionalInformation;
  String? accommodationRules;
  String? cancellations;
  String? totalIncludeVat;
  String? discount;
  String? chatWithTheHost;
  String? checkInOut;
  String? similarListing;
  String? suitableForChildren;
  String? cancelUptoPrior;
  String? noRevYet;
  String? by;
  String? reserve;
  String? youSaved;
  String? singleBeds;
  String? doubleBeds;
  String? childrenNotAllowed;
  String? adultMaxLimit;
  String? childMaxLimit;
  String? cleanliness;
  String? statness;
  String? joinedIn;
  String? weApologizeItSeemsThatTheProperty;
  String? browse;
  String? error;
  String? noHouseRuleAvailable;
  String? tamayouz;
  String? darentService;
  String? darentRecomended;
  String? monthlyAndWeeklyDiscount;
  String? weeklyDiscountAvailable;
  String? monthlyDiscountAvailable;
  String? exclusive;
  String? dailyDiscountAmount;
  String? dailyDiscount;

  PropertySingle(
      {this.guest,
      this.joined,
      this.enterCoupon,
      this.couponCode,
      this.bed,
      this.perNight,
      this.sarPerNight,
      this.readMore,
      this.perMonth,
      this.checkIn,
      this.checkOut,
      this.night,
      this.nights,
      this.serviceFee,
      this.securityFee,
      this.additionalGuestFee,
      this.cleaningFee,
      this.total,
      this.dateNotAvailable,
      this.viewOtherList,
      this.requestBook,
      this.instantBook,
      this.reviewOfPay,
      this.aboutList,
      this.theSpace,
      this.bedType,
      this.noRooms,
      this.propertyType,
      this.accommodate,
      this.bedroom,
      this.bathroom,
      this.amenity,
      this.more,
      this.price,
      this.extraPeople,
      this.afterNight,
      this.guests,
      this.noCharge,
      this.weeklyPrice,
      this.week,
      this.monthlyPrice,
      this.month,
      this.description,
      this.aboutPlace,
      this.placeGreatFor,
      this.interactionGuest,
      this.aboutNeighborhood,
      this.guestAccess,
      this.getAround,
      this.other,
      this.safetyFeature,
      this.avialability,
      this.viewCalendar,
      this.noReviewYet,
      this.summary,
      this.aboutHost,
      this.trust,
      this.review,
      this.similarList,
      this.kilometerAway,
      this.weeklyDiscount,
      this.monthlyDiscount,
      this.viewAll,
      this.ivaTax,
      this.accommodatitonTax,
      this.youHaveBook,
      this.nightDates,
      this.addToWishlist,
      this.overveiw,
      this.location,
      this.locationOnMap,
      this.additionalInformation,
      this.accommodationRules,
      this.cancellations,
      this.totalIncludeVat,
      this.discount,
      this.chatWithTheHost,
      this.checkInOut,
      this.similarListing,
      this.suitableForChildren,
      this.cancelUptoPrior,
      this.noRevYet,
      this.by,
      this.reserve,
      this.youSaved,
      this.singleBeds,
      this.doubleBeds,
      this.childrenNotAllowed,
      this.adultMaxLimit,
      this.childMaxLimit,
      this.cleanliness,
      this.statness,
      this.joinedIn,
      this.weApologizeItSeemsThatTheProperty,
      this.browse,
      this.error,
      this.noHouseRuleAvailable,
      this.tamayouz,
      this.darentService,
      this.darentRecomended,this.exclusive,this.dailyDiscountAmount,this.dailyDiscount});

  PropertySingle.fromJson(Map<String, dynamic> json) {
    guest = json['guest'];
    joined = json['joined'];
    enterCoupon = json['enter_coupon'];
    couponCode = json['coupon_code'];
    bed = json['bed'];
    perNight = json['per_night'];
    sarPerNight = json['sar_per_night'];
    readMore = json['read_more'];
    perMonth = json['per_month'];
    checkIn = json['check_in'];
    checkOut = json['check_out'];
    night = json['night'];
    nights = json['nights'];
    serviceFee = json['service_fee'];
    securityFee = json['security_fee'];
    additionalGuestFee = json['additional_guest_fee'];
    cleaningFee = json['cleaning_fee'];
    total = json['total'];
    dateNotAvailable = json['date_not_available'];
    viewOtherList = json['view_other_list'];
    requestBook = json['request_book'];
    instantBook = json['instant_book'];
    reviewOfPay = json['review_of_pay'];
    aboutList = json['about_list'];
    theSpace = json['the_space'];
    bedType = json['bed_type'];
    noRooms = json['no_rooms'];
    propertyType = json['property_type'];
    accommodate = json['accommodate'];
    bedroom = json['bedroom'];
    bathroom = json['bathroom'];
    amenity = json['amenity'];
    more = json['more'];
    price = json['price'];
    extraPeople = json['extra_people'];
    afterNight = json['after_night'];
    guests = json['guests'];
    noCharge = json['no_charge'];
    weeklyPrice = json['weekly_price'];
    week = json['week'];
    monthlyPrice = json['monthly_price'];
    month = json['month'];
    description = json['description'];
    aboutPlace = json['about_place'];
    placeGreatFor = json['place_great_for'];
    interactionGuest = json['interaction_guest'];
    aboutNeighborhood = json['about_neighborhood'];
    guestAccess = json['guest_access'];
    getAround = json['get_around'];
    other = json['other'];
    safetyFeature = json['safety_feature'];
    avialability = json['avialability'];
    viewCalendar = json['view_calendar'];
    noReviewYet = json['no_review_yet'];
    summary = json['summary'];
    aboutHost = json['about_host'];
    trust = json['trust'];
    review = json['review'];
    similarList = json['similar_list'];
    kilometerAway = json['kilometer_away'];
    weeklyDiscount = json['weekly_discount'];
    monthlyDiscount = json['monthly_discount'];
    viewAll = json['view_all'];
    ivaTax = json['iva_tax'];
    accommodatitonTax = json['accommodatiton_tax'];
    youHaveBook = json['you_have_book'];
    nightDates = json['night_dates'];
    addToWishlist = json['add_to_wishlist'];
    overveiw = json['overveiw'];
    location = json['location'];
    locationOnMap = json['location_on_map'];
    additionalInformation = json['additional_information'];
    accommodationRules = json['accommodation_rules'];
    cancellations = json['cancellations'];
    totalIncludeVat = json['total_include_vat'];
    discount = json['discount'];
    chatWithTheHost = json['chat_with_the_host'];
    checkInOut = json['check_in_out'];
    similarListing = json['similar_listing'];
    suitableForChildren = json['suitable_for_children'];
    cancelUptoPrior = json['cancel_upto_prior'];
    noRevYet = json['no_rev_yet'];
    by = json['by'];
    reserve = json['reserve'];
    youSaved = json['you_saved'];
    singleBeds = json['single_beds'];
    doubleBeds = json['double_beds'];
    childrenNotAllowed = json['children_not_allowed'] ??
        "Children are not allowed in this property";
    adultMaxLimit =
        json['adult_max_limit'] ?? "Maximum adult guests limit reached!";
    childMaxLimit =
        json['child_max_limit'] ?? "Maximum children guests limit reached!";
    cleanliness = json['cleanliness'];
    statness = json['statness'];
    joinedIn = json['joined_in'];
    weApologizeItSeemsThatTheProperty =
        json['we_apologize_it_seems_that_the_property'];
    browse = json['browse'];
    error = json['error'];
    noHouseRuleAvailable = json['no_house_rule_available'] ?? "";
    tamayouz = json['tamayouz'];
    darentService = json['darent_service'];
    darentRecomended = json['darent_recomended'];
    monthlyAndWeeklyDiscount = json['monthly_and_weekly_discount'];
    weeklyDiscountAvailable = json['weekly_discount_available'];
    monthlyDiscountAvailable = json['monthly_discount_available'];
    exclusive = json['exclusive'];
    dailyDiscountAmount = json['daily_discount_amount'];
    dailyDiscount = json['daily_discount'];
  }
  Map<String,String?> toJson(){
    return {
      'monthly_and_weekly_discount':monthlyAndWeeklyDiscount,
      'weekly_discount_available':weeklyDiscountAvailable,
      'monthly_discount_available':monthlyDiscountAvailable,
      'weekly_discount':weeklyDiscount,
      'monthly_discount':monthlyDiscount,
      'daily_discount':dailyDiscountAmount
    };
  }
}

class Purchase {
  Purchase({
    required this.orderHistory,
    required this.purchaseShowHere,
    required this.browsePhotos,
  });

  String orderHistory;
  String purchaseShowHere;
  String browsePhotos;

  factory Purchase.fromJson(Map<String, dynamic> json) => Purchase(
        orderHistory: json["order_history"],
        purchaseShowHere: json["purchase_show_here"],
        browsePhotos: json["browse_photos"],
      );

  Map<String, dynamic> toJson() => {
        "order_history": orderHistory,
        "purchase_show_here": purchaseShowHere,
        "browse_photos": browsePhotos,
      };
}

class Reservation {
  Reservation({
    required this.current,
    required this.reservation,
    required this.yourNextReservation,
    required this.upComing,
    required this.history,
    required this.rate,
    required this.rated,
    required this.report,
    required this.expired,
    required this.noUpcomingBookings,
    required this.ongoing,
    required this.gettingThere,
    required this.messageYourHost,
    required this.yourPlace,
    required this.instructionsAndHouseRules,
    required this.reservationDetail,
    required this.reservationNumber,
    required this.termsPolicies,
    required this.cancelRefundPolicy,
    required this.checkInInfo,
    required this.hostInformation,
    required this.hostName,
    required this.reservationSummary,
    required this.paymentInformation,
    required this.paidFull,
    required this.propertyLocation,
    required this.getSupportAnytime,
    required this.ifYouNeedHelp,
    required this.youWillBeStayingInSomeone,
    required this.duringYourStay,
    required this.checkInAfter,
    required this.checkOutBefore,
    required this.youAreAllSetFor,
    required this.reservationConfirmNumber,
    required this.inDays,
    required this.inDay,
    required this.cancelReservation,
    required this.callHost,
    required this.throughChat,
    required this.viewReservationSummary,
    required this.rateYourStay,
    required this.copied,
    required this.youWillSeeAddress,
    required this.youWillSeeContact,
    required this.qCleannes,
    required this.cleannessOption1,
    required this.cleannessOption2,
    required this.cleannessOption3,
    required this.cleannessOption4,
    required this.cleannessOption5,
    required this.communicationOption3,
    required this.communicationOption4,
    required this.communicationOption5,
    required this.qCommunication,
    required this.qLocation,
    required this.qStates,
    required this.statesOption1,
    required this.statesOption2,
    required this.statesOption3,
    required this.statesOption4,
    required this.statesOption5,
    required this.qWhatLikes,
    required this.writeHere,
    required this.addImage,
    required this.qService,
    required this.serviceOption1,
    required this.serviceOption2,
    required this.serviceOption3,
    required this.serviceOption4,
    required this.serviceOption5,
    required this.qRecomend,
    required this.recomendOption1,
    required this.recomendOption2,
    required this.recomendOption3,
    required this.recomendOption4,
    required this.recomendOption5,
    required this.locationOption3,
    required this.locationOption4,
    required this.locationOption5,
    required this.qImproment,
    required this.howDoYouShare,
    required this.sendALink,
    required this.anyoneWithTheLink,
    required this.inviteOthers,
    required this.onceYouShare,
    required this.cancelled,
    required this.ratingPosted,
    required this.noOngoingBookings,
    required this.noPastBookings,
    required this.noCancelledBookings,
    required this.noExpiredBookings,
    required this.priceDetails,
  });

  String current;

  String reservation;
  String yourNextReservation;
  String upComing;
  String history;
  String rate;
  String rated;
  String report;
  String expired;
  String noUpcomingBookings;
  String ongoing;
  String gettingThere;
  String messageYourHost;
  String yourPlace;
  String instructionsAndHouseRules;
  String reservationDetail;
  String reservationNumber;
  String termsPolicies;
  String cancelRefundPolicy;
  String checkInInfo;
  String hostInformation;
  String hostName;
  String reservationSummary;
  String paymentInformation;
  String paidFull;
  String propertyLocation;
  String getSupportAnytime;
  String ifYouNeedHelp;
  String youWillBeStayingInSomeone;
  String duringYourStay;
  String checkInAfter;
  String checkOutBefore;
  String youAreAllSetFor;
  String reservationConfirmNumber;
  String inDays;
  String inDay;
  String cancelReservation;
  String callHost;
  String throughChat;
  String viewReservationSummary;
  String rateYourStay;
  String copied;
  String youWillSeeAddress;
  String youWillSeeContact;
  String qCleannes;
  String cleannessOption1;
  String cleannessOption2;
  String cleannessOption3;
  String cleannessOption4;
  String cleannessOption5;
  String communicationOption3;
  String communicationOption4;
  String communicationOption5;
  String qCommunication;
  String qLocation;
  String qStates;
  String statesOption1;
  String statesOption2;
  String statesOption3;
  String statesOption4;
  String statesOption5;
  String qWhatLikes;
  String writeHere;
  String addImage;
  String qService;
  String serviceOption1;
  String serviceOption2;
  String serviceOption3;
  String serviceOption4;
  String serviceOption5;
  String qRecomend;
  String recomendOption1;
  String recomendOption2;
  String recomendOption3;
  String recomendOption4;
  String recomendOption5;
  String locationOption3;
  String locationOption4;
  String locationOption5;
  String qImproment;
  String howDoYouShare;
  String sendALink;
  String anyoneWithTheLink;
  String inviteOthers;
  String onceYouShare;
  String cancelled;
  String ratingPosted;
  String noOngoingBookings;
  String noPastBookings;
  String noCancelledBookings;
  String noExpiredBookings;
  String priceDetails;

  factory Reservation.fromJson(Map<String, dynamic> json) => Reservation(
        current: json['current'] ?? "Current",
        reservation: json["reservation"],
        yourNextReservation: json["your_next_reservation"],
        upComing: json["upcoming"],
        history: json["history"],
        rate: json["rate"],
        rated: json["rated"],
        report: json["report"],
        expired: json["expired"],
        noUpcomingBookings: json['no_upcoming_bookings'],
        ongoing: json['ongoing'],
        gettingThere: json['getting_there'],
        messageYourHost: json['message_your_host'],
        yourPlace: json['your_place'],
        instructionsAndHouseRules: json['Instructions_and_house_rules'] ??
            'Instructions And House Rules',
        reservationDetail: json['reservation_detail'] ?? 'Reservation Detail',
        reservationNumber: json['reservation_number'] ?? '',
        termsPolicies: json['terms_policies'],
        cancelRefundPolicy: json['cancel_refund_policy'],
        checkInInfo: json['Check_in_info'],
        hostInformation: json['host_information'],
        hostName: json['host_name'] ?? 'Host name',
        reservationSummary:
            json['reservation_summary'] ?? 'Reservation Summary',
        paymentInformation: json['payment_information'],
        paidFull: json['paid_full'],
        propertyLocation: json["property_location"],
        getSupportAnytime: json["get_support_anytime"],
        ifYouNeedHelp: json['if_you_need_help'],
        youWillBeStayingInSomeone: json['You_will_be_staying_in_someone'],
        duringYourStay: json['during_your_stay'],
        checkInAfter: json['check_in_after'] ?? '',
        checkOutBefore: json['check_out_before'] ?? '',
        youAreAllSetFor: json['you_are_all_set_for'] ?? 'you_are_all_set_for',
        reservationConfirmNumber:
            json['reservation_confirm_number'] ?? 'you_are_all_set_for',
        inDays: json['in_days'],
        inDay: json['in_day'] ?? 'In 1 day',
        cancelReservation: json['cancel_reservation'],
        callHost: json['call_host'],
        throughChat: json['through_chat'],
        viewReservationSummary: json['view_reservation_summary'],
        rateYourStay: json['rate'],
        copied: json['copied'],
        youWillSeeAddress: json['You_will_see_address'],
        youWillSeeContact: json['you_will_see_contact'],
        qCleannes: json["q_cleannes"] ?? "How was property cleanness ?",
        cleannessOption1: json["cleanness_option1"] ?? "",
        cleannessOption2: json["cleanness_option2"] ?? "",
        cleannessOption3: json["cleanness_option3"] ?? "",
        cleannessOption4: json["cleanness_option4"] ?? "",
        cleannessOption5: json["cleanness_option5"] ?? "",
        communicationOption3: json["communication_option3"] ?? "",
        communicationOption4: json["communication_option4"] ?? "",
        communicationOption5: json["communication_option5"] ?? "",
        qCommunication:json["q_communication"] ?? "How was your host communication?",
        qLocation:json["q_location"] ?? "",
        qStates: json["q_states"] ?? "How was property states ?",
        statesOption1: json["states_option1"] ?? "Old",
        statesOption2: json["states_option2"] ?? "New/Refurbished",
        statesOption3: json["states_option3"] ?? '',
        statesOption4: json["states_option4"] ?? '',
        statesOption5: json["states_option5"] ?? '',
        qWhatLikes: json["q_what_likes"] ??
            "What did you like in the property and needs ?",
        writeHere: json["write_here"] ?? "Write here",
        addImage: json["add_image"] ?? "Add Image",
        qService: json["q_service"] ?? "Darent Service ?",
        serviceOption1: json["service_option1"] ?? '',
        serviceOption2: json["service_option2"] ?? '',
        serviceOption3: json["service_option3"] ?? '',
        serviceOption4: json["service_option4"] ?? '',
        serviceOption5: json["service_option5"] ?? '',
        qRecomend:
            json["q_recomend"] ?? "Do you recommend your friends to DARENT ?",
        recomendOption1: json["recomend_option1"] ?? '',
        recomendOption2: json["recomend_option2"] ?? '',
        recomendOption3: json["recomend_option3"] ?? '',
        recomendOption4: json["recomend_option4"] ?? '',
        recomendOption5: json["recomend_option5"] ?? '',
        locationOption3: json["location_option3"] ?? "Okay",
        locationOption4: json["location_option4"] ?? "Good",
        locationOption5: json["location_option5"] ?? "Excelent",
        qImproment: json["q_improment"] ??
            "What do you like in DARENT ? What improvement needed ?",
        howDoYouShare: json['how_do_you_share'],
        sendALink: json['send_a_link'],
        anyoneWithTheLink: json['anyone_with_the_link'],
        inviteOthers: json['invite_others'],
        onceYouShare: json['once_you_share'],
        cancelled: json['cancelled'],
        ratingPosted: json['rating_posted'] ?? '',
        noOngoingBookings: json['no_ongoing_bookings'] ?? '',
        noPastBookings: json['no_past_bookings'] ?? '',
        noCancelledBookings: json['no_cancelled_bookings'] ?? '',
        noExpiredBookings: json['no_expired_bookings'] ?? '',
        priceDetails: json['pric_details'] ?? 'Price Details',
      );
  Map<String, dynamic> toJson() {
    return {
      'cleanness_option1': cleannessOption1,
      'cleanness_option2': cleannessOption2,
      'cleanness_option3': cleannessOption3,
      'cleanness_option4': cleannessOption4,
      'cleanness_option5': cleannessOption5,
      'communication_option1': Get.find<TranslationHelper>()
          .translations
          .hostDashboard
          .communicationOption1,
      'communication_option2': Get.find<TranslationHelper>()
          .translations
          .hostDashboard
          .communicationOption2,
      'communication_option3': communicationOption3,
      'communication_option4': communicationOption4,
      'communication_option5': communicationOption5,
      'states_option1': statesOption1,
      'states_option2': statesOption2,
      'states_option3': statesOption3,
      'states_option4': statesOption4,
      'states_option5': statesOption5,
      'service_option1': serviceOption1,
      'service_option2': serviceOption2,
      'service_option3': serviceOption3,
      'service_option4': serviceOption4,
      'service_option5': serviceOption5,
      'recomend_option1': recomendOption1,
      'recomend_option2': recomendOption2,
      'recomend_option3': recomendOption3,
      'recomend_option4': recomendOption4,
      'recomend_option5': recomendOption5,

      'location_option1': Get.find<TranslationHelper>().translations.hostDashboard.locationOption1,
      'location_option2': Get.find<TranslationHelper>().translations.hostDashboard.locationOption2,
      'location_option3': locationOption3,
      'location_option4': locationOption4,
      'location_option5': locationOption5,
    };
  }
}

class Listing {
  String? propertySuccessful;
  String? messageRecieveShortly;
  String? propertyOk;
  String? start;
  String? addListingInStep;
  String? askToAddProperty;
  String? whatKindOf;
  String? whatKind;
  String? willHost;
  String? spaceGuest;
  String? whereIsPlace;
  String? located;
  String? adults;
  String? children;
  String? howManyGuest;
  String? letGuestKnow;
  String? haveAnyStandoutAmenity;
  String? haveAnySafetyItem;
  String? addSomePhoto;
  String? addPhotos;
  String? letsGiveName;
  String? createTitle;
  String? letsDescribePlace;
  String? createYourDescription;
  String? setPrice;
  String? pricePerNight;
  String? keepInMindPlaceRange;
  String? fewLastQuestion;
  String? howAreYouHosting;
  String? reviewRequest;
  String? bookInstantly;
  String? haveAnyAtPlace;
  String? securityCamera;
  String? weapons;
  String? dangerousAnimals;
  String? someImportantThings;
  String? beSureToComplyYourLaws;
  String? publishYourListing;
  String? listingManagement;
  String? whatKindOfWillHost;
  String? whereIsPlaceLocated;
  String? searchLocation;
  String? addAddress;
  String? confirmLocation;
  String? buildingNo;
  String? street;
  String? youCanMovePointer;
  String? saveLocation;
  String? pets;
  String? guests;
  String? smoking;
  String? kids;
  String? party;
  String? waitingApproval;
  String? numberOfAppartment;
  String? whatKindOfSpace;
  String? noListingAdded;
  String? translateToEnglish;
  String? translateToArabic;
  String? promotions;
  String? promotion;
  String? mytickets;
  String? createPromo;
  String? showYourHouseOnDarent;
  String? ableToEarn;
  String? showHouseOnDarentDescription;
  String? residenceConsistingRoom;
  String? presentPropertyEffectiveWay;
  String? helpWithAdviceToRaisePropertyReservationProfessionalRealEstate;
  String? damageInsurance;
  String? damageInsuranceForSafetyFromTheftOrDamage;
  String? takeCareOfMarketing;
  String? bestMarketingForYourPropertyForHighestRateReservation;
  String? customDashboardToMakeEasyManageProperty;
  String? isEasyToStart;
  String? tellAboutYourResidence;
  String? step1ProvideBasicInfo;
  String? makeYourHomeStandOut;
  String? step2AddPhotoDescriptionTitle;
  String? completionAndPublication;
  String? step3CompletionAndPublication;
  String? step1;
  String? stepOneDescription;
  String? closestDescriptionOfYourHome;
  String? exit;
  String? typeOfAccommodationAvailableForGuest;
  String? landingManageTitle;
  String? landingManageSubTitle;
  String? landingListProperty;
  String? landingDoNotManage;
  String? landingClick;
  String? landingHandleEveryThing;
  String? landingHandlePropertyManagement;
  String? landingSheetTitle;
  String? landingHouseKeeping;
  String? landingOnSiteCord;
  String? landingSkip;

  String? manageTitle;
  String? manageSubTitle;
  String? fullName;
  String? enterFullName;
  String? noOfUnit;
  String? district;
  String? contact;
  String? submit;
  String? propertyTitle;
  String? willContact;

  String? whereDoYouLive;
  String? invisibleAddressStepNote;
  String? confirmYourLocation;
  String? showYourExactLocation;
  String? visibleLocationForGuestNote;
  String? someInformationAboutYourListing;
  String? addMoreDetailLaterSuchBedTypes;
  String? step2;
  String? addSomeFeaturesYouHaveInYourListing;
  String? tellGuestsAboutFeatureInYourSpace;
  String? canAddMoreFeatureAfterAdPosted;
  String? addSomePictureOfHouseOnATree;
  String? atleast5PhotosToGetStarted;
  String? dragYourPhoto;
  String? chooseAtleast5Photo;
  String? downloadFromYourDevice;
  String? magicallyArrangeYourPhoto;
  String? uploadedFrom1To5;
  String? successfullyCompletedStepDidYouLikeArrangement;
  String? dragPictureToRearrange;
  String? followUp;
  String? moveBack;
  String? setCoverPhoto;
  String? chooseTitleForHouse;
  String? setTitleNote;
  String? writeDescriptionOfDwelling;
  String? addDescriptionNote;
  String? step3;
  String? step3Description;
  String? chooseGuestOnFirstReservation;
  String? chooseFirstGuestDescription;
  String? guestOnDarent;
  String? seasonalGuest;
  String? setThePrice;
  String? priceChangeNote;
  String? reviewYourListing;
  String? reviewPropertyNote;
  String? whatNext;
  String? adjustYourSetting;
  String? confirmDetailAndPost;
  String? confirmIdentityAndRegistrationWithLocalGovernment;
  String? startBySettingCalendar;
  String? chooseAvailableDateForYourProperty;
  String? setHouseRulesCancellationPolicyHowGuestCanBook;
  String? flowHost;
  String? addProperty;
  String? saveExit;
  String? child;
  String? amenityValidation;
  String? propertyValidation;
  String? spaceValidation;
  String? bedValidation;
  String? minImagesValidation;
  String? maxImagesValidation;
  String? pleaseWait;
  String? placeName;
  String? descriptionMinValidation;
  String? minPriceValidation;
  String? minMaxNightsValidation;
  String? hostAgree;
  String? iAgree;
  String? hostAgreement;
  String? priceOn;
  String? checkInTime;
  String? checkOutTime;
  String? referralTitle;
  String? referralSubtitle;
  String? identityVerification;
  String? successUpdated;
  String? successSubmit;
  String? datesReserve;
  String? verificationDone;
  String? rateListing;
  String? messageAlso;
  String? validNumber;
  String? greaterToSix;
  String? ticketSubmitted;
  String? selectPayMethod;
  String? onlyFiveImages;
  String? numOrMailOnce;
  String? selectedDateReserved;
  String? calenderImported;
  String? cardHasAdded;

  String? paymentSuccessful;
  String? paymentFailed;
  String? darentCalenderExport;
  String? copyPasteLink;
  String? copiedClipboard;
  String? permitNo;
  String? individual;
  String? company;
  String? licenseNumber;
  String? licenseNoHint;
  String? crNumber;
  String? crNoHint;
  String? learnMoreListing;
  String? gotoHere;
  String? totalPrice;
  String? priceAfterDiscount;

  Listing(
      {this.propertySuccessful,
      this.messageRecieveShortly,
      this.propertyOk,
      this.start,
      this.addListingInStep,
      this.askToAddProperty,
      this.whatKindOf,
      this.whatKind,
      this.willHost,
      this.spaceGuest,
      this.whereIsPlace,
      this.located,
      this.adults,
      this.children,
      this.howManyGuest,
      this.letGuestKnow,
      this.haveAnyStandoutAmenity,
      this.haveAnySafetyItem,
      this.addSomePhoto,
      this.addPhotos,
      this.letsGiveName,
      this.createTitle,
      this.letsDescribePlace,
      this.createYourDescription,
      this.setPrice,
      this.pricePerNight,
      this.keepInMindPlaceRange,
      this.fewLastQuestion,
      this.howAreYouHosting,
      this.reviewRequest,
      this.bookInstantly,
      this.haveAnyAtPlace,
      this.securityCamera,
      this.weapons,
      this.dangerousAnimals,
      this.someImportantThings,
      this.beSureToComplyYourLaws,
      this.publishYourListing,
      this.listingManagement,
      this.whatKindOfWillHost,
      this.whereIsPlaceLocated,
      this.searchLocation,
      this.addAddress,
      this.confirmLocation,
      this.buildingNo,
      this.street,
      this.youCanMovePointer,
      this.saveLocation,
      this.pets,
      this.guests,
      this.smoking,
      this.kids,
      this.party,
      this.waitingApproval,
      this.numberOfAppartment,
      this.whatKindOfSpace,
      this.noListingAdded,
      this.translateToEnglish,
      this.translateToArabic,
      this.promotions,
      this.promotion,
      this.mytickets,
      this.createPromo,
      this.showYourHouseOnDarent,
      this.ableToEarn,
      this.showHouseOnDarentDescription,
      this.residenceConsistingRoom,
      this.presentPropertyEffectiveWay,
      this.helpWithAdviceToRaisePropertyReservationProfessionalRealEstate,
      this.damageInsurance,
      this.damageInsuranceForSafetyFromTheftOrDamage,
      this.takeCareOfMarketing,
      this.bestMarketingForYourPropertyForHighestRateReservation,
      this.customDashboardToMakeEasyManageProperty,
      this.isEasyToStart,
      this.tellAboutYourResidence,
      this.step1ProvideBasicInfo,
      this.makeYourHomeStandOut,
      this.step2AddPhotoDescriptionTitle,
      this.completionAndPublication,
      this.step3CompletionAndPublication,
      this.step1,
      this.stepOneDescription,
      this.closestDescriptionOfYourHome,
      this.exit,
      this.typeOfAccommodationAvailableForGuest,
      this.landingManageTitle,
      this.landingManageSubTitle,
      this.landingDoNotManage,
      this.landingListProperty,
      this.landingClick,
      this.landingHandleEveryThing,
      this.landingHandlePropertyManagement,
      this.landingSheetTitle,
      this.landingHouseKeeping,
      this.landingOnSiteCord,
      this.landingSkip,
      this.manageTitle,
      this.manageSubTitle,
      this.fullName,
      this.enterFullName,
      this.noOfUnit,
      this.district,
      this.contact,
      this.submit,
        this.propertyTitle,
      this.willContact,
      this.whereDoYouLive,
      this.invisibleAddressStepNote,
      this.confirmYourLocation,
      this.showYourExactLocation,
      this.visibleLocationForGuestNote,
      this.someInformationAboutYourListing,
      this.addMoreDetailLaterSuchBedTypes,
      this.step2,
      this.addSomeFeaturesYouHaveInYourListing,
      this.tellGuestsAboutFeatureInYourSpace,
      this.canAddMoreFeatureAfterAdPosted,
      this.addSomePictureOfHouseOnATree,
      this.atleast5PhotosToGetStarted,
      this.dragYourPhoto,
      this.chooseAtleast5Photo,
      this.downloadFromYourDevice,
      this.magicallyArrangeYourPhoto,
      this.uploadedFrom1To5,
      this.successfullyCompletedStepDidYouLikeArrangement,
      this.dragPictureToRearrange,
      this.followUp,
      this.moveBack,
      this.setCoverPhoto,
      this.chooseTitleForHouse,
      this.setTitleNote,
      this.writeDescriptionOfDwelling,
      this.addDescriptionNote,
      this.step3,
      this.step3Description,
      this.chooseGuestOnFirstReservation,
      this.chooseFirstGuestDescription,
      this.guestOnDarent,
      this.seasonalGuest,
      this.setThePrice,
      this.priceChangeNote,
      this.reviewYourListing,
      this.reviewPropertyNote,
      this.whatNext,
      this.adjustYourSetting,
      this.confirmDetailAndPost,
      this.confirmIdentityAndRegistrationWithLocalGovernment,
      this.startBySettingCalendar,
      this.chooseAvailableDateForYourProperty,
      this.setHouseRulesCancellationPolicyHowGuestCanBook,
      this.flowHost,
      this.addProperty,
      this.saveExit,
      this.child,
      this.amenityValidation,
      this.propertyValidation,
      this.spaceValidation,
      this.bedValidation,
      this.minImagesValidation,
      this.maxImagesValidation,
      this.pleaseWait,
      this.placeName,
      this.descriptionMinValidation,
      this.minPriceValidation,
      this.minMaxNightsValidation,
      this.hostAgree,
      this.iAgree,
      this.hostAgreement,
      this.priceOn,
      this.checkInTime,
      this.checkOutTime,
      this.referralTitle,
      this.referralSubtitle,
      this.identityVerification,
      this.successUpdated,
      this.successSubmit,
      this.datesReserve,
      this.verificationDone,
      this.rateListing,
      this.messageAlso,
      this.validNumber,
      this.greaterToSix,
      this.ticketSubmitted,
      this.selectPayMethod,
      this.onlyFiveImages,
      this.numOrMailOnce,
      this.selectedDateReserved,
      this.calenderImported,
      this.cardHasAdded,
      this.paymentSuccessful,
      this.paymentFailed,
      this.darentCalenderExport,
      this.copyPasteLink,
      this.copiedClipboard,
      this.permitNo,
      this.individual,
      this.company,
      this.licenseNumber,
      this.crNumber,
      this.learnMoreListing,
      this.gotoHere,
      this.licenseNoHint,
      this.crNoHint,this.totalPrice,this.priceAfterDiscount});

  Listing.fromJson(Map<String, dynamic> json) {
    propertySuccessful = json['your_property_has_been_successfully'];
    messageRecieveShortly =
        json['you_will_receive_a_message_shortly_to_confirm'];
    propertyOk = json['publish_ok'];
    start = json['start'];
    addListingInStep = json['add_listing_in_step'];
    askToAddProperty = json['ask_to_add_property'];
    whatKindOf = json['what_kind_of'];
    whatKind = json['what_kind'];
    willHost = json['will_host'];
    spaceGuest = json['space_guest'];
    whereIsPlace = json['where_is_place'];
    located = json['located'];
    adults = json['adults'];
    children = json['children'];
    howManyGuest = json['how_many_guest'];
    letGuestKnow = json['let_guest_know'];
    haveAnyStandoutAmenity = json['have_any_standout_amenity'];
    haveAnySafetyItem = json['have_any_safety_item'];
    addSomePhoto = json['add_some_photo'];
    addPhotos = json['add_photos'];
    letsGiveName = json['lets_give_name'];
    createTitle = json['create_title'];
    letsDescribePlace = json['lets_describe_place'];
    createYourDescription = json['create_your_description'];
    setPrice = json['set_price'];
    pricePerNight = json['price_per_night'];
    keepInMindPlaceRange = json['keep_in_mind_place_range'];
    fewLastQuestion = json['few_last_question'];
    howAreYouHosting = json['how_are_you_hosting'];
    reviewRequest = json['review_request'];
    bookInstantly = json['book_instantly'];
    haveAnyAtPlace = json['have_any_at_place'];
    securityCamera = json['security_camera'];
    weapons = json['weapons'];
    dangerousAnimals = json['dangerous_animals'];
    someImportantThings = json['some_important_things'];
    beSureToComplyYourLaws = json['be_sure_to_comply_your_laws'];
    publishYourListing = json['publish_your_listing'];
    listingManagement = json['listing_management'];
    whatKindOfWillHost = json['what_kind_of_will_host'];
    whereIsPlaceLocated = json['where_is_place_located'];
    searchLocation = json['search_location'];
    addAddress = json['add_address'];
    confirmLocation = json['confirm_location'];
    buildingNo = json['building_no'];
    street = json['street'];
    youCanMovePointer = json['you_can_move_pointer'];
    saveLocation = json['save_location'];
    pets = json['pets'];
    guests = json['guests'];
    smoking = json['smoking'];
    kids = json['kids'];
    party = json['party'];
    waitingApproval = json['waiting_approval'];
    numberOfAppartment = json['number_of_appartment'];
    whatKindOfSpace = json['what_kind_of_space'];
    noListingAdded = json['no_listing_added'];
    translateToEnglish = json['translate_to_english'];
    translateToArabic = json['translate_to_arabic'];
    promotions = json['promotions'];
    promotion = json['promotion'];
    mytickets = json['mytickets'];
    createPromo = json['create_promo'];
    showYourHouseOnDarent = json['show_your_house_on_darent'];
    ableToEarn = json['able_to_earn'];
    showHouseOnDarentDescription = json['show_house_on_darent_description'];
    residenceConsistingRoom = json['residence_consisting_room'];
    presentPropertyEffectiveWay = json['present_property_effective_way'];
    helpWithAdviceToRaisePropertyReservationProfessionalRealEstate = json[
        'help_with_advice_to_raise_property_reservation_professional_real_estate'];
    damageInsurance = json['damage_insurance'];
    damageInsuranceForSafetyFromTheftOrDamage =
        json['damage_insurance_for_safety_from_theft_or_damage'];
    takeCareOfMarketing = json['take_care_of_marketing'];
    bestMarketingForYourPropertyForHighestRateReservation =
        json['best_marketing_for_your_property_for_highest_rate_reservation'];
    customDashboardToMakeEasyManageProperty =
        json['custom_dashboard_to_make_easy_manage_property'];
    isEasyToStart = json['is_easy_to_start'];
    tellAboutYourResidence = json['tell_about_your_residence'];
    step1ProvideBasicInfo = json['step_1_provide_basic_info'];
    makeYourHomeStandOut = json['make_your_home_stand_out'];
    step2AddPhotoDescriptionTitle = json['step_2_add_photo_description_title'];
    completionAndPublication = json['completion_and_publication'];
    step3CompletionAndPublication = json['step_3_completion_and_publication'];
    step1 = json['step_1'];
    stepOneDescription = json['step_one_description'];
    closestDescriptionOfYourHome = json['closest_description_of_your_home'];
    exit = json['exit'];
    typeOfAccommodationAvailableForGuest =
        json['type_of_accommodation_available_for_guest'];
    landingManageTitle = json['landing_manage_title'] ??
        "Complete your listing and start welcoming guests";
    landingManageSubTitle = json['landing_manage_subtitle'] ??
        "Reach thousands of travelers and manage your bookings your way.";
    landingListProperty = json['landing_list_property'] ?? "List My Property";
    landingDoNotManage =
        json['landing_not_manage'] ?? "Don’t have time to manage it yourself?";
    landingClick = json['landing_click'] ?? "click here";
    landingHandleEveryThing = json['landing_handle_everything'] ??
        "Let Darent handle everything - from guest check-in to cleaning and pricing.";
    landingHandlePropertyManagement =
        json['landing_property'] ?? "Request Property Management";

    landingSheetTitle = json['landing_sheet_title'] ??
        "We take care of every detail, from pricing and guest communication to ";
    landingHouseKeeping = json['landing_house_keeping'] ?? "housekeeping ";
    landingOnSiteCord = json['landing_on_site_cord'] ?? "On-site coordination";
    landingSkip = json['landing_skip'] ?? "Skip";

    manageTitle = json['manage_title'] ?? "Property Management Registration";
    manageSubTitle = json['manage_sub_title'] ??
        "Fill out the form below and our team will get in touch to discuss next steps";
    fullName = json['full_name'] ?? "Full Name";
    enterFullName = json['enter_full_name'] ?? "Enter your full name";
    noOfUnit = json['no_unit'] ?? "No of Units ";
    district = json['district'] ?? "District/Neighborhood";
    contact = json['contact'] ?? "Contact Number";
    submit = json['submit'] ?? "Submit Request";
    propertyTitle = json['property_name'] ?? "Enter Property Name";
    willContact = json['will_contact'] ??
        "One of our team members will contact you within 24 hours";

    whereDoYouLive = json['where_do_you_live'];
    invisibleAddressStepNote = json['invisible_address_step_note'];
    confirmYourLocation = json['confirm_your_location'];
    showYourExactLocation = json['show_your_exact_location'];
    visibleLocationForGuestNote = json['visible_location_for_guest_note'];
    someInformationAboutYourListing =
        json['some_information_about_your_listing'];
    addMoreDetailLaterSuchBedTypes =
        json['add_more_detail_later_such_bed_types'];
    step2 = json['step_2'];
    addSomeFeaturesYouHaveInYourListing =
        json['add_some_features_you_have_in_your_listing'];
    tellGuestsAboutFeatureInYourSpace =
        json['tell_guests_about_feature_in_your_space'];
    canAddMoreFeatureAfterAdPosted =
        json['can_add_more_feature_after_ad_posted'];
    addSomePictureOfHouseOnATree = json['add_some_picture_of_house_on_a_tree'];
    atleast5PhotosToGetStarted = json['atleast_5_photos_to_get_started'];
    dragYourPhoto = json['drag_your_photo'];
    chooseAtleast5Photo = json['choose_atleast_5_photo'];
    downloadFromYourDevice = json['download_from_your_device'];
    magicallyArrangeYourPhoto = json['magically_arrange_your_photo'];
    uploadedFrom1To5 = json['uploaded_from_1_to_5'];
    successfullyCompletedStepDidYouLikeArrangement =
        json['successfully_completed_step_did_you_like_arrangement'];
    dragPictureToRearrange = json['drag_picture_to_rearrange'];
    followUp = json['follow_up'];
    moveBack = json['move_back'];
    setCoverPhoto = json['set_cover_photo'];
    chooseTitleForHouse = json['choose_title_for_house'];
    setTitleNote = json['set_title_note'];
    writeDescriptionOfDwelling = json['write_description_of_dwelling'];
    addDescriptionNote = json['add_description_note'];
    step3 = json['step_3'];
    step3Description = json['step_3_description'];
    chooseGuestOnFirstReservation = json['choose_guest_on_first_reservation'];
    chooseFirstGuestDescription = json['choose_first_guest_description'];
    guestOnDarent = json['guest_on_darent'];
    seasonalGuest = json['seasonal_guest'];
    setThePrice = json['set_the_price'];
    priceChangeNote = json['price_change_note'];
    reviewYourListing = json['review_your_listing'];
    reviewPropertyNote = json['review_property_note'];
    whatNext = json['what_next'];
    adjustYourSetting = json['adjust_your_setting'];
    confirmDetailAndPost = json['confirm_detail_and_post'];
    confirmIdentityAndRegistrationWithLocalGovernment =
        json['confirm_identity_and_registration_with_local_government'];
    startBySettingCalendar = json['start_by_setting_calendar'];
    chooseAvailableDateForYourProperty =
        json['choose_available_date_for_your_property'];
    setHouseRulesCancellationPolicyHowGuestCanBook =
        json['set_house_rules_cancellation_policy_how_guest_can_book'];
    flowHost = json['flow_host'];
    addProperty = json['add_property'];
    saveExit = json['save_exit'] ?? "Save & Exit";
    child = json['child'] ?? "Child";
    amenityValidation =
        json['amenity_validation'] ?? "Please select at-least 1 Amenity.";
    propertyValidation =
        json['property_validation'] ?? "Select property type to proceed.";
    spaceValidation = json['space_validation'] ?? "Select space type first.";
    bedValidation = json['bed_validation'] ??
        "Select at-least 1 bed when the bedroom is greater than zero";
    minImagesValidation =
        json['min_images_validation'] ?? "Select at-least 5 images";
    maxImagesValidation =
        json['max_images_validation'] ?? "Maximum 20 images are allowed";
    pleaseWait = json['please_wait'] ?? "Please wait";
    placeName = json['place_name'] ?? "Place name";
    descriptionMinValidation =
        json['description_min_validation'] ?? "Minimum 20 characters";
    minPriceValidation =
        json['min_price_validation'] ?? "Prices must be at-least 50";
    minMaxNightsValidation = json['min_max_nights_validation'] ??
        "Max Nights Should be Greater than Min Nights";
    hostAgree = json['host_agree'] ?? "You must agree to the host Agreement";
    iAgree = json['i_agree'] ?? "I Agree to the";
    hostAgreement = json['host_agreement'] ?? "Host Agreement";
    priceOn = json['price_on'] ?? "Price on";
    checkInTime = json['check_in_time'];
    checkOutTime = json['check_out_time'];
    referralTitle = json['referral_title'];
    referralSubtitle = json['referral_subtitle'];
    identityVerification = json['identity_verification'];
    successUpdated =
        json['success_updated'] ?? "Successfully updated your property";
    successSubmit = json['success_submit'] ?? "successfully submit";
    datesReserve = json['dates_reserve'] ?? "All selected dates are Reserved!";
    verificationDone = json['verification_done'] ?? "Verification Done";
    rateListing = json['rate_listing'] ?? "Please rate this listing";
    messageAlso = json['message_also'] ?? "Write a message also";
    validNumber = json['valid_number'] ?? "Please enter Valid phone Number";
    greaterToSix =
        json['greater_to_six'] ?? "Password must be 6 digits or greater than";
    ticketSubmitted =
        json['ticket_submited'] ?? "Your ticket has been submitted.";
    selectPayMethod = json['select_pay_method'] ?? "Select Payment Method";
    onlyFiveImages = json['only_five_images'] ?? "Only 5 images are allowed!";
    numOrMailOnce = json['num_or-mail_once'] ??
        "Please Enter Phone Number or E-mail at a time";
    selectedDateReserved =
        json['selected_date_reserved'] ?? "All selected dates are Reserved!";
    calenderImported =
        json['calender_imported'] ?? "Calendar successfully imported";
    cardHasAdded = json['card_has_added '] ?? "Card has been Added";
    paymentSuccessful = json['paymnet_successfull'] ?? "Payment Successful";
    paymentFailed = json['payment_failed'] ?? "Payment Failed";
    darentCalenderExport =
        json['darent_calender_export'] ?? "Darent Calendar Export";
    copyPasteLink = json["copy_paste_link"] ??
        "Copy And Paste The Link Into Other Services You Use.";
    copiedClipboard = json['copied_clipboard'] ?? "Copied to Clipboard";

    permitNo = json['permitnumber'] ?? "Permit Number";
    individual = json['individual'] ?? "Individual";
    company = json['company'] ?? "Company";
    licenseNumber = json['license_number'] ?? '';
    crNumber = json['cr_number'] ?? '';
    learnMoreListing = json['learn_more_listing'] ?? '';
    gotoHere = json['goto_here'] ?? '';
    licenseNoHint = json['license_no_hint'] ?? '';
    crNoHint = json['cr_no_hint'] ?? '';
    totalPrice = json['total_price'] ?? 'Total Price';
    priceAfterDiscount = json['price_after_discount'] ?? 'Price after discount';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['start'] = start;
    data['add_listing_in_step'] = addListingInStep;
    data['ask_to_add_property'] = askToAddProperty;
    data['what_kind_of'] = whatKindOf;
    data['what_kind'] = whatKind;
    data['will_host'] = willHost;
    data['space_guest'] = spaceGuest;
    data['where_is_place'] = whereIsPlace;
    data['located'] = located;
    data['adults'] = adults;
    data['children'] = children;
    data['how_many_guest'] = howManyGuest;
    data['let_guest_know'] = letGuestKnow;
    data['have_any_standout_amenity'] = haveAnyStandoutAmenity;
    data['have_any_safety_item'] = haveAnySafetyItem;
    data['add_some_photo'] = addSomePhoto;
    data['add_photos'] = addPhotos;
    data['lets_give_name'] = letsGiveName;
    data['create_title'] = createTitle;
    data['lets_describe_place'] = letsDescribePlace;
    data['create_your_description'] = createYourDescription;
    data['set_price'] = setPrice;
    data['price_per_night'] = pricePerNight;
    data['keep_in_mind_place_range'] = keepInMindPlaceRange;
    data['few_last_question'] = fewLastQuestion;
    data['how_are_you_hosting'] = howAreYouHosting;
    data['review_request'] = reviewRequest;
    data['book_instantly'] = bookInstantly;
    data['have_any_at_place'] = haveAnyAtPlace;
    data['security_camera'] = securityCamera;
    data['weapons'] = weapons;
    data['dangerous_animals'] = dangerousAnimals;
    data['some_important_things'] = someImportantThings;
    data['be_sure_to_comply_your_laws'] = beSureToComplyYourLaws;
    data['publish_your_listing'] = publishYourListing;
    data['listing_management'] = listingManagement;
    data['what_kind_of_will_host'] = whatKindOfWillHost;
    data['where_is_place_located'] = whereIsPlaceLocated;
    data['search_location'] = searchLocation;
    data['add_address'] = addAddress;
    data['confirm_location'] = confirmLocation;
    data['building_no'] = buildingNo;
    data['street'] = street;
    data['you_can_move_pointer'] = youCanMovePointer;
    data['save_location'] = saveLocation;
    data['pets'] = pets;
    data['guests'] = guests;
    data['smoking'] = smoking;
    data['kids'] = kids;
    data['party'] = party;
    data['waiting_approval'] = waitingApproval;
    data['number_of_appartment'] = numberOfAppartment;
    data['what_kind_of_space'] = whatKindOfSpace;
    data['no_listing_added'] = noListingAdded;
    data['translate_to_english'] = translateToEnglish;
    data['translate_to_arabic'] = translateToArabic;
    data['promotions'] = promotions;
    data['promotion'] = promotion;
    data['mytickets'] = mytickets;
    data['create_promo'] = createPromo;
    data['show_your_house_on_darent'] = showYourHouseOnDarent;
    data['able_to_earn'] = ableToEarn;
    data['show_house_on_darent_description'] = showHouseOnDarentDescription;
    data['residence_consisting_room'] = residenceConsistingRoom;
    data['present_property_effective_way'] = presentPropertyEffectiveWay;
    data['help_with_advice_to_raise_property_reservation_professional_real_estate'] =
        helpWithAdviceToRaisePropertyReservationProfessionalRealEstate;
    data['damage_insurance'] = damageInsurance;
    data['damage_insurance_for_safety_from_theft_or_damage'] =
        damageInsuranceForSafetyFromTheftOrDamage;
    data['take_care_of_marketing'] = takeCareOfMarketing;
    data['best_marketing_for_your_property_for_highest_rate_reservation'] =
        bestMarketingForYourPropertyForHighestRateReservation;
    data['custom_dashboard_to_make_easy_manage_property'] =
        customDashboardToMakeEasyManageProperty;
    data['is_easy_to_start'] = isEasyToStart;
    data['tell_about_your_residence'] = tellAboutYourResidence;
    data['step_1_provide_basic_info'] = step1ProvideBasicInfo;
    data['make_your_home_stand_out'] = makeYourHomeStandOut;
    data['step_2_add_photo_description_title'] = step2AddPhotoDescriptionTitle;
    data['completion_and_publication'] = completionAndPublication;
    data['step_3_completion_and_publication'] = step3CompletionAndPublication;
    data['step_1'] = step1;
    data['step_one_description'] = stepOneDescription;
    data['closest_description_of_your_home'] = closestDescriptionOfYourHome;
    data['exit'] = exit;
    data['type_of_accommodation_available_for_guest'] =
        typeOfAccommodationAvailableForGuest;
    data['landing_manage_title'] = landingManageTitle;
    data['landing_not_manage'] = landingDoNotManage;
    data['landing_click'] = landingClick;
    data['landing_handle_everything'] = landingHandleEveryThing;
    data['landing_property'] = landingHandlePropertyManagement;
    data['landing_list_property'] = landingListProperty;

    data['landing_manage_subtitle'] = landingManageSubTitle;
    data['landing_sheet_title'] = landingSheetTitle;
    data['landing_house_keeping'] = landingHouseKeeping;
    data['landing_on_site_cord'] = landingOnSiteCord;
    data['landing_skip'] = landingSkip;

    data['manage_title'] = manageTitle;
    data['manage_sub_title'] = manageSubTitle;
    data['full_name'] = fullName;
    data['enter_full_name'] = enterFullName;
    data['no_unit'] = noOfUnit;
    data['district'] = district;
    data['contact'] = contact;
    data['submit'] = submit;
    data['property_name'] = propertyTitle;
    data['will_contact'] = willContact;
    data['where_do_you_live'] = whereDoYouLive;
    data['invisible_address_step_note'] = invisibleAddressStepNote;
    data['confirm_your_location'] = confirmYourLocation;
    data['show_your_exact_location'] = showYourExactLocation;
    data['visible_location_for_guest_note'] = visibleLocationForGuestNote;
    data['some_information_about_your_listing'] =
        someInformationAboutYourListing;
    data['add_more_detail_later_such_bed_types'] =
        addMoreDetailLaterSuchBedTypes;
    data['step_2'] = step2;
    data['add_some_features_you_have_in_your_listing'] =
        addSomeFeaturesYouHaveInYourListing;
    data['tell_guests_about_feature_in_your_space'] =
        tellGuestsAboutFeatureInYourSpace;
    data['can_add_more_feature_after_ad_posted'] =
        canAddMoreFeatureAfterAdPosted;
    data['add_some_picture_of_house_on_a_tree'] = addSomePictureOfHouseOnATree;
    data['atleast_5_photos_to_get_started'] = atleast5PhotosToGetStarted;
    data['drag_your_photo'] = dragYourPhoto;
    data['choose_atleast_5_photo'] = chooseAtleast5Photo;
    data['download_from_your_device'] = downloadFromYourDevice;
    data['magically_arrange_your_photo'] = magicallyArrangeYourPhoto;
    data['uploaded_from_1_to_5'] = uploadedFrom1To5;
    data['successfully_completed_step_did_you_like_arrangement'] =
        successfullyCompletedStepDidYouLikeArrangement;
    data['drag_picture_to_rearrange'] = dragPictureToRearrange;
    data['follow_up'] = followUp;
    data['move_back'] = moveBack;
    data['set_cover_photo'] = setCoverPhoto;
    data['choose_title_for_house'] = chooseTitleForHouse;
    data['set_title_note'] = setTitleNote;
    data['write_description_of_dwelling'] = writeDescriptionOfDwelling;
    data['add_description_note'] = addDescriptionNote;
    data['step_3'] = step3;
    data['step_3_description'] = step3Description;
    data['choose_guest_on_first_reservation'] = chooseGuestOnFirstReservation;
    data['choose_first_guest_description'] = chooseFirstGuestDescription;
    data['guest_on_darent'] = guestOnDarent;
    data['seasonal_guest'] = seasonalGuest;
    data['set_the_price'] = setThePrice;
    data['price_change_note'] = priceChangeNote;
    data['review_your_listing'] = reviewYourListing;
    data['review_property_note'] = reviewPropertyNote;
    data['what_next'] = whatNext;
    data['adjust_your_setting'] = adjustYourSetting;
    data['confirm_detail_and_post'] = confirmDetailAndPost;
    data['confirm_identity_and_registration_with_local_government'] =
        confirmIdentityAndRegistrationWithLocalGovernment;
    data['start_by_setting_calendar'] = startBySettingCalendar;
    data['choose_available_date_for_your_property'] =
        chooseAvailableDateForYourProperty;
    data['set_house_rules_cancellation_policy_how_guest_can_book'] =
        setHouseRulesCancellationPolicyHowGuestCanBook;
    data['flow_host'] = flowHost;
    data['add_property'] = addProperty;
    data['identity_verification'] = identityVerification;
    data['success_updated'] = successUpdated;
    data['success_submit'] = successSubmit;
    data['dates_reserve'] = datesReserve;
    data['verification_done'] = verificationDone;
    data['rate_listing'] = rateListing;
    data['message_also'] = messageAlso;
    data['valid_number'] = validNumber;
    data['greater_to_six'] = greaterToSix;
    data['ticket_submited'] = ticketSubmitted;
    data['select_pay_method'] = selectPayMethod;
    data['only_five_images'] = onlyFiveImages;
    data['num_or-mail_once'] = numOrMailOnce;
    data['selected_date_reserved'] = selectedDateReserved;
    data['calender_imported'] = calenderImported;
    data['card_has_added'] = cardHasAdded;
    data['paymnet_successfull'] = paymentSuccessful;
    data['payment_failed'] = paymentFailed;
    data['darent_calender_export'] = darentCalenderExport;
    data['copy_paste_link'] = copyPasteLink;
    data['copied_clipboard'] = copiedClipboard;
    return data;
  }
}

class Reviews {
  Reviews({
    required this.aboutYou,
    required this.byYou,
  });

  String aboutYou;
  String byYou;

  factory Reviews.fromJson(Map<String, dynamic> json) => Reviews(
        aboutYou: json["about_you"],
        byYou: json["by_you"],
      );
}

class Search {
  Search({
    required this.resultsFor,
    required this.dates,
    required this.checkIn,
    required this.checkOut,
    required this.guest,
    required this.roomType,
    required this.priceRange,
    required this.moreFilters,
    required this.noResultFound,
    required this.size,
    required this.bedrooms,
    required this.bathrooms,
    required this.beds,
    required this.amenities,
    required this.propertyType,
    required this.bookType,
    required this.cancel,
    required this.copylink,
    required this.applyFilter,
    required this.showMap,
    required this.viewList,
    required this.of,
    required this.rentals,
    required this.planTrip,
    required this.clearValues,
    required this.safetyItems,
    required this.minimum,
    required this.maximum,
    required this.roomsAndBeds,
    required this.totalOf,
    required this.nights,
  });

  String resultsFor;
  String dates;
  String checkIn;
  String checkOut;
  String guest;
  String roomType;
  String priceRange;
  String moreFilters;
  String? noResultFound;
  String size;
  String bedrooms;
  String bathrooms;
  String beds;
  String amenities;
  String propertyType;
  String? bookType;
  String cancel;
  String copylink;
  String applyFilter;
  String showMap;
  String viewList;
  String of;
  String rentals;
  String planTrip;
  String clearValues;
  String safetyItems;
  String minimum;
  String maximum;
  String roomsAndBeds;
  String? totalOf;
  String? nights;

  factory Search.fromJson(Map<String, dynamic> json) => Search(
        resultsFor: json["results_for"],
        dates: json["dates"],
        checkIn: json["check_in"],
        checkOut: json["check_out"],
        guest: json["guest"],
        roomType: json["room_type"],
        priceRange: json["price_range"],
        moreFilters: json["more_filters"],
        noResultFound: json["no_result_found"],
        size: json["size"],
        bedrooms: json["bedrooms"],
        bathrooms: json["bathrooms"],
        beds: json["beds"],
        amenities: json["amenities"],
        propertyType: json["property_type"],
        bookType: json["book_type"],
        cancel: json["cancel"],
        copylink: json["copy_link"],
        applyFilter: json["apply_filter"],
        showMap: json["show_map"],
        viewList: json["view_list"],
        of: json["of"],
        rentals: json["rentals"],
        planTrip: json['plan_trip'],
        clearValues: json['clear_values'],
        safetyItems: json['safety_items'],
        minimum: json['minimum'],
        maximum: json['maximum'],
        roomsAndBeds: json['rooms_and_beds'],
        totalOf: json['total_of'],
        nights: json['nights'],
      );
}

class Settings {
  Settings({
    required this.name,
    required this.aboutYou,
    required this.yourLocation,
  });

  String name;
  String aboutYou;
  String yourLocation;

  factory Settings.fromJson(Map<String, dynamic> json) => Settings(
        name: json["name"],
        aboutYou: json["about_you"],
        yourLocation: json["your_location"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "about_you": aboutYou,
        "your_location": yourLocation,
      };
}

class Sidenav {
  Sidenav({
    required this.yourListing,
    required this.propertyBooking,
    required this.yourTrip,
    required this.previousTrip,
    required this.editProfile,
    required this.photo,
    required this.verification,
    required this.reviews,
    required this.payouts,
    required this.wallet,
    required this.accountManager,
    required this.bankAccount,
    required this.addManager,
    required this.noManagerAvailable,
    required this.noTicket,
  });

  String yourListing;
  String propertyBooking;
  String yourTrip;
  String previousTrip;
  String editProfile;
  String photo;
  String verification;
  String reviews;
  String payouts;
  String wallet;
  String accountManager;
  String bankAccount;
  String addManager;
  String noManagerAvailable;
  String noTicket;

  factory Sidenav.fromJson(Map<String, dynamic> json) => Sidenav(
      yourListing: json["your_listing"],
      propertyBooking: json["property_booking"],
      yourTrip: json["your_trip"],
      previousTrip: json["previous_trip"],
      editProfile: json["edit_profile"],
      photo: json["photo"],
      verification: json["verification"],
      reviews: json["reviews"],
      payouts: json["payouts"],
      wallet: json["wallet"],
      accountManager: json["account_manager"],
      bankAccount: json["bank_account"],
      addManager: json["add_manager"] ?? "Add Manager",
      noManagerAvailable:
          json["no_manager_available"] ?? "No Manager Available",
      noTicket: json["no_ticket"] ?? "No Tickets Available");

  Map<String, dynamic> toJson() => {
        "your_listing": yourListing,
        "property_booking": propertyBooking,
        "your_trip": yourTrip,
        "previous_trip": previousTrip,
        "edit_profile": editProfile,
        "photo": photo,
        "verification": verification,
        "reviews": reviews,
        "payouts": payouts,
        "wallet": wallet,
        "add_manager": addManager,
        "no_manager_available": noManagerAvailable,
        "no_ticket": noTicket
      };
}

class SignUp {
  SignUp({
    required this.signUpWithFacebook,
    required this.signUpWithGoogle,
    required this.signUpWithEmail,
    required this.signUp,
    required this.signUpWith,
    required this.facebook,
    required this.google,
    required this.firstName,
    required this.lastName,
    required this.birthDay,
    required this.month,
    required this.day,
    required this.year,
    required this.agreeTo,
    required this.serviceTerm,
    required this.privacyPolicy,
    required this.refundPolicy,
    required this.and,
    required this.hostGuarantee,
    required this.already,
    required this.member,
    required this.login,
    required this.welcomeDarent,
    required this.bySubmittingYourNumber,
    required this.termCondition,
    required this.submit,
    required this.loginWith,
    required this.email,
    required this.yourEmail,
    required this.password,
    required this.alreadyHave,
    required this.bySubmittingYourIqama,
    required this.enterCodeEmail,
    required this.resendCode,
    required this.changeEmail,
    required this.writeOtp,
    required this.confirmYourPhone,
    required this.enterCodeWeSent,
  });

  String signUpWithFacebook;
  String signUpWithGoogle;
  String signUpWithEmail;
  String signUp;
  String signUpWith;
  String facebook;
  String google;
  String firstName;
  String lastName;
  String birthDay;
  String month;
  String day;
  String year;
  String agreeTo;
  String serviceTerm;
  String privacyPolicy;
  String refundPolicy;
  String and;
  String hostGuarantee;
  String already;
  String member;
  String login;
  String welcomeDarent;
  String bySubmittingYourNumber;
  String termCondition;
  String submit;
  String loginWith;
  String email;
  String yourEmail;
  String password;
  String alreadyHave;
  String bySubmittingYourIqama;
  String? enterCodeEmail;
  String? resendCode;
  String? changeEmail;
  String? writeOtp;
  String? confirmYourPhone;
  String? enterCodeWeSent;

  factory SignUp.fromJson(Map<String, dynamic> json) => SignUp(
        signUpWithFacebook: json["sign_up_with_facebook"],
        signUpWithGoogle: json["sign_up_with_google"],
        signUpWithEmail: json["sign_up_with_email"],
        signUp: json["sign_up"],
        signUpWith: json["sign_up_with"],
        facebook: json["facebook"],
        google: json["google"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        birthDay: json["birth_day"],
        month: json["month"],
        day: json["day"],
        year: json["year"],
        agreeTo: json["agree_to"],
        serviceTerm: json["service_term"],
        privacyPolicy: json["privacy_policy"],
        refundPolicy: json["refund_policy"],
        and: json["and"],
        hostGuarantee: json["host_guarantee"],
        already: json["already"],
        member: json["member"],
        login: json["login"],
        welcomeDarent: json["welcome_darent"],
        bySubmittingYourNumber: json["by_submitting_your_number"],
        termCondition: json["term_condition"],
        submit: json["submit"],
        loginWith: json["login_with"],
        email: json["email"],
        yourEmail: json["your_email"],
        password: json["password"],
        alreadyHave: json["already_have"],
        bySubmittingYourIqama: json["by_submitting_your_iqama"],
        enterCodeEmail: json["enter_code_email"] ??
            "Enter the code sent to \$email. If you didn't Receive Code",
        resendCode: json["resend_code"] ?? "Resend Code",
        changeEmail: json["change_email"] ?? "Change Email",
        writeOtp: json["write_otp"] ?? "Write OTP Code",
        confirmYourPhone: json["confirm_your_phone"] ?? "Confirm your Number",
        enterCodeWeSent: json["enter_code_we_sent"] ?? "Confirm your Number",
      );

  Map<String, dynamic> toJson() => {
        "sign_up_with_facebook": signUpWithFacebook,
        "sign_up_with_google": signUpWithGoogle,
        "sign_up_with_email": signUpWithEmail,
        "sign_up": signUp,
        "sign_up_with": signUpWith,
        "facebook": facebook,
        "google": google,
        "first_name": firstName,
        "last_name": lastName,
        "birth_day": birthDay,
        "month": month,
        "day": day,
        "year": year,
        "agree_to": agreeTo,
        "service_term": serviceTerm,
        "privacy_policy": privacyPolicy,
        "refund_policy": refundPolicy,
        "and": and,
        "host_guarantee": hostGuarantee,
        "already": already,
        "member": member,
        "login": login,
        "welcome_darent": welcomeDarent,
        "by_submitting_your_number": bySubmittingYourNumber,
        "term_condition": termCondition,
        "submit": submit,
        "login_with": loginWith,
        "email": email,
        "your_email": yourEmail,
        "password": password,
        "already_have": alreadyHave,
        "by_submitting_your_iqama": bySubmittingYourIqama,
        "enter_code_email": enterCodeEmail,
        "resend_code": resendCode,
        "change_email": changeEmail,
        "write_otp": writeOtp,
      };
}

class Sort {
  Sort({
    required this.lowPricesFirst,
    required this.highPricesFirst,
    required this.mostRatedFirst,
    required this.newestToTheCity,
  });

  String lowPricesFirst;
  String highPricesFirst;
  String mostRatedFirst;
  String newestToTheCity;

  factory Sort.fromJson(Map<String, dynamic> json) => Sort(
        lowPricesFirst: json["low_prices_first"],
        highPricesFirst: json["high_prices_first"],
        mostRatedFirst: json["most_rated_first"],
        newestToTheCity: json["newest_to_the_city"],
      );

  Map<String, dynamic> toJson() => {
        "low_prices_first": lowPricesFirst,
        "high_prices_first": highPricesFirst,
        "most_rated_first": mostRatedFirst,
        "newest_to_the_city": newestToTheCity,
      };
}

class Wallet {
  Wallet({
    required this.walletCredit,
    required this.paymentMethods,
    required this.paymentMethodNote,
    required this.savedCards,
    required this.noSavedCard,
    required this.addCard,
    required this.holderName,
    required this.expMonth,
    required this.expYear,
    required this.cardInfoSaved,
    required this.myWallet,
    required this.payoutInfo,
    required this.atleastOnePayoutMethod,
    required this.setupPayout,
    required this.latestTransaction,
    required this.noTransactionFound,
    required this.makePaymentThroughDarent,
    required this.alwaysPayAndCommunicateThroughDarent,
    required this.ticketListing,
    required this.createTicket,
    required this.payBy,
    required this.subject,
    required this.ticketType,
    required this.bookingCode,
    required this.selectFile,
    required this.bookingId,
    required this.createDate,
    required this.ticketId,
    required this.ticketSubject,
    required this.selectYourTicketType,
    required this.description,
    required this.doYouWantToDeleteCard,
    required this.addBankAccount,
    required this.cards,
  });

  String walletCredit;
  String paymentMethods;
  String paymentMethodNote;
  String savedCards;
  String noSavedCard;
  String addCard;
  String holderName;
  String expMonth;
  String expYear;
  String cardInfoSaved;
  String myWallet;
  String payoutInfo;
  String atleastOnePayoutMethod;
  String setupPayout;
  String latestTransaction;
  String noTransactionFound;
  String makePaymentThroughDarent;
  String alwaysPayAndCommunicateThroughDarent;
  String ticketListing;
  String createTicket;
  String payBy;
  String subject;
  String ticketType;
  String bookingCode;
  String selectFile;
  String bookingId;
  String createDate;
  String ticketId;
  String ticketSubject;
  String selectYourTicketType;
  String description;
  String doYouWantToDeleteCard;
  String addBankAccount;
  String cards;

  factory Wallet.fromJson(Map json) => Wallet(
        walletCredit: json["wallet_credit"],
        paymentMethods: json["payment_methods"],
        paymentMethodNote: json["payment_method_note"],
        savedCards: json["saved_cards"],
        noSavedCard: json["no_saved_card"],
        addCard: json["add_card"],
        holderName: json['holder_name'],
        expMonth: json['exp_month'],
        expYear: json['exp_year'],
        cardInfoSaved: json['card_info_saved'],
        myWallet: json['my_wallet'],
        payoutInfo: json['payout_info'],
        atleastOnePayoutMethod: json['atleat_one_payment_method'],
        setupPayout: json['setup_payout'],
        latestTransaction: json['latest_transaction'],
        noTransactionFound: json['no_transaction_found'],
        makePaymentThroughDarent: json['make_payment_through_darent'],
        alwaysPayAndCommunicateThroughDarent:
            json['always_pay_and_communicate_through_darent'],
        ticketListing: json['ticket_listing'],
        createTicket: json['create_ticket'],
        payBy: json['pay_by'] ?? 'Pay by',
        subject: json['subject'],
        ticketType: json['ticket_type'],
        bookingCode: json['booking_code'],
        selectFile: json['select_file'],
        bookingId: json['booking_id'],
        createDate: json['create_date'],
        ticketId: json['ticket_id'],
        ticketSubject: json['ticket_subject'] ?? 'Ticket Subject',
        selectYourTicketType:
            json['select_your_ticket_type'] ?? 'Select your Ticket Type',
        description: json['description'],
        doYouWantToDeleteCard: json['do_you_want_to_delete_card'],
        addBankAccount: json['addbank_account'] ??
            "To ensure the successful transfer of reservation amounts, please make sure to register your banking information.",
        cards: json["cards"] ?? "Cards",
      );
}

class StaticPages {
  StaticPages({
    required this.hosting,
    required this.company,
  });

  String hosting;
  String company;

  factory StaticPages.fromJson(Map<String, dynamic> json) => StaticPages(
        hosting: json["hosting"],
        company: json["company"],
      );

  Map<String, dynamic> toJson() => {
        "hosting": hosting,
        "company": company,
      };
}

class Success {
  Success({
    required this.bookingAcceptSuccess,
    required this.bookingDeclineSuccess,
    required this.bookingExpireSuccess,
    required this.resetPassSendSuccess,
    required this.passChangeSuccess,
    required this.paymentSuccess,
    required this.paymentCompleteSuccess,
    required this.addedSuccess,
    required this.resereCancelSuccess,
    required this.registerSuccess,
    required this.emailCofirmationSuccess,
    required this.payoutUpdateSuccess,
    required this.payoutDeleteSuccess,
    required this.defaultPayoutSuccess,
    required this.deleteSuccess,
    required this.favouriteAddSuccess,
    required this.favouriteRemoveSuccess,
  });

  String bookingAcceptSuccess;
  String bookingDeclineSuccess;
  String bookingExpireSuccess;
  String resetPassSendSuccess;
  String passChangeSuccess;
  String paymentSuccess;
  String paymentCompleteSuccess;
  String addedSuccess;
  String resereCancelSuccess;
  String registerSuccess;
  String emailCofirmationSuccess;
  String payoutUpdateSuccess;
  String payoutDeleteSuccess;
  String defaultPayoutSuccess;
  String deleteSuccess;
  String favouriteAddSuccess;
  String favouriteRemoveSuccess;

  factory Success.fromJson(Map<String, dynamic> json) => Success(
        bookingAcceptSuccess: json["booking_accept_success"],
        bookingDeclineSuccess: json["booking_decline_success"],
        bookingExpireSuccess: json["booking_expire_success"],
        resetPassSendSuccess: json["reset_pass_send_success"],
        passChangeSuccess: json["pass_change_success"],
        paymentSuccess: json["payment_success"],
        paymentCompleteSuccess: json["payment_complete_success"],
        addedSuccess: json["added_success"],
        resereCancelSuccess: json["resere_cancel_success"],
        registerSuccess: json["register_success"],
        emailCofirmationSuccess: json["email_cofirmation_success"],
        payoutUpdateSuccess: json["payout_update_success"],
        payoutDeleteSuccess: json["payout_delete_success"],
        defaultPayoutSuccess: json["default_payout_success"],
        deleteSuccess: json["delete_success"],
        favouriteAddSuccess: json["favourite_add_success"],
        favouriteRemoveSuccess: json["favourite_remove_success"],
      );

  Map<String, dynamic> toJson() => {
        "booking_accept_success": bookingAcceptSuccess,
        "booking_decline_success": bookingDeclineSuccess,
        "booking_expire_success": bookingExpireSuccess,
        "reset_pass_send_success": resetPassSendSuccess,
        "pass_change_success": passChangeSuccess,
        "payment_success": paymentSuccess,
        "payment_complete_success": paymentCompleteSuccess,
        "added_success": addedSuccess,
        "resere_cancel_success": resereCancelSuccess,
        "register_success": registerSuccess,
        "email_cofirmation_success": emailCofirmationSuccess,
        "payout_update_success": payoutUpdateSuccess,
        "payout_delete_success": payoutDeleteSuccess,
        "default_payout_success": defaultPayoutSuccess,
        "delete_success": deleteSuccess,
        "favourite_add_success": favouriteAddSuccess,
        "favourite_remove_success": favouriteRemoveSuccess,
      };
}

class TripsActive {
  TripsActive(
      {required this.yourTrip,
      required this.noCurrentTrip,
      required this.pendingTrip,
      required this.status,
      required this.location,
      required this.host,
      required this.date,
      required this.option,
      required this.cancelRequest,
      required this.currentTrip,
      required this.viewReceipt,
      required this.upcomingTrip,
      required this.cancelBooking,
      required this.cancelBookingData,
      required this.responseNotShare,
      required this.declining,
      required this.needAccommodation,
      required this.travelDateChange,
      required this.madeItAccident,
      required this.extenuatingCircumstance,
      required this.hostNeedCancel,
      required this.uncomfortableHost,
      required this.placeNotExpect,
      required this.other,
      required this.whyAreCancel,
      required this.typeMessage,
      required this.cancelMyBooking,
      required this.close,
      required this.pending,
      required this.processing,
      required this.accepted,
      required this.declined,
      required this.listView,
      required this.calendarView,
      required this.cancelReserveWarn,
      required this.unpaid});

  String yourTrip;
  String noCurrentTrip;
  String pendingTrip;
  String status;
  String location;
  String host;
  String date;
  String option;
  String cancelRequest;
  String currentTrip;
  String viewReceipt;
  String upcomingTrip;
  String cancelBooking;
  String cancelBookingData;
  String responseNotShare;
  String declining;
  String needAccommodation;
  String travelDateChange;
  String madeItAccident;
  String extenuatingCircumstance;
  String hostNeedCancel;
  String uncomfortableHost;
  String placeNotExpect;
  String other;
  String whyAreCancel;
  String typeMessage;
  String cancelMyBooking;
  String close;
  String pending;
  String processing;
  String accepted;
  String declined;
  String listView;
  String calendarView;
  String cancelReserveWarn;
  String unpaid;

  factory TripsActive.fromJson(Map<String, dynamic> json) => TripsActive(
        yourTrip: json["your_trip"],
        noCurrentTrip: json["no_current_trip"],
        pendingTrip: json["pending_trip"],
        status: json["status"],
        location: json["location"],
        host: json["host"],
        date: json["date"],
        option: json["option"],
        cancelRequest: json["cancel_request"],
        currentTrip: json["current_trip"],
        viewReceipt: json["view_receipt"],
        upcomingTrip: json["upcoming_trip"],
        cancelBooking: json["cancel_booking"],
        cancelBookingData: json["cancel_booking_data"],
        responseNotShare: json["response_not_share"],
        declining: json["declining"],
        needAccommodation: json["need_accommodation"],
        travelDateChange: json["travel_date_change"],
        madeItAccident: json["made_it_accident"],
        extenuatingCircumstance: json["extenuating_circumstance"],
        hostNeedCancel: json["host_need_cancel"],
        uncomfortableHost: json["uncomfortable_host"],
        placeNotExpect: json["place_not_expect"],
        other: json["other"],
        whyAreCancel: json["why_are_cancel"],
        typeMessage: json["type_message"],
        cancelMyBooking: json["cancel_my_booking"],
        close: json["close"],
        pending: json['pending'],
        processing: json['processing'],
        accepted: json['accepted'],
        declined: json['declined'],
        listView: json['list_view'],
        calendarView: json['calendar_view'],
        cancelReserveWarn: json['cancel_reserve_warn'],
        unpaid: json['unpaid'] ?? "Unpaid",
      );
}

class TripsReceipt {
  TripsReceipt({
    required this.customerReceipt,
    required this.confirmationCode,
    required this.receipt,
    required this.name,
    required this.accommodatoinAddress,
    required this.travelDestination,
    required this.accommodationHost,
    required this.night,
    required this.duration,
    required this.checkIn,
    required this.flexibleCheckTime,
    required this.accommodationType,
    required this.checkOut,
    required this.flexibleCheckOut,
    required this.bookingCharge,
    required this.additionalGuestFee,
    required this.cleaningFee,
    required this.securityFee,
    required this.serviceFee,
    required this.total,
    required this.paymentReceived,
  });

  String customerReceipt;
  String confirmationCode;
  String receipt;
  String name;
  String accommodatoinAddress;
  String travelDestination;
  String accommodationHost;
  String night;
  String duration;
  String checkIn;
  String flexibleCheckTime;
  String accommodationType;
  String checkOut;
  String flexibleCheckOut;
  String bookingCharge;
  String additionalGuestFee;
  String cleaningFee;
  String securityFee;
  String serviceFee;
  String total;
  String paymentReceived;

  factory TripsReceipt.fromJson(Map<String, dynamic> json) => TripsReceipt(
        customerReceipt: json["customer_receipt"],
        confirmationCode: json["confirmation_code"],
        receipt: json["receipt"],
        name: json["name"],
        accommodatoinAddress: json["accommodatoin_address"],
        travelDestination: json["travel_destination"],
        accommodationHost: json["accommodation_host"],
        night: json["night"],
        duration: json["duration"],
        checkIn: json["check_in"],
        flexibleCheckTime: json["flexible_check_time"],
        accommodationType: json["accommodation_type"],
        checkOut: json["check_out"],
        flexibleCheckOut: json["flexible_check_out"],
        bookingCharge: json["booking_charge"],
        additionalGuestFee: json["additional_guest_fee"],
        cleaningFee: json["cleaning_fee"],
        securityFee: json["security_fee"],
        serviceFee: json["service_fee"],
        total: json["total"],
        paymentReceived: json["payment_received"],
      );

  Map<String, dynamic> toJson() => {
        "customer_receipt": customerReceipt,
        "confirmation_code": confirmationCode,
        "receipt": receipt,
        "name": name,
        "accommodatoin_address": accommodatoinAddress,
        "travel_destination": travelDestination,
        "accommodation_host": accommodationHost,
        "night": night,
        "duration": duration,
        "check_in": checkIn,
        "flexible_check_time": flexibleCheckTime,
        "accommodation_type": accommodationType,
        "check_out": checkOut,
        "flexible_check_out": flexibleCheckOut,
        "booking_charge": bookingCharge,
        "additional_guest_fee": additionalGuestFee,
        "cleaning_fee": cleaningFee,
        "security_fee": securityFee,
        "service_fee": serviceFee,
        "total": total,
        "payment_received": paymentReceived,
      };
}

class Upload {
  Upload({
    required this.dropOrClickToUpload,
  });

  String dropOrClickToUpload;

  factory Upload.fromJson(Map<String, dynamic> json) => Upload(
        dropOrClickToUpload: json["drop_or_click_to_upload"],
      );

  Map<String, dynamic> toJson() => {
        "drop_or_click_to_upload": dropOrClickToUpload,
      };
}

class UsersDashboard {
  UsersDashboard({
    required this.addProfilePhoto,
    required this.viewProfile,
    required this.completeProfile,
    required this.welcomeTo,
    required this.message,
    required this.usersDashboardNew,
    required this.allMessages,
    required this.emailAddress,
    required this.unread,
    required this.acceptedRequests,
    required this.pendingRequests,
    required this.cancelledRequests,
    required this.declinedRequests,
    required this.expiredRequests,
    required this.latestBookings,
    required this.latestTransactions,
    required this.myLists,
    required this.myTrips,
    required this.bookingRequest,
    required this.myWallet,
    required this.sortBy,
    required this.about,
    required this.identityVerified,
    required this.identityUnverified,
    required this.confirmed,
    required this.favourite,
    required this.booking,
    required this.bookingHistory,
    required this.emptyWishlistTitle,
    required this.emptyWishlistNote,
    required this.privacySharing,
  });

  String addProfilePhoto;
  String viewProfile;
  String completeProfile;
  String welcomeTo;
  String message;
  String usersDashboardNew;
  String allMessages;
  String emailAddress;
  String unread;
  String acceptedRequests;
  String pendingRequests;
  String cancelledRequests;
  String declinedRequests;
  String expiredRequests;
  String latestBookings;
  String latestTransactions;
  String myLists;
  String myTrips;
  String bookingRequest;
  String myWallet;
  String sortBy;
  String about;
  String identityVerified;
  String identityUnverified;
  String confirmed;
  String favourite;
  String booking;
  String bookingHistory;
  String emptyWishlistTitle;
  String emptyWishlistNote;
  String privacySharing;

  factory UsersDashboard.fromJson(Map<String, dynamic> json) {
    return UsersDashboard(
      addProfilePhoto: json["add_profile_photo"],
      viewProfile: json["view_profile"],
      completeProfile: json["complete_profile"],
      welcomeTo: json["welcome_to"],
      message: json["message"],
      usersDashboardNew: json["new"],
      allMessages: json["all_messages"],
      emailAddress: json["email_address"],
      unread: json["unread"],
      acceptedRequests: json["accepted_requests"],
      pendingRequests: json["pending_requests"],
      cancelledRequests: json["cancelled_requests"],
      declinedRequests: json["declined_requests"],
      expiredRequests: json["expired_requests"],
      latestBookings: json["latest_bookings"],
      latestTransactions: json["latest_transactions"],
      myLists: json["my_lists"],
      myTrips: json["my_trips"],
      bookingRequest: json["booking_request"],
      myWallet: json["my_wallet"],
      sortBy: json["sort_by"],
      about: json["about"],
      identityVerified: json["identity_verified"],
      identityUnverified: json["identity_unverified"],
      confirmed: json["confirmed"],
      favourite: json["favourite"],
      booking: json["booking"],
      bookingHistory: json["booking_history"],
      emptyWishlistTitle: json["empty_wishlist_title"],
      emptyWishlistNote: json["empty_wishlist_note"],
      privacySharing: json["privacy_sharing"] ?? "Privacy & Sharing",
    );
  }

  Map<String, dynamic> toJson() => {
        "add_profile_photo": addProfilePhoto,
        "view_profile": viewProfile,
        "complete_profile": completeProfile,
        "welcome_to": welcomeTo,
        "message": message,
        "new": usersDashboardNew,
        "all_messages": allMessages,
        "email_address": emailAddress,
        "unread": unread,
        "accepted_requests": acceptedRequests,
        "pending_requests": pendingRequests,
        "cancelled_requests": cancelledRequests,
        "declined_requests": declinedRequests,
        "expired_requests": expiredRequests,
        "latest_bookings": latestBookings,
        "latest_transactions": latestTransactions,
        "my_lists": myLists,
        "my_trips": myTrips,
        "booking_request": bookingRequest,
        "my_wallet": myWallet,
        "sort_by": sortBy,
        "about": about,
        "identity_verified": identityVerified,
        "identity_unverified": identityUnverified,
        "confirmed": confirmed,
        "favourite": favourite,
        "booking": booking,
        "booking_history": bookingHistory,
      };
}

class UsersMedia {
  UsersMedia({
    required this.profilePhoto,
    required this.photoData,
    required this.fileUpload,
    required this.uploaded,
  });

  String profilePhoto;
  String photoData;
  String fileUpload;
  String uploaded;

  factory UsersMedia.fromJson(Map<String, dynamic> json) => UsersMedia(
        profilePhoto: json["profile_photo"],
        photoData: json["photo_data"],
        fileUpload: json["file_upload"],
        uploaded: json["uploaded"],
      );

  Map<String, dynamic> toJson() => {
        "profile_photo": profilePhoto,
        "photo_data": photoData,
        "file_upload": fileUpload,
        "uploaded": uploaded,
      };
}

class UsersProfile {
  UsersProfile(
      {required this.userProfile,
      required this.firstName,
      required this.lastName,
      required this.iAm,
      required this.gender,
      required this.male,
      required this.female,
      required this.other,
      required this.birthDate,
      required this.emailAddress,
      required this.whereLive,
      required this.describe,
      required this.save,
      required this.phone,
      required this.changePhoto,
      required this.personalInfo,
      required this.loginSecurity,
      required this.aboutMe,
      required this.verifiedEmail,
      required this.unVerifiedEmail,
      required this.verifiedPhoneNumber,
      required this.documentVerification,
      required this.pleaseUploadPhonePassport,
      required this.enterYourPass,
      required this.clickToCopy,
      required this.enterYourIqama,
      required this.profile,
      required this.verified,
      required this.ninIqama,
      required this.genderNotSet,
      required this.gregorian,
      required this.hijri,
      required this.yourPrefMethod,
      required this.selectAtleast1,
      required this.prefMethodUpdate,
      required this.prefsTitle,
      required this.selectDob,
      required this.fillCode,
      required this.fillCompCode,
      required this.nationalitySelect,
      required this.nationalityOption1,
      required this.nationalityOption2,
      required this.nationalityOption3,
      required this.nationalityOption4,
      required this.activatePropertyLicense,
      required this.activatePropertyLicenseNote,
      required this.issuePropertyLicense,
      required this.infoAboutActivateSteps,
      required this.issuePropertyLicenseNote,
      required this.activatePropertyLicenseNotee,
      required this.infoAboutActivateStepsNote});

  String userProfile;
  String firstName;
  String lastName;
  String iAm;
  String gender;
  String male;
  String female;
  String other;
  String birthDate;
  String emailAddress;
  String whereLive;
  String describe;
  String save;
  String phone;
  String changePhoto;
  String personalInfo;
  String loginSecurity;
  String aboutMe;
  String verifiedEmail;
  String unVerifiedEmail;
  String verifiedPhoneNumber;
  String documentVerification;
  String pleaseUploadPhonePassport;
  String clickToCopy;
  String enterYourIqama;
  String? enterYourPass;
  String profile;
  String verified;
  String ninIqama;
  String genderNotSet;
  String gregorian;
  String hijri;
  String? yourPrefMethod;
  String? selectAtleast1;
  String? prefMethodUpdate;
  String? prefsTitle;
  String? selectDob;
  String? fillCode;
  String? fillCompCode;
  String nationalitySelect;
  String nationalityOption1;
  String nationalityOption2;
  String nationalityOption3;
  String nationalityOption4;
  String activatePropertyLicense;
  String activatePropertyLicenseNote;
  String issuePropertyLicense;
  String infoAboutActivateSteps;
  String issuePropertyLicenseNote;
  String activatePropertyLicenseNotee;
  String infoAboutActivateStepsNote;

  factory UsersProfile.fromJson(Map<String, dynamic> json) => UsersProfile(
      userProfile: json["user_profile"],
      firstName: json["first_name"],
      lastName: json["last_name"],
      iAm: json["i_am"],
      gender: json["gender"],
      male: json["male"],
      female: json["female"],
      other: json["other"],
      birthDate: json["birth_date"],
      emailAddress: json["email_address"],
      whereLive: json["where_live"],
      describe: json["describe"],
      save: json["save"],
      phone: json["phone"],
      changePhoto: json["change_photo"],
      personalInfo: json["personal_info"],
      loginSecurity: json["login_security"],
      aboutMe: json["about_me"],
      verifiedEmail: json["verified_email"],
      unVerifiedEmail: json["unverified_email"],
      verifiedPhoneNumber: json["verified_phone_number"],
      documentVerification: json["document_verification"],
      pleaseUploadPhonePassport: json["please_upload_phone_passport"],
      clickToCopy: json['click_to_copy'],
      enterYourIqama: json['enter_your_iqama'],
      profile: json['profile'],
      verified: json['verified'],
      ninIqama: json['nin_iqama'],
      genderNotSet: json['gender_not_set'],
      gregorian: json['gregorian'] ?? 'Gregorian',
      hijri: json['hijri'] ?? 'Hijri',
      yourPrefMethod: json["your_pref_method"],
      selectAtleast1: json["select_atleast1"],
      prefMethodUpdate: json["pref_method_update"],
      prefsTitle: json["pref_title"] ?? "Preferred Communication",
      selectDob: json["select_birth_date"] ?? "Please Select Date of Birth",
      fillCode: json["fill_code"] ?? "Please fill code",
      fillCompCode: json["fill_com_code"] ?? "Please enter complete code",
      nationalitySelect:
          json["nationality_select"] ?? "Select Your Nationality Type",
      nationalityOption1:
          json["nationality_option1"] ?? "Saudi Nationality Holder",
      nationalityOption2:
          json["nationality_option2"] ?? "Saudi Non Nationality Holder",
      nationalityOption3: json["nationality_option3"] ?? "Visitor//Tourist",
      nationalityOption4: json["nationality_option3"] ?? '',
      enterYourPass: json['enter_pass'] ?? "Enter Passport Number",
      activatePropertyLicense: json['activate_property_license'] ?? '',
      activatePropertyLicenseNote: json['activate_property_license_note'] ?? '',
      issuePropertyLicense: json['issue_property_license'],
      infoAboutActivateSteps: json['info_about_activate_steps'],
      issuePropertyLicenseNote: json['issue_property_license_note'],
      activatePropertyLicenseNotee: json['activate_property_license_notee'],
      infoAboutActivateStepsNote: json['info_about_activate_steps_note'] ?? '');
}

class UsersShow {
  UsersShow({
    required this.aboutMe,
    required this.school,
    required this.work,
    required this.hey,
    required this.memberSince,
    required this.review,
    required this.reviewHost,
    required this.reviewGuest,
  });

  String aboutMe;
  String school;
  String work;
  String hey;
  String memberSince;
  String review;
  String reviewHost;
  String reviewGuest;

  factory UsersShow.fromJson(Map<String, dynamic> json) => UsersShow(
        aboutMe: json["about_me"],
        school: json["school"],
        work: json["work"],
        hey: json["hey"],
        memberSince: json["member_since"],
        review: json["review"],
        reviewHost: json["review_host"],
        reviewGuest: json["review_guest"],
      );

  Map<String, dynamic> toJson() => {
        "about_me": aboutMe,
        "school": school,
        "work": work,
        "hey": hey,
        "member_since": memberSince,
        "review": review,
        "review_host": reviewHost,
        "review_guest": reviewGuest,
      };
}

class Utility {
  Utility({
    required this.loadMore,
    required this.uploadPhoto,
    required this.delete,
    required this.justMadeAComment,
    required this.reportThisPhoto,
    required this.reportMessage,
    required this.location,
    required this.moreDetails,
    required this.buy,
    required this.download,
    required this.photo,
    required this.photos,
    required this.dateOfPurchase,
    required this.paymentMethod,
    required this.paymentGateway,
    required this.price,
    required this.total,
    required this.withdraw,
    required this.profile,
    required this.account,
    required this.payments,
    required this.viewProfile,
    required this.saveChanges,
    required this.paypalEmail,
    required this.submit,
    required this.close,
    required this.next,
    required this.views,
    required this.follow,
    required this.following,
    required this.noPhotosFound,
    required this.noUsersFound,
    required this.seeAll,
    required this.signInRequired,
    required this.please,
    required this.signIn,
    required this.beforePerforming,
    required this.rememberMe,
  });

  String loadMore;
  String uploadPhoto;
  String delete;
  String justMadeAComment;
  String reportThisPhoto;
  String reportMessage;
  String location;
  String moreDetails;
  String buy;
  String download;
  String photo;
  String photos;
  String dateOfPurchase;
  String paymentMethod;
  String paymentGateway;
  String price;
  String total;
  String withdraw;
  String profile;
  String account;
  String payments;
  String viewProfile;
  String saveChanges;
  String paypalEmail;
  String submit;
  String close;
  String next;
  String views;
  String follow;
  String following;
  String noPhotosFound;
  String noUsersFound;
  String seeAll;
  String signInRequired;
  String please;
  String signIn;
  String beforePerforming;
  String rememberMe;

  factory Utility.fromJson(Map<String, dynamic> json) => Utility(
        loadMore: json["load_more"],
        uploadPhoto: json["upload_photo"],
        delete: json["delete"],
        justMadeAComment: json["just_made_a_comment"],
        reportThisPhoto: json["report_this_photo"],
        reportMessage: json["report_message"],
        location: json["location"],
        moreDetails: json["more_details"],
        buy: json["buy"],
        download: json["download"],
        photo: json["photo"],
        photos: json["photos"],
        dateOfPurchase: json["date_of_purchase"],
        paymentMethod: json["payment_method"],
        paymentGateway: json["payment_gateway"],
        price: json["price"],
        total: json["total"],
        withdraw: json["withdraw"],
        profile: json["profile"],
        account: json["account"],
        payments: json["payments"],
        viewProfile: json["view_profile"],
        saveChanges: json["save_changes"],
        paypalEmail: json["paypal_email"],
        submit: json["submit"],
        close: json["close"],
        next: json["next"],
        views: json["views"],
        follow: json["follow"],
        following: json["following"],
        noPhotosFound: json["no_photos_found"],
        noUsersFound: json["no_users_found"],
        seeAll: json["see_all"],
        signInRequired: json["sign_in_required"],
        please: json["please"],
        signIn: json["sign_in"],
        beforePerforming: json["before_performing"],
        rememberMe: json["remember_me"],
      );

  Map<String, dynamic> toJson() => {
        "load_more": loadMore,
        "upload_photo": uploadPhoto,
        "delete": delete,
        "just_made_a_comment": justMadeAComment,
        "report_this_photo": reportThisPhoto,
        "report_message": reportMessage,
        "location": location,
        "more_details": moreDetails,
        "buy": buy,
        "download": download,
        "photo": photo,
        "photos": photos,
        "date_of_purchase": dateOfPurchase,
        "payment_method": paymentMethod,
        "payment_gateway": paymentGateway,
        "price": price,
        "total": total,
        "withdraw": withdraw,
        "profile": profile,
        "account": account,
        "payments": payments,
        "view_profile": viewProfile,
        "save_changes": saveChanges,
        "paypal_email": paypalEmail,
        "submit": submit,
        "close": close,
        "next": next,
        "views": views,
        "follow": follow,
        "following": following,
        "no_photos_found": noPhotosFound,
        "no_users_found": noUsersFound,
        "see_all": seeAll,
        "sign_in_required": signInRequired,
        "please": please,
        "sign_in": signIn,
        "before_performing": beforePerforming,
        "remember_me": rememberMe,
      };
}

class Wishlist {
  Wishlist({
    required this.wishlist,
    required this.createNewWishlist,
    required this.nameThisWishlist,
    required this.characterMaximum,
    required this.yourWishlists,
    required this.create,
  });

  String wishlist;
  String createNewWishlist;
  String nameThisWishlist;
  String characterMaximum;
  String yourWishlists;
  String create;

  factory Wishlist.fromJson(Map<String, dynamic> json) => Wishlist(
        wishlist: json["wishlist"],
        nameThisWishlist: json["name_this_wishlist"],
        createNewWishlist: json["create_new_wishlist"],
        characterMaximum: json["character_maximum"],
        yourWishlists: json["your_wishlists"],
        create: json["create"],
      );
}

class Withdraw {
  Withdraw({
    required this.dateOfRequest,
    required this.status,
    required this.amount,
    required this.withdrawBalance,
    required this.doWithdrawRequest,
    required this.yourBalanceIs,
    required this.withdrawAmount,
    required this.pleaseProvide,
    required this.paymentAccount,
    required this.informationWithdraw,
    required this.overAllRating,
    required this.guestFavourite,
    required this.guestFavouriteIntro,
    required this.showMore,
    required this.loading,
  });

  String dateOfRequest;
  String status;
  String amount;
  String withdrawBalance;
  String doWithdrawRequest;
  String yourBalanceIs;
  String withdrawAmount;
  String pleaseProvide;
  String paymentAccount;
  String informationWithdraw;
  String overAllRating;
  String guestFavourite;
  String guestFavouriteIntro;
  String showMore;
  String loading;

  factory Withdraw.fromJson(Map<String, dynamic> json) => Withdraw(
        dateOfRequest: json["date_of_request"],
        status: json["status"],
        amount: json["amount"],
        withdrawBalance: json["withdraw_balance"],
        doWithdrawRequest: json["do_withdraw_request"],
        yourBalanceIs: json["your_balance_is"],
        withdrawAmount: json["withdraw_amount"],
        pleaseProvide: json["please_provide"],
        paymentAccount: json["payment_account"],
        informationWithdraw: json["information_withdraw"],
        overAllRating: json['overall_rating'] ?? "Overall Rating",
        guestFavourite: json['guest_favourite'] ?? "Guest Favourite",
        guestFavouriteIntro: json['guest_favourite_intro'] ??
            "One of the most loved homes on Darent based on rating, review and reliability",
        showMore: json['show_more'] ?? "Show More",
        loading: json['loading'] ?? "Loading....",
      );
}

class ScoringPointsExplain {
  String? cleanlinessQuery;
  String? communicationQuery;
  String? accuracyQuery;
  String? locationQuery;
  String? reviewQuery;
  String? responseRateQuery;
  String? cancelationRateQuery;
  String? acceptanceRateQuery;
  String? instantBookingAdoptionRateQuery;
  String? decisionTimeQuery;
  String? timelyResponseQuery;
  String? noOofPhotoQuery;
  String? amenitiesOfferedQuery;
  String? discountAppliedQuery;

  String? responseRate;
  String? cancelationRate;
  String? acceptanceRate;
  String? instantBookingAdoptionRate;
  String? decisionTime;
  String? timelyResponse;
  String? guestReview;
  String? noOfPhoto;
  String? amenitiesOffered;
  String? discountApplied;
  String? pointHeading;

  ScoringPointsExplain(
      {this.cleanlinessQuery,
      this.communicationQuery,
      this.accuracyQuery,
      this.locationQuery,
      this.reviewQuery,
      this.responseRateQuery,
      this.cancelationRateQuery,
      this.acceptanceRateQuery,
      this.instantBookingAdoptionRateQuery,
      this.decisionTimeQuery,
      this.timelyResponseQuery,
      this.noOofPhotoQuery,
      this.amenitiesOfferedQuery,
      this.discountAppliedQuery,
      this.responseRate,
      this.cancelationRate,
      this.acceptanceRate,
      this.instantBookingAdoptionRate,
      this.decisionTime,
      this.timelyResponse,
      this.guestReview,
      this.noOfPhoto,
      this.amenitiesOffered,
      this.discountApplied,
      this.pointHeading});

  factory ScoringPointsExplain.fromJson(Map<String, dynamic> json) =>
      ScoringPointsExplain(
        cleanlinessQuery: json["cleanliness_query"] ??
            "How are evaluation points calculated?",
        communicationQuery: json["communication_query"] ??
            "How are evaluation points calculated?",
        accuracyQuery:
            json["accuracy_query"] ?? "How are evaluation points calculated?",
        locationQuery:
            json["location_query"] ?? "How are evaluation points calculated?",
        reviewQuery:
            json["review_query"] ?? "How are evaluation points calculated?",
        responseRateQuery: json["response_rate_query"] ??
            "How are Response Rate points calculated?",
        cancelationRateQuery: json["cancelation_rate_query"] ??
            "How are Cancellation Rate points calculated?",
        acceptanceRateQuery: json["acceptance_rate_query"] ??
            "How are Booking Acceptance Rate points calculated?",
        instantBookingAdoptionRateQuery:
            json["instant_booking_adoption_rate_query"] ??
                "How are Instant Booking Adoption Rate points calculated?",
        decisionTimeQuery: json["decision_time_query"] ??
            "How are Response Time to Chat points calculated?",
        timelyResponseQuery: json["timely_response_query"] ??
            "How are Booking Request Decision Time points calculated?",
        noOofPhotoQuery: json["no_of_photo_query"] ??
            "How are No. of Photos points calculated?",
        amenitiesOfferedQuery: json["amenities_offered_query"] ??
            "How are Amenities Offered points calculated?",
        discountAppliedQuery: json["discount_applied_query"] ??
            "How are Discount Applied points calculated?",
        responseRate: json["response_rate"] ??
            "Measured by the Percentage of inquiries responded to within 24 hours.",
        cancelationRate: json["response_rate"] ??
            "Measured by the Percentage of inquiries responded to within 24 hours.",
        acceptanceRate: json["acceptance_rate"] ??
            "The percentage of booking requests that the host accepts.",
        instantBookingAdoptionRate: json["instant_booking_adoption_rate"] ??
            "The percentage of a host’s listings that are available for instant booking.",
        decisionTime: json["decision_time"] ??
            "Measures the speed at which the host responds to the first message.",
        timelyResponse: json["timely_response"] ??
            "Assesses the host's efficiency in accepting or rejecting 'per request' booking inquiries.",
        guestReview: json["guest_review"] ??
            "Based on guest reviews on your property Darent is assigning points out of 7",
        noOfPhoto: json["no_of_photo"] ??
            "The quantity of photos influence guest decisions.",
        amenitiesOffered: json["amenities_offered"] ??
            "Amenities can significantly enhance guest comfort and satisfaction.",
        discountApplied: json["discount_applied"] ??
            "Offering discounts can make a listing more appealing and competitive.",
        pointHeading: json["point_heading"] ?? "\$heading points calculation",
      );
}

class HostDashboard {
  String? commonMessage;
  String? syncCalendar;
  String? accountInfo;
  String? financialTransaction;
  String? hostSuggestion;
  String? hostReports;
  String? units;
  String? sales;
  String? doNotHaveBooking;
  String? evaluation;
  String? code;
  String? welcomeAgain;
  String? yourReservation;
  String? allReservation;
  String? guestCheckOut;
  String? currentlyHosting;
  String? guestArrivingSoon;
  String? pendingReview;
  String? upcoming;
  String? completeTheProfile;
  String? showExternalPlugin;
  String? guestLoveToEnjoye;
  String? addDetails;
  String? designatedWorkspaceLocatedInSharedResidence;
  String? yes;
  String? no;
  String? today;
  String? messages;
  String? calendar;
  String? list;
  String? createANewListing;
  String? reservation;
  String? listing;
  String? listings;
  String? singleDateSelected;
  String? customPrice;
  String? availability;
  String? searchListing;
  String? searchBookingCode;
  String? baths;
  String? youHaveNoListings;
  String? pending;
  String? coming;
  String? ongoing;
  String? completed;
  String? canceled;
  String? all;
  String? previousGuest;
  String? reservationDetail;
  String? guestSar;
  String? about;
  String? averageRatingOutOf;
  String? review;
  String? dateOfJoiningDarent;
  String? livesIn;
  String? message;
  String? thePhone;
  String? guests;
  String? child;
  String? accessTime;
  String? access;
  String? departure;
  String? confirmaionCode;
  String? sortBy;
  String? lastModified;
  String? listingDetails;
  String? pricingAndAvailibility;
  String? policiesAndRules;
  String? infoForGuests;
  String? photosMore;
  String? edit;
  String? photos;
  String? listingBasics;
  String? cancel;
  String? change;
  String? listingDescription;
  String? numberOfGuests;
  String? adult;
  String? moreThan13Year;
  String? children;
  String? lessThan13Year;
  String? listingStatus;
  String? listingStatusSnoozed;
  String? guestCanFindYourListingInSearch;
  String? guestCanNotBookYourListing;
  String? roomsAndSpaces;
  String? allAreasAreJustForGuest;
  String? previewWhatGuestPay;
  String? pricing;
  String? total;
  String? sr;
  String? neW;
  String? show;
  String? nights;
  String? nightlyPrice;
  String? youAreResponsibleForChoosingTheListingPrice;
  String? smartPricingOn;
  String? minimumPrice;
  String? maximumPrice;
  String? listingCurrency;
  String? sar;
  String? usd;
  String? additionalCharges;
  String? cleaningFee;
  String? perStayIncludingShortStay;
  String? policies;
  String? cancellationPolicy;
  String? flexible;
  String? fullRefund1DayPriorToArrival;
  String? moderate;
  String? fullRefund5DayPriorToArrival;
  String? firm;
  String? fullRefundUpto30DayBeforeUpdate;
  String? strict;
  String? fullRefundForCancelWithin48Hours;
  String? instantBookGuestMeetAllRequirements;
  String? instantBookOn;
  String? instantBookOff;
  String? beforeBooking;
  String? anyoneCanSeeThisInfoOnListing;
  String? checkinWindow;
  String? checkoutTime;
  String? address;
  String? letsAddSomePhoto;
  String? changeCover;
  String? coverPhotos;
  String? uploadImages;
  String? allPhotos;
  String? globalPreferences;
  String? preferredLanguage;
  String? personalInfo;
  String? providePersonalDetail;
  String? loginSecurity;
  String? updateYourPasswordAndSecure;
  String? paymentAndPayouts;
  String? reviewPayoutCouponGift;
  String? setYourDefaultLanguage;
  String? addNumberSoConfirmedGuestAndAirbnb;
  String? governmentId;
  String? dateOfBirth;
  String? phoneNumber;
  String? yourPayments;
  String? keepTrackOfAllYourPayments;
  String? savedCards;
  String? addBankAccount;
  String? addCard;
  String? addBank;
  String? account;
  String? firstName;
  String? lastName;
  String? emailAddress;
  String? gender;
  String? login;
  String? adults;
  String? listed;
  String? unlisted;
  String? bedroom;
  String? bed;
  String? amenities;
  String? essential;
  String? theGuestPaidIt;
  String? hostsFinancialCollection;
  String? theTotalPriceOfTheStay;
  String? hostServiceFee;
  String? vat;
  String? vatIncluded;
  String? averageRatingOf;
  String? identityAchieved;
  String? vatInvoice;
  String? details;
  String? editPersonalInfo;
  String? markAsRead;
  String? pastGuest;
  String? identity;
  String? day;
  String? coverPhoto;
  String? noCover;
  String? createNewAd;
  String? menu;
  String? dwellings;
  String? manageAccount;
  String? hostSetting;
  String? notProvided;
  String? translation;
  String? translateNote;
  String? optional;
  String? howGuestsBook;
  String? howGuestsBookNote;
  String? privacySharing;
  String? monthOnDarent;
  String? confirmInfo;
  String? verifyIdentity;
  String? beforeYouBookOrHost;
  String? getVerified;
  String? timeToCreateProfile;
  String? timeToCreateProfileNote;
  String? createProfile;
  String? arrangement;
  String? newToOldCheckin;
  String? oldToNewCheckin;
  String? newToOldBooking;
  String? oldToNewBooking;
  String? clearAll;
  String? setHouseRules;
  String? setHouseRulesNote;
  String? listingLocation;
  String? discounts;
  String? referralLink;
  String? referralCopied;
  String? sorryCantSendMsg;
  String? linksCantShare;
  String? removeInfo;
  String? editMessage;
  String? cohostDeleteWarn;
  String? addCustomRule;
  String? customRule;
  String? add;
  String? changeEmailAdddress;
  String? performanceReport;
  String? yourUnitPoints;
  String? points;
  String? performanceReports;
  String? performanceReport1;
  String? performanceReport2;
  String? performanceReport3;
  String? performanceReport4;
  String? performanceReport5;
  String? performanceReportPositive;
  String? performanceReportNegative;

  String? numberOfViews;
  String? totalReservations;
  String? clickRateOnBooking;
  String? numberOfReviews;
  String? accuracy;
  String? communication;
  String? pointsThisWeek;
  String? changesFromThePrevious;
  String? nightsPoints;
  String? ratingsPoints;
  String? responseRateScore;
  String? responseSpeedPoints;
  String? weeklyNote;
  String? scoringPoints;
  String? scoringNote;
  String? reviewScore;
  String? hostScore;
  String? listingScore;
  String? writtenReviews;
  String? cancellationRate;
  String? acceptanceRate;
  String? instantBookingAdoptionRate;
  String? decisionTime;
  String? responseRate;
  String? timelyResponse;
  String? noOfPhotos;
  String? amenitiesOffered;
  String? discountApplied;
  String? specialDayPrice;
  String? normalPrice;
  String? weeklyMonthlyDiscount;
  String? bookingSettings;
  String? discountNote;
  String? selectUnit;
  String? selectUnitMessage;
  String? selectProperty;
  String? createDiscountSetting;
  String? weeklyDiscountSetting;
  String? emptyDiscountSetting;
  String? discountInfo;
  String? weekly;
  String? monthly;
  String? weeklyDiscountInstruction;
  String? submitAndShow;
  String? weeklyDiscountRecommendation;
  String? monthlyDiscountInstruction;
  String? monthlyDiscountRecommendation;
  String? priceBeforeAfter;
  String? before;
  String? after;
  String? changeStatusListed;
  String? changeStatusUnlisted;
  String? previewYourUnit;
  String? shareYourUnit;
  String? generateReferral;
  String? insurance;
  String? todayAllReview;
  String? allMergedReview;
  String? ratingFromGuest;
  String? ratingEvaluation;
  String? icalDeleteNote;
  String? allCalImports;
  String? guestName;
  String? reviewDate;
  String? propertyId;
  String? unitName;
  String? seeYourRating;
  String? hostRatingInfo;
  String? guestComment;
  String? noReviewAvailable;
  String? stepOutof;
  String? instantBookNote;
  String? communicationOption1;
  String? communicationOption2;
  String? locationOption1;
  String? locationOption2;
  String? finishYourListing;
  String? startNewListing;

  HostDashboard(
      {this.commonMessage,
      this.syncCalendar,
      this.guestName,
      this.reviewDate,
      this.propertyId,
      this.unitName,
      this.seeYourRating,
      this.hostRatingInfo,
      this.accountInfo,
      this.financialTransaction,
      this.hostSuggestion,
      this.hostReports,
      this.units,
      this.sales,
      this.doNotHaveBooking,
      this.evaluation,
      this.code,
      this.welcomeAgain,
      this.yourReservation,
      this.allReservation,
      this.guestCheckOut,
      this.currentlyHosting,
      this.guestArrivingSoon,
      this.pendingReview,
      this.upcoming,
      this.completeTheProfile,
      this.showExternalPlugin,
      this.guestLoveToEnjoye,
      this.addDetails,
      this.designatedWorkspaceLocatedInSharedResidence,
      this.yes,
      this.no,
      this.today,
      this.messages,
      this.calendar,
      this.list,
      this.createANewListing,
      this.reservation,
      this.listing,
      this.listings,
      this.singleDateSelected,
      this.customPrice,
      this.availability,
      this.searchListing,
      this.searchBookingCode,
      this.baths,
      this.youHaveNoListings,
      this.pending,
      this.coming,
      this.ongoing,
      this.completed,
      this.canceled,
      this.all,
      this.previousGuest,
      this.reservationDetail,
      this.guestSar,
      this.about,
      this.averageRatingOutOf,
      this.review,
      this.dateOfJoiningDarent,
      this.livesIn,
      this.message,
      this.thePhone,
      this.guests,
      this.child,
      this.accessTime,
      this.access,
      this.departure,
      this.confirmaionCode,
      this.sortBy,
      this.lastModified,
      this.listingDetails,
      this.pricingAndAvailibility,
      this.policiesAndRules,
      this.infoForGuests,
      this.photosMore,
      this.edit,
      this.photos,
      this.listingBasics,
      this.cancel,
      this.change,
      this.listingDescription,
      this.numberOfGuests,
      this.adult,
      this.moreThan13Year,
      this.children,
      this.lessThan13Year,
      this.listingStatus,
      this.listingStatusSnoozed,
      this.guestCanFindYourListingInSearch,
      this.guestCanNotBookYourListing,
      this.roomsAndSpaces,
      this.allAreasAreJustForGuest,
      this.previewWhatGuestPay,
      this.pricing,
      this.total,
      this.sr,
      this.neW,
      this.show,
      this.nights,
      this.nightlyPrice,
      this.youAreResponsibleForChoosingTheListingPrice,
      this.smartPricingOn,
      this.minimumPrice,
      this.maximumPrice,
      this.listingCurrency,
      this.sar,
      this.usd,
      this.additionalCharges,
      this.cleaningFee,
      this.perStayIncludingShortStay,
      this.policies,
      this.cancellationPolicy,
      this.flexible,
      this.fullRefund1DayPriorToArrival,
      this.moderate,
      this.fullRefund5DayPriorToArrival,
      this.firm,
      this.fullRefundUpto30DayBeforeUpdate,
      this.strict,
      this.fullRefundForCancelWithin48Hours,
      this.instantBookGuestMeetAllRequirements,
      this.instantBookOn,
      this.instantBookOff,
      this.beforeBooking,
      this.anyoneCanSeeThisInfoOnListing,
      this.checkinWindow,
      this.checkoutTime,
      this.address,
      this.letsAddSomePhoto,
      this.changeCover,
      this.coverPhotos,
      this.uploadImages,
      this.allPhotos,
      this.globalPreferences,
      this.preferredLanguage,
      this.personalInfo,
      this.providePersonalDetail,
      this.loginSecurity,
      this.updateYourPasswordAndSecure,
      this.paymentAndPayouts,
      this.reviewPayoutCouponGift,
      this.setYourDefaultLanguage,
      this.addNumberSoConfirmedGuestAndAirbnb,
      this.governmentId,
      this.dateOfBirth,
      this.phoneNumber,
      this.yourPayments,
      this.keepTrackOfAllYourPayments,
      this.savedCards,
      this.addBankAccount,
      this.addCard,
      this.addBank,
      this.account,
      this.firstName,
      this.lastName,
      this.emailAddress,
      this.gender,
      this.login,
      this.adults,
      this.listed,
      this.unlisted,
      this.bedroom,
      this.bed,
      this.amenities,
      this.essential,
      this.theGuestPaidIt,
      this.hostsFinancialCollection,
      this.theTotalPriceOfTheStay,
      this.hostServiceFee,
      this.vat,
      this.vatIncluded,
      this.averageRatingOf,
      this.identityAchieved,
      this.vatInvoice,
      this.details,
      this.editPersonalInfo,
      this.markAsRead,
      this.pastGuest,
      this.identity,
      this.day,
      this.coverPhoto,
      this.noCover,
      this.createNewAd,
      this.menu,
      this.dwellings,
      this.manageAccount,
      this.hostSetting,
      this.notProvided,
      this.translation,
      this.translateNote,
      this.optional,
      this.howGuestsBook,
      this.howGuestsBookNote,
      this.privacySharing,
      this.monthOnDarent,
      this.confirmInfo,
      this.verifyIdentity,
      this.beforeYouBookOrHost,
      this.getVerified,
      this.timeToCreateProfile,
      this.timeToCreateProfileNote,
      this.arrangement,
      this.newToOldCheckin,
      this.oldToNewCheckin,
      this.newToOldBooking,
      this.oldToNewBooking,
      this.clearAll,
      this.setHouseRules,
      this.setHouseRulesNote,
      this.listingLocation,
      this.discounts,
      this.referralLink,
      this.referralCopied,
      this.sorryCantSendMsg,
      this.linksCantShare,
      this.removeInfo,
      this.editMessage,
      this.cohostDeleteWarn,
      this.addCustomRule,
      this.customRule,
      this.add,
      this.changeEmailAdddress,
      this.performanceReport,
      this.yourUnitPoints,
      this.points,
      this.performanceReports,
      this.performanceReport1,
      this.performanceReport2,
      this.performanceReport3,
      this.performanceReport4,
      this.performanceReport5,
      this.performanceReportPositive,
      this.performanceReportNegative,
      this.numberOfViews,
      this.totalReservations,
      this.clickRateOnBooking,
      this.numberOfReviews,
      this.accuracy,
      this.communication,
      this.pointsThisWeek,
      this.changesFromThePrevious,
      this.nightsPoints,
      this.ratingsPoints,
      this.responseRateScore,
      this.responseSpeedPoints,
      this.weeklyNote,
      this.scoringPoints,
      this.scoringNote,
      this.reviewScore,
      this.hostScore,
      this.listingScore,
      this.writtenReviews,
      this.cancellationRate,
      this.acceptanceRate,
      this.instantBookingAdoptionRate,
      this.decisionTime,
      this.responseRate,
      this.timelyResponse,
      this.noOfPhotos,
      this.amenitiesOffered,
      this.discountApplied,
      this.specialDayPrice,
      this.normalPrice,
      this.weeklyMonthlyDiscount,
      this.bookingSettings,
      this.discountNote,
      this.selectUnit,
      this.selectUnitMessage,
      this.selectProperty,
      this.createDiscountSetting,
      this.weeklyDiscountSetting,
      this.emptyDiscountSetting,
      this.discountInfo,
      this.weekly,
      this.monthly,
      this.weeklyDiscountInstruction,
      this.submitAndShow,
      this.weeklyDiscountRecommendation,
      this.monthlyDiscountInstruction,
      this.monthlyDiscountRecommendation,
      this.priceBeforeAfter,
      this.before,
      this.after,
      this.changeStatusListed,
      this.changeStatusUnlisted,
      this.previewYourUnit,
      this.shareYourUnit,
      this.generateReferral,
      this.insurance,
      this.todayAllReview,
      this.allMergedReview,
      this.ratingFromGuest,
      this.ratingEvaluation,
      this.guestComment,
      this.noReviewAvailable,
      this.icalDeleteNote,
      this.allCalImports,
      this.stepOutof,
      this.instantBookNote,
      this.communicationOption1,
      this.communicationOption2,
      this.locationOption1,
      this.locationOption2,
      this.finishYourListing,
      this.startNewListing});

  HostDashboard.fromJson(Map<String, dynamic> json) {
    commonMessage =
        json['common_message'] ?? "Please list any of your property";
    syncCalendar = json['sync_calendar'] ?? "Sync Calendar";
    guestName = json["guest_name"] ?? "Guest's name";
    reviewDate = json["review_date"] ?? "Review date";
    propertyId = json["property_id"] ?? "Property ID";
    unitName = json["unit_name"] ?? "Unit name";
    seeYourRating = json["see_your_rating"] ?? "See Your Rating";
    hostRatingInfo =
        json["host_rating_info"] ?? "It will not be visible to the guest";
    guestComment = json["guest_comment"] ?? 'Guest comment';
    noReviewAvailable =
        json["no_review_available"] ?? "There is No Review available right now";

    accountInfo = json["account_info"] ?? "Account Information";
    financialTransaction =
        json["financial_transaction"] ?? "Financial transactions";
    hostSuggestion = json["host_suggestion"] ?? "Hosts' suggestions";
    hostReports = json["host_reports"] ?? "Reports and complaints";
    units = json["units"] ?? "Units";
    sales = json["sales"] ?? "Sales";
    doNotHaveBooking = json['do_not_have_booking'];
    evaluation = json['evaluation'];
    code = json['code'];
    welcomeAgain = json['welcome_again'];
    yourReservation = json['your_reservation'];
    allReservation = json['all_reservation'];
    guestCheckOut = json['guest_check_out'];
    currentlyHosting = json['currently_hosting'];
    guestArrivingSoon = json['guest_arriving_soon'];
    pendingReview = json['pending_review'];
    upcoming = json['upcoming'];
    completeTheProfile = json['complete_the_profile'];
    showExternalPlugin = json['show_external_plugin'];
    guestLoveToEnjoye = json['guest_love_to_enjoye'];
    addDetails = json['add_details'];
    designatedWorkspaceLocatedInSharedResidence =
        json['designated_workspace_located_in_shared_residence'];
    yes = json['yes'];
    no = json['no'];
    today = json['today'];
    messages = json['messages'];
    calendar = json['calendar'];
    list = json['list'];
    createANewListing = json['create_a_new_listing'];
    reservation = json['reservation'];
    listing = json['listing'];
    listings = json['listings'];
    singleDateSelected = json['single_date_selected'];
    customPrice = json['custom_price'];
    availability = json['availability'];
    searchListing = json['search_listing'];
    searchBookingCode =
        json['search_booking_code'] ?? "Search with booking code";
    baths = json['baths'];
    youHaveNoListings = json['you_have_no_listings'];
    pending = json['pending'];
    coming = json['coming'];
    ongoing = json['ongoing'];
    completed = json['completed'];
    canceled = json['canceled'];
    all = json['all'];
    previousGuest = json['previous_guest'];
    reservationDetail = json['reservation_detail'];
    guestSar = json['guest_sar'];
    about = json['about'];
    averageRatingOutOf = json['average_rating_out_of'];
    review = json['review'];
    dateOfJoiningDarent = json['date_of_joining_darent'];
    livesIn = json['lives_in'];
    message = json['message'];
    thePhone = json['the_phone'];
    guests = json['guests'];
    child = json['child'];
    accessTime = json['access_time'];
    access = json['access'];
    departure = json['departure'];
    confirmaionCode = json['confirmaion_code'];
    sortBy = json['sort_by'];
    lastModified = json['last_modified'];
    listingDetails = json['listing_details'];
    pricingAndAvailibility = json['pricing_and_availibility'];
    policiesAndRules = json['policies_and_rules'];
    infoForGuests = json['info_for_guests'];
    photosMore = json['photos_more'];
    edit = json['edit'];
    photos = json['photos'];
    listingBasics = json['listing_basics'];
    cancel = json['cancel'];
    change = json['change'];
    listingDescription = json['listing_description'];
    numberOfGuests = json['number_of_guests'];
    adult = json['adult'];
    moreThan13Year = json['more_than_13_year'];
    children = json['children'];
    lessThan13Year = json['less_than_13_year'];
    listingStatus = json['listing_status'];
    listingStatusSnoozed = json['listing_status_snoozed'];
    guestCanFindYourListingInSearch =
        json['guest_can_find_your_listing_in_search'];
    guestCanNotBookYourListing = json['guest_can_not_book_your_listing'];
    roomsAndSpaces = json['rooms_and_spaces'];
    allAreasAreJustForGuest = json['all_areas_are_just_for_guest'];
    previewWhatGuestPay = json['preview_what_guest_pay'];
    pricing = json['pricing'];
    total = json['total'];
    sr = json['sr'];
    neW = json['new'];
    show = json['show'];
    nights = json['nights'];
    nightlyPrice = json['nightly_price'];
    youAreResponsibleForChoosingTheListingPrice =
        json['you_are_responsible_for_choosing_the_listing_price'];
    smartPricingOn = json['smart_pricing_on'];
    minimumPrice = json['minimum_price'];
    maximumPrice = json['maximum_price'];
    listingCurrency = json['listing_currency'];
    sar = json['sar'];
    usd = json['usd'];
    additionalCharges = json['additional_charges'];
    cleaningFee = json['cleaning_fee'];
    perStayIncludingShortStay = json['per_stay_including_short_stay'];
    policies = json['policies'];
    cancellationPolicy = json['cancellation_policy'];
    flexible = json['flexible'];
    fullRefund1DayPriorToArrival = json['full_refund_1_day_prior_to_arrival'];
    moderate = json['moderate'];
    fullRefund5DayPriorToArrival = json['full_refund_5_day_prior_to_arrival'];
    firm = json['firm'];
    fullRefundUpto30DayBeforeUpdate =
        json['full_refund_upto_30_day_before_update'];
    strict = json['strict'];
    fullRefundForCancelWithin48Hours =
        json['full_refund_for_cancel_within_48_hours'];
    instantBookGuestMeetAllRequirements =
        json['instant_book_guest_meet_all_requirements'];
    instantBookOn = json['instant_book_on'];
    instantBookOff = json['instant_book_off'];
    beforeBooking = json['before_booking'];
    anyoneCanSeeThisInfoOnListing = json['anyone_can_see_this_info_on_listing'];
    checkinWindow = json['checkin_window'];
    checkoutTime = json['checkout_time'];
    address = json['address'];
    letsAddSomePhoto = json['lets_add_some_photo'];
    changeCover = json['change_cover'];
    coverPhotos = json['cover_photos'];
    uploadImages = json['upload_images'];
    allPhotos = json['all_photos'];
    globalPreferences = json['global_preferences'];
    preferredLanguage = json['preferred_language'];
    personalInfo = json['personal_info'];
    providePersonalDetail = json['provide_personal_detail'];
    loginSecurity = json['login_security'];
    updateYourPasswordAndSecure = json['update_your_password_and_secure'];
    paymentAndPayouts = json['payment_and_payouts'];
    reviewPayoutCouponGift = json['review_payout_coupon_gift'];
    setYourDefaultLanguage = json['set_your_default_language'];
    addNumberSoConfirmedGuestAndAirbnb =
        json['add_number_so_confirmed_guest_and_airbnb'];
    governmentId = json['government_id'];
    dateOfBirth = json['date_of_birth'];
    phoneNumber = json['phone_number'];
    yourPayments = json['your_payments'];
    keepTrackOfAllYourPayments = json['keep_track_of_all_your_payments'];
    savedCards = json['saved_cards'];
    addBankAccount = json['add_bank_account'];
    addCard = json['add_card'];
    addBank = json['add_bank'];
    account = json['account'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    emailAddress = json['email_address'];
    gender = json['gender'];
    login = json['login'];
    adults = json['adults'];
    listed = json['listed'];
    unlisted = json['unlisted'];
    bedroom = json['bedroom'];
    bed = json['bed'];
    amenities = json['amenities'];
    essential = json['essential'];
    theGuestPaidIt = json['the_guest_paid_it'];
    hostsFinancialCollection = json['hosts_financial_collection'];
    theTotalPriceOfTheStay = json['the_total_price_of_the_stay'];
    hostServiceFee = json['host_service_fee'];
    vat = json['vat'];
    vatIncluded = json['vat_included'];
    averageRatingOf = json['average_rating_of'];
    identityAchieved = json['identity_achieved'];
    vatInvoice = json['vat_invoice'];
    details = json['details'];
    editPersonalInfo = json['edit_personal_info'];
    markAsRead = json['mark_as_read'];
    pastGuest = json['past_guest'];
    identity = json['identity'];
    day = json['day'];
    coverPhoto = json['cover_photo'];
    noCover = json['no_cover'];
    createNewAd = json['create_new_ad'];
    menu = json['menu'];
    dwellings = json['dwellings'];
    manageAccount = json['manage_account'];
    hostSetting = json['host_setting'];
    notProvided = json['not_provided'];
    translation = json['translation'];
    translateNote = json['translate_note'];
    optional = json['optional'];
    howGuestsBook = json['how_guests_book'];
    howGuestsBookNote = json['how_guests_book_note'];
    privacySharing = json['privacy_sharing'];
    monthOnDarent = json['month_on_darent'];
    confirmInfo = json['confirm_info'] ?? "Confirm Information";
    verifyIdentity = json['verify_identity'] ?? 'Verify your Identity';
    beforeYouBookOrHost = json['before_you_book_or_host'] ??
        'Before you book or host on Darent you will need to complete this step';
    getVerified = json['get_verified'] ?? 'Get Verified';
    timeToCreateProfile = json['time_to_create_profile'];
    timeToCreateProfileNote = json['time_to_create_profile_note'];
    createProfile = json['create_profile'];
    arrangement = json['arrangement'] ?? 'Arrangement';
    newToOldCheckin =
        json['new_to_old_checkin'] ?? "Check- in date from newest to oldest";
    oldToNewCheckin = json['old_to_new_checkin'] ??
        "Check -in date from the oldest to the latest";
    newToOldBooking =
        json['new_to_old_booking'] ?? "Booking date from newest to oldest";
    oldToNewBooking = json['old_to_new_booking'] ??
        "Booking date from the oldest to the latest";
    clearAll = json['clear_all'] ?? "Clear All";
    setHouseRules = json['set_house_rules'] ?? "Set your house rules";
    setHouseRulesNote = json['set_house_rules_note'] ??
        "Guest are supposed to follow your rules. and can causing be removed from Darent just in case causing them problem";
    listingLocation = json["listing_location"] ??
        "Exact location information is provided after a booking is confirm";
    discounts = json["discounts"] ?? "Discounts";
    referralLink = json["referral_link"] ?? "Referral link";
    referralCopied = json["referral_copied"] ?? "Referral link has been copied";
    sorryCantSendMsg = json["sorry_cant_send_msg"];
    linksCantShare = json["links_cant_share"];
    removeInfo = json["remove_info"];
    editMessage = json["edit_message"];
    cohostDeleteWarn = json["cohost_delete_warn"];
    addCustomRule = json['add_custom_rules'];
    customRule = json['custom_rules'];
    add = json['add'];
    changeEmailAdddress = json["change_email"] ?? "Change Email address";
    performanceReport = json["performance_report"] ?? "Performance Report";
    yourUnitPoints = json["your_unit_points"] ?? "Your unit points";
    points = json["points"] ?? "Points";
    performanceReports = json["performance_reports"];
    performanceReport1 = json["performance_report1"];
    performanceReport2 = json["performance_report2"];
    performanceReport3 = json["performance_report3"];
    performanceReport4 = json["performance_report4"];
    performanceReport5 = json["performance_report5"];
    performanceReportPositive = json["performance_report_positive"];
    performanceReportNegative = json["performance_report_negative"];

    numberOfViews = json["number_of_views"];
    totalReservations = json["total_reservations"];
    clickRateOnBooking = json["click_rate_on_booking"];
    numberOfReviews = json["number_of_reviews"];
    accuracy = json["accuracy"];
    communication = json["communication"];
    pointsThisWeek = json["points_this_week"];
    changesFromThePrevious = json["changes_from_the_previous"];
    nightsPoints = json["nights_points"];
    ratingsPoints = json["ratings_points"];
    responseRateScore = json["response_rate_score"];
    responseSpeedPoints = json["response_speed_points"];
    weeklyNote = json["weekly_note"];
    scoringPoints = json["scoring_points"] ?? 'Scoring Points';
    scoringNote = json["scoring_note"] ?? 'Scoring Note';

    reviewScore = json["review_score"] ?? 'Review Score';
    hostScore = json["host_score"] ?? 'Host Score';
    listingScore = json["listing_score"] ?? 'Listing Score';

    writtenReviews = json["writtenReviews"] ?? 'writtenReviews';
    cancellationRate = json["cancellationRate"] ?? 'cancellationRate';
    acceptanceRate = json["acceptanceRate"] ?? 'acceptanceRate';
    instantBookingAdoptionRate =
        json["instantBookingAdoptionRate"] ?? 'instantBookingAdoptionRate';
    decisionTime = json["decisionTime"] ?? 'decisionTime';
    responseRate = json["responseRate"] ?? 'responseRate';
    timelyResponse = json["timelyResponse"] ?? 'timelyResponse';
    noOfPhotos = json["noOfPhotos"] ?? 'noOfPhotos';
    amenitiesOffered = json["amenitiesOffered"] ?? 'amenitiesOffered';
    discountApplied = json["discountApplied"] ?? 'Discount Applied';
    specialDayPrice = json['special_day_price'] ?? "Special Day Prices  ";
    normalPrice = json['normal_price'] ?? "Normal Prices";
    weeklyMonthlyDiscount =
        json['weekly_monthly_discount'] ?? "Weekly and Monthly Discount";
    bookingSettings = json['booking_settings'] ?? 'Booking Settings';
    discountNote = json['discount_note'] ??
        "This feature enables you to add a discount to a multi-night reservation (a week, a month) to encourage guests to specify more nights in a single reservation.";
    selectUnit = json['select_unit'] ?? "Select a Unit";
    selectUnitMessage =
        json['select_unit_message'] ?? "Please Select a Unit First";
    selectProperty = json['select_property'] ?? "Select Property";
    createDiscountSetting =
        json['create_discount_setting'] ?? "Create discount settings";
    weeklyDiscountSetting = json['weekly_discount_setting'] ??
        "Weekly and monthly discount settings";
    emptyDiscountSetting = json['empty_discount_setting'] ??
        "There are no settings for weekly or monthly discounts currently";
    discountInfo = json['discount_info'] ??
        "The weekly and monthly discount applies to basic prices, as well as when there are holiday prices And holidays, the discount is applied to them, not to the prices the basic";
    weekly = json['weekly'] ?? "Weekly";
    monthly = json['monthly'] ?? "Monthly";
    weeklyDiscountInstruction = json['weekly_discount_instruction'] ??
        "Weekly discount (for reservations of 7 nights or more)";
    submitAndShow = json['submit_and_show'] ?? "Submit And Show";
    weeklyDiscountRecommendation = json['weekly_discount_recommendation'] ??
        "It is recommended to discount more than 50% and cannot add less than 10%";
    monthlyDiscountInstruction = json['monthly_discount_instruction'] ??
        "Monthly discount (for reservations of 28 nights or more)";
    monthlyDiscountRecommendation = json['monthly_discount_recommendation'] ??
        "It is recommended to discount more than 50% and cannot add less than 10%";
    priceBeforeAfter =
        json['price_before_after'] ?? "Price before and after discount";
    before = json['before'] ?? "Before";
    after = json['after'] ?? "After";
    changeStatusListed = json['change_status_listed'] ?? "";
    changeStatusUnlisted = json['change_status_unlisted'] ?? "";
    previewYourUnit = json['preview_your_unit'] ?? "";
    shareYourUnit = json['share_your_unit'] ?? "";
    generateReferral = json['generate_referral'] ?? "";
    insurance = json['insurance'] ?? "Insurance";
    todayAllReview = json["today_all_review"] ?? "Today all reviews";
    allMergedReview = json["all_merged_review"] ?? "All merged reviews";
    ratingFromGuest = json["rating_from_guest"] ?? "Your guests rate you";
    ratingEvaluation =
        json["rating_evaluation"] ?? "Your evaluation of your guests";
    icalDeleteNote = json['ical_delete_note'] ?? '';
    allCalImports = json['all_cal_imports'] ?? 'All Calendar Imports';
    stepOutof = json['step_outof'] ?? 'Step :step of 3';
    instantBookNote = json['instant_book_note'] ?? 'Instant Book note';
    communicationOption1 = json["communication_option1"] ?? "Unclear";
    communicationOption2 = json["communication_option2"] ?? "Good";
    locationOption1 = json["location_option1"] ?? "Very bad";
    locationOption2 = json["location_option2"] ?? "Bad";
    finishYourListing = json["finish_your_listing"] ?? '';
    startNewListing = json["start_new_listing"] ?? '';
  }

  toJson() => {
        'Flexible': flexible,
        'Strict': strict,
        'Moderate': moderate,
        'Firm': firm,
        'Flexible_d': fullRefund1DayPriorToArrival,
        'Strict_d': fullRefundForCancelWithin48Hours,
        'Moderate_d': fullRefund5DayPriorToArrival,
        'Firm_d': fullRefundUpto30DayBeforeUpdate,
        'accuracy': accuracy,
        'location':
            Get.find<TranslationHelper>().translations.tripsActive.location,
        'communication': communication,
        'cleanliness': Get.find<TranslationHelper>()
            .translations
            .propertySingle
            .cleanliness,
        'written_reviews': writtenReviews,
        'cancellation_rate': cancellationRate,
        'acceptance_rate': acceptanceRate,
        'instant_booking_adoption_rate': instantBookingAdoptionRate,
        'decision_time': decisionTime,
        'response_rate': responseRate,
        'timely_response': timelyResponse,
        'no_of_photos': noOfPhotos,
        'amenities_offered': amenitiesOffered,
        'discount_applied': discountApplied,
      };
}

class HostListing {
  String? listings;
  String? listing;
  String? bookingSettings;
  String? status;
  String? toDo;
  String? instantBook;
  String? bedrooms;
  String? beds;
  String? bathrooms;
  String? location;
  String? lastModified;
  String? action;
  String? calendar;
  String? update;
  String? finish;
  String? listed;
  String? unlisted;
  String? continuE;
  String? clearFilter;
  String? amenities;
  String? listingStatus;
  String? inProgress;
  String? moreFilters;
  String? instantBookOff;
  String? updateRequired;
  String? searchListings;
  String? youHaveNoListings;
  String? bedroomsAndBeds;
  String? off;
  String? on;
  String? createListing;
  String? basicsTitle;
  String? basicsDescription;
  String? showDwellings;
  String? waitWhilePropertyLoaded;
  String? actionNeeded;
  String? licenseWarning;
  String? learnMore;
  String? instructions;
  String? propertiesNeedLicense;
  String? pleaseEnsureVerifyingProperties;
  String? houseListingStartedOn;
  String? imagesUploadMsg;
  String? referralCodeLimitError;

  HostListing(
      {this.listings,
      this.listing,
      this.bookingSettings,
      this.status,
      this.toDo,
      this.instantBook,
      this.bedrooms,
      this.beds,
      this.bathrooms,
      this.location,
      this.lastModified,
      this.action,
      this.calendar,
      this.update,
      this.finish,
      this.listed,
      this.unlisted,
      this.continuE,
      this.clearFilter,
      this.amenities,
      this.listingStatus,
      this.inProgress,
      this.moreFilters,
      this.instantBookOff,
      this.updateRequired,
      this.searchListings,
      this.youHaveNoListings,
      this.bedroomsAndBeds,
      this.off,
      this.on,
      this.createListing,
      this.basicsTitle,
      this.basicsDescription,
      this.showDwellings,
      this.waitWhilePropertyLoaded,
      this.actionNeeded,
      this.licenseWarning,
      this.learnMore,
      this.instructions,
      this.propertiesNeedLicense,
      this.pleaseEnsureVerifyingProperties,
      this.houseListingStartedOn,
      this.imagesUploadMsg,
      this.referralCodeLimitError});

  HostListing.fromJson(Map<String, dynamic> json) {
    listings = json['listings'];
    listing = json['listing'];
    bookingSettings = json['booking_settings'];
    status = json['status'];
    toDo = json['to_do'];
    instantBook = json['instant_book'];
    bedrooms = json['bedrooms'];
    beds = json['beds'];
    bathrooms = json['bathrooms'];
    location = json['location'];
    lastModified = json['last_modified'];
    action = json['action'];
    calendar = json['calendar'];
    update = json['update'];
    finish = json['finish'];
    listed = json['listed'];
    unlisted = json['unlisted'];
    continuE = json['continue'];
    clearFilter = json['clear_filter'];
    amenities = json['amenities'];
    listingStatus = json['listing_status'];
    inProgress = json['in_progress'];
    moreFilters = json['more_filters'];
    instantBookOff = json['instant_book_off'];
    updateRequired = json['update_required'];
    searchListings = json['search_listings'];
    youHaveNoListings = json['you_have_no_listings'];
    bedroomsAndBeds = json['bedrooms_and_beds'];
    off = json['off'];
    on = json['on'];
    createListing = json['create_listing'];
    basicsTitle = json['basics_title'];
    basicsDescription = json['basics_description'];
    showDwellings = json['show_dwellings'];
    waitWhilePropertyLoaded = json['wait_while_property_loaded'] ?? '';
    actionNeeded = json['action_needed'] ?? 'Action Needed!';
    licenseWarning = json['license_warning'] ??
        'We found that you have properties that are missing the registration number, so you have to provide a valid property registration number for all your properties before the end of October 2024.';
    learnMore = json['learn_more'] ?? 'Learn more';
    instructions = json['instructions'] ?? 'Instructions';
    propertiesNeedLicense = json['properties_need_license'] ?? '';
    pleaseEnsureVerifyingProperties =
        json['please_ensure_verifying_properties'] ?? '';
    houseListingStartedOn = json['house_listing_started_on'] ?? '';
    imagesUploadMsg = json['images_upload_msg'] ?? '';
    referralCodeLimitError = json['referral_code_limit_error'] ??
        'Referral Code must be of 10 characters';
  }
}

class HostReservation {
  String? pending;
  String? coming;
  String? ongoing;
  String? completed;
  String? canceled;
  String? all;
  String? exporT;
  String? filter;
  String? condition;
  String? guest;
  String? checkIn;
  String? checkOut;
  String? bookedUp;
  String? address;
  String? confirmationCode;
  String? totalReturn;
  String? downloadCsvFile;
  String? reservationsThatStartOrEndWithinTheFollowingDates;
  String? from;
  String? to;
  String? youHaveNoPendingReservations;
  String? seeAllReservations;
  String? downloadPdf;
  String? accept;
  String? decline;
  String? waitingForPayment;
  String? youHaveNoReservations;
  String? reservationDetails;
  String? previousGuest;
  String? about;
  String? averageReview;
  String? theDateOfJoiningDarentIs;
  String? livesIn;
  String? guests;
  String? adults;
  String? adult;
  String? children;
  String? child;
  String? accessTime;
  String? access;
  String? departure;
  String? accommodation;
  String? cleaningFee;
  String? guestServiceFee;
  String? total;
  String? hostFinancialCollection;
  String? theTotalPriceOfTheStay;
  String? hostServiceFee;
  String? vatInvoice;
  String? monthYear;
  String? details;
  String? continuE;
  String? clearFilter;
  String? thePhone;
  String? message;
  String? filters;
  String? housingAdvertisements;
  String? noFiltersApplied;
  String? selectCheckBackTime;
  String? completionDate;
  String? addDate;
  String? confirm;
  String? darentDiscount;

  HostReservation(
      {this.pending,
      this.coming,
      this.ongoing,
      this.completed,
      this.canceled,
      this.all,
      this.exporT,
      this.filter,
      this.condition,
      this.guest,
      this.checkIn,
      this.checkOut,
      this.bookedUp,
      this.address,
      this.confirmationCode,
      this.totalReturn,
      this.downloadCsvFile,
      this.reservationsThatStartOrEndWithinTheFollowingDates,
      this.from,
      this.to,
      this.youHaveNoPendingReservations,
      this.seeAllReservations,
      this.downloadPdf,
      this.accept,
      this.decline,
      this.waitingForPayment,
      this.youHaveNoReservations,
      this.reservationDetails,
      this.previousGuest,
      this.about,
      this.averageReview,
      this.theDateOfJoiningDarentIs,
      this.livesIn,
      this.guests,
      this.adults,
      this.adult,
      this.children,
      this.child,
      this.accessTime,
      this.access,
      this.departure,
      this.accommodation,
      this.cleaningFee,
      this.guestServiceFee,
      this.total,
      this.hostFinancialCollection,
      this.theTotalPriceOfTheStay,
      this.hostServiceFee,
      this.vatInvoice,
      this.monthYear,
      this.details,
      this.continuE,
      this.clearFilter,
      this.thePhone,
      this.message,
      this.filters,
      this.housingAdvertisements,
      this.noFiltersApplied,
      this.selectCheckBackTime,
      this.completionDate,
      this.addDate,
      this.confirm,
      this.darentDiscount});

  HostReservation.fromJson(Map<String, dynamic> json) {
    pending = json['pending'];
    coming = json['coming'];
    ongoing = json['ongoing'];
    completed = json['completed'];
    canceled = json['canceled'];
    all = json['all'];
    exporT = json['export'];
    filter = json['filter'];
    condition = json['condition'];
    guest = json['guest'];
    checkIn = json['check_in'];
    checkOut = json['check_out'];
    bookedUp = json['booked_up'];
    address = json['address'];
    confirmationCode = json['confirmation_code'];
    totalReturn = json['total_return'];
    downloadCsvFile = json['download_csv_file'];
    reservationsThatStartOrEndWithinTheFollowingDates =
        json['reservations_that_start_or_end_within_the_following_dates'];
    from = json['from'];
    to = json['to'];
    youHaveNoPendingReservations = json['you_have_no_pending_reservations'];
    seeAllReservations = json['see_all_reservations'];
    downloadPdf = json['download_pdf'];
    accept = json['accept'];
    decline = json['decline'];
    waitingForPayment = json['waiting_for_payment'];
    youHaveNoReservations = json['you_have_no_reservations'];
    reservationDetails = json['reservation_details'];
    previousGuest = json['previous_guest'];
    about = json['about'];
    averageReview = json['average_review'];
    theDateOfJoiningDarentIs = json['the_date_of_joining_darent_is'];
    livesIn = json['lives_in'];
    guests = json['guests'];
    adults = json['adults'];
    adult = json['adult'];
    children = json['children'];
    child = json['child'];
    accessTime = json['access_time'];
    access = json['access'];
    departure = json['departure'];
    accommodation = json['accommodation'];
    cleaningFee = json['cleaning_fee'];
    guestServiceFee = json['guest_service_fee'];
    total = json['total'];
    hostFinancialCollection = json['host_financial_collection'];
    theTotalPriceOfTheStay = json['the_total_price_of_the_stay'];
    hostServiceFee = json['host_service_fee'];
    vatInvoice = json['vat_invoice'];
    monthYear = json['month_year'];
    details = json['details'];
    continuE = json['continue'];
    clearFilter = json['clear_filter'];
    thePhone = json['the_phone'];
    message = json['message'];
    filters = json['filters'] ?? "Filters";
    housingAdvertisements =
        json['housing_advertisements'] ?? "Housing advertisements";
    noFiltersApplied = json['no_filters_applied'] ?? "No filters applied";
    selectCheckBackTime =
        json['select_check_back_time'] ?? "Select check back time";
    completionDate = json['completion_date'] ?? "Completion date";
    addDate = json['add_date'] ?? "Add date";
    confirm = json['confirm'] ?? "confirm";
    darentDiscount = json['darent_discount'] ?? "Darent's Discount";
  }
}
