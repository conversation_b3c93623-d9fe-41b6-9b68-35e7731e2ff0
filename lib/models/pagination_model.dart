class Pagination {
  int? total;
  int? count;
  int? perPage;
  int currentPage;
  int? totalPages;

  Pagination(
      {this.total,
        this.count,
        this.perPage,
        this.currentPage=1,
        this.totalPages=1});

  factory Pagination.fromJson(Map<String, dynamic> json) {
    return Pagination(
    total : json['total'],
    count : json['count'],
    perPage : json['per_page'],
    currentPage : json['current_page'],
    totalPages : json['total_pages']);
  }
}