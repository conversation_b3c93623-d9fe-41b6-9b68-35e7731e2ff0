class TicketModel{
  int id=0;
  String? bookingCode;
  String? ticketNumber;
  String? ticketType;
  String? subject;
  String? createdAt;
  String? status;
  int? bookingId;
  TicketModel.fromJson(Map json){
    id = json['id'];
    bookingCode = json['booking_code'];
    ticketNumber = json['ticket_number'];
    ticketType = json['ticket_type'];
    subject = json['subject'];
    createdAt = json['created_at'];
    status = json['status'];
    bookingId = json['booking_id'];
  }
}