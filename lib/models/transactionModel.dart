class TransactionModel{
  int id;
  String transactionTypes;
  String transactionCategories;
  double amount;
  DateTime date;
  TransactionModel(this.id,this.transactionTypes,this.transactionCategories,this.amount,this.date);
  factory TransactionModel.fromJson(Map<String,dynamic> json){
    return TransactionModel(
        json['id'],
        json['transaction_types']??"",
        json['transaction_categories']??"",
        json['amount'] is int ?json['amount'].toDouble():json['amount'],
        DateTime.parse(json['date']));
  }
}