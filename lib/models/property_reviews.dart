class PropertyReviews {
  int? id;
  String? message;
  double? rating;
  String? createdAt;
  String? reviewerFirstName;
  String? reviewerLastName;
  String? reviewerProfileImage;
  PropertyReviews({this.id, this.message, this.createdAt, this.reviewerFirstName,this.reviewerLastName,this.reviewerProfileImage,this.rating});
  PropertyReviews.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    message = json['message'];
    createdAt = json['review_date'];
    reviewerFirstName = json['first_name'];
    reviewerLastName = json['last_name'];
    reviewerProfileImage = json['profile_image'];
    rating = json['rating'] is String
        ? double.parse(json['rating'])
        : json['rating'] is int
        ? double.parse(json['rating'].toString())
        : json['rating'];
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['message'] = message;
    data['rating'] = rating;
    data['review_date'] = createdAt;
    data['first_name'] = reviewerFirstName;
    data['last_name'] = reviewerLastName;
    data['profile_image'] = reviewerProfileImage;
    return data;
  }
}
// class Reviewer {
//   int? id;
//   String? name;
//   String? profileImage;
//
//   Reviewer({this.id, this.name, this.profileImage});
//
//   Reviewer.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     name = json['name'];
//     profileImage = json['profile_image'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = {};
//     data['id'] = id;
//     data['name'] = name;
//     data['profile_image'] = profileImage;
//     return data;
//   }
// }