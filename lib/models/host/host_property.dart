
class HostProperty{
  int id;
  String name;
  String? description;
  String photo;
  bool checked;
  String propertyCode;
  HostProperty({required this.id,required this.name,this.description,required this.photo,this.checked=false,this.propertyCode=''});
  factory HostProperty.fromJson(Map json){
    return HostProperty(
        id:json['id'],
        name:json['name']??'',
        description:json['description']??'',
        photo:json['cover_photo'],
        propertyCode:json['property_code']
    );
  }
}