import 'package:darent/utils/constants.dart';

class DwellingModel {
  int? id;
  String? name;
  String? code;
  String? slug;
  String? address;
  double? singleBeds;
  double? doubleBeds;
  double? bathrooms;
  double? square;
  String? propertyType;
  String? coverPhoto;
  String? status;
  int? visibility;
  double? perNightPrice;
  SpecialDaysPrice? specialDaysPrice;
  bool licenseVerified=false;
  String? createdAt;
  DwellingModel({this.name, this.slug, this.coverPhoto, this.perNightPrice, this.specialDaysPrice,this.licenseVerified=false,this.createdAt});

  DwellingModel.fromJson(Map<String, dynamic> json) {
    licenseVerified = json['license']!=null;
    id = json['id'];
    name = json['name'];
    code = json['code'];
    slug = json['slug'];
    singleBeds = checkAndGetDouble(json['single_beds']??0.0);
    doubleBeds = checkAndGetDouble(json['double_beds']??0.0);
    bathrooms = checkAndGetDouble(json['bathrooms']??0.0);
    // On API side will add in future
    square = checkAndGetDouble(json['square_meter']??0.0);
    address = json['address'];
    propertyType = json['propertyType'];
    coverPhoto = json['cover_photo'];
    status = json['status'];
    visibility = json['visibility']??0;
    perNightPrice = checkAndGetDouble(json['per_night_price']??0.0);
    specialDaysPrice = json["special_days_price"] != null ? SpecialDaysPrice.fromJson(json["special_days_price"]):json["special_days_price"];
    createdAt = json['created_at']??'13 December 2024';
  }
}

class SpecialDaysPrice {
  double? thursday;
  double? friday;
  double? saturday;

  SpecialDaysPrice({
    required this.thursday,
    required this.friday,
    required this.saturday,
  });

  factory SpecialDaysPrice.fromJson(Map<String, dynamic> json) => SpecialDaysPrice(
    thursday: checkAndGetDouble(json["thursday"]),
    friday: checkAndGetDouble(json["friday"]),
    saturday: checkAndGetDouble(json["saturday"]),
  );

  Map<String, dynamic> toJson() => {
    "thursday": thursday,
    "friday": friday,
    "saturday": saturday,
  };
}