class BookingModel {
  int? id;
  String? condition;
  String? departure;
  int? propertyId;
  String? bookingCode;
  String? userName;
  String? phone;
  String? profile;
  int? joined;
  String? startDate;
  String? endDate;
  String? propertyName;
  int? guest;
  String? userRating;
  String? checkinTime;
  String? checkoutTime;
  double? total;
  int? basePrice;
  int? cleaningFee;
  int? guestFee;
  double? hostFee;
  double? hostTotal;
  int? chatHeadId;

  BookingModel(
      {this.id,
        this.condition,
        this.departure,
        this.propertyId,
        this.bookingCode,
        this.userName,
        this.phone,
        this.profile,
        this.joined,
        this.startDate,
        this.endDate,
        this.propertyName,
        this.guest,
        this.userRating,
        this.checkinTime,
        this.checkoutTime,
        this.total,
        this.basePrice,
        this.cleaningFee,
        this.guestFee,
        this.hostFee,
        this.hostTotal,
        this.chatHeadId});

  BookingModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    condition = json['condition'];
    departure = json['departure'];
    propertyId = json['property_id'];
    bookingCode = json['booking_code'];
    userName = json['user_name'];
    phone = json['phone'];
    profile = json['profile'];
    joined = json['joined'];
    startDate = json['start_date'];
    endDate = json['end_date'];
    propertyName = json['property_name'];
    guest = json['guest'];
    userRating = json['user_rating'];
    checkinTime = json['checkin_time'];
    checkoutTime = json['checkout_time'];
    total = json['total'];
    basePrice = json['base_price'];
    cleaningFee = json['cleaning_fee'];
    guestFee = json['guest_fee'];
    hostFee = json['host_fee'];
    hostTotal = json['host_total'];
    chatHeadId = json['chat_head_id'];
  }
}