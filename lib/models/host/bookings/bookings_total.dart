class BookingsTotal{
  int allCount;
  int checkoutCount;
  int ongoingCount;
  int arrivingCount;
  int pendingreviewsCount;
  int comingCount;
  int cancelledCount;
  double totalPrice;
  BookingsTotal({this.allCount=0,this.checkoutCount=0,this.ongoingCount=0,this.arrivingCount=0,this.pendingreviewsCount=0,this.comingCount=0,this.cancelledCount=0,this.totalPrice=0});
factory BookingsTotal.fromJson(Map json){
  return BookingsTotal(
    allCount  :json['AllCount'],
    checkoutCount  :json['checkout_count'],
    ongoingCount:json['ongoing_count'],
    arrivingCount:json['ariving_count'],
    pendingreviewsCount:json['pendingreviews_count'],
    comingCount:json['coming_count'],
    cancelledCount:json['cancelled_count'],
    totalPrice: json['total_price'] is String ? double.parse(json['total_price']) : (json['total_price'] ?? 0),
  );
}
toJson()=>{
    "Current Reservations": ongoingCount,
    "Total Price": totalPrice,
    "Cancelled Reservations": cancelledCount,
    "All Reservations": allCount
  };
}