class ReportModel {
  String? startDate;
  String? endDate;
  Report? report;

  ReportModel({this.startDate, this.endDate, this.report});

  ReportModel.fromJson(Map<String, dynamic> json) {
    startDate = json['start_date'];
    endDate = json['end_date'];
    report =
    json['report'] != null ? Report.fromJson(json['report']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['start_date'] = startDate;
    data['end_date'] = endDate;
    if (report != null) {
      data['report'] = report!.toJson();
    }
    return data;
  }
}

class Report {
  Views? views;
  Reviews? reviews;
  Sales? sales;
  Points? points;

  Report({this.views, this.reviews, this.sales, this.points});

  Report.fromJson(Map<String, dynamic> json) {
    views = json['views'] != null ? Views.fromJson(json['views']) : null;
    reviews =
    json['reviews'] != null ? Reviews.fromJson(json['reviews']) : null;
    sales = json['sales'] != null ? Sales.fromJson(json['sales']) : null;
    points =
    json['points'] != null ? Points.fromJson(json['points']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    if (this.views != null) {
      data['views'] = this.views!.toJson();
    }
    if (this.reviews != null) {
      data['reviews'] = this.reviews!.toJson();
    }
    if (this.sales != null) {
      data['sales'] = this.sales!.toJson();
    }
    if (this.points != null) {
      data['points'] = this.points!.toJson();
    }
    return data;
  }
}

class Views {
  double? numberOfViews;

  Views({this.numberOfViews});

  Views.fromJson(Map<String, dynamic> json) {
    numberOfViews = json['number_of_views'] is int?(json['number_of_views']).toDouble():json['number_of_views'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['number_of_views'] = this.numberOfViews;
    return data;
  }
}

class Reviews {
  double? numberOfReviews;
  double? cleanliness;
  double? location;
  double? accuracy;
  double? communication;

  Reviews(
      {this.numberOfReviews,
        this.cleanliness,
        this.location,
        this.accuracy,
        this.communication});

  Reviews.fromJson(Map<String, dynamic> json) {
    numberOfReviews = json['number_of_views'] is int?(json['number_of_views']).toDouble():json['number_of_views'];
    cleanliness = json['cleanliness'] is int?(json['cleanliness']).toDouble():json['cleanliness'];
    location = json['location'] is int?(json['location']).toDouble():json['location'];
    accuracy = json['accuracy'] is int?(json['accuracy']).toDouble():json['accuracy'];
    communication = json['communication'] is int?(json['communication']).toDouble():json['communication'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['number_of_reviews'] = this.numberOfReviews;
    data['cleanliness'] = this.cleanliness;
    data['location'] = this.location;
    data['accuracy'] = this.accuracy;
    data['communication'] = this.communication;
    return data;
  }
}

class Sales {
  double? sales;
  double? totalReservations;
  double? clickRateBooking;

  Sales({this.sales, this.totalReservations, this.clickRateBooking});

  Sales.fromJson(Map<String, dynamic> json) {
    sales = json['sales'] is int?(json['sales']).toDouble():json['sales'];
    totalReservations = json['total_reservations'] is int?(json['total_reservations']).toDouble():json['total_reservations'];
    clickRateBooking = json['click_rate_booking'] is int?(json['click_rate_booking']).toDouble():json['click_rate_booking'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['sales'] = this.sales;
    data['total_reservations'] = this.totalReservations;
    data['click_rate_booking'] = this.clickRateBooking;
    return data;
  }
}

class Points {
  double? cancellationRate;
  double? acceptanceRate;
  double? decisionTime;
  double? responseRate;
  double? timelyResponse;

  Points(
      {this.cancellationRate,
        this.acceptanceRate,
        this.decisionTime,
        this.responseRate,
        this.timelyResponse});

  Points.fromJson(Map<String, dynamic> json) {
    cancellationRate = json['cancellation_rate'] is int?(json['cancellation_rate']).toDouble():json['cancellation_rate'];
    acceptanceRate = json['acceptance_rate'] is int?(json['acceptance_rate']).toDouble():json['acceptance_rate'];
    decisionTime = json['decision_time'] is int?(json['decision_time']).toDouble():json['decision_time'];;
    responseRate = json['response_rate'] is int?(json['response_rate']).toDouble():json['response_rate'];;
    timelyResponse = json['timely_response'] is int?(json['timely_response']).toDouble():json['timely_response'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['cancellation_rate'] = this.cancellationRate;
    data['acceptance_rate'] = this.acceptanceRate;
    data['decision_time'] = this.decisionTime;
    data['response_rate'] = this.responseRate;
    data['timely_response'] = this.timelyResponse;
    return data;
  }
}