

import 'package:darent/utils/constants.dart';

class ReviewModel {
  List<ListElement> list;
  Average average;
  bool hasMore;
  String? nextPage;
  int perPage;

  ReviewModel({
    required this.list,
    required this.average,
    required this.hasMore,
    required this.nextPage,
    required this.perPage,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) => ReviewModel(
    list: List<ListElement>.from(json["list"].map((x) => ListElement.fromJson(x))),
    average: Average.fromJson(json["average"]),
    hasMore: json["has_more"],
    nextPage: json["next_page"],
    perPage: json["per_page"],
  );

  Map<String, dynamic> toJson() => {
    "list": List<dynamic>.from(list.map((x) => x.toJson())),
    "average": average.toJson(),
    "has_more": hasMore,
    "next_page": nextPage,
    "per_page": perPage,
  };
}

class Average {
  double cleanliness;
  double communication;
  double accuracy;
  double location;
  double rating;
  int total;

  Average({
    required this.cleanliness,
    required this.communication,
    required this.accuracy,
    required this.location,
    required this.rating,
    required this.total,
  });

  factory Average.fromJson(Map<String, dynamic> json,{int total=0}) => Average(
      cleanliness: checkAndGetDouble(json["cleanliness"]??0.0),
      communication: checkAndGetDouble(json["communication"]??0.0),
      accuracy: checkAndGetDouble(json["accuracy"]??0.0),
      location: checkAndGetDouble(json["location"]??0.0),
      rating: checkAndGetDouble(json["rating"]??0.0),
      total: total
  );

  Map<String, dynamic> toJson() => {
    "cleanliness": cleanliness,
    "communication": communication,
    "accuracy": accuracy,
    "location": location,
    "rating": rating,
  };
}

class ListElement {
  int id;
  double rating;
  String message;
  String createdAt;
  double cleanliness;
  double communication;
  double accuracy;
  double location;
  Reviewer reviewer;
  Property property;

  ListElement({
    required this.id,
    required this.rating,
    required this.message,
    required this.createdAt,
    required this.cleanliness,
    required this.communication,
    required this.accuracy,
    required this.location,
    required this.reviewer,
    required this.property,
  });

  factory ListElement.fromJson(Map<String, dynamic> json) => ListElement(
    id: json["id"]??0,
    rating: checkAndGetDouble(json["rating"]??0.0),
    message: json["message"]??"",
    createdAt: json["created_at"]??"",
    cleanliness: checkAndGetDouble(json["cleanliness"]??0.0) ,
    communication: checkAndGetDouble(json["communication"]??0.0),
    accuracy: checkAndGetDouble(json["accuracy"]??0.0),
    location: checkAndGetDouble(json["location"]??0.0),
    reviewer: Reviewer.fromJson(json["reviewer"]),
    property: Property.fromJson(json["property"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "rating": rating,
    "message": message,
    "created_at": createdAt,
    "cleanliness": cleanliness,
    "communication": communication,
    "accuracy": accuracy,
    "location": location,
    "reviewer": reviewer.toJson(),
    "property": property.toJson(),
  };
}

class Property {
  int id;
  String name;
  String image;

  Property({
    required this.id,
    required this.name,
    required this.image,
  });

  factory Property.fromJson(Map<String, dynamic> json) => Property(
    id: json["id"],
    name: json["name"],
    image: json["image"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "image": image,
  };
}

class Reviewer {
  int id;
  String name;
  String profileImage;

  Reviewer({
    required this.id,
    required this.name,
    required this.profileImage,
  });

  factory Reviewer.fromJson(Map<String, dynamic> json) => Reviewer(
    id: json["id"],
    name: json["name"],
    profileImage: json["profile_image"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "profile_image": profileImage,
  };
}
