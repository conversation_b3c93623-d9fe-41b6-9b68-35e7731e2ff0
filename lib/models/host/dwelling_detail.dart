import 'package:get/get_rx/src/rx_types/rx_types.dart';
import './property_photo.dart';

class DwellingDetail {
  int? id;
  String? propertyCode;
  String? name;
  String? nameAr;
  String? slug;
  int? hostId;
  int? bedrooms;
  int? beds;
  int? singleBeds;
  int? doubleBeds;
  int? bedType;
  double? bathrooms;
  String? amenities;
  String? customAmenities;
  int? propertyType;
  int? spaceType;
  int? accommodates;
  int? adultGuest;
  int? childrenGuest;
  String? bookingType;
  String? cancellation;
  String? status;
  int? visibility;
  String? checkinTime;
  String? checkoutTime;
  int? noOfAppartment;
  int? maxNights;
  int? minNights;
  int? priority;
  String? rejectionReason;
  List<String>? images;
  String? spaceTypeName;
  String? propertyPhoto;
  String? coverPhoto;
  String? missedStep;
  List<PropertyPhoto>? propertyPhotos;
  List<PropertyPhoto>? tempPhotos;
  PropertyAddress? propertyAddress;
  PropertyPrice? propertyPrice;
  PropertyDescription? propertyDescription;
  List<PropertyCoHost>? propertyCoHost;
  final RxList<IcalImport> icalImports = RxList<IcalImport>();
  String? licenseVerifiedAt;
  bool? licenseMandatory;

  DwellingDetail(
      {this.id,
      this.propertyCode,
      this.name,
      this.nameAr,
      this.slug,
      this.hostId,
      this.bedrooms,
      this.beds,
      this.singleBeds,
      this.doubleBeds,
      this.bedType,
      this.bathrooms,
      this.amenities,
      this.customAmenities,
      this.propertyType,
      this.spaceType,
      this.accommodates,
      this.adultGuest,
      this.childrenGuest,
      this.bookingType,
      this.cancellation,
      this.status,
      this.visibility,
      this.checkinTime,
      this.checkoutTime,
      this.noOfAppartment,
      this.maxNights,
      this.minNights,
      this.priority,
      this.rejectionReason,
      this.images,
      this.spaceTypeName,
      this.propertyPhoto,
      this.coverPhoto,
      this.missedStep,
      this.propertyPhotos = const [],
      this.propertyAddress,
      this.propertyPrice,
      this.propertyDescription,
      this.propertyCoHost,this.licenseMandatory});

  DwellingDetail.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    propertyCode = json['property_code'];
    name = json['name'];
    nameAr = json['name_ar'];
    slug = json['slug'];
    hostId = json['host_id'];
    bedrooms = json['bedrooms'];
    beds = json['beds'];
    singleBeds = json['single_beds'];
    doubleBeds = json['double_beds'];
    bedType = json['bed_type'];
    bathrooms = json['bathrooms'] is int
        ? json['bathrooms'].toDouble()
        : json['bathrooms'];
    amenities = json['amenities'];
    customAmenities = json['custom_amenities'];
    propertyType = json['property_type'];
    spaceType = json['space_type'];
    accommodates = json['accommodates'];
    adultGuest = json['adult_guest'];
    childrenGuest = json['children_guest'];
    bookingType = json['booking_type'];
    cancellation = json['cancellation'];
    status = json['status'];
    visibility = json['visibility'];
    checkinTime = json['checkinTime'];
    checkoutTime = json['checkoutTime'];
    noOfAppartment = json['no_of_appartment'];
    maxNights = json['max_nights'];
    minNights = json['min_nights'];
    rejectionReason = json['rejection_reason'];
    priority = json['priority'];
    images = json['images'].cast<String>();
    spaceTypeName = json['space_type_name'];
    propertyPhoto = json['property_photo'];
    coverPhoto = json['cover_photo'];
    missedStep = json['missed_step'];
    if (json['property_photos'] != null) {
      propertyPhotos = json['property_photos']
          .map<PropertyPhoto>((e) => PropertyPhoto.fromJson(e))
          .toList();
    }
    if (json['photos_temp'] != null) {
      tempPhotos = json['photos_temp']
          .map<PropertyPhoto>((e) => PropertyPhoto.fromJson(e,coverPhoto:0))
          .toList();
    }
    propertyAddress = json['property_address'] != null
        ? PropertyAddress.fromJson(json['property_address'])
        : null;
    propertyPrice = json['property_price'] != null
        ? PropertyPrice.fromJson(json['property_price'])
        : null;
    propertyDescription = json['property_description'] != null
        ? PropertyDescription.fromJson(json['property_description'])
        : null;
    if (json['property_cohost'] != null) {
      propertyCoHost = json['property_cohost']
          .map<PropertyCoHost>((e) => PropertyCoHost.fromJson(e))
          .toList();
    }
    if (json['ical_import'] != null) {
      icalImports.value = json['ical_import']
          .reversed
          .map<IcalImport>((e) => IcalImport.fromJson(e))
          .toList();
    }
    licenseVerifiedAt = json['license_verified_at'];
    licenseMandatory = json['license_mandatory'];
  }
}

class PropertyAddress {
  int? id;
  int? propertyId;
  String? addressLine1;
  String? addressLine2;
  double? latitude;
  double? longitude;
  String? city;
  String? state;
  String? district;
  String? districtAr;
  String? country;
  String? postalCode;

  PropertyAddress(
      {this.id,
      this.propertyId,
      this.addressLine1,
      this.addressLine2,
      this.latitude,
      this.longitude,
      this.city,
      this.state,
      this.country,
      this.postalCode,
      this.district,
      this.districtAr});

  PropertyAddress.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    propertyId = json['property_id'];
    addressLine1 = json['address_line_1'];
    addressLine2 = json['address_line_2'];
    latitude = double.parse(json['latitude'] ?? '0.0');
    longitude = double.parse(json['longitude'] ?? '0.0');
    city = json['city'];
    state = json['state'];
    country = json['country'];
    postalCode = json['postal_code'];
    district = json['district'];
    districtAr = json['district_ar'] ?? json['district'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['property_id'] = propertyId;
    data['address_line_1'] = addressLine1;
    data['address_line_2'] = addressLine2;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['city'] = city;
    data['state'] = state;
    data['country'] = country;
    data['postal_code'] = postalCode;
    return data;
  }
}

class PropertyPrice {
  int? id;
  int? propertyId;
  int? cleaningFee;
  int? guestAfter;
  int? guestFee;
  int? securityFee;
  int? price;
  int? weekendPrice;
  int? weeklyDiscount;
  int? monthlyDiscount;
  Map? specialDaysPrice;
  String? currencyCode;
  int? originalCleaningFee;
  int? originalGuestFee;
  int? originalPrice;
  int? originalWeekendPrice;
  int? originalSecurityFee;
  String? defaultCode;
  String? defaultSymbol;

  PropertyPrice(
      {this.id,
      this.propertyId,
      this.cleaningFee,
      this.guestAfter,
      this.guestFee,
      this.securityFee,
      this.price,
      this.weekendPrice,
      this.weeklyDiscount,
      this.monthlyDiscount,
      this.specialDaysPrice,
      this.currencyCode,
      this.originalCleaningFee,
      this.originalGuestFee,
      this.originalPrice,
      this.originalWeekendPrice,
      this.originalSecurityFee,
      this.defaultCode,
      this.defaultSymbol});

  PropertyPrice.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    propertyId = json['property_id'];
    cleaningFee = json['cleaning_fee'];
    guestAfter = json['guest_after'];
    guestFee = json['guest_fee'];
    securityFee = json['security_fee'];
    price = json['price'];
    weekendPrice = json['weekend_price'];
    weeklyDiscount = json['weekly_discount'];
    monthlyDiscount = json['monthly_discount'];
    specialDaysPrice = json['special_days_price'] ?? {};
    currencyCode = json['currency_code'];
    originalCleaningFee = json['original_cleaning_fee'];
    originalGuestFee = json['original_guest_fee'];
    originalPrice = json['original_price'];
    originalWeekendPrice = json['original_weekend_price'];
    originalSecurityFee = json['original_security_fee'];
    defaultCode = json['default_code'];
    defaultSymbol = json['default_symbol'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['property_id'] = propertyId;
    data['cleaning_fee'] = cleaningFee;
    data['guest_after'] = guestAfter;
    data['guest_fee'] = guestFee;
    data['security_fee'] = securityFee;
    data['price'] = price;
    data['weekend_price'] = weekendPrice;
    data['weekly_discount'] = weeklyDiscount;
    data['monthly_discount'] = monthlyDiscount;
    data['special_days_price'] = specialDaysPrice;
    data['currency_code'] = currencyCode;
    data['original_cleaning_fee'] = originalCleaningFee;
    data['original_guest_fee'] = originalGuestFee;
    data['original_price'] = originalPrice;
    data['original_weekend_price'] = originalWeekendPrice;
    data['original_security_fee'] = originalSecurityFee;
    data['default_code'] = defaultCode;
    data['default_symbol'] = defaultSymbol;
    return data;
  }
}

class IcalImport {
  int? id;
  String? icalendarUrl;
  String? icalendarName;
  //icalendar_last_sync
  IcalImport(this.id, this.icalendarUrl, this.icalendarName);
  IcalImport.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    icalendarUrl = json['icalendar_url'] ?? '';
    icalendarName = json['icalendar_name'] ?? "";
  }
}

class PropertyDescription {
  int? id;
  int? propertyId;
  RxString summary = "".obs;
  RxString summaryAr = "".obs;
  String? placeIsGreatFor;
  String? aboutPlace;
  String? guestCanAccess;
  String? interactionGuests;
  String? other;
  String? aboutNeighborhood;
  String? getAround;

  // PropertyDescription({this.id, this.propertyId, this.summary, this.summaryAr, this.placeIsGreatFor, this.aboutPlace, this.guestCanAccess, this.interactionGuests, this.other, this.aboutNeighborhood, this.getAround});

  PropertyDescription.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    propertyId = json['property_id'];
    summary.value = json['summary'] ?? "";
    summaryAr.value = json['summary_ar'] ?? "";
    placeIsGreatFor = json['place_is_great_for'];
    aboutPlace = json['about_place'];
    guestCanAccess = json['guest_can_access'];
    interactionGuests = json['interaction_guests'];
    other = json['other'];
    aboutNeighborhood = json['about_neighborhood'];
    getAround = json['get_around'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['property_id'] = propertyId;
    data['summary'] = summary;
    data['summary_ar'] = summaryAr;
    data['place_is_great_for'] = placeIsGreatFor;
    data['about_place'] = aboutPlace;
    data['guest_can_access'] = guestCanAccess;
    data['interaction_guests'] = interactionGuests;
    data['other'] = other;
    data['about_neighborhood'] = aboutNeighborhood;
    data['get_around'] = getAround;
    return data;
  }
}

class PropertyCoHost {
  int? id;
  int? isRequest;
  String? name;
  String? status;
  String? image;
  int? coHostId;
  DateTime? createdAt;

  PropertyCoHost(
    this.id,
    this.isRequest,
    this.name,
    this.status,
    this.image,
    this.coHostId,
    this.createdAt,
  );

  factory PropertyCoHost.fromJson(Map<String, dynamic> json) {
    return PropertyCoHost(
        json['id'],
        json['is_request'],
        json['name'],
        json['status'],
        json['image'],
        json['co_host_id'] is String
            ? int.parse(json['co_host_id'])
            : json['co_host_id'],
        json['created_at'] != null ? DateTime.parse(json['created_at']) : null);
  }
}
