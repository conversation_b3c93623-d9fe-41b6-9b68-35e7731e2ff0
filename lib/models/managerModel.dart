class ManagerList {
  int? id;
  String? firstName;
  String? lastName;
  String? email;
  String? formattedPhone;
  String? status;
  String? role;

  ManagerList(
      {this.id,
      this.firstName,
      this.lastName,
      this.email,
      this.formattedPhone,
      this.status,
      this.role});

  ManagerList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    email = json['email'];
    formattedPhone = json['formatted_phone'];
    status = json['status'];
    role = json['role'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['first_name'] = this.firstName;
    data['last_name'] = this.lastName;
    data['email'] = this.email;
    data['formatted_phone'] = this.formattedPhone;
    data['status'] = this.status;
    data['role'] = this.role;
    return data;
  }
}