class Property{
  int? id;
  int? bookingId;
  String? name;
  String? summary;
  String? photo;
  String? slug;
  Property.fromJson(json){
    id = json['id'];
    bookingId = json['booking_id'];
    name = json['name'];
    summary = json['summary'];
    photo = json['photo'];
    slug = json['slug'];
  }
}
class ChatWindow {
  int? id;
  int? bookingId;
  int? senderId;
  int? receiverId;
  String? message;
  int? typeId;
  int? read;
  int? archive;
  int? star;
  int? hostId;
  String? createdAt;
  String? updatedAt;
  String? createdTime;
  int? hostUser;
  int? guestUser;
  String? propertyName;

  ChatWindow(
      {this.id,
        this.bookingId,
        this.senderId,
        this.receiverId,
        this.message,
        this.typeId,
        this.read,
        this.archive,
        this.star,
        this.hostId,
        this.createdAt,
        this.updatedAt,
        this.createdTime,
        this.hostUser,
        this.guestUser,
        this.propertyName
      });

  ChatWindow.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    bookingId = json['booking_id'];
    senderId = json['sender_id'];
    receiverId = json['receiver_id'];
    message = json['message'];
    typeId = json['type_id'];
    read = json['read'];
    archive = json['archive'];
    star = json['star'];
    hostId = json['host_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    createdTime = json['created_time'];
    hostUser = json['host_user'];
    guestUser = json['guest_user'];
    propertyName = json['property_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['booking_id'] = bookingId;
    data['sender_id'] = senderId;
    data['receiver_id'] = receiverId;
    data['message'] = message;
    data['type_id'] = typeId;
    data['read'] = read;
    data['archive'] = archive;
    data['star'] = star;
    data['host_id'] = hostId;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['created_time'] = createdTime;
    data['host_user'] = hostUser;
    data['guest_user'] = guestUser;
    data['property_name'] = propertyName;
    return data;
  }
}
class Receiver{
  String? firstName;
  String? lastName;
  String? profileImage;
  String? joinDate;
  bool verified;
  String rating;
  Receiver(this.firstName,this.lastName,this.profileImage,this.joinDate,this.verified,this.rating);
  factory Receiver.fromJson(Map json){
    return Receiver(json['first_name'], json['last_name'], json['profile_image'], json['join_date'], json['verified']==1,json['rating'] is int?json['rating'].toString():json['rating']??"0.0");
  }
}