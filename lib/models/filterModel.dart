import 'package:get/get_rx/get_rx.dart';

class FilterModel {
  int? lat;
  int? long;
  String? location;
  double min_price = 5.0;
  double max_price = 1.0;
  List<SpaceType> spaceType = RxList();
  List<PropertyType> property_type = RxList();
  List<Amenity> amenities = RxList();
  List<Amenity> recommendedAmenities = RxList();
  List<Amenity> safetyAmenities = RxList();
  List<Amenity> houseRules = RxList();
  // List<StartingCity> startCities = RxList();
  List<TypeGeneral> ticketTypes = RxList();
  List<InitialCities> cities = RxList();

  FilterModel(
      this.lat,
      this.long,
      this.location,
      this.spaceType,
      this.property_type,
      this.amenities,
      this.recommendedAmenities,
      this.safetyAmenities,
      this.houseRules,
      this.min_price,
      this.max_price,
      // this.startCities,
      this.ticketTypes,
      this.cities
      );

  factory FilterModel.fromJson(Map<String, dynamic> json) {
    List<Amenity> amenities = (json['common_amenities']??json['amenities']).map<Amenity>((item_) => Amenity.fromJson(item_)).toList();
    List<Amenity> recommendedAmenities = (json['recommended_amenities']??[]).map<Amenity>((item_) => Amenity.fromJson(item_)).toList();
    List<Amenity> safetyAmenities = (json['safety_amenities']??[]).map<Amenity>((item_) => Amenity.fromJson(item_)).toList();
    List<Amenity> houseRules_ = (json['house_rules']??[]).map<Amenity>((item_) => Amenity.fromJson(item_)).toList();
    final spaceTypes = json['space_type'].map<SpaceType>((item) => SpaceType.fromJson(item)).toList();
    List<PropertyType> propertyTypes = json['property_type']
        .map<PropertyType>((item) => PropertyType.fromJson(item))
        .toList();
    // List<StartingCity>? startingCities;
    // if (json['starting_cities'] != null) {
    //   startingCities = json['starting_cities'].map<StartingCity>((item) => StartingCity.fromJson(item)).toList();
    // }
    List<InitialCities> cities =[];
    if(json['cities'] != null){
      cities = json['cities'].map<InitialCities>((item) => InitialCities.fromJson(item)).toList();
    }
    List<TypeGeneral> ticketTypes_ = [];
    if (json['ticket_types'] != null) {
      ticketTypes_ = json['ticket_types'].map<TypeGeneral>((item) => TypeGeneral.fromJson(item)).toList();
    }
    double minPrice =  (json['min_price'] is int? json['min_price'].toDouble(): json['min_price'] ?? 5.0);
    double maxPrice =  (json['max_price'] is int? json['max_price'].toDouble(): json['max_price'] ?? 5.0);
    return FilterModel(
        json['lat'],
        json['long'],
        json['location'],
        spaceTypes,
        propertyTypes,
        amenities,
        recommendedAmenities,
        safetyAmenities,
        houseRules_,
        minPrice<5?5:minPrice,
        maxPrice<5?5:maxPrice>5000?5000:maxPrice,
        // startingCities!,
        ticketTypes_,
      cities
    );
  }
}

class InitialCities {
  final int id;
  final String name;
  final String nameAr;
  final int countryId;
  final double latitude;
  final double longitude;
  final String image;
  int mediaId;

  InitialCities({
    required this.id,
    required this.name,
    required this.nameAr,
    required this.countryId,
    required this.latitude,
    required this.longitude,
    required this.image,
    required this.mediaId
  });

  factory InitialCities.fromJson(Map<String, dynamic> json) => InitialCities(
    id: json["id"],
    name: json["name"],
    nameAr: json["name_ar"],
    countryId: json["country_id"],
    latitude: json["latitude"]?.toDouble(),
    longitude: json["longitude"]?.toDouble(),
    image: json["image"],
    mediaId: json["media_id"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "name_ar": nameAr,
    "country_id": countryId,
    "latitude": latitude,
    "longitude": longitude,
    "image": image,
    "media_id": mediaId,
  };
}

class Amenity {
  int? id;
  String? title;
  String? titleAr;
  String? description;
  String? symbol;
  String? iconImage;
  int? typeId;
  String? status;
  bool isChecked = false;

  Amenity.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    title = json['title'];
    titleAr = json['title_ar'];
    description = json['description'];
    symbol = json['symbol'];
    iconImage = json['icon_image'];
    typeId = json['type_id'];
    status = json['status'];
  }
}

class TypeGeneral {
  int? id;
  String? name;
  String? nameAr;
  bool isChecked = false;
  TypeGeneral.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    nameAr = json['name_ar'];
  }
}
class PropertyType extends TypeGeneral{
  String? image;
  PropertyType.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    image = json['image'];
  }
}
class SpaceType extends TypeGeneral{
  String? iconImage;
  int? propertyType;
  int? priority;
  SpaceType.fromJson(Map<String, dynamic> json):super.fromJson(json){
    iconImage = json['icon_image'];
    propertyType= json['property_type'];
    priority = json['priority'];
  }
}

class StartingCity {
  String? name;
  String? nameAr;
  String? imageUrl;

  StartingCity.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    nameAr = json['name_ar'];
    imageUrl = json['image_url'];
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['name'] = name;
    data['name_ar'] = nameAr;
    data['isChecked'] = imageUrl;
    return data;
  }
}

class CityBasedDistricModel {
  final int id;
  final String name;
  final String nameAr;
  final int countryId;
  final double latitude;
  final double longitude;
  final String country;
  final String coordinates;
  final List<District> districts;
  bool isChecked = false;

  CityBasedDistricModel({
    required this.id,
    required this.name,
    required this.nameAr,
    required this.countryId,
    required this.latitude,
    required this.longitude,
    required this.country,
    required this.coordinates,
    required this.districts,
  });

  factory CityBasedDistricModel.fromJson(Map<String, dynamic> json) => CityBasedDistricModel(
    id: json["id"],
    name: json["name"],
    nameAr: json["name_ar"],
    countryId: json["country_id"],
    latitude: json["latitude"]?.toDouble(),
    longitude: json["longitude"]?.toDouble(),
    country: json["country"],
    coordinates: json["coordinates"],
    districts: List<District>.from(json["districts"].map((x) => District.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "name_ar": nameAr,
    "country_id": countryId,
    "latitude": latitude,
    "longitude": longitude,
    "country": country,
    "coordinates": coordinates,
    "districts": List<dynamic>.from(districts.map((x) => x.toJson())),
  };
}

class District {
  final int id;
  final int cityId;
  final String name;
  final String nameAr;
  final String city;
  bool isChecked = false;

  District({
    required this.id,
    required this.cityId,
    required this.name,
    required this.nameAr,
    required this.city,
  });

  factory District.fromJson(Map<String, dynamic> json) => District(
    id: json["id"],
    cityId: json["city_id"],
    name: json["name"],
    nameAr: json["name_ar"],
    city: json["city"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "city_id": cityId,
    "name": name,
    "name_ar": nameAr,
    "city": city,
  };
}