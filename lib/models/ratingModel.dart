class ReviewModel{
  int? id;
  String? propertyName;
  String? propertyPhoto;
  String? message;
  String senderName;
  String rating;
  String? createdAt;
  bool? rated = false;

  ReviewModel(this.id ,this.propertyName, this.propertyPhoto,this.message,this.senderName,this.rating,this.createdAt,this.rated);
  factory ReviewModel.fromJson(Map json){
    return ReviewModel(
    json["id"],
    json["property_name"],
    json["property_photo"],
    json["message"],
    json["sender_name"],
    '${json["rating"]}',
    json["created_at"],
    json['rated']);
  }
}