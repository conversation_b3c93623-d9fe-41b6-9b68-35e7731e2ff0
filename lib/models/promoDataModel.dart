class PromoDataModel {
  double? amount;
  double? amountAfterDiscount;
  String? discountType;
  double? discountValue;
  double? serviceFee;
  int? tamayouzId;
  PromoDataModel({
    this.amount,
    this.amountAfterDiscount,
    this.discountType,
    this.discountValue,
    this.serviceFee,this.tamayouzId});

  PromoDataModel.fromJson(Map<String, dynamic> json) {
    amount = json['amount'] is int? json['amount'].toDouble():json['amount'] is String?double.parse(json['amount']):json['amount'];
    amountAfterDiscount = json['amount_after_discount'] is int? json['amount_after_discount'].toDouble(): json['amount_after_discount'];
    discountType = json['discount_type'];
    discountValue = json['discount_value'] is int?json['discount_value'].toDouble():json['discount_value'] is String?double.parse(json['discount_value']):json['discount_value'];
    serviceFee = json['servicefee'] is double
        ?json['servicefee']
        :json['servicefee'] is int
        ?json['servicefee'].toDouble()
        :double.parse(json['servicefee']??"0.0");
     tamayouzId = json['tamayouz_id'];
  }
}