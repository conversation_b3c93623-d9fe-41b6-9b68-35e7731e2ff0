class BookingHistory {
  int? id;
  String? bookingNumber;
  String? startDate;
  String? endDate;
  String? propertyName;
  String? slug;
  String? propertyImage;
  String? propertySpaceName;
  String? propertyAvgRating;
  int? guestId;
  int? reviewId;
  int? reviewCount;
  int? propertyId;
  String? guestName;
  String? guestProfileSrc;
  String? guestJoinDate;
  String? cancelledBy;
  BookingHistory(
      this.id,
      this.bookingNumber,
      this.startDate,
      this.endDate,
      this.propertyName,
      this.slug,
      this.propertyImage,
      this.propertySpaceName,
      this.propertyAvgRating,
      this.guestId,
      this.guestName,
      this.guestProfileSrc,
      this.guestJoinDate,
      this.reviewId,
      this.propertyId,
      this.reviewCount,this.cancelledBy);

  factory BookingHistory.fromJson(Map<String, dynamic> json) {
    return BookingHistory(
        json['id'],
        json['booking_number'],
        json['start_date'],
        json['end_date'],
        json['propertyName'],
        json['slug'],
        json['propertyImage'],
        json['propertySpaceName'],
        json['propertyAvg Rating'] is int ?json['propertyAvgRating'].toString(): json['propertyAvgRating'],
        json['guest_id'],
        json['guest_name'],
        json['guest_profilesrc'],
        json['guest_createdat'],
        json['rating_id'],
        json['property_id'],
        json['reviewCount'],
        json['cancelled_by']
    );
  }
  }