class TicketDetailModel {
  int? createdBy;
  String? subject;
  String? ticketNumber;
  String? status;
  List? type;
  List<Comments>? comments;
  Attachment? attachment;

  TicketDetailModel(
      {this.createdBy,
        this.subject,
        this.ticketNumber,
        this.status,
        this.type,
        this.comments,
        this.attachment});

  TicketDetailModel.fromJson(Map<String, dynamic> json) {
    createdBy = json['created_by'];
    subject = json['subject'];
    ticketNumber = json['ticket_number'];
    status = json['status'];
    type = json['type'];
    if (json['comments'] != null) {
      comments = json['comments'].map<Comments>((item)=>(Comments.fromJson(item))).toList();
    }
    if(json['attachment']!=null){
      attachment = Attachment.fromJson(json['attachment']);
    }
  }
}

class Comments {
  int? id;
  String? ticketNumber;
  String? userName;
  int? userId;
  String? comment;
  String? createdAt;
  String? updatedAt;

  Comments(
      {this.id,
        this.ticketNumber,
        this.userName,
        this.userId,
        this.comment,
        this.createdAt,
        this.updatedAt});

  Comments.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    ticketNumber = json['ticket_number'];
    userName = json['user_name'];
    userId = json['user_id'];
    comment = json['comment'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}
class Attachment{
  String? fileName;
  String? filePath;
  Attachment.fromJson(Map<String, dynamic> json) {
    fileName = json['file_name'];
    filePath = json['file_path'];
  }
}