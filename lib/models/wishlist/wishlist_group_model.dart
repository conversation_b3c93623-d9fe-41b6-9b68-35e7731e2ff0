class WishlistGroupModel {
  int? id;
  String? code;
  String? name;
  String? shareWith;
  String? link;
  List<WishlistProperties>? wishlistProperties;

  WishlistGroupModel(
      {this.id,
        this.code,
        this.name,
        this.shareWith,
        this.link,
        this.wishlistProperties});

  WishlistGroupModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    code = json['code'];
    name = json['name'];
    shareWith = json['share_with'];
    link = json['link'];
    if (json['wishlist_properties'] != null) {
      wishlistProperties = <WishlistProperties>[];

      json['wishlist_properties'].map((v)=>wishlistProperties!.add(WishlistProperties.fromJson(v))).toList();
    }
  }
}

class WishlistProperties {
  int? propertyId;
  int? wishlistNameId;
  String? coverPhoto;

  WishlistProperties({this.propertyId, this.wishlistNameId, this.coverPhoto});

  WishlistProperties.fromJson(Map<String, dynamic> json) {
    propertyId = json['property_id'];
    wishlistNameId = json['wishlist_name_id'];
    coverPhoto = json['cover_photo'];
  }
}