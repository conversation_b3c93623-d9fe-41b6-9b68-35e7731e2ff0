import 'package:darent/utils/constants.dart';
import './productDetailModel.dart';

class ReservationDetailModel {
  int? id;
  String? condition;
  String? departure;
  int? propertyId;
  String? bookingCode;
  String? userName;
  String? cancelPolicy;
  String? hostNumber;
  String? profile;
  int? joined;
  DateTime? startDate;
  DateTime? endDate;
  String? propertyName;
  int? guest;
  String? userAddress;
  String? status;
  String? userRating;
  String? checkinTime;
  String? checkoutTime;
  double? total;
  double? subTotal;
  double? perNight;
  int? cleaningFee;
  double? guestFee;
  int? chatHeadId;
  int? totalNights;
  String? propertyAddress;
  double? latitude;
  double? longitude;
  List<String>? photos;
  String? unitCode;
  double? serviceFee;
  double? securityFee;
  String? paymentType;
  bool? hasDiscount;
  String? slug;
  List<Amenity>? houseRules;
  String? city;
  double? basePrice;
  double? basePriceWithDiscount;
  double? serviceFeeCleaning;
  double? serviceFeeSecurity;
  double? additionalGuest;
  double? ivaTax;
  double? accommodationTax;
  bool? rated;
  String? description;
  bool? hasPromo;
  String? propertyType;
  int? adults;
  int? children;
  double? totalDiscount;
  double? walletDeduction;
  bool? propertyStatus;
  String? paymentGetway;
  String? tamayouzId;
  String? bookingType;
  int? hostId;
  String? discountType;
  num? youSaved;

  ReservationDetailModel(
      {this.id,
        this.condition,
        this.departure,
        this.propertyId,
        this.bookingCode,
        this.userName,
        this.cancelPolicy,
        this.hostNumber,
        this.profile,
        this.joined,
        this.startDate,
        this.endDate,
        this.propertyName,
        this.guest,
        this.userAddress,
        this.status,
        this.userRating,
        this.checkinTime,
        this.checkoutTime,
        this.total,
        this.subTotal,
        this.perNight,
        this.cleaningFee,
        this.guestFee,
        this.chatHeadId,
        this.totalNights,
        this.propertyAddress,
        this.latitude,
        this.longitude,
        this.photos,
        this.unitCode,
        this.serviceFee,
        this.securityFee,
        this.paymentType,
        this.hasDiscount,
        this.slug,
        this.houseRules,
        this.city,
        this.basePrice,
        this.basePriceWithDiscount,
        this.serviceFeeCleaning,
        this.serviceFeeSecurity,
        this.additionalGuest,this.ivaTax,this.accommodationTax,this.rated,this.description,this.hasPromo,this.propertyType,this.adults,this.children,
        this.walletDeduction,
        this.totalDiscount,this.propertyStatus,this.paymentGetway,this.tamayouzId,this.bookingType,this.hostId,this.discountType,this.youSaved});

  ReservationDetailModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    condition = json['condition'];
    departure = json['departure'];
    propertyId = json['property_id'];
    bookingCode = json['booking_code'];
    userName = json['user_name'];
    cancelPolicy = json['cancel_policy'];
    hostNumber = json['host_number'];
    profile = json['profile'];
    joined = json['joined'];
    startDate = json['start_date']==null?null:formDateFormat.parse(json['start_date']);
    endDate = json['end_date']==null?null:formDateFormat.parse(json['end_date']);
    propertyName = json['property_name'];
    guest = json['guest'];
    userAddress = json['user_address'];
    status = json['status'];
    userRating = json['user_rating'].toString();
    checkinTime = json['checkin_time'];
    checkoutTime = json['checkout_time'];
    total = json['total'] is int?json['total'].toDouble():json['total'];
    subTotal = json['sub_total'] is int?json['sub_total'].toDouble():json['sub_total'];
    perNight = json['per_night'] is int?json['per_night'].toDouble():json['per_night'];
    cleaningFee = json['cleaning_fee'];
    guestFee = json['guest_fee'] is int?json['guest_fee'].toDouble():json['guest_fee'];
    chatHeadId = json['chat_head_id'];
    totalNights = json['total_nights'];
    propertyAddress = json['property_address'];
    latitude = json['latitude'] is String?double.parse(json['latitude']):json['latitude'];
    longitude = json['longitude'] is String?double.parse(json['longitude']):json['longitude'];
    photos = json['photos'].map<String>((item)=>item['photo'] as String).toList();
    unitCode = json['unit_code'];
    serviceFee = json['service_fee'] is int?json['service_fee'].toDouble():json['service_fee']??0.0;
    securityFee = json['security_fee'] is int?json['security_fee'].toDouble():json['security_fee'] is String?double.parse(json['security_fee']): json['security_fee']??0.0;
    paymentType = json['payment_type'];
    hasDiscount = json['has_discount'];
    slug = json['slug'];
    houseRules = json['houserule_amenities'].map<Amenity>((item)=>Amenity.fromJson(item)).toList();
    city= json['city'];
    basePrice= json['base_price'] is int?json['base_price'].toDouble():json['base_price']??0.0;
    basePriceWithDiscount= json['base_price_with_discount'] is int?json['base_price_with_discount'].toDouble():json['base_price_with_discount']??0.0;
    serviceFeeCleaning= json['service_fee_on_cleaning'] is int?json['service_fee_on_cleaning'].toDouble():json['service_fee_on_cleaning']??0.0;
    serviceFeeSecurity= json['service_fee_security'] is int?json['service_fee_security'].toDouble():json['service_fee_security']??0.0;
    additionalGuest= json['additional_guest'] is int?json['additional_guest'].toDouble():json['additional_guest']??0.0;
    ivaTax= json['iva_tax'] is int?json['iva_tax'].toDouble():json['iva_tax']??0.0;
    accommodationTax= json['accommodation_tax'] is int?json['accommodation_tax'].toDouble():json['accommodation_tax']??0.0;
    rated= json['rated']==true;
    description=json['description'];
    hasPromo=json['has_promo'];
    propertyType=json['property_type'];
    adults=json['adults'];
    children=json['children'];
    walletDeduction= json['wallet_deduction'] == null
        ? 0.0
        : json['wallet_deduction'] is int
        ? double.parse(json['wallet_deduction'].toString())
        : json['wallet_deduction'] is String
        ? double.parse(json['wallet_deduction'])
        : json['wallet_deduction'];
    totalDiscount = json['total_discount'] is int?json['total_discount'].toDouble():json['total_discount'] is String?double.parse(json['total_discount']): json['total_discount']??0.0;
    propertyStatus = json['property_status']??true;
    paymentGetway = json['payment_getway'];
    tamayouzId = json['tamayouz_id'];
    bookingType = json['booking_type']??'';
    hostId = json['host_id'];
    discountType = json['discount_type'];
    youSaved = json['you_saved'];
  }
}