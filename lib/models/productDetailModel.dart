import 'package:darent/helperMethods/globalHelpers.dart';

class PropertyDetail {
  int? id;
  String? name;
  String? slug;
  String? urlName;
  int? hostId;
  int? bedrooms;
  int? beds;
  int? bedType;
  num? bathrooms;
  PropertyType? propertyType;
  int? spaceType;
  int? adultGuest;
  int? childrenGuest;
  String? bookingType;
  String? cancellation;
  String? status;
  int? recomended;
  int? visibility;
  String? deletedAt;
  String? createdAt;
  String? updatedAt;
  int? stepsCompleted;
  String? spaceTypeName;
  // This key gives us space_type_name as per selected language
  String? spaceTypeLang;
  String? propertyTypeName;
  String? propertyTitle;
  String? propertyPhoto;
  String? hostName;
  bool? bookMark;
  bool? wishlist;
  bool? isNewLable;
  int? reviewsCount;
  int? overallRating;
  String? coverPhoto;
  String? avgRating;
  PropertyDescription? propertyDescription;
  PropertyAddress? propertyAddress;
  List<Amenity>? amenities;
  List<Amenity>? safetyAmenities;
  List<PropertyPhotos>? propertyPhotos;
  String? checkin;
  String? checkout;
  int? minNights;
  int? maxNights;
  ReviewAverage? reviewAverage;
  PropertyPrice? propertyPrice;
  List<DateTime>? unavailabeDates;
  List<Amenity>? houseruleAmenities;
  int? discount;
  double? discountedAmount;
  DateTime? hostJoiningDate;
  String? hostProfile;
  int? chatHeadId;
  String? propertyCode;
  String? licenseNo;

  PropertyDetail(
      {this.id,
      this.name,
      this.slug,
      this.urlName,
      this.hostId,
      this.bedrooms,
      this.beds,
      this.bedType,
      this.bathrooms,
      this.propertyType,
      this.spaceType,
      this.adultGuest,
      this.childrenGuest,
      this.bookingType,
      this.cancellation,
      this.status,
      this.recomended,
      this.visibility,
      this.deletedAt,
      this.createdAt,
      this.updatedAt,
      this.stepsCompleted,
      this.spaceTypeName,
      this.propertyTypeName,
      this.spaceTypeLang,
      this.propertyPhoto,
      this.hostName,
      this.bookMark,
      this.wishlist,
      this.isNewLable,
      this.reviewsCount,
      this.overallRating,
      this.coverPhoto,
      this.avgRating,
      this.propertyDescription,
      this.propertyAddress,
      this.amenities,
      this.safetyAmenities,
      this.propertyPhotos,
      this.checkin,
      this.checkout,
      this.minNights,
      this.maxNights,
      this.reviewAverage,
      this.propertyPrice,
      this.unavailabeDates,
      this.propertyTitle,
      this.houseruleAmenities,
      this.discount,
      this.discountedAmount,
      this.hostJoiningDate,
      this.hostProfile,
      this.chatHeadId,
      this.licenseNo});

  PropertyDetail.fromJson(Map<String, dynamic> json) {
    id = json['result']['id'];
    name = json['result']['name'];
    slug = json['result']['slug'];
    urlName = json['result']['url_name'];
    hostId = json['result']['host_id'];
    bedrooms = json['result']['bedrooms'];
    beds = json['result']['beds'];
    bedType = json['result']['bed_type'];
    bathrooms = json['result']['bathrooms'];
    propertyType = json['result']['property_type'] != null
        ? PropertyType.fromJson(json['result']['property_type'])
        : null;
    spaceType = json['result']['space_type'];
    adultGuest = json['result']['adult_guest'] ?? 0;
    childrenGuest = json['result']['children_guest'] ?? 0;
    bookingType = json['result']['booking_type'];
    cancellation = json['result']['cancellation'];
    status = json['result']['status'];
    recomended = json['result']['recomended'];
    visibility = json['result']['visibility'];
    deletedAt = json['result']['deleted_at'];
    createdAt = json['result']['created_at'];
    updatedAt = json['result']['updated_at'];
    minNights = json['result']['min_nights'] ?? 5;
    maxNights = json['result']['max_nights'] ?? 6;
    reviewAverage = json['result']['review_average'] != null
        ? ReviewAverage.fromJson(json['result']['review_average'])
        : null;
    stepsCompleted = json['result']['steps_completed'];
    spaceTypeName = json['result']['space_type_name'];
    propertyTypeName = json['result']['property_type_name'];
    spaceTypeLang = json['result']['space_type_lang'] ?? "";
    propertyPhoto = json['result']['property_photo'];
    hostName = json['result']['host_name'];
    bookMark = json['result']['book_mark'] is bool
        ? json['result']['book_mark']
        : json['result']['book_mark'] == "Active";
    wishlist = json['result']['wishlist'] == true;
    isNewLable =  json['result']['is_new_lable'] == true;
    reviewsCount = json['result']['reviews_count'];
    overallRating = json['result']['overall_rating'];
    coverPhoto = json['result']['cover_photo'];
    propertyTitle = json['result']['property_title'] is int
        ? json['result']['property_title'].toString()
        : json['result']['property_title'];
    avgRating = json['result']['rating_avg'] is int
        ? json['result']['rating_avg'].toString()
        : json['result']['rating_avg'] ?? "0";
    propertyAddress = json['result']['property_address'] != null
        ? PropertyAddress.fromJson(json['result']['property_address'])
        : null;
    propertyDescription = json['result']['property_description'] != null
        ? PropertyDescription.fromJson(json['result']['property_description'])
        : null;
    if (json['amenities'] != null) {
      List filterAmenities = json['amenities']
          .where((item) =>
              json['result']['amenities'].contains("${item['id']}") as bool)
          .toList();
      amenities = filterAmenities
          .map<Amenity>((item) => Amenity.fromJson(item))
          .toList();
    }
    safetyAmenities = json['safety_amenities'].map<Amenity>((item) {
      return Amenity.fromJson(item);
    }).toList();
    houseruleAmenities = json['houserule_amenities'].map<Amenity>((item) {
      return Amenity.fromJson(item);
    }).toList();

    propertyPhotos = json['property_photos']
        .map<PropertyPhotos>((item) => PropertyPhotos.fromJson(item))
        .toList();
    if (json['checkin'] != null && json['checkin'].isNotEmpty) {
      checkin = GlobalHelper.to12HoursString(json['checkin']);
    }
    if (json['checkout'] != null && json['checkout'].isNotEmpty) {
      checkout = GlobalHelper.to12HoursString(json['checkout']);
    }
    discount = json['result']['discount'];
    discountedAmount = json['result']['discounted_amount'] is int
        ? json['result']['discounted_amount'].toDouble()
        : json['result']['discounted_amount'];
    propertyPrice = PropertyPrice.fromJson(json['property_price'][0]);
    if (json['available_dates'] == null) {
      unavailabeDates = null;
    } else {
      unavailabeDates = json['available_dates']
          .map<DateTime>((item) => DateTime.parse(item))
          .toList();
    }
    if (json['result']['users'] != null) {
      hostJoiningDate = json['result']['users']['created_at'] != null
          ? DateTime.parse(json['result']['users']['created_at'])
          : DateTime.now();
      hostProfile = json['result']['users']['profile_src'];
    }
    chatHeadId = json['result']['chat_head_id'];
    propertyCode = json['result']['property_code'];
    licenseNo = json['license_no'];
  }

  bool isInstantBooking() => bookingType == 'instant';
}

class PropertyDescription {
  String? summary, summaryAr;
  PropertyDescription({this.summary, this.summaryAr});
  factory PropertyDescription.fromJson(Map json) => PropertyDescription(
      summary: json['summary'], summaryAr: json['summary_ar']);
}

class PropertyAddress {
  int? id;
  int? propertyId;
  String? addressLine1;
  String? addressLine2;
  double? latitude;
  double? longitude;
  String? city;
  String? state;
  String? country;
  String? postalCode;
  NearbyCoordinates? nearbyCoordinates;

  PropertyAddress({
    this.id,
    this.propertyId,
    this.addressLine1,
    this.addressLine2,
    this.latitude,
    this.longitude,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.nearbyCoordinates,
  });

  PropertyAddress.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    propertyId = json['property_id'];
    addressLine1 = json['address_line_1'];
    addressLine2 = json['address_line_2'];
    latitude = double.parse(json['latitude']);
    longitude = double.parse(json['longitude']);
    city = json['city'];
    state = json['state'];
    country = json['country'];
    postalCode = json['postal_code'];
    nearbyCoordinates = json["nearbyCoordinates"] != null
        ? NearbyCoordinates.fromJson(json["nearbyCoordinates"])
        : null;
  }
}

class NearbyCoordinates {
  final double latitude;
  final double longitude;

  NearbyCoordinates({
    required this.latitude,
    required this.longitude,
  });

  factory NearbyCoordinates.fromJson(Map<String, dynamic> json) =>
      NearbyCoordinates(
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "latitude": latitude,
        "longitude": longitude,
      };
}

class PropertyPrice {
  int? id;
  int? propertyId;
  int? cleaningFee;
  int? guestAfter;
  int? guestFee;
  int? securityFee;
  double? price;
  int? weekendPrice;
  int? weeklyDiscount;
  int? monthlyDiscount;
  String? currencyCode;
  int? originalCleaningFee;
  int? originalGuestFee;
  int? originalPrice;
  int? originalWeekendPrice;
  int? originalSecurityFee;
  String? defaultCode;
  String? defaultSymbol;
  double serviceFee = 0;
  double? perNightPrice;
  double perNightDiscountedPrice = 0;

  PropertyPrice(
      {this.id,
      this.propertyId,
      this.cleaningFee,
      this.guestAfter,
      this.guestFee,
      this.securityFee,
      this.price,
      this.weekendPrice,
      this.weeklyDiscount = 0,
      this.monthlyDiscount = 0,
      this.currencyCode,
      this.originalCleaningFee,
      this.originalGuestFee,
      this.originalPrice,
      this.originalWeekendPrice,
      this.originalSecurityFee,
      this.defaultCode,
      this.defaultSymbol,
      this.perNightPrice,
      this.perNightDiscountedPrice = 0});

  PropertyPrice.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    propertyId = json['property_id'];
    cleaningFee = json['cleaning_fee'];
    guestAfter = json['guest_after'];
    guestFee = json['guest_fee'];
    securityFee = json['security_fee'];
    price = json['price'].toDouble();
    weekendPrice = json['weekend_price'];
    weeklyDiscount = json['weekly_discount'];
    monthlyDiscount = json['monthly_discount'];
    currencyCode = json['currency_code'];
    originalCleaningFee = json['original_cleaning_fee'];
    originalGuestFee = json['original_guest_fee'];
    originalPrice = json['original_price'];
    originalWeekendPrice = json['original_weekend_price'];
    originalSecurityFee = json['original_security_fee'];
    defaultCode = json['default_code'];
    defaultSymbol = json['default_symbol'];
    serviceFee =
        (json['cleaning_fee'] + json['guest_fee'] + json['security_fee'])
            .toDouble();
  }
}

class PropertyType {
  String? name;
  String? nameAr;
  PropertyType({this.name, this.nameAr});

  PropertyType.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    nameAr = json['name_ar'];
  }
}

class Amenity {
  String? titleAr;
  String? title;
  int? id;
  String? symbol;
  int? status;
  String? icon_image;

  Amenity(
      {this.titleAr,
      this.title,
      this.id,
      this.symbol,
      this.status,
      this.icon_image});

  Amenity.fromJson(Map<String, dynamic> json) {
    titleAr = json['title_ar'];
    title = json['title'];
    id = json['id'];
    symbol = json['symbol'];
    status = json['status'];
    icon_image = json['icon_image'];
  }
}

class PropertyPhotos {
  int? id;
  int? propertyId;
  String? photo;
  String? message;
  int? coverPhoto;
  int? serial;

  PropertyPhotos(
      {this.id,
      this.propertyId,
      this.photo,
      this.message,
      this.coverPhoto,
      this.serial});

  PropertyPhotos.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    propertyId = json['property_id'];
    photo = json['photo'];
    message = json['message'];
    coverPhoto = json['cover_photo'];
    serial = json['serial'];
  }
}

class ReviewAverage {
  double? cleanliness;
  double? location;
  double? communication;
  double? accuracy;
  double? darentService;
  double? darentRecomended;
  double? avgTotal;
  int? totalReviews;
  ReviewAverage(
      {this.cleanliness,
      this.location,
      this.communication,
      this.accuracy,
      this.avgTotal,
      this.totalReviews,
      this.darentService,
      this.darentRecomended});
  ReviewAverage.fromJson(Map<String, dynamic> json) {
    cleanliness = json['cleanliness'] is String
        ? double.parse(json['cleanliness'])
        : json['cleanliness'];
    location = json['location'] is String
        ? double.parse(json['location'])
        : json['location'];
    communication = json['communication'] is String
        ? double.parse(json['communication'])
        : json['communication'];
    accuracy = json['accuracy'] is String
        ? double.parse(json['accuracy'])
        : json['accuracy'];
    avgTotal = json['avg_total'] is String
        ? double.parse(json['avg_total'])
        : json['avg_total'] is int
            ? json['avg_total'].toDouble()
            : json['avg_total'];
    totalReviews = json['total_reviews'] is String
        ? double.parse(json['total_reviews'])
        : json['total_reviews'];
    darentService = json['darent_service'] is String
        ? double.parse(json['darent_service'])
        : json['darent_service'];
    darentRecomended = json['darent_recomended'] is String
        ? double.parse(json['darent_recomended'])
        : json['darent_recomended'];
  }
  Map<String, dynamic> toJson() {
    return {
      'cleanliness': cleanliness,
      'location': location,
      'communication': communication,
      'accuracy': accuracy,
      'avg_total': avgTotal,
      'total_reviews': totalReviews,
    };
  }
}
