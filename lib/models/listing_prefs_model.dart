
class ListingPrefsModel {
  GeneralModel whatsapp;
  GeneralModel sms;
  GeneralModel email;

  ListingPrefsModel(
     this.whatsapp,
     this.sms,
     this.email,
  );


  factory ListingPrefsModel.fromJson(Map<String, dynamic> json) => ListingPrefsModel(
    GeneralModel.fromJson(json["whatsapp"]),
    GeneralModel.fromJson(json["sms"]),
    GeneralModel.fromJson(json["email"]),
  );

  Map<String, dynamic> toJson() => {
    "whatsapp": whatsapp.toJson(),
    "sms": sms.toJson(),
    "email": email.toJson(),
  };
}

class  BankModel{
  String? name;
  String? arName;
  String? bicCode;
  bool isChecked = false;

  BankModel(
      this.name,
      this.arName,
      this.bicCode
      );

  factory BankModel.fromJson(Map<String, dynamic> json) => BankModel(
      json["title"]??"",
      json["ar_title"]??"",
      json["bic_code"]??"",
  );

  Map<String, dynamic> toJson() => {
    "title": name,
    "bic_code": bicCode,
  };

}

class GeneralModel {
  String? name;
  String? nameAr;
  String? image;
  bool? isChecked;

  GeneralModel(
      this.name,
      this.nameAr,
      this.image,
      {this.isChecked = false,}
      );

   factory GeneralModel.fromJson(Map<String, dynamic> json) => GeneralModel(
       json["name"],
       json["name_ar"],
       json["image"]
   );

  Map<String, dynamic> toJson() => {
    "name": name,
    "name_ar": nameAr,
    "image": image,
  };
}