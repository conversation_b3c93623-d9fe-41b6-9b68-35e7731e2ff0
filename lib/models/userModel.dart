class UserModel {
  int? id;
  String? uuid;
  String first_name;
  String last_name;
  String? email;
  String? phone;
  String? formatted_phone;
  String? carrier_code;
  String? default_country;
  String? profile_image;
  int? balance;
  String? status;
  String? google_id;
  String? old_id;
  String? profile_src;
  String? dateofbirth;
  int? isTermsAndConditionMarked;
  String? gender;
  String? location;
  String? about;
  String? createdAt;
  String? token;
  bool isVerified;
  int? document;
  String? phoneVerified;
  String? communicationPrefs;
  bool contactMethodAdded;
  // String? avgRating;
  String lang;
  Verification? userVerification;
  int? email_otp;
  bool yaqeenVerified;
  bool showYaqeenReservation;
  Bank? bank;
  // int? is_email_verified;
  UserModel(
      this.id,
      this.uuid,
      this.first_name,
      this.last_name,
      this.email,
      this.phone,
      this.formatted_phone,
      this.carrier_code,
      this.default_country,
      this.profile_image,
      this.balance,
      this.status,
      this.google_id,
      this.old_id,
      this.profile_src,
      this.dateofbirth,
      this.isTermsAndConditionMarked,
      this.gender,
      this.location,
      this.about,
      this.createdAt,
      this.token,
      this.isVerified,
      this.document,
      this.phoneVerified,
      this.communicationPrefs,
      this.contactMethodAdded,
      // this.avgRating,
      this.lang,
      this.userVerification,
      this.email_otp,
      this.yaqeenVerified,
      this.showYaqeenReservation,
      this.bank
      );
  factory UserModel.fromLocalJson(Map<String, dynamic> json) => UserModel(
      json['id'],
      json['uuid'],
      json['first_name'],
      json['last_name'],
      json['email'],
      json['phone'],
      json['formatted_phone'],
      json['carrier_code'],
      json['default_country'],
      json['profile_image'],
      json['balance'],
      json['status'],
      json['google_id'],
      json['old_id'],
      json['profile_src'],
      json['date_of_birth'],
      json['terms_condition'],
      json['gender'],
      json['location'],
      json['about'],
      json['created_at'],
      json['token'],
      json['verified'] == true || json['verified'] == 1,
      json['license_code']??0,
      json['phoneVerified'],
    json['communication_preference'] ?? "",
    json['communication_preference'] != null && json['communication_preference'] != ""
        ? true : false,
      // json['avg_rating'],
      json['lang'] ?? "en",
      json['users_verification'] != null
          ? Verification.fromJson(json['users_verification'])
          : null,
      json['email_otp'],
      json['yaqeen_verified']==true,
      (json['show_yaqeen_reservation']??false)==true,
      json['bank'] != null
          ? Bank.fromJson(json['bank'])
          : null
  );
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
        json['user']['id'],
        json['user']['uuid'],
        json['user']['first_name'],
        json['user']['last_name'],
        json['user']['email'],
        json['user']['phone'],
        json['user']['formatted_phone'],
        json['user']['carrier_code'],
        json['user']['default_country'],
        json['user']['profile_image'],
        json['user']['balance'],
        json['user']['status'],
        json['user']['google_id'],
        json['user']['old_id'],
        json['user']['profile_src'],
        json['user']['date_of_birth'],
        json['user']['terms_condition'],
        json['user']['gender'],
        json['user']['location'],
        json['user']['about'],
        json['user']['created_at'],
        json['token'],
        json['user']['verified'] == true || json['user']['verified'] == 1,
        json['user']['license_code'] ??0,
        json['user']['phoneVerified'],
        json['user']['communication_preference']??"",
        json['user']['communication_preference'] != null && json['user']['communication_preference'] != ""
            ? true : false,
        // json['user']['avg_rating'],
        json['user']['lang'] ?? "en",
        json['user']['users_verification'] != null
            ? Verification.fromJson(json['user']['users_verification'])
            : null,
        json['user']['email_otp'],
        json['user']['is_elm_verified'],
        json['user']['show_yaqeen_reservation']??false,
        json['bank'] != null
            ? Bank.fromJson(json['bank'])
            : null,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'uuid': uuid,
      'first_name': first_name,
      'last_name': last_name,
      'email': email,
      'phone': phone,
      'formatted_phone': formatted_phone,
      'carrier_code': carrier_code,
      'default_country': default_country,
      'profile_image': profile_image,
      'balance': balance,
      'status': status,
      'google_id': google_id,
      'old_id': old_id,
      'profile_src': profile_src,
      'date_of_birth': dateofbirth,
      'terms_condition': isTermsAndConditionMarked,
      'gender': gender,
      'location': location,
      'about': about,
      'created_at': createdAt,
      'token': token,
      'verified': isVerified,
      'license_code': document,
      'communication_preference' : communicationPrefs,
      // 'avg_rating': avgRating,
      'lang': lang,
      'users_verification': userVerification,
      'email_otp': email_otp,
      'yaqeen_verified': yaqeenVerified==true,
      'show_yaqeen_reservation': showYaqeenReservation == true,
      'bank' : bank?.toJson()??{},
      // 'is_email_verified': is_email_verified
    };
  }
  toAttributeJson(){
    return <String,dynamic>{
      'First Name':first_name,
      'Last Name':last_name,
      'Gender':gender??'Male',
      'Email':email??'',
      'Phone Number':formatted_phone??'',
    };
  }
}

class Bank {
  final int? id;
  final int? userId;
  String? accountTitle;
   String? iban;
  final String swiftCode;
   String? accountNumber;
  final String? routingNo;
  String? bankName;
  final String? branchName;
  final String? branchCity;
  final String? branchAddress;
  String? phone;
  String? carrierCode;
  final String? description;
  final String? country;
  final String? logo;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Bank({
    required this.id,
    required this.userId,
    required this.accountTitle,
    required this.iban,
    required this.swiftCode,
    required this.accountNumber,
    required this.routingNo,
    required this.bankName,
    required this.branchName,
    required this.branchCity,
    required this.branchAddress,
    required this.phone,
    required this.carrierCode,
    required this.description,
    required this.country,
    required this.logo,
    required this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory Bank.fromJson(Map<String, dynamic> json) => Bank(
    id: json["id"] ?? 0,
    userId: json["user_id"] ?? 0,
    accountTitle: json["account_title"] ?? "",
    iban: json["iban"] ?? "",
    swiftCode: json["swift_code"] ?? "",
    accountNumber: json["account_number"] ?? "",
    routingNo: json["routing_no"] ?? "",
    bankName: json["bank_name"] ?? "",
    branchName: json["branch_name"] ?? "",
    branchCity: json["branch_city"] ?? "",
    branchAddress: json["branch_address"] ?? "",
    phone: json["phone"] ?? "",
    carrierCode: json["carrier_code"] ?? "",
    description: json["description"] ?? "",
    country: json["country"] ?? "",
    logo: json["logo"]?? "",
    status: json["status"] ?? '',
    createdAt: json["created_at"]!=null?DateTime.parse(json["created_at"]):null,
    updatedAt: json["updated_at"]!=null?DateTime.parse(json["updated_at"]):null,
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "user_id": userId,
    "account_title": accountTitle,
    "iban": iban,
    "swift_code": swiftCode,
    "account_number": accountNumber,
    "routing_no": routingNo,
    "bank_name": bankName,
    "branch_name": branchName,
    "branch_city": branchCity,
    "branch_address": branchAddress,
    "phone": phone,
    "description": description,
    "country": country,
    "logo": logo,
    "status": status,
    // "created_at": createdAt!.toIso8601String(),
    // "updated_at": updatedAt!.toIso8601String(),
  };
}


class Verification {
  int? id;
  int? userId;
  String? email;
  String? identity;
  String? facebook;
  String? google;
  String? linkedin;
  String? phone;
  int? verifiedNumber;
  Verification(
      {this.id,
        this.userId,
        this.email,
        this.identity,
        this.facebook,
        this.google,
        this.linkedin,
        this.phone,
        this.verifiedNumber});
  Verification.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    email = json['email'];
    identity = json['identity'];
    facebook = json['facebook'];
    google = json['google'];
    linkedin = json['linkedin'];
    phone = json['phone'];
    verifiedNumber = json['verified_number'];
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['user_id'] = userId;
    data['email'] = email;
    data['identity'] = identity;
    data['facebook'] = facebook;
    data['google'] = google;
    data['linkedin'] = linkedin;
    data['phone'] = phone;
    data['verified_number'] = verifiedNumber;
    return data;
  }
}