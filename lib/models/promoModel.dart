class PromoModel {
  int? id;
  String? code;
  String? title;
  String? description;
  int? percentage;
  int? discountUpto;
  int? maxUsage;
  int? perUserUsage;
  int? forNewCustomer;
  String? startDate;
  String? expiryDate;
  late int usageCount;
  bool isExpired = false;
  List<CodeProperties>? codeProperties;

  PromoModel(
      {this.id,
      this.code,
      this.title,
      this.description,
      this.percentage,
      this.discountUpto,
      this.maxUsage,
      this.perUserUsage,
      this.forNewCustomer,
      this.startDate,
      this.expiryDate,
      this.usageCount=0,
      this.codeProperties,
      this.isExpired = false});

  PromoModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    code = json['code'];
    title = json['title'];
    description = json['description'];
    percentage = json['percentage'];
    discountUpto = json['discount_upto'];
    maxUsage = json['max_usage'];
    perUserUsage = json['per_user_usage'];
    forNewCustomer = json['for_new_customer'];
    startDate = json['start_date'];
    expiryDate = json['expiry_date'];
    usageCount = json['usage_count'];
    if (json['code_properties'] != null) {
      codeProperties = <CodeProperties>[];
      json['code_properties'].forEach((v) {
        codeProperties!.add(CodeProperties.fromJson(v));
      });
    }
    isExpired = json['isExpired'];
  }
}

class CodeProperties {
  int? property;

  CodeProperties({this.property});

  CodeProperties.fromJson(Map<String, dynamic> json) {
    property = json['property'];
  }
}

class Pagination {
  int? total;
  int? count;
  int? perPage;
  int currentPage;
  int? totalPages;

  Pagination(
      {this.total=1,
      this.count=1,
      this.perPage=1,
      this.currentPage=1,
      this.totalPages=1});

  factory Pagination.fromJson(Map<String, dynamic> json) {
    return Pagination(
    total : json['total'],
    count : json['count'],
    perPage : json['per_page'],
    currentPage : json['current_page'],
    totalPages : json['total_pages']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['total'] = total;
    data['count'] = count;
    data['per_page'] = perPage;
    data['current_page'] = currentPage;
    data['total_pages'] = totalPages;
    return data;
  }
}
