class Attributes {
  static Map<String,dynamic>? guestAttributes;
  static setGuestAttributes(Map? json,[Map<String,dynamic>? userJson]) {
    guestAttributes = {
      "Current Reservations": json?['current_reservations'] ?? 0,
      "Total Price": json?['total_price'] is String ? double.parse(json!['total_price']) : (json?['total_price'] ?? 0),
      "Cancelled Reservations": json?['cancelled_reservations'] ?? 0,
      "All Reservations": json?['all_reservations'] ?? 0,
    };
    if(userJson!=null){
      guestAttributes!.addAll(userJson);
    }
  }
}
