class ReceiptModel{
  String? propertyName;
  String? propertyAddress;
  String? city;
  String? countryName;
  String? state;
  String? postalCode;
  String? destination;
  String? hostName;
  String? guestName;
  int? receiptNo;
  String? totalPayment;
  String? payementReceived;
  String? vatNumber;
  String? subTotal;
  List<DatePrice>? datePrice;
  String? additionalTitle;
  String? title;
  String? checkIn;
  String? checkOut;
  String? currencySymbol;

  ReceiptModel(
      {this.propertyName,
      this.propertyAddress,
      this.city,
      this.countryName,
      this.state,
      this.postalCode,
      this.destination,
      this.hostName,
      this.guestName,
      this.receiptNo,
      this.totalPayment,
      this.payementReceived,
      this.vatNumber,
      this.subTotal,
      this.datePrice,
      this.additionalTitle,
      this.title,
      this.checkIn,
      this.checkOut,
      this.currencySymbol});

  ReceiptModel.fromJson(Map<String, dynamic> json) {
    propertyName = json['property_name'];
    propertyAddress = json['property_address'];
    city = json['city'];
    countryName = json['country_name'];
    state = json['state'];
    postalCode = json['postal_code'];
    destination = json['destination'];
    hostName = json['host_name'];
    guestName = json['guest_name'];
    receiptNo = json['receipt_no'];
    totalPayment = json['total_payment'];
    payementReceived = json['payement_received'];
    vatNumber = json['vat_number'];
    subTotal = json['sub_total'];
    if (json['date_price'] != null) {
      datePrice = <DatePrice>[];
      json['date_price'].forEach((v) {
        datePrice!.add(new DatePrice.fromJson(v));
      });
    }
    additionalTitle = json['additional_title'];
    title = json['title'];
    checkIn = json['checkin'];
    checkOut = json['checkout'];
    currencySymbol = json['currency_symbol'];

  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['property_name'] = this.propertyName;
    data['property_address'] = this.propertyAddress;
    data['city'] = this.city;
    data['country_name'] = this.countryName;
    data['state'] = this.state;
    data['postal_code'] = this.postalCode;
    data['destination'] = this.destination;
    data['host_name'] = this.hostName;
    data['guest_name'] = this.guestName;
    data['receipt_no'] = this.receiptNo;
    data['total_payment'] = this.totalPayment;
    data['payement_received'] = this.payementReceived;
    data['vat_number'] = this.vatNumber;
    data['sub_total'] = this.subTotal;
    if (this.datePrice != null) {
      data['date_price'] = this.datePrice!.map((v) => v.toJson()).toList();
    }
    data['additional_title'] = this.additionalTitle;
    data['title'] = this.title;
    data['checkin'] = this.checkIn;
    data['checkout'] = this.checkOut;
    data['currency_symbol'] = this.currencySymbol;
    return data;
  }
}

class DatePrice {
  int? price;
  String? date;

  DatePrice({this.price, this.date});

  DatePrice.fromJson(Map<String, dynamic> json) {
    price = json['price'];
    date = json['date'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['price'] = this.price;
    data['date'] = this.date;
    return data;
  }
}