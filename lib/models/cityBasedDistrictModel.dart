
import 'dart:convert';

CityBasedDistrictModel cityBasedDistrictModelFromJson(String str) => CityBasedDistrictModel.fromJson(json.decode(str));

String cityBasedDistrictModelToJson(CityBasedDistrictModel data) => json.encode(data.toJson());

class CityBasedDistrictModel {
  final List<Datum> data;

  CityBasedDistrictModel({
    required this.data,
  });

  factory CityBasedDistrictModel.fromJson(Map<String, dynamic> json) => CityBasedDistrictModel(
    data: List<Datum>.from(json["data"].map((x) => Datum.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "data": List<dynamic>.from(data.map((x) => x.toJson())),
  };
}

class Datum {
  final dynamic city;
  final List<String> districts;

  Datum({
    required this.city,
    required this.districts,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
    city: json["city"],
    districts: List<String>.from(json["districts"].map((x) => x)),
  );

  Map<String, dynamic> toJson() => {
    "city": city,
    "districts": List<dynamic>.from(districts.map((x) => x)),
  };
}
