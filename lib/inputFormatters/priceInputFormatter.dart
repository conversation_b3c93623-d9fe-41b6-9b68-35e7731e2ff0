import 'package:flutter/services.dart';

class PriceInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    double? newDouble = double.tryParse(newValue.text);
    if(newDouble==null || newDouble<50){
      return newValue.copyWith(text:"50" ,selection: TextSelection.collapsed(offset: 2));
    }
    return newValue.copyWith(text:newValue.text);
  }
}