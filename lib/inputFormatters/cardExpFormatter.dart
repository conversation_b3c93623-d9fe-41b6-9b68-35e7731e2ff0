import 'package:flutter/services.dart';

class CardExpirationFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
  TextEditingValue oldValue, TextEditingValue newValue) {
  final newValueString = newValue.text;
  String valueToReturn = '';

  for (int i = 0; i < newValueString.length; i++) {
    if (newValueString[i] != '/') valueToReturn += newValueString[i];
    var nonZeroIndex = i + 1;
    final contains = valueToReturn.contains(RegExp(r'\/'));
    if (nonZeroIndex % 2 == 0 &&
        nonZeroIndex != newValueString.length &&
        !(contains)) {
      valueToReturn += '/';
    }
  }

  // Check if the first two digits are greater than 12
  if (valueToReturn.length >= 2) {
    final firstTwoDigits = int.tryParse(valueToReturn.substring(0, 2));
    if (firstTwoDigits != null && firstTwoDigits > 12) {
      valueToReturn = '12' + valueToReturn.substring(2);
    }
  }

  return newValue.copyWith(
    text: valueToReturn,
    selection: TextSelection.fromPosition(
      TextPosition(offset: valueToReturn.length),
    ),
  );
}

}
