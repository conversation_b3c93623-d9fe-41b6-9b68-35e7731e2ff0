import 'package:darent/utils/constants.dart';
import 'package:flutter/services.dart';

class PromoCodeInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    if(newValue.text.isNotEmpty && !alphaNumericRegex8.hasMatch(newValue.text)){
      return oldValue.copyWith(text: oldValue.text,selection: TextSelection.collapsed(offset: oldValue.text.length));
    }
    if (newValue.text.length>15) {
      return oldValue.copyWith(text: oldValue.text,selection: const TextSelection.collapsed(offset: 15));
    }
    return newValue.copyWith(text:newValue.text.toUpperCase());
  }
}