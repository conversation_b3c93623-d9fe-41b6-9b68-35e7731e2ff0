import 'package:flutter/services.dart';

class PercentageInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final input = newValue.text;
    final parsedValue = int.tryParse(input);
    if (parsedValue != null && parsedValue > 100) {
      return newValue.copyWith(text: "100",selection: const TextSelection.collapsed(offset: 3));
    }
    return newValue;
  }
}