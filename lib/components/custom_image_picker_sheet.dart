import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

dynamic imagePickerModes(Function(ImageSource) onOptionSelect) {
  ViewsCommon.showModalBottom(DraggableScrollableSheet(
      minChildSize: .20,
      initialChildSize: .20,
      expand: false,
      builder: (context, scrollController) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildGalleryOption(onOptionSelect),
            SizedBox(width: widthSpace(5.5)),
            buildCameraOption(onOptionSelect),
          ],
        ).paddingOnly(
            left: widthSpace(viewPadding * 2),
            right: widthSpace(viewPadding * 2),
            top: heightSpace(viewPadding));
      }));
}

Widget buildGalleryOption(Function(ImageSource) onOptionSelect) {
  return GestureDetector(
    onTap: () => onOptionSelect(ImageSource.gallery),
    child: Column(
      children: [
        Container(
          decoration: BoxDecoration(
              color: Colors.grey[200],
              border: Border.all(color: Colors.grey, width: 1),
              shape: BoxShape.circle),
          padding: const EdgeInsets.all(8.0), // Adjust the padding as needed
          child: const Icon(
            Icons.photo,
            size: 30.0, // Adjust the size of the icon as needed
            color: Colors.black, // Choose the color of the icon
          ),
        ),
        SizedBox(height: heightSpace(1)),
        Text(Get.find<TranslationHelper>().translations.header.gallery),
      ],
    ),
  );
}

Widget buildCameraOption(Function(ImageSource) onOptionSelect) {
  return GestureDetector(
    onTap: () => onOptionSelect(ImageSource.camera),
    child: Column(
      children: [
        Container(
            decoration: BoxDecoration(
                color: Colors.grey[200],
                border: Border.all(color: Colors.grey, width: 1),
                shape: BoxShape.circle),
            padding: const EdgeInsets.all(8.0),
            child: const Icon(
              Icons.camera_alt,
              size: 30.0, // Adjust the size of the icon as needed
              color: Colors.black,
            )),
        SizedBox(height: heightSpace(1)),
        Text(Get.find<TranslationHelper>().translations.header.camera),
      ],
    ),
  );
}
