import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../utils/sizeconfig.dart';

class ProductsShimmer extends StatelessWidget {
  const ProductsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: <PERSON><PERSON><PERSON>(children: [
          SizedBox(height: heightSpace(3)),
          Container(
            height: heightSpace(31),
            decoration: BoxDecoration(
                color: Colors.black,
                borderRadius: BorderRadius.circular(heightSpace(1.6))
            ),
          ),
          SizedBox(height: heightSpace(2)),
          SizedBox(
              height: heightSpace(1.9),
              child:Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: widthSpace(32),
                      color: Colors.black,
                    ),
                    Container(
                      width: widthSpace(20),
                      color: Colors.black,
                    )
                  ])
          ),
          SizedBox(height: heightSpace(1)),
          Container(
            height: heightSpace(1.8),
            color: Colors.black,
          ),
          Sized<PERSON><PERSON>(height: heightSpace(1)),
          Container(
            height: heightSpace(5),
            color: Colors.black,
          ),
          SizedBox(height: heightSpace(1)),
          Container(
            height: heightSpace(1.8),
            color: Colors.black,
          ),
          SizedBox(height: heightSpace(1)),
          Container(
            height: heightSpace(1.8),
            color: Colors.black,
          ),

        ]));
  }
}
