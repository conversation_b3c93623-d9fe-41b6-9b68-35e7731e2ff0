import 'package:darent/components/custom_text.dart';
import 'package:darent/controllers/property_detail_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/models/homeProperty.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../helperMethods/remote_config.dart';
import '../models/productDetailModel.dart';
import '../utils/routes.dart';

class ProductItemInfo extends StatelessWidget {
  final HomeProperty data;
  final double imgHeight;
  final String noDescription;
  const ProductItemInfo(
      {Key? key,
      required this.data,
      this.imgHeight = 40,
      required this.noDescription})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Get.delete<PropertyDetailController>();
        Get.to(() => PropertyDetail(slug: data.slug!));
        // Get.toNamed('/${Routes.propertySingle}/${data.slug??''}');
      },
      child: Container(
        padding: EdgeInsets.all(widthSpace(2.5)),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(heightSpace(1.8))),
        child: Column(children: [
          Container(
              height: heightSpace(imgHeight),
              alignment: Alignment.topRight,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(heightSpace(1.5)),
                  image: DecorationImage(
                      image: GlobalHelper.buildNetworkImageProvider(url: data.photo??''),
                      fit: BoxFit.cover))),
          SizedBox(height: heightSpace(2)),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CustomText(data.title ?? "",
                            size: 2.2,
                            maxlines: 1,
                            textOverflow: TextOverflow.ellipsis),
                        const SizedBox(height: 1.5),
                        CustomText(
                          data.summary ?? noDescription,
                          size: 1.9,
                          color: Colors.grey,
                          maxlines: 2,
                          textOverflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 3),
                        CustomText(
                            "${Get.locale?.languageCode == "ar" ? convertToAabic("${data.price}") : data.price} SAR",
                            size: 2.5,
                            weight: FontWeight.w500)
                      ]),
                ),
                Row(children: [
                  const Icon(Icons.star_rounded, size: 17),
                  CustomText(
                      " ${Get.locale?.languageCode == "ar" ? convertToAabic(data.rating ?? "0") : data.rating}/${Get.locale?.languageCode == "ar" ? convertToAabic("5") : "5"}",
                      size: 2.4),
                ])
              ])
        ]),
      ),
    );
  }
}
