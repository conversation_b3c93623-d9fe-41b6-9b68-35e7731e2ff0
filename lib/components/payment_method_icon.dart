import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/cupertino.dart';

class PaymentMethodIcon extends StatelessWidget {
  const PaymentMethodIcon({
    super.key,
    this.size,
    required this.methodId,
  });

  final Size? size;
  final int methodId;

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      'assets/icons/payment_methods/$methodId.png',
      width: size?.width ?? widthSpace(12),
      height: size?.height,
    );
  }
}
