import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';

class OtpInput extends StatelessWidget {
  final TextEditingController controller;
  final bool autoFocus;
  const OtpInput({this.autoFocus=false,required this.controller,Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: heightSpace(5.5),
      width: widthSpace(3),
      child: TextField(
        controller: controller,
        textAlign: TextAlign.center,
        keyboardType: TextInputType.number,
        style: TextStyle(fontSize: heightSpace(1.7)),
        // controller: controller,
        maxLength: 1,
        // cursorColor: Theme.of(context).primaryColor,
        scrollPadding: EdgeInsets.zero,
        decoration: const InputDecoration(
          counterText: "",
            // hintStyle: TextStyle(color: Colors.black, fontSize: 20.0)
        ),
        onChanged: (value) {
          if (value.length == 1) {
            FocusScope.of(context).nextFocus();
          }
        },
      ),
    );
  }
}
