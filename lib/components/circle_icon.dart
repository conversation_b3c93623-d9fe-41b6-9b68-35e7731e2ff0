import '../utils/constants.dart';
import 'package:flutter/material.dart';

import '../utils/sizeconfig.dart';

class CircleIcon extends StatelessWidget {
  final IconData icon;
  final Function()? onPressed;
  const CircleIcon({super.key,required this.icon,this.onPressed});

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onPressed,
      // splashRadius: 1,
      padding: EdgeInsets.zero,
      icon: Container(
          width: widthSpace(30),
          height: widthSpace(30),
          decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: const Color(greyText),width: 2)),
          child: Icon(icon,size: widthSpace(5),color:const Color(greyText))),
    );
  }
}
