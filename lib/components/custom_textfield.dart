import 'package:flutter/material.dart';

class CustomTextField extends StatelessWidget {
  final controller,
      onClick,
      hint,
      isEnabled,
      inputType,
      isPassword,
      isRoundedBorder,
      validator,
      formatter,
      suffix,
      suffixIcon,
      prefixIcon,
      errorText,
      textCapitalization,
      maxlines,
      maxLength,
      focusNode,
      isReadOnly;
  final Function(String)? onChanged;
  final Function(String)? onFieldSubmitted;
  final Function()? onEditingComplete;
  final Color? filledColor;
  final double borderRadius;

  const CustomTextField(
      {Key? key,
      this.controller,
        this.onClick,
        this.hint,
        this.isEnabled = true,
        this.inputType,
        this.isPassword = false,
        this.isRoundedBorder = false,
        this.validator,
        this.formatter,
        this.suffix,
        this.suffixIcon,
        this.prefixIcon,
        this.errorText,
        this.textCapitalization = TextCapitalization.none,
        this.maxlines = 1,
        this.maxLength,
        this.onChanged,
        this.onFieldSubmitted,
        this.onEditingComplete,
        this.focusNode,this.isReadOnly=false,this.filledColor,this.borderRadius=25}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      readOnly: isReadOnly,
      controller: controller,
      onTap: onClick,
      onChanged: onChanged,
      onFieldSubmitted: onFieldSubmitted,
      onEditingComplete: onEditingComplete,
      validator: validator,
      focusNode: focusNode,
      style: TextStyle(
          fontWeight: isRoundedBorder ? null : FontWeight.w500, fontSize: 16),
      // textAlign: TextAlign.left,
      keyboardType: inputType,
      textCapitalization: textCapitalization,
      obscureText: isPassword,
      inputFormatters: formatter,
      maxLines: maxlines,
      maxLength: maxLength,
      decoration: InputDecoration(
        filled: filledColor!=null,
        fillColor: filledColor,
        hintText: hint,
        hintStyle:
            const TextStyle(fontSize: 14, color: Colors.black38),
        prefixIcon: prefixIcon,
        suffixIcon: suffix,
        suffix: suffixIcon,
        errorText: errorText,
        contentPadding: EdgeInsets.only(
            bottom: 5,
            left: isRoundedBorder ? 15 : 0,
            top: maxlines > 1 ? 15 : 0,
            right: isRoundedBorder ? 15 : 0),
        enabled: isEnabled,
        border: isRoundedBorder
            ? OutlineInputBorder(
                borderSide: const BorderSide(color: Color.fromARGB(255, 243, 243, 243)),
                borderRadius: BorderRadius.circular(borderRadius))
            : null,
        enabledBorder: isRoundedBorder
            ? OutlineInputBorder(
                borderSide: const BorderSide(color: Color.fromARGB(255, 240, 240, 240)),
                borderRadius: BorderRadius.circular(borderRadius))
            : null,
      ),
    );
  }
}
