import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/property_detail_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/models/property_reviews.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

dynamic showReviewsSheet() {
  PropertyDetailController c = Get.find();
  ViewsCommon.showModalBottom(
    DraggableScrollableSheet(
        maxChildSize: .90,
        initialChildSize: .90,
        expand: false,
        builder: (context, scrollController) {
          return Obx(
            () => SingleChildScrollView(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(
                      alignment: Alignment.topRight,
                      child: IconButton(
                          onPressed: () {
                            Get.back();
                          },
                          icon: const Icon(
                            Icons.cancel_outlined,
                            color: Colors.black,
                            weight: 2.0,
                          )),
                    ),
                    SizedBox(height: heightSpace(2.5)),
                    reviewHeading(c),
                    SizedBox(height: heightSpace(2.5)),
                    Align(
                        alignment: Alignment.center,
                        child: CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .withdraw
                              .guestFavourite,
                          size: 2.7,
                          weight: FontWeight.bold,
                          textAlign: TextAlign.center,
                        )),
                    SizedBox(height: heightSpace(0.5)),
                    Align(
                        alignment: Alignment.center,
                        child: SizedBox(
                            width: widthSpace(80),
                            child: CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .withdraw
                                  .guestFavouriteIntro,
                              size: 1.9,
                              color: const Color(greyText),
                              textAlign: TextAlign.center,
                            ))),
                    SizedBox(height: heightSpace(2.5)),
                    const Divider(),
                    //overAll rating
                    CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .withdraw
                            .overAllRating,
                        size: 2.1,
                        weight: FontWeight.bold),
                    reviewCard(c),
                    const Divider(),
                    SizedBox(height: heightSpace(2.5)),
                    renderFourField(c),
                    SizedBox(height: heightSpace(2.5)),
                    CustomText(
                        "${c.data.value?.reviewAverage!.totalReviews ?? "0.0"} ${Get.find<TranslationHelper>().translations.sidenav.reviews}",
                        size: 3.1,
                        weight: FontWeight.bold),
                    SizedBox(height: heightSpace(2.5)),
                    renderSortingHere(c),
                    renderSearchHere(c),
                    SizedBox(height: heightSpace(2.5)),
                    reviewSheet(c),
                    if (c.reviewsNextPage != null) ...[
                      SizedBox(height: heightSpace(3)),
                      InkWell(
                        onTap: c.reviewsLoading.value
                            ? null
                            : () => c.getReviews(c.data.value?.id ?? 0),
                        child: Row(children: [
                          CustomText(
                              c.reviewsLoading.value
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .withdraw
                                      .loading
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .withdraw
                                      .showMore,
                              underline: true),
                          const Icon(Icons.chevron_right, size: 19)
                        ]),
                      )
                    ]
                  ]).paddingAll(widthSpace(viewPadding)),
            ),
          );
        }),
    then: (_) => c.clearFiltersAndGetData(getAPIData: true),
  );
}

reviewHeading(PropertyDetailController c) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      (Get.locale?.languageCode ?? 'en') == 'en'
          ? SvgPicture.asset(
              "assets/icons/review-lft.svg",
              height: heightSpace(14),
              width: 10,
              fit: BoxFit.fill,
            )
          : SvgPicture.asset(
              "assets/icons/review-rgt.svg",
              height: heightSpace(14),
              width: 10,
              fit: BoxFit.fill,
            ),
      CustomText(
        "${c.data.value?.reviewAverage!.avgTotal ?? "0.0"}",
        size: 8.1,
        weight: FontWeight.w500,
        textAlign: TextAlign.center,
      ),
      (Get.locale?.languageCode ?? 'en') == 'en'
          ? SvgPicture.asset(
              "assets/icons/review-rgt.svg",
              height: heightSpace(14),
              width: 10,
              fit: BoxFit.fill,
            )
          : SvgPicture.asset(
              "assets/icons/review-lft.svg",
              height: heightSpace(14),
              width: 10,
              fit: BoxFit.fill,
            ),
    ],
  );
}

renderFourField(PropertyDetailController c) {
  return Column(
    children: [
      ...ratingBar(
          Get.find<TranslationHelper>().translations.propertySingle.cleanliness,
          c.data.value!.reviewAverage?.cleanliness ?? 0.0,
          "/clean.svg"),
      const Divider(),
      ...ratingBar(
          Get.find<TranslationHelper>().translations.hostDashboard.accuracy,
          c.data.value!.reviewAverage?.accuracy ?? 0.0,
          "/accuracy.svg"),
      const Divider(),
      ...ratingBar(
          Get.find<TranslationHelper>()
              .translations
              .hostDashboard
              .communication,
          c.data.value!.reviewAverage?.communication ?? 0.0,
          "/communication.svg"),

      const Divider(),
      ...ratingBar(
          Get.find<TranslationHelper>().translations.propertySingle.location,
          c.data.value!.reviewAverage?.location ?? 0.0,
          "/location.svg"),
      // const Divider(),
      // ...ratingBar(Get.find<TranslationHelper>().translations.propertySingle.darentService, c.data.value!.reviewAverage?.darentService??0.0,"/app-icon.png"),
      // const Divider(),
      // ...ratingBar(Get.find<TranslationHelper>().translations.propertySingle.darentRecomended, c.data.value!.reviewAverage?.darentRecomended??0.0,Icons.thumb_up_alt_outlined),
    ],
  );
}

ratingBar(title, double value, icon, {isLast = false}) {
  return [
    Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
      icon is IconData
          ? Icon(icon, size: heightSpace(2))
          : icon.contains('png')
              ? ColorFiltered(
                  colorFilter: const ColorFilter.mode(Colors.black, BlendMode.srcIn),
                  child: Image.asset('assets/icons$icon',
                    height: heightSpace(2), fit: BoxFit.fill))
              : SvgPicture.asset("assets/icons$icon",
                  height: heightSpace(2),
                  width: 10,
                  fit: BoxFit.fill,
                  colorFilter: const ColorFilter.mode(Colors.black, BlendMode.srcIn)),
      SizedBox(
        width: widthSpace(5),
      ),
      CustomText(title, size: 1.9, color: Colors.black87),
      const Spacer(),
      // Container(
      //   height: heightSpace(1.3),
      //   width: widthSpace(45),
      //   alignment: Alignment.centerLeft,
      //   decoration: BoxDecoration(
      //       color: const Color(greyBorder),
      //       borderRadius: BorderRadius.circular(10)),
      //   child: Container(
      //       width: ratingWidth,
      //       decoration: BoxDecoration(
      //           color: const Color(themeColor),
      //           borderRadius: BorderRadius.circular(10))),
      // ),
      SizedBox(width: widthSpace(4)),
      renderHead(value.toString())
    ]),
  ];
}

renderHead(text) => CustomText(
      text,
      size: 2.4,
      weight: FontWeight.bold,
      maxlines: 3,
    );
reviewSheet(PropertyDetailController c) {
  return ListView.separated(
    shrinkWrap: true,
    itemCount: c.reviews.length,
    physics: const NeverScrollableScrollPhysics(),
    itemBuilder: (context, index) {
      var item = c.reviews[index];
      return reviewItem(item);
    },
    separatorBuilder: (BuildContext context, int index) {
      return SizedBox(height: heightSpace(2));
    },
  );
}

reviewItem(PropertyReviews item) {
  String? joinedDate;
  if (item.createdAt != null) {
    joinedDate = item.createdAt!;
  }
  return Container(
    padding: EdgeInsets.all(widthSpace(viewPadding / 2)),
    decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15.0),
        border: Border.all(color: Colors.black, width: 0.5)),
    child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          item.reviewerProfileImage != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(widthSpace(6)),
                  child: (item.reviewerProfileImage??"").contains("svg")
                      ? GlobalHelper.buildNetworkSvgWidget(
                        url: item.reviewerProfileImage??"",
                    width: widthSpace(12),
                    height: widthSpace(12),
                    defaultOption: const Icon(Icons.person,
                      size:48,
                    ),)
                      : GlobalHelper.resolveImageUrl(item.reviewerProfileImage??"").isNotEmpty
                      ? Image(
                          image: GlobalHelper.buildNetworkImageProvider(
                              url: item.reviewerProfileImage ?? '',
                          ),
                          width: widthSpace(12),
                          height: widthSpace(12),
                          fit: BoxFit.fill,
                        )
                      :  Container(
                      width: widthSpace(12),
                      height: widthSpace(12),
                      decoration: BoxDecoration(
                          color: Colors.grey[200], shape: BoxShape.circle),
                      child: Icon(Icons.person, size: widthSpace(8)))
          )
              : Container(
                  width: widthSpace(12),
                  height: widthSpace(12),
                  decoration: BoxDecoration(
                      color: Colors.grey[200], shape: BoxShape.circle),
                  child: Icon(Icons.person, size: widthSpace(8))),
          SizedBox(width: widthSpace(5)),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                  "${item.reviewerFirstName ?? ""} ${item.reviewerLastName ?? ""}",
                  size: 2.3),
              renderRatingBar(item.rating ?? 0.0),
              SizedBox(
                  width: widthSpace(40),
                  child: CustomText(item.message ?? "No message given",
                      color: Colors.black54)),
            ],
          ),
          const Spacer(),
          if (joinedDate != null) ...[
            SizedBox(height: heightSpace(1)),
            CustomText(joinedDate, color: const Color(greyText), size: 1.7)
          ]
        ]),
  );
}

renderRatingBar(rating) {
  return RatingBar(
    ignoreGestures: true,
    initialRating: rating,
    minRating: 0,
    direction: Axis.horizontal,
    allowHalfRating: true,
    unratedColor: Colors.grey.withValues(red: 128, green: 128, blue: 128, alpha: 128),
    itemCount: 5,
    itemSize: widthSpace(4),
    itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
    ratingWidget: RatingWidget(
      full: Icon(
        Icons.star,
        color: Colors.black,
        size: widthSpace(2),
      ),
      half: Icon(
        Icons.star_half,
        color: Colors.black,
        size: widthSpace(2),
      ),
      empty: Icon(
        Icons.star_border,
        color: Colors.black,
        size: widthSpace(2),
      ),
    ),
    onRatingUpdate: (rating) {
      debugPrint(rating.toString());
    },
  );
}

renderSortingHere(PropertyDetailController c) {
  var selectedItem;
  if (c.selectedSortingType != "") {
    selectedItem = c.sortByList
        .firstWhere((element) => element['slug'] == c.selectedSortingType);
  }
  return c.isReviewSortingOpen.value
      ? renderContainer(
          c.toggleReviewSortingOpen,
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            CustomText(
                c.selectedSortingType != ""
                    ? (Get.locale?.languageCode ?? 'en') == 'en'
                        ? selectedItem['title_en']
                        : selectedItem['title_ar']
                    : c.selectedSortingType,
                size: 2.0,
                color: Colors.black,
                textOverflow: TextOverflow.ellipsis,
                textAlign: TextAlign.end),
            SizedBox(
                height: heightSpace(20),
                child: ListView.builder(
                  shrinkWrap: false,
                  itemCount: c.sortByList.length,
                  itemBuilder: (context, index) {
                    return InkWell(
                        onTap: c.reviewsLoading.value
                            ? null
                            : () {
                                c.setSortingType(c.sortByList[index]['slug']);
                                c.toggleReviewSortingOpen();
                                c.reviews.clear();
                                c.reviews.refresh();
                                c.getReviews(c.data.value?.id ?? 0,
                                    isForFilerReviews: true);
                              },
                        child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CustomText(
                                  (Get.locale?.languageCode ?? 'en') == 'en'
                                      ? c.sortByList[index]['title_en']
                                      : c.sortByList[index]['title_ar'],
                                  textOverflow: TextOverflow.ellipsis,
                                  color: Colors.black87,
                                  weight: FontWeight.w500),
                            ]).paddingAll(
                          widthSpace(3),
                        ));
                  },
                ))
          ]))
      : renderBigHead(c.selectedSortingType, c.toggleReviewSortingOpen, c);
}

Widget renderBigHead(text2, toggleView, PropertyDetailController c) {
  var selectedItem;
  if (text2 != "") {
    selectedItem =
        c.sortByList.firstWhere((element) => element['slug'] == text2);
  }

  return renderContainer(
      toggleView,
      Row(children: [
        Expanded(
            child: CustomText(
                text2 != ""
                    ? (Get.locale?.languageCode ?? 'en') == 'en'
                        ? selectedItem['title_en']
                        : selectedItem['title_ar']
                    : text2,
                size: 2.0,
                color: Colors.black,
                textOverflow: TextOverflow.ellipsis,
                textAlign: TextAlign.start)),
        const Icon(
          Icons.keyboard_arrow_down,
          color: Colors.black,
        )
      ]));
}

renderContainer(toggleView, Widget child) {
  return InkWell(
    onTap: toggleView,
    child: Container(
        padding: EdgeInsets.all(widthSpace(4.8)),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: ViewsCommon.boxShadow,
          borderRadius: BorderRadius.circular(22),
        ),
        child: child),
  );
}

renderSearchHere(PropertyDetailController c) {
  return TextField(
    readOnly: false,
    controller: c.reviewSearchC,
    focusNode: c.reviewSearchFocus,
    onSubmitted: c.reviewsLoading.value
        ? null
        : (value) async {
            c.reviews.clear();
            c.reviews.refresh();
            c.getReviews(c.data.value?.id ?? 0, isForFilerReviews: true);
            c.update();
          },
    style: TextStyle(fontWeight: FontWeight.w500, fontSize: heightSpace(2.0)),
    decoration: InputDecoration(
        hintText: Get.find<TranslationHelper>().translations.header.searchHere,
        contentPadding: const EdgeInsets.only(left: 20, right: 20),
        prefixIcon: const Icon(Icons.search),
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Color(greyBorder))),
        enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(18),
            borderSide: const BorderSide(color: Color(greyBorder)))),
  ).paddingSymmetric(vertical: widthSpace(viewPadding));
}

reviewCard(PropertyDetailController c) {
  int numOfReviews = c.reviews.length;
  final value = countRatingInstance(c);
  return Container(
    padding: const EdgeInsets.all(5),
    width: double.infinity,
    child: Row(
      children: [
        Expanded(
          child: Column(
            children: [
              rateBar(star: 5, value: value[5] / numOfReviews),
              rateBar(star: 4, value: value[4] / numOfReviews),
              rateBar(star: 3, value: value[3] / numOfReviews),
              rateBar(star: 2, value: value[2] / numOfReviews),
              rateBar(star: 1, value: value[1] / numOfReviews),
            ],
          ),
        ),
      ],
    ),
  );
}

countRatingInstance(PropertyDetailController c) {
  Map<int, int> starCounts = {
    5: 0,
    4: 0,
    3: 0,
    2: 0,
    1: 0,
  };

  for (var review in c.reviews) {
    // Increment the counter for the corresponding star rating
    starCounts[int.parse(review.rating!.toStringAsFixed(0))] =
        (starCounts[review.rating] ?? 0) + 1;
  }

  return starCounts;
}

rateBar({int star = 0, double value = 0.0}) {
  double maxWidth = widthSpace(100);
  double ratingWidth = maxWidth * value;
  return Padding(
    padding: EdgeInsets.only(bottom: star == 1 ? 0 : 2),
    child: Row(
      children: [
        CustomText(star.toString()),
        SizedBox(width: widthSpace(2)),
        Expanded(
          child: Container(
            height: heightSpace(1.3),
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
                color: const Color(greyBorder),
                borderRadius: BorderRadius.circular(10)),
            child: Container(
                width: ratingWidth.isNaN ? 0.0 : ratingWidth,
                decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(10))),
          ),
        ),
      ],
    ),
  );
}
