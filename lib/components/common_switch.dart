import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';

class CommonSwitch extends StatelessWidget {
  final bool isSelected;
  final Color selectedColor,unSelectedColor;
  final Color? bgColor;
  final double selectorSize;
  final onChanged;
  final double padding;
  const CommonSwitch({Key? key,this.isSelected=false,this.selectedColor=Colors.white,this.unSelectedColor=Colors.white,this.selectorSize=2.1,this.onChanged,this.bgColor,this.padding=0.6}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onChanged,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width:widthSpace(selectorSize*2),
        alignment:isSelected?Alignment.centerRight:Alignment.centerLeft,
        padding:EdgeInsets.all(widthSpace(padding)),
        decoration: BoxDecoration(
            color:bgColor,
            borderRadius: BorderRadius.circular(50),
            border: bgColor==null?Border.all(color:isSelected ? selectedColor:unSelectedColor):null,
        ),child: Container(width: widthSpace(selectorSize),height: widthSpace(selectorSize),decoration: BoxDecoration(color: isSelected ? selectedColor:unSelectedColor,shape: BoxShape.circle),),
      ),
    );
  }
}
