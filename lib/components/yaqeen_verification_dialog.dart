import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import '../helperMethods/translation_helper.dart';
import '../helperMethods/authHelper.dart';
import '../helperMethods/globalHelpers.dart';
import 'common_button.dart';

class YaqeenVerificationDialog extends StatelessWidget {
  const YaqeenVerificationDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {

    return Dialog(
      clipBehavior: Clip.antiAlias,
      // insetPadding: EdgeInsets.symmetric(horizontal: widthSpace(largeText?6:13),vertical: heightSpace(largeText?22:25)),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(8), vertical: widthSpace(viewPadding)),
        child: Obx(
          () => Column(mainAxisSize: MainAxisSize.min, children: [
            Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                    onPressed: Get.back,
                    icon: const CircleAvatar(
                        backgroundColor: Colors.black12,
                        foregroundColor: Colors.black,
                        child: Icon(Icons.close)))),
            Image.asset("assets/elm.jpg",
                height: heightSpace(12), width: widthSpace(15)),
            CustomText(
                Get.find<TranslationHelper>()
                    .translations
                    .usersProfile
                    .nationalitySelect,
                size: 2.1,
                weight: FontWeight.w500),
            Row(
              children: [
                Radio<int>(
                  value: 0,
                  groupValue: AuthHelper.c.iqamaValue.value,
                  onChanged: AuthHelper.c.updateIqamaValue,
                ),
                CustomText(Get.find<TranslationHelper>()
                    .translations
                    .usersProfile
                    .nationalityOption1),
              ],
            ),
            Row(
              children: [
                Radio<int>(
                  value: 1,
                  groupValue: AuthHelper.c.iqamaValue.value,
                  onChanged: AuthHelper.c.updateIqamaValue,
                ),
                CustomText(Get.find<TranslationHelper>()
                    .translations
                    .usersProfile
                    .nationalityOption2),
              ],
            ),
            // Row(
            //   children: [
            //     Radio<int>(
            //       value: 2,
            //       groupValue: AuthHelper.c.iqamaValue.value,
            //       onChanged:AuthHelper.c.updateIqamaValue,
            //     ),
            //     CustomText(Get.find<TranslationHelper>().translations.usersProfile.nationalityOption3),
            //   ],
            // ),
            if (AuthHelper.c.iqamaValue.value != 5) ...[
              if (AuthHelper.c.iqamaValue.value == 2) ...[
                Container(
                  width: double.maxFinite,
                  decoration: const BoxDecoration(
                      border: Border(bottom: BorderSide(width: .5))),
                  alignment: Alignment.centerLeft,
                  padding:
                      const EdgeInsets.only(left: 15, right: 15, bottom: 5),
                  child: Directionality(
                    textDirection: TextDirection.ltr,
                    child: IntlPhoneField(
                      controller: AuthHelper.c.iqma,
                      initialCountryCode: AuthHelper.c.selectedCountryCode,
                      onCountryChanged: (countryCode) => AuthHelper.c
                          .selectedCountryCode = countryCode.code.toLowerCase(),
                    ),
                  ),
                  // CountryCodePicker(
                  //   onChanged: (countryCode){
                  //     AuthHelper.c.selectedCountryCode = countryCode.code!.toLowerCase();
                  //   },
                  //   builder: (e) => Row(
                  //     children: [
                  //       Image.asset(
                  //         e!.flagUri!,
                  //         package: 'country_code_picker',
                  //         width: widthSpace(7),
                  //       ),
                  //       SizedBox(width: widthSpace(20),),
                  //       Text(e.name!),
                  //       const Spacer(),
                  //       Icon(
                  //         Icons.arrow_drop_down,
                  //         color: Colors.grey,
                  //         size: widthSpace(5),
                  //       )
                  //     ],
                  //   ),
                  //   initialSelection: 'sa',
                  // ),
                ),
                CustomTextField(
                  controller: AuthHelper.c.iqma,
                  maxLength: 9,
                  hint: Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .enterYourPass,
                  textCapitalization: TextCapitalization.characters,
                )
              ] else ...[
                CustomTextField(
                  controller: AuthHelper.c.iqma,
                  maxLength: 10,
                  hint: Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .enterYourIqama,
                  inputType: TextInputType.number,
                ),
                SizedBox(height: heightSpace(1)),
                Container(
                  width: double.maxFinite,
                  padding: EdgeInsets.only(
                      bottom: heightSpace(2), top: heightSpace(1.7)),
                  decoration: const BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: Color.fromARGB(255, 230, 230, 230),
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomText(
                        AuthHelper.c.iqamaValue.value == 0
                            ? AuthHelper.c.dobHijriDate.value == null
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .hostDashboard
                                    .dateOfBirth
                                : "${AuthHelper.c.dobHijriDate.value!.year}-${GlobalHelper.twoNumberFormat(AuthHelper.c.dobHijriDate.value!.month)}-${GlobalHelper.twoNumberFormat(AuthHelper.c.dobHijriDate.value!.day)}"
                            : AuthHelper.c.dobVerification.value == null
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .hostDashboard
                                    .dateOfBirth
                                : formDateFormat.format(
                                    AuthHelper.c.dobVerification.value!),
                        size: 2.1,
                        color: AuthHelper.c.dobVerification.value == null
                            ? const Color(greyText)
                            : null,
                        weight: FontWeight.w500,
                      ),
                      InkWell(
                        onTap: AuthHelper.c.selectDobVerification,
                        child: const Icon(Icons.calendar_month_outlined),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: heightSpace(1)),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Radio<int>(
                          value: 0,
                          groupValue: AuthHelper.c.iqamaValue.value,
                          onChanged: (val) {},
                        ),
                        CustomText(Get.find<TranslationHelper>()
                            .translations
                            .usersProfile
                            .hijri),
                      ],
                    ),
                    Row(
                      children: [
                        Radio<int>(
                          value: 1,
                          groupValue: AuthHelper.c.iqamaValue.value,
                          onChanged: (val) {},
                        ),
                        CustomText(Get.find<TranslationHelper>()
                            .translations
                            .usersProfile
                            .gregorian),
                      ],
                    ),
                  ],
                ),
                SizedBox(height: heightSpace(1)),
                RichText(
                  text: TextSpan(
                      text:
                          "${Get.find<TranslationHelper>().translations.signUp.bySubmittingYourIqama} ",
                      style: TextStyle(
                          color: const Color(greyText),
                          fontSize: heightSpace(1.75)),
                      children: <TextSpan>[
                        TextSpan(
                            text: Get.find<TranslationHelper>()
                                .translations
                                .footer
                                .privacyPolicy,
                            style: TextStyle(color: Colors.blue[900]),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () => AuthHelper.c.openWebView(
                                  'privacy-policy',
                                  Get.find<TranslationHelper>()
                                      .translations
                                      .footer
                                      .privacyPolicy))
                      ]),
                ),
              ],
              SizedBox(height: heightSpace(2)),
              SizedBox(
                  width: double.maxFinite,
                  height: heightSpace(6),
                  child: CommonButton(
                      title: Get.find<TranslationHelper>()
                          .translations
                          .usersProfile
                          .save,
                      onPressed: () => AuthHelper.c.submitIqma(
                          maxLength:
                              AuthHelper.c.iqamaValue.value == 2 ? 9 : 10),
                      isLoading: AuthHelper.c.isLoading.isTrue,
                      backgroundBg: isHost ? Colors.black : null,
                      horizontalPadding: 8)),
            ],
            SizedBox(height: heightSpace(2))
          ]),
        ),
      ),
    );
  }
}
