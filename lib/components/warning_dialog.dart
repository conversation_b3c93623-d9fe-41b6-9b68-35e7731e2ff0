import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../utils/constants.dart';
import '../utils/sizeconfig.dart';
import 'custom_text.dart';

class WarningDialog extends StatelessWidget {
  final String title, description;
  DialogKeyword keyword;
  String? confirmText;
  String? imagePath;
  final bool isIcon, largeText, isLargeIcon;
  final VoidCallback? onConfirmed;

  WarningDialog({
    Key? key,
    required this.title,
    required this.description,
    this.keyword = DialogKeyword.warning,
    this.isLargeIcon = false,
    this.isIcon = true,
    this.largeText = false,
    this.onConfirmed,
    this.confirmText,
    this.imagePath,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    int color = keyword == DialogKeyword.warning
        ? warningColor
        : keyword == DialogKeyword.success
        ? successDialogColor
        : themeColor;

    return Dialog(
      insetPadding: EdgeInsets.symmetric(
        horizontal: widthSpace(largeText ? 6 : 13),
        vertical: heightSpace(largeText ? 22 : 25),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8, // Limit height
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!largeText || isLargeIcon)
              if (imagePath != null)
                Image.asset(
                  "assets/$imagePath",
                  height: heightSpace(12),
                  width: widthSpace(15),
                )
              else ...[
                CircleAvatar(
                  backgroundColor: Color(color),
                  radius: isLargeIcon ? widthSpace(9.5) : widthSpace(3.5),
                  child: Icon(
                    keyword == DialogKeyword.warning
                        ? Icons.close
                        : keyword == DialogKeyword.success
                        ? Icons.check
                        : Icons.info,
                    size: isLargeIcon ? heightSpace(4.5) : 21,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: heightSpace(.5)),
              ],
            CustomText(
              title,
              size: largeText ? 2.2 : 2.4,
              textAlign: TextAlign.center,
              color: !isIcon ? null : Color(color),
              weight: FontWeight.w500,
            ),
            SizedBox(height: heightSpace(1)),
            Flexible(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: widthSpace(3)),
                  child: CustomText(
                    description,
                    size: largeText ? 1.8 : 1.9,
                    textAlign: TextAlign.center,
                    color: const Color(greyText),
                  ),
                ),
              ),
            ),
            SizedBox(height: heightSpace(2)),
            SizedBox(
              width: double.maxFinite,
              height: heightSpace(6),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: isIcon ? Color(color) : null,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(isIcon ? 100 : 6),
                  ),
                ),
                onPressed: onConfirmed ?? Get.back,
                child: CustomText(
                  confirmText ?? (onConfirmed != null ? "confirm".tr : "okay".tr),
                  weight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ).paddingAll(widthSpace(viewPadding)),
      ),
    );
  }
}