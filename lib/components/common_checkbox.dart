import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';

class CommonCheckBox extends StatelessWidget {
  final onPressed,isSelected,title,bottomMargin,selectedColor,crossAxisAlignment,isExpanded;
  final double fontSize;
  final bool isBg;
  const CommonCheckBox({Key? key,required this.onPressed,this.isSelected=false,required this.title,this.bottomMargin,this.selectedColor=greyText,this.crossAxisAlignment=CrossAxisAlignment.center,this.isExpanded=false,
  this.fontSize=2,this.isBg = false}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(crossAxisAlignment: crossAxisAlignment,mainAxisSize: MainAxisSize.min,children: [
      InkWell(
        onTap: onPressed,
        child: Container(
          height: 22,width: 22,
          margin:  bottomMargin!=null? EdgeInsets.only(bottom: bottomMargin):null,
          decoration: BoxDecoration(
              color:!isBg ?null: Color(isSelected?lightBlack:greyBorder),
              borderRadius: BorderRadius.circular(3),
              border: Border.all(color: Color(isSelected?selectedColor:greyText))
          ),
          child: isSelected? const Icon(Icons.check,size: 14,color:Color(themeColor)):null,
        ),
      ),
      SizedBox(width: widthSpace(4)),
      isExpanded?Expanded(child: CustomText(title,color: Colors.grey[800],size: fontSize)): title is String?CustomText(title,color: Colors.grey[700]):title,
    ]);
  }
}
