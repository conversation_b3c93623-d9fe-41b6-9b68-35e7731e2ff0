import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/listing_helper/listing_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../helperMethods/translation_helper.dart';
import '../../analytics/analytics_manager.dart';
import '../../analytics/analytics_constants.dart';
import '../../helperMethods/globalHelpers.dart';

class LicenseWarningDialog extends StatelessWidget {
  const LicenseWarningDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: EdgeInsets.symmetric(horizontal: widthSpace(13)),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: EdgeInsets.symmetric(
            horizontal: widthSpace(5), vertical: widthSpace(5)),
        child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const CircleAvatar(
                        backgroundColor: Colors.black12,
                        foregroundColor: Colors.black,
                        child: Icon(Icons.close))),
              ),
              Container(
                width: 40,
                height: 40,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: const Color(themeColor).withOpacity(.3),
                    borderRadius: BorderRadius.circular(9),
                    border:
                        Border.all(width: 2, color: const Color(themeColor))),
                child: const CustomText('!',
                    size: 3, color: Color(themeColor), weight: FontWeight.bold),
              ),
              SizedBox(height: heightSpace(3)),
              CustomText(
                  Get.find<TranslationHelper>()
                          .translations
                          .hostListing
                          .actionNeeded ??
                      '',
                  size: 2.4,
                  textAlign: TextAlign.center,
                  weight: FontWeight.bold),
              SizedBox(height: heightSpace(3)),
              CustomText(
                  Get.find<TranslationHelper>()
                          .translations
                          .hostListing
                          .licenseWarning ??
                      '',
                  size: 1.8,
                  color: Colors.black87,
                  textAlign: TextAlign.center),
              SizedBox(height: heightSpace(3)),
              GestureDetector(
                onTap: () {
                  // Track license expiry notification when warning is shown
                  _trackLicenseExpiryEvent();

                  Get.back();
                  ListingHelper.c.changeIndex(3);
                  // Navigator.pop(context);
                  // AuthHelper.c.openWebView('managehost/instruction',Get.find<TranslationHelper>().translations.hostListing.instructions!);
                },
                child: Container(
                    padding: EdgeInsets.symmetric(
                        vertical: widthSpace(4), horizontal: widthSpace(8)),
                    decoration: BoxDecoration(
                      color: const Color(themeColor),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: CustomText(
                        Get.find<TranslationHelper>()
                            .translations
                            .general
                            .verifyNow,
                        weight: FontWeight.w500)),
              ),
              // SizedBox(
              //     width: double.maxFinite,
              //     height: heightSpace(6),
              //     child: ElevatedButton(
              //         style: ElevatedButton.styleFrom(
              //             backgroundColor:isIcon?Color(color):null,
              //             shape: RoundedRectangleBorder(
              //                 borderRadius:BorderRadius.circular(isIcon ? 100 : 6))),
              //         onPressed: onConfirmed ?? Get.back,
              //         child: CustomText(
              //             confirmText?? (onConfirmed != null ? "confirm".tr : "okay".tr),
              //             weight: FontWeight.w500,
              //             color: Colors.white))),
            ]),
      ),
    );
  }

  void _trackLicenseExpiryEvent() {
    if (GlobalHelper.storageBox.hasData('user')) {
      final userId = GlobalHelper.storageBox.read('user')['id']?.toString();

      Get.find<AnalyticsManager>().trackEvent(
        AnalyticsEvents.hostLicenseExpiryNotification,
        eventAttributes: {
          AnalyticsAttributes.hostId: userId,
          AnalyticsAttributes.hostLicenseId: 'unknown',
          AnalyticsAttributes.hostLicenseExpiryDate: 'unknown',
          AnalyticsAttributes.daysToExpiry: 'unknown',
          AnalyticsAttributes.sessionTimestamp:
              DateTime.now().toIso8601String(),
        },
      );
    }
  }
}
