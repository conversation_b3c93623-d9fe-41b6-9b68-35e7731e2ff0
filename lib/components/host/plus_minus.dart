import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';

class PlusMinus extends StatelessWidget{
  final String title;
  final Rx<num> value;
  final num minValue;
  final String? icon;
  const PlusMinus({super.key,required this.title,required this.value,this.minValue = 0,this.icon});

  @override
  Widget build(BuildContext context) {
    return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              if(icon!=null)...[
                SvgPicture.asset('assets/icons/$icon.svg',height: 18),
                SizedBox(width: widthSpace(1.7)),
              ],
              CustomText(title,size: 2.15,weight: FontWeight.w500),
            ],
          ),
          Obx(()=>Row(
            crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                InkWell(
                  borderRadius: BorderRadius.circular(80),
                  onTap: (){
                    if(minValue==0.5){
                      if(value.value>1){
                        value.value--;
                      }else{
                        value.value = 0.5;
                      }
                    }else{
                      if(value.value>minValue){
                        value.value--;
                      }
                    }
                  },
                  child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(color:value>minValue?Colors.black:const Color(greyBorder))
                      ),
                      child: Icon(Icons.remove,color:value>minValue?Colors.black:const Color(greyBorder),size: 15)),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(
                      horizontal: widthSpace(3)
                  ),child:CustomText(
                    value.value>0.5?value.toStringAsFixed(0):value.value.toString(),
                    size: 2.2,
                    weight: FontWeight.w500)
                ),
                InkWell(
                  borderRadius: BorderRadius.circular(80),
                  onTap: (){
                    if(value.value==0.5){
                      value.value+=.5;
                    }else{
                      value.value++;
                    }

                  },
                  child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all()
                      ),
                      child: const Icon(Icons.add,size: 15)),
                ),
              ],
            ),
          ),
        ]);
  }

}