import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class EditComponent extends StatelessWidget {
  final String title;
  final String? value;
  final Widget? secondaryWidget;
  final Widget? secondaryIcon;
  final Function()? clickEvent;
  const EditComponent({super.key,required this.title,this.value,this.secondaryWidget,
    this.secondaryIcon,this.clickEvent});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: clickEvent,
      borderRadius: BorderRadius.circular(10),
      child: Container(
        padding: EdgeInsets.all(widthSpace(4)),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(color:Color(greyBorder))
        ),child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CustomText(title,color: Colors.black54),
                  if(clickEvent!=null)secondaryIcon??SvgPicture.asset('assets/icons/edit.svg')
            ]),
            if(value!=null)...[
              SizedBox(height: heightSpace(1)),
              CustomText(value,size: 2.2,lineSpacing: 1.5,weight: FontWeight.w500,maxlines: 3,textOverflow: TextOverflow.ellipsis),
            ],
            if(secondaryWidget!=null)...[
              SizedBox(height: heightSpace(1)),
              secondaryWidget!
            ],
          ],
        ),
      ),
    );
  }
}
