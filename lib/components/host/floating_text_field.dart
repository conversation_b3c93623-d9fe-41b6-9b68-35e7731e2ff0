import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show TextInputFormatter;

class FloatingTextField extends StatelessWidget{
  final TextEditingController? controller;
  final String? labelText;
  final String? errorText;
  final InputBorder borderType;
  final InputBorder? errorBorder;
  final EdgeInsets? contentPadding;
  final int lines;
  final int? length;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? formatters;
  final TextInputType? inputType;
  final Widget? suffix;
  final bool? enabled;
  final bool textCenter;
  final TextStyle? textStyle;
  final FontWeight fontWeight;
  final Function(String)? onChanged;
  final double fontSize;
  const FloatingTextField({
    super.key,
    this.controller,
    this.labelText,
    this.errorText,
    this.borderType=InputBorder.none,
    this.errorBorder,
    this.contentPadding,
    this.lines=1,
    this.length,
    this.validator,
    this.formatters,
    this.inputType,
    this.suffix,
    this.enabled,
    this.textStyle,this.onChanged,this.textCenter=false,this.fontSize=3,this.fontWeight=FontWeight.w500});
  @override
  Widget build(BuildContext context) {
    bool isBorder = borderType!=InputBorder.none;
    return TextFormField(
        controller: controller,
        maxLines: lines,
        validator: validator,
        maxLength: length,
        inputFormatters: formatters,
        keyboardType: inputType,
        enabled: enabled,
        onChanged: onChanged,
        textAlignVertical: textCenter?TextAlignVertical.center:null,
        style: textStyle??TextStyle(fontSize: heightSpace(fontSize), height: 1, fontWeight: fontWeight),
        decoration: InputDecoration(
            labelText: labelText,
            errorText: errorText,
            isCollapsed: textCenter,
            suffix: suffix,
            contentPadding: contentPadding??(isBorder?null:EdgeInsets.only(left:widthSpace(viewPadding))),
            labelStyle: hintStyle,
            floatingLabelStyle: hintStyle,
            enabledBorder: borderType,
            disabledBorder: borderType,
            focusedBorder: borderType,
            errorBorder: errorBorder,
        )
    );
  }
TextStyle get hintStyle=>TextStyle(
    fontSize: heightSpace(2.2),
    color: const Color(greyText),
    fontWeight: FontWeight.w500
    );
}