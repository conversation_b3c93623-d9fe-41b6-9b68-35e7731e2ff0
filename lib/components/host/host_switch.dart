import 'package:flutter/material.dart';

class HostSwitch extends StatelessWidget{
  final VoidCallback? onConfirm;
  final bool value;
  final double fontSize;
  const HostSwitch({super.key,this.onConfirm,this.value=false,this.fontSize=2});
  @override
  Widget build(BuildContext context) {
   return GestureDetector(
     onTap: onConfirm,
     child: Container(
       width: 45,
       height: 25,
       decoration: BoxDecoration(
         borderRadius: BorderRadius.circular(15),
         color: value ? Colors.black : Colors.black.withOpacity(0.18),
       ),
       child: Padding(
         padding: const EdgeInsets.all(2.5),
         child: Align(
           alignment: value
               ? Alignment.centerRight
               : Alignment.centerLeft,
           child: Container(
             width: 22,
             height: 22,
             decoration: const BoxDecoration(
               shape: BoxShape.circle,
               color: Colors.white,
             ),
             child: Center(
               child: Visibility(
                 visible: value,
                 child: const Icon(Icons.done, size: 17, color: Colors.black),
               ),
             ),
           ),
         ),
       ),
     ),
   );
  }

}