import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class EmptyReservation extends StatelessWidget{
  final String msg;
  const EmptyReservation({super.key,required this.msg});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widthSpace(100),
      child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
        SvgPicture.asset("assets/icons/empty_reserve.svg"),
        SizedBox(height:heightSpace(3)),
        CustomText(msg,textAlign: TextAlign.center)
          ]),
    );
  }

}