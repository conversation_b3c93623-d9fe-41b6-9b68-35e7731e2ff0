import 'package:darent/components/common_button.dart';
import 'package:darent/components/host/host_radio.dart';
import 'package:darent/controllers/host_calendar_controller.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../helperMethods/translation_helper.dart';

class CalendarViewSettings extends StatelessWidget {
  const CalendarViewSettings({super.key});
  @override
  Widget build(BuildContext context) {
    final HostCalendarController c = Get.find();
    return DraggableScrollableSheet(
        maxChildSize: .6,
        initialChildSize: .6,
        expand: false,
        builder: (context, scrollController) => Obx(
              () => ListView(
                  controller: scrollController,
                  padding: EdgeInsets.all(widthSpace(viewPadding)),
                  children: [
                    AppBar(
                        title: Text(Get.find<TranslationHelper>()
                            .translations
                            .tripsActive
                            .calendarView),
                        automaticallyImplyLeading: false,
                        elevation: 0.0,
                        actions: [
                          IconButton(
                              icon: const Icon(Icons.close),
                              onPressed: Get.back)
                        ]),
                    Container(
                        margin: EdgeInsets.only(top: heightSpace(3)),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: const Color(greyBorder))),
                        child: Column(children: [
                          HostRadio<bool>(
                                  title: Get.find<TranslationHelper>()
                                      .translations
                                      .signUp
                                      .year,
                                  value: false,
                                  parent: c.monthView.value,
                                  onPressed: (val) => c.monthView.value = val)
                              .paddingAll(widthSpace(3)),
                          const Divider(),
                          HostRadio<bool>(
                                  title: Get.find<TranslationHelper>()
                                      .translations
                                      .signUp
                                      .month,
                                  value: true,
                                  parent: c.monthView.value,
                                  onPressed: (val) => c.monthView.value = val)
                              .paddingAll(widthSpace(3)),
                          // const Divider(),
                          // HostRadio(
                          //     title: "List",
                          //     value:"list",
                          //     parent:c.calendarViewType.value,
                          //     onPressed:c.selectCalendarView).paddingAll(widthSpace(3))
                        ])),
                    // Padding(
                    //   padding: EdgeInsets.all(widthSpace(5.5)),
                    //   child: const CustomText("Show in calendar",size: 2.3,weight: FontWeight.w500),
                    // ),
                    // Container(
                    //     decoration: BoxDecoration(
                    //         borderRadius: BorderRadius.circular(12),
                    //         border: Border.all(color:const Color(greyBorder))),
                    //     child:
                    //     Column(children: [
                    //       for(var item in c.calendarFeatures)...[
                    //         HostRadio(
                    //             title: item['title'],
                    //             value:item,
                    //             parent:c.calendarViewType.value,
                    //             onPressed:c.checkCalendarFeature).paddingAll(widthSpace(3)),
                    //         if(item['value']!="comments")const Divider(),
                    //       ],
                    //     ])),
                    SizedBox(height: heightSpace(6)),
                    CommonButton(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .usersProfile
                            .save,
                        onPressed: c.submitMonthView,
                        backgroundBg: Colors.black)
                  ]),
            ));
  }
}
