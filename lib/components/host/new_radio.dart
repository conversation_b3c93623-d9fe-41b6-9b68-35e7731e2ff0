import 'package:darent/components/custom_text.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../helperMethods/remote_config.dart';
import '../common_switch.dart';

class NewRadio extends StatelessWidget {
  final String title;
  final onChanged;
  final bool selected;
  final marginPadding;
  final String? icon;
  const NewRadio(
      {super.key,
      required this.title,
      this.onChanged,
      required this.selected,
      this.marginPadding,
      this.icon});

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.only(bottom: marginPadding ?? heightSpace(3)),
        padding: EdgeInsets.only(bottom: marginPadding ?? 10),
        child: InkWell(
          onTap: onChanged,
          child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    GlobalHelper.buildNetworkSvgWidget(
                      url: icon ??"",
                      width: widthSpace(5),
                      myColor: Colors.black,
                      defaultOption: SvgPicture.asset('assets/icons/apartment.svg',
                          width: widthSpace(5)),),
                    SizedBox(width: widthSpace(4)),
                    CustomText(title, size: 2.15, weight: FontWeight.w500),
                  ],
                ),
                CommonSwitch(
                  // onChanged: (){},
                  isSelected: selected,
                  bgColor: selected ? const Color(successColor) : Colors.grey,
                  selectorSize: 4.5,
                  // Get.find<TranslationHelper>().translations.hostListing.instantBook!,
                  // toogle: c.instantBook.value=="instant",
                  // onConfirm:()=>c.instantBook.value = c.instantBook.value=="instant"?"request":"instant",fontSize: 2.15
                )
              ]),
        ));
  }
}
