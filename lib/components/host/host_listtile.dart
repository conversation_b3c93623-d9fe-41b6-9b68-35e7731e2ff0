import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HostListTile extends StatelessWidget{
  final String title;
  final subtitle;
  final bool showBorder;
  final screen;
  final trailing;
  final Color? borderColor;
  final marginPadding;
  final String? description;
  const HostListTile({super.key, required this.title,this.subtitle,this.showBorder=true, this.screen,this.trailing,this.borderColor,this.marginPadding, this.description});

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.only(bottom: marginPadding??heightSpace(3)),
        padding: EdgeInsets.only(bottom: marginPadding??10),
        decoration: BoxDecoration(
            border:showBorder?Border(bottom: BorderSide(color: borderColor??Colors.black54)):null),
        child: InkWell(
          onTap:screen is VoidCallback?screen:(){
            if(screen!=null){
              Get.to(screen);
            }
          },
          child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment:CrossAxisAlignment.start,
                    children: [
                      description != null ? CustomText(title) : Expanded(child: CustomText(title)),
                      if(trailing!=null)trailing is String?CustomText(trailing!,underline: true,size: 1.9):trailing
                      else if(showBorder)const Icon(Icons.chevron_right)
                    ]),
                if(description != null)...[
                  SizedBox(width: heightSpace(1)),
                  CustomText(
                    '( $description )',size: 1.6,color: Colors.black54, textAlign: TextAlign.justify),
                ],
                if(subtitle!=null)...[
                  SizedBox(height: heightSpace(1)),
                  subtitle is Widget?subtitle:CustomText(
                      subtitle,size: 1.9,textOverflow: TextOverflow.visible,color: const Color(greyText))
                ]
              ]),
        ));
  }

}