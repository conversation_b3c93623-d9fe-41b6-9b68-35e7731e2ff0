import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';

class CalendarWidget extends StatelessWidget{
  final String title;
  final String? subTitle;
  final VoidCallback function;
  final bool? isSelected;
  final double titleFontSize,subTitleFontSize;
  const CalendarWidget({super.key, required this.title,this.subTitle,required this.function,this.titleFontSize=2.35,this.subTitleFontSize=2.1, this.isSelected});

  @override
  Widget build(BuildContext context) {
    return InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap:function,
        child:borderContainer(Row(children: [
          Expanded(
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(title,size: titleFontSize,weight: FontWeight.w500),
                  if(subTitle!=null)...[
                    SizedBox(height: heightSpace(1)),
                    CustomText(subTitle,size: subTitleFontSize,color:Colors.black54),
                  ]
                ]),
          ),
          const Icon(Icons.chevron_right,size: 30)
        ]),isCalendar:true, isSelected: isSelected ??false));
  }

}
borderContainer(Widget child,{isCalendar=false, isSelected=false}){
  return Container(
      padding: isCalendar
          ?EdgeInsets.all(widthSpace(4.5))
          :EdgeInsets.symmetric(vertical: widthSpace(4.5)),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: isSelected ? Colors.black : const Color(greyBorder))
      ),child: child);
}