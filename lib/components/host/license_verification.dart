import 'package:darent/components/common_button.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../controllers/property_controller.dart';
import '../../helperMethods/authHelper.dart';
import '../../helperMethods/translation_helper.dart';
import '../../utils/constants.dart';
import '../../utils/sizeconfig.dart';
import '../custom_text.dart';
import '../custom_textfield.dart';
import '../views_common.dart';
import '../../analytics/analytics_manager.dart';
import '../../analytics/analytics_constants.dart';

class LicenseVerification extends StatelessWidget {
  final dwelling;
  LicenseVerification({super.key, this.dwelling});
  late PropertyController c;
  @override
  Widget build(BuildContext context) {
    c = Get.find();
    return Obx(() => Form(
              key: c.licenseForm,
              child: SingleChildScrollView(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      SizedBox(height: heightSpace(2)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .general
                                  .addLicense,
                              size: 2.3,
                              weight: FontWeight.w500),
                          GestureDetector(
                              onTap: Get.back,
                              child: Container(
                                  padding: const EdgeInsets.all(7),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    shape: BoxShape.circle,
                                  ),
                                  child:
                                      const Center(child: Icon(Icons.close))))
                        ],
                      ),
                      if (c.licenseStep.value == 1) ...[
                        SizedBox(height: heightSpace(4)),
                        Container(
                            padding: const EdgeInsets.all(10),
                            height: heightSpace(7),
                            decoration: BoxDecoration(
                                color: Colors.grey[100]!,
                                border: const Border(
                                    bottom:
                                        BorderSide(color: Color(greyBorder)))),
                            child: Obx(
                              () => Row(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    companyTab(
                                        true,
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .listing
                                            .company),
                                    companyTab(
                                        false,
                                        Get.find<TranslationHelper>()
                                            .translations
                                            .listing
                                            .individual),
                                  ]),
                            )),
                      ],
                      SizedBox(height: heightSpace(2)),

                      if (c.licenseStep.value == 1) ...[
                        CustomText(
                            c.isCompany.value
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .listing
                                    .licenseNumber
                                : Get.find<TranslationHelper>()
                                    .translations
                                    .listing
                                    .permitNo,
                            size: 1.9,
                            color: const Color(greyText)),
                        SizedBox(height: heightSpace(1.5)),
                        SizedBox(
                            width: widthSpace(80),
                            child: CustomTextField(
                                controller: c.license,
                                validator: (val) => val!.trim().isEmpty
                                    ? Get.find<TranslationHelper>()
                                        .translations
                                        .jqueryValidation
                                        .required
                                    : null,
                                formatter: [
                                  FilteringTextInputFormatter.digitsOnly,
                                  //LengthLimitingTextInputFormatter(8)
                                ],
                                inputType:
                                    const TextInputType.numberWithOptions(),
                                hint: c.isCompany.value
                                    ? Get.find<TranslationHelper>()
                                        .translations
                                        .listing
                                        .licenseNoHint
                                    : Get.find<TranslationHelper>()
                                        .translations
                                        .listing
                                        .permitNo,
                                isRoundedBorder: true,
                                borderRadius: 10)),
                        SizedBox(height: heightSpace(3)),
                        CustomText(
                            c.isCompany.value
                                ? Get.find<TranslationHelper>()
                                    .translations
                                    .listing
                                    .crNumber
                                : Get.find<TranslationHelper>()
                                    .translations
                                    .hostDashboard
                                    .identity,
                            size: 1.9,
                            color: const Color(greyText)),
                        SizedBox(height: heightSpace(1.5)),
                        SizedBox(
                            width: widthSpace(80),
                            child: CustomTextField(
                              controller: c.cr,
                              validator: (val) => val!.trim().isEmpty
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .jqueryValidation
                                      .required
                                  : null,
                              formatter: [
                                FilteringTextInputFormatter.digitsOnly,
                                //LengthLimitingTextInputFormatter(10)
                              ],
                              inputType:
                                  const TextInputType.numberWithOptions(),
                              hint: c.isCompany.value
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .listing
                                      .crNoHint
                                  : Get.find<TranslationHelper>()
                                      .translations
                                      .hostDashboard
                                      .identity,
                              isRoundedBorder: true,
                              borderRadius: 10,
                            ))
                      ],
                      //else
                      ...[
                        SizedBox(height: heightSpace(3)),
                        if (c.isCompany.value) ...[
                          const CustomText('Company Name',
                              size: 1.9, color: Color(greyText)),
                          SizedBox(height: heightSpace(1.5)),
                          SizedBox(
                              width: widthSpace(80),
                              child: CustomTextField(
                                  controller: c.coName,
                                  validator: (val) => val!.trim().isEmpty
                                      ? Get.find<TranslationHelper>()
                                          .translations
                                          .jqueryValidation
                                          .required
                                      : null,
                                  hint: 'Company Name',
                                  textCapitalization: TextCapitalization.words,
                                  isRoundedBorder: true,
                                  borderRadius: 10)),
                          SizedBox(height: heightSpace(3)),
                        ],

                        const CustomText('Expiry Date',
                            size: 1.9, color: Color(greyText)),
                        SizedBox(height: heightSpace(1.5)),
                        InkWell(
                          borderRadius: BorderRadius.circular(10),
                          onTap: () {
                            showDatePicker(
                                    context: context,
                                    initialDate: c.licenseExpiry.value,
                                    firstDate:
                                        DateTime(DateTime.now().year - 10),
                                    lastDate:
                                        DateTime(DateTime.now().year + 10))
                                .then((val) {
                              if (val != null) {
                                c.licenseExpiry.value = val;
                                _checkLicenseExpiry(val);
                              }
                            });
                            GlobalHelper.removeFocus();
                          },
                          child: Container(
                            padding: const EdgeInsets.all(15),
                            decoration: BoxDecoration(
                                border: Border.all(color: Color(greyBorder)),
                                borderRadius: BorderRadius.circular(10)),
                            child: CustomText(
                                formatter.format(c.licenseExpiry.value)),
                          ),
                        ),
                        // SizedBox(height: heightSpace(3)),
                        //
                        // CustomText(
                        //     'Document',
                        //     size: 1.9,color: const Color(greyText)),
                        // SizedBox(height: heightSpace(1.5)),
                        // InkWell(
                        //   borderRadius: BorderRadius.circular(10),
                        //   onTap: c.licenseDocument,
                        //   child: Container(
                        //     padding: const EdgeInsets.all(15),
                        //     decoration: BoxDecoration(
                        //         border: Border.all(color:Color(greyBorder)),
                        //         borderRadius: BorderRadius.circular(10)
                        //     ),child: CustomText(
                        //     c.licenseDoc.value?.name??'Choose a File',
                        //     color: c.licenseDoc.value==null?const Color(greyText):null,
                        //   ),
                        //   ),
                        // ),
                      ],
                      SizedBox(height: heightSpace(3)),
                      Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            if (c.licenseStep.value == 2)
                              CommonButton(
                                  title: Get.find<TranslationHelper>()
                                      .translations
                                      .listingDescription
                                      .back,
                                  isBorder: true,
                                  onPressed: c.moveLicenseBack),
                            CommonButton(
                                title: c.licenseStep.value == 2 || true
                                    ? Get.find<TranslationHelper>()
                                        .translations
                                        .listingCalendar
                                        .submit
                                    : Get.find<TranslationHelper>()
                                        .translations
                                        .listingBasic
                                        .next,
                                isLoading: c.isLoading.value,
                                onPressed: () => c.uploadDocument(dwelling))
                          ]),
                      SizedBox(height: heightSpace(3)),
                      CustomText(Get.find<TranslationHelper>()
                          .translations
                          .listing
                          .learnMoreListing),
                      InkWell(
                          onTap: () {
                            AuthHelper.c.openWebView(
                                'managehost/instruction',
                                Get.find<TranslationHelper>()
                                    .translations
                                    .hostListing
                                    .instructions!);
                          },
                          child: CustomText(
                              Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .gotoHere,
                              underline: true,
                              weight: FontWeight.w500)),
                    ]),
              ),
            ))
        .paddingSymmetric(
            horizontal: widthSpace(viewPadding), vertical: heightSpace(3));
  }

  companyTab(value, title) {
    return Expanded(
      child: InkWell(
        onTap: () {
          if (c.isCompany != value) {
            c.isCompany.toggle();
            c.cr.clear();
            c.license.clear();
          }
        },
        child: Container(
            alignment: Alignment.center,
            decoration: c.isCompany == value
                ? BoxDecoration(
                    color: Colors.white, boxShadow: ViewsCommon.boxShadow)
                : null,
            child: CustomText(title, size: 1.9, weight: FontWeight.w500)),
      ),
    );
  }

  void _checkLicenseExpiry(DateTime expiryDate) {
    final now = DateTime.now();
    final daysToExpiry = expiryDate.difference(now).inDays;

    // Track license expiry notification if expiring within 30 days
    if (daysToExpiry <= 30 && daysToExpiry > 0) {
      if (GlobalHelper.storageBox.hasData('user')) {
        final userId = GlobalHelper.storageBox.read('user')['id']?.toString();

        Get.find<AnalyticsManager>().trackEvent(
          AnalyticsEvents.hostLicenseExpiryNotification,
          eventAttributes: {
            AnalyticsAttributes.hostId: userId,
            AnalyticsAttributes.hostLicenseId:
                c.license.text.isNotEmpty ? c.license.text : 'unknown',
            AnalyticsAttributes.hostLicenseExpiryDate:
                expiryDate.toIso8601String().split('T')[0],
            AnalyticsAttributes.daysToExpiry: daysToExpiry.toString(),
            AnalyticsAttributes.sessionTimestamp:
                DateTime.now().toIso8601String(),
          },
        );
      }
    }
  }
}
