//
// import 'dart:io';
//
// import 'package:darent/components/common_button.dart';
// import 'package:darent/components/custom_text.dart';
// import 'package:darent/components/custom_textfield.dart';
// import 'package:darent/controllers/property_controller.dart';
// import 'package:darent/helperMethods/translation_helper.dart';
// import 'package:darent/utils/constants.dart';
// import 'package:darent/utils/sizeconfig.dart';
// import 'package:dotted_border/dotted_border.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
//
// class SaudiLicenseDialogue extends StatelessWidget {
//    SaudiLicenseDialogue(
//       {Key? key}) : super(key: key);
//
//   late PropertyController c ;
//   @override
//   Widget build(BuildContext context) {
//     if(Get.isRegistered<PropertyController>()){
//       c = Get.find();
//     }else{
//       c = Get.put(PropertyController());
//     }
//     return Obx(()=>PopScope(
//       onPopInvoked: (val){
//
//       },
//       child: GestureDetector(
//         onTap: () {
//           FocusScope.of(context).requestFocus(FocusNode());
//         },
//         child: Dialog(
//           clipBehavior: Clip.antiAlias,
//           // insetPadding: EdgeInsets.symmetric(horizontal: widthSpace(largeText?6:13),vertical: heightSpace(largeText?22:25)),
//           shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
//           child: Padding(
//             padding: EdgeInsets.symmetric(
//                 horizontal: widthSpace(8), vertical: widthSpace(viewPadding)),
//             child: SingleChildScrollView(
//               child: Column(mainAxisSize: MainAxisSize.min, children: [
//                 Align(
//                   alignment: Alignment.centerRight,
//                   child: IconButton(
//                       icon: Icon(Icons.cancel_outlined, size: widthSpace(10),),
//                     onPressed: () {
//                       c.removeDocument.value = true;
//                       c.removeDocument.refresh();
//                         Get.back();
//                         },
//                   ),
//                 ),
//                 SizedBox(height: heightSpace(2)),
//                 InkWell(
//                   onTap: c.pickLicenseFile,
//                   child: SizedBox(
//                     height: heightSpace(30),
//                     child: DottedBorder(
//                         color: const Color(greyText),
//                         borderType: BorderType.RRect,
//                         radius: const Radius.circular(2),
//                         dashPattern: const [9],
//                         padding: EdgeInsets.zero,
//                         child: c.isLoading.value
//                             ? Center(child: Image.asset('assets/loader.gif',width: widthSpace(10)))
//                             : c.licensePath == null
//                             ? Center  (
//                           child: Column(
//                             mainAxisAlignment: MainAxisAlignment.center,
//                             children: [
//                               Icon(Icons.perm_media_outlined,size: widthSpace(10)),
//                               SizedBox(height: heightSpace(1)),
//                               const CustomText("إرفاق الترخيص/تصريح وزارة السياحة", size: 2.0,maxlines: 2,weight: FontWeight.w500, textAlign: TextAlign.center,),
//                               SizedBox(height: heightSpace(1)),
//                               const CustomText(
//                                   "يجب أن تكون الملفات بتنسيق pdf أو نص أو صورة",
//                                   color: Color(greyText),textAlign: TextAlign.center).paddingSymmetric(horizontal: 4),
//                             ],
//                           ),
//                         )
//                             : GridView(
//                           physics: const NeverScrollableScrollPhysics(),
//                           shrinkWrap: true,
//                           // dragPlaceHolder: placeHolderWidget,
//                           gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                             crossAxisCount: 2,
//                             crossAxisSpacing: widthSpace(3),
//                             mainAxisSpacing: widthSpace(3),
//                             childAspectRatio: 0.75,
//                           ),
//                           children: List.generate(c.propertyImages.length, (index) {
//                             if(index == c.propertyImages.length){
//                               return InkWell(
//                                 onTap:c.pickLicenseFile,
//                                 child: DottedBorder(
//                                     child: SizedBox(
//                                       width: widthSpace(35),height: widthSpace(40),
//                                       child: Icon(Icons.add,size: widthSpace(12)),
//                                     )),
//                               );
//                             }
//                             else{
//                               return Container(
//                                   width: widthSpace(35),height: widthSpace(40),
//                                   clipBehavior: Clip.antiAlias,
//                                   alignment: Alignment.topRight,
//                                   decoration: BoxDecoration(
//                                       borderRadius:BorderRadius.circular(13),
//                                       image: DecorationImage(
//                                           image: FileImage(File(c.licensePath!.paths.first!)),
//                                           fit: BoxFit.cover)),
//                                   child:IconButton(
//                                       padding:const EdgeInsets.only(bottom: 18, left: 40),
//                                       onPressed: () => c.removeImage(index),
//                                       icon: const Icon(
//                                           Icons.cancel_outlined,
//                                           size: 20,
//                                           shadows: <Shadow>[Shadow(color: Colors.white, blurRadius: 15.0)]
//                                       )
//                                   ));
//                             }
//                           }
//                           ),
//                         )
//                             ),
//                   ),
//                 ),
//                 SizedBox(height: heightSpace(2)),
//                 CustomTextField(
//                   controller:c.cr,
//                   hint: Get.find<TranslationHelper>().translations.usersProfile.crNum,
//                   inputType: TextInputType.number,
//                 ),
//                 SizedBox(height: heightSpace(1)),
//                 CustomTextField(
//                   controller: c.license,
//                   hint: Get.find<TranslationHelper>().translations.usersProfile.licenseNum,
//                   inputType: TextInputType.number,
//                 ),
//                 SizedBox(height: heightSpace(1)),
//                 SizedBox(
//                     width: double.maxFinite,
//                     height: heightSpace(6),
//                     child: CommonButton(title: Get.find<TranslationHelper>().translations.usersProfile.save,
//                         onPressed: c.uploadDocument,
//                         isLoading: c.isLoading.value,
//                         isDisabled: c.licensePath == null,
//                         backgroundBg: isHost ? Colors.black :null,
//                         horizontalPadding: 8)
//                 ),
//                 SizedBox(height: heightSpace(2)),
//               ]),
//             ),
//           ),
//         ),
//       ),
//     ));
//   }
// }
