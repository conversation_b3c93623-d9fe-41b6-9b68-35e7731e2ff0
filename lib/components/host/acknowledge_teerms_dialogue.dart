import 'package:darent/components/common_button.dart';
import 'package:darent/components/common_checkbox.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/hostDashboard_controller.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';

class AcknowledgeTermsDialogue extends StatelessWidget {
  final WebViewController controller;
  AcknowledgeTermsDialogue({super.key, required this.controller});

  final HostDashboardController c = Get.find();
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Obx(
        () => Get.find<TranslationHelper>().translateKeywords.isEmpty
            ? ViewsCommon.getShimmerLoading()
            : Scaffold(
                appBar: AppBar(
                  automaticallyImplyLeading: false,
                  title: Text(Get.find<TranslationHelper>()
                          .translations
                          .accountMobile
                          .termsAndConditions ??
                      "Terms"),
                ),
                body: GestureDetector(
                  onTap: () {
                    FocusScope.of(context).requestFocus(FocusNode());
                  },
                  child: Stack(
                    children: [
                      Positioned.fill(
                        child: GestureDetector(),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: widthSpace(2),
                            vertical: widthSpace(viewPadding)),
                        child: SingleChildScrollView(
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: heightSpace(70),
                                  child: WebViewWidget(
                                    controller: controller,
                                  ),
                                ),
                                SizedBox(height: heightSpace(2)),
                                CommonCheckBox(
                                    onPressed: c.markedTerms,
                                    isSelected: c.termsMarked.value,
                                    title: "Acknowledge"),
                                SizedBox(height: heightSpace(2)),
                                Align(
                                    alignment: Alignment.bottomRight,
                                    child: CommonButton(
                                        title: Get.find<TranslationHelper>()
                                            .translations
                                            .usersProfile
                                            .save,
                                        isDisabled: !c.termsMarked.value,
                                        onPressed: c.submitTerms,
                                        isLoading: c.isLoading.value,
                                        backgroundBg:
                                            isHost ? Colors.black : null,
                                        horizontalPadding: 8))
                              ]),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }
}
