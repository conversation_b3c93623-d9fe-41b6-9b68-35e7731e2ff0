import 'package:darent/components/custom_text.dart';
import 'package:darent/utils/constants.dart' show greyText;
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';

class HostRadio<T> extends StatelessWidget{
  final String title;
  final String? subTitle;
  final T value,parent;
  final void Function(T value) onPressed;
  final double fontSize;
  final double height;
  final Color? fontColor;
  final bool isRadioFront;
  const HostRadio({super.key, required this.title,this.subTitle,required this.value,required this.parent,required this.onPressed,this.fontSize=2.3,this.height=37,
    this.fontColor,this.isRadioFront=false});
  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children:[
      if(isRadioFront)Radio<T>(value: value, groupValue: parent, onChanged:(val)=> onPressed(val!)),
      Expanded(child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomText(title,textOverflow: TextOverflow.visible,size: fontSize,color: fontColor),
          if(subTitle!=null)...[
            SizedBox(height: heightSpace(1)),
            CustomText(subTitle,size: 1.9,textOverflow: TextOverflow.visible,color: const Color(greyText))
          ]
        ],
      )),
      if(!isRadioFront)IconButton(
        constraints: BoxConstraints(maxHeight: height,minHeight: height,maxWidth: 37),
        icon: AnimatedContainer(
          decoration: BoxDecoration(
            border: Border.all(width:value==parent || (value is Map? (value as Map)['checked']:false)?6.2:1),
            shape: BoxShape.circle
          ), duration: const Duration(milliseconds: 125),
        ),onPressed: ()=>onPressed(value),
      )
    ]);
  }

}