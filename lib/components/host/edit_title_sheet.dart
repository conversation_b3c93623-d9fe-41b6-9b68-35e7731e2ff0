import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/floating_text_field.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../helperMethods/listing_helper/listing_helper.dart';
import '../../helperMethods/translation_helper.dart';

class EditTitleSheet extends StatelessWidget {
  const EditTitleSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: heightSpace(67),
      child: BottomSheet(
          builder: (context) {
            return SingleChildScrollView(
              child: Form(
                key: ListingHelper.c.nameForm,
                child: Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Align(
                          alignment: Alignment.topRight,
                          child: InkWell(
                              onTap: Get.back, child: const Icon(Icons.clear))),
                      SizedBox(height: heightSpace(2)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .listing
                              .placeName,
                          size: 2.3,
                          weight: FontWeight.w500),
                      SizedBox(height: heightSpace(3)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const CustomText("عربي",
                              color: Color(greyText), size: 1.9),
                          CommonButton(
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .translateToEnglish,
                              onPressed: () => ListingHelper.c.translate('en',
                                  ListingHelper.c.name, ListingHelper.c.nameAr),
                              fontSize: 1.75,
                              backgroundBg: Colors.black)
                        ],
                      ),
                      FloatingTextField(
                        controller: ListingHelper.c.nameAr,
                        labelText: "أدخل عنوانك هنا",
                        validator: (val) => val!.trim().isEmpty
                            ? Get.find<TranslationHelper>()
                                .translations
                                .jqueryValidation
                                .required
                            : null,
                        borderType: OutlineInputBorder(
                            borderSide: const BorderSide(),
                            borderRadius: BorderRadius.circular(10)),
                        length: 64,
                        lines: 3,
                        fontSize: 2.6,
                      ),
                      SizedBox(height: heightSpace(3)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const CustomText("English",
                              color: Color(greyText), size: 1.9),
                          CommonButton(
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .listing
                                  .translateToArabic,
                              onPressed: () => ListingHelper.c.translate('ar',
                                  ListingHelper.c.name, ListingHelper.c.nameAr),
                              fontSize: 1.75,
                              backgroundBg: Colors.black)
                        ],
                      ),
                      FloatingTextField(
                        controller: ListingHelper.c.name,
                        labelText: "Enter your title here",
                        validator: (val) => val!.trim().isEmpty
                            ? Get.find<TranslationHelper>()
                                .translations
                                .jqueryValidation
                                .required
                            : null,
                        borderType: OutlineInputBorder(
                            borderSide: const BorderSide(),
                            borderRadius: BorderRadius.circular(10)),
                        length: 64,
                        lines: 3,
                        fontSize: 2.6,
                      ),
                      Container(
                        padding: const EdgeInsets.only(top: 15),
                        decoration: const BoxDecoration(
                            border: Border(
                                top: BorderSide(color: Color(greyBorder)))),
                        child: Obx(
                          () => CommonButton(
                              title: Get.find<TranslationHelper>()
                                  .translations
                                  .usersProfile
                                  .save,
                              minimumSize: Size.fromHeight(heightSpace(6)),
                              isLoading: ListingHelper.c.isLoading.value,
                              onPressed: ListingHelper.c.submitName,
                              backgroundBg: Colors.black,
                              borderRadius: 5),
                        ),
                      )
                    ]).paddingAll(widthSpace(viewPadding)),
              ),
            );
          },
          onClosing: () {}),
    );
  }
}
