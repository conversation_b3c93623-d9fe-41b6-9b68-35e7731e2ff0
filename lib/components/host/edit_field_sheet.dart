import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/host/floating_text_field.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
class EditFieldSheet extends StatelessWidget{
  final String title,subTitle;
  final TextEditingController? controller;
  const EditFieldSheet({super.key, required this.title,required this.subTitle,this.controller});
  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
        maxChildSize: .7,
        initialChildSize: .7,
        expand: false,
        builder: (BuildContext context, ScrollController scrollController){
          return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Align(
                    alignment: Alignment.topRight,
                    child:InkWell(
                        onTap: () => Get.back(),
                        child: const Icon(Icons.clear))),

                SizedBox(height: heightSpace(2)),
                CustomText(title,size: 2.3,weight: FontWeight.w500),
                SizedBox(height: heightSpace(2)),
                CustomText(subTitle,color: const Color(greyText),size: 1.55),
                SizedBox(height: heightSpace(3)),
                FloatingTextField(
                    labelText: title,
                    borderType: OutlineInputBorder(borderSide: const BorderSide(),
                        borderRadius: BorderRadius.circular(10)),
                lines: 5,
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.only(top: 15),
                  decoration: const BoxDecoration(
                    border: Border(top: BorderSide(color: Color(greyBorder)))
                  ),child:CommonButton(
                  title: "Save",
                  onPressed: (){},
                  backgroundBg: Colors.black,
                  borderRadius: 5
                )
                )
              ]).paddingAll(widthSpace(viewPadding));
        });
  }

}