import 'package:darent/utils/constants.dart';
import 'package:flutter/material.dart';

class HostTextField extends StatelessWidget {
  final String? hint;
  final TextEditingController? textController;
  final Function(String)? onChanged;
  const HostTextField({Key? key,this.hint,this.onChanged, this.textController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: textController,
      onChanged: onChanged,
      decoration: InputDecoration(
        hintText: hint,
        border: border,
        focusedBorder:border
      ),
    );
  }
  get border => OutlineInputBorder(
    borderRadius: BorderRadius.circular(12),
    borderSide: BorderSide(color: Color(lightBg)),
  );
}
