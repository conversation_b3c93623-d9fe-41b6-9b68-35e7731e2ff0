import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';

class CommonButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final title, isBorder, backgroundBg;
  final double horizontalPadding,verticalPadding;
  final bool isLoading,isDisabled;
  final double fontSize;
  final FontWeight? fontWeight;
  IconData? icon;
  Color? buttonThemeColor;
  Size? minimumSize;
  double borderRadius;
  CommonButton(
      {Key? key,
        required this.title,
        required this.onPressed,
        this.isBorder = false,
        this.backgroundBg,
        this.horizontalPadding = 4,
        this.verticalPadding=0,
        this.fontSize = 2.1,
        this.fontWeight,
        this.icon,
        this.isLoading = false,this.isDisabled=false,this.buttonThemeColor,this.minimumSize,this.borderRadius=8}) : super(key: key);


  @override
  Widget build(BuildContext context) {
    return TextButton(
        onPressed:isLoading || isDisabled ?null: onPressed,
        style: TextButton.styleFrom(
            minimumSize: minimumSize,
            backgroundColor:
            isDisabled
                ? Colors.grey[300]
                : isBorder
                    ? null
                : backgroundBg != null && (backgroundBg != Colors.black|| backgroundBg != const Color(themeColor) )
                        ? backgroundBg
                        : isHost
                          ? Colors.black
                          : const Color(themeColor),
            padding:
                EdgeInsets.symmetric(horizontal: widthSpace(horizontalPadding),vertical: widthSpace(verticalPadding)),
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(borderRadius),
                side: isBorder || buttonThemeColor!=null
                    ? BorderSide(color: buttonThemeColor??Colors.black87, width: 0.6)
                    : BorderSide.none)
        ),
        child: icon != null
            ? Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
                Text(title,
                    style: TextStyle(
                        color: isBorder || buttonThemeColor!=null? buttonThemeColor??Colors.black87 : Colors.white,
                        fontWeight: fontWeight,
                        fontSize: heightSpace(fontSize))),
                // const SizedBox(width: 3),
                isLoading?const SizedBox(width: 16,height: 16,child: CircularProgressIndicator(strokeWidth:2)):Icon(icon, color: isBorder || buttonThemeColor!=null? buttonThemeColor??Colors.black87 : Colors.white, size: 19)
              ])
            : isLoading
                ? SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                        color: isBorder?const Color(themeColor): Colors.white, strokeWidth: 1.5))
                : Text(title,
                    style: TextStyle(
                        color: isBorder || buttonThemeColor!=null? buttonThemeColor??Colors.black87 : Colors.white,
                        fontWeight: fontWeight,
                        fontSize: heightSpace(fontSize))));
  }
}
