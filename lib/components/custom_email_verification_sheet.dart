import 'package:darent/components/views_common.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../helperMethods/authHelper.dart';
import '../helperMethods/translation_helper.dart';
import '../utils/constants.dart';
import '../utils/sizeconfig.dart';
import 'common_button.dart';
import 'custom_text.dart';
import 'custom_textfield.dart';
import 'host/floating_text_field.dart';

dynamic showEmailChangeSheet() {
  ViewsCommon.showModalBottom(DraggableScrollableSheet(
      maxChildSize: .65,
      initialChildSize: .65,
      expand: false,
      builder: (context, scrollController) {
        return Obx(
          () => SingleChildScrollView(
            child: Form(
              key: AuthHelper.c.changeEmailKey,
              child: Directionality(
                textDirection: TextDirection.ltr,
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Align(
                          alignment: Alignment.topRight,
                          child: InkWell(
                              onTap: Get.back, child: const Icon(Icons.clear))),
                      SizedBox(height: heightSpace(2)),
                      CustomText(
                          Get.find<TranslationHelper>()
                              .translations
                              .hostDashboard
                              .changeEmailAdddress,
                          size: 2.3,
                          weight: FontWeight.w500),
                      SizedBox(height: heightSpace(3)),
                      // Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                      //   CustomText(
                      //       Get.find<TranslationHelper>().translations.hostDashboard.changeEmailAdddress,
                      //       color: const Color(greyText), size: 2.1, weight: FontWeight.w500),
                      // ]),
                      // const SizedBox(height: 5),
                      CustomTextField(
                        isEnabled: !AuthHelper.c.otpChangeEnabled.value,
                        controller: AuthHelper.c.tempEmail,
                        onChanged: (value) {
                          AuthHelper.c.toggleEmailChanged(value);
                        },
                        inputType: TextInputType.emailAddress,
                        errorText: AuthHelper.c.emailError.value,
                      ),
                      // FloatingTextField(
                      //   onChanged: (value){
                      //
                      //   },
                      //   enabled: !AuthHelper.c.submitOtpEnabled.value,
                      //   controller: AuthHelper.c.tempEmail,
                      //   labelText: Get.find<TranslationHelper>().translations.hostDashboard.changeEmailAdddress!,
                      //   inputType: TextInputType.emailAddress,
                      //   errorText: AuthHelper.c.emailError.value,
                      //   textStyle:TextStyle(fontSize: heightSpace(2.3)),
                      //   borderType: const UnderlineInputBorder(),
                      // ),
                      if (AuthHelper.c.otpChangeEnabled.value) ...[
                        FloatingTextField(
                          onChanged: (value) {
                            AuthHelper.c.toggleOtpChanged(value);
                          },
                          controller: AuthHelper.c.controller,
                          labelText: Get.find<TranslationHelper>()
                              .translations
                              .signUp
                              .writeOtp!,
                          length: 4,
                          inputType: TextInputType.number,
                          errorText: AuthHelper.c.otpError.value,
                          textStyle: TextStyle(fontSize: heightSpace(2.3)),
                          borderType: const UnderlineInputBorder(),
                        ),
                        SizedBox(height: heightSpace(5)),
                        RichText(
                          text: TextSpan(
                              text: Get.find<TranslationHelper>()
                                  .translations
                                  .signUp
                                  .enterCodeEmail!
                                  .replaceAll(
                                      "\$email.", AuthHelper.c.tempEmail.text),
                              style: TextStyle(
                                  color: const Color(greyText),
                                  fontSize: heightSpace(1.75)),
                              children: <TextSpan>[
                                TextSpan(
                                    text: "   ",
                                    style: TextStyle(color: Colors.blue[900]),
                                    recognizer: TapGestureRecognizer()),
                                TextSpan(
                                  text: AuthHelper.c.isLoading.value
                                      ? '${Get.find<TranslationHelper>().translations.listing.pleaseWait!} '
                                      : AuthHelper.c.secondsRemaining.value == 0
                                          ? "${Get.find<TranslationHelper>().translations.signUp.resendCode!} "
                                          : "${Get.find<TranslationHelper>().translations.listing.pleaseWait!} ${AuthHelper.c.secondsRemaining} sec",
                                  style: TextStyle(
                                      color: Colors.blue[900],
                                      fontWeight: FontWeight.bold),
                                  recognizer: AuthHelper.c.isLoading.value
                                      ? null
                                      : (TapGestureRecognizer()
                                        ..onTap = () {
                                          if (!AuthHelper.c.isLoading.value &&
                                              AuthHelper.c.secondsRemaining
                                                      .value ==
                                                  0) {
                                            AuthHelper.c.controller.clear();
                                            AuthHelper.c.onSubmitChangeEmail(
                                                isNavigate: false);
                                          }
                                        }),
                                ),
                                // TextSpan(
                                //     text: "   ",
                                //     style: TextStyle(color: Colors.blue[900]),
                                //     recognizer: TapGestureRecognizer()),
                                // TextSpan(
                                //   text: Get.find<TranslationHelper>().translations.signUp.changeEmail!,
                                //   style: TextStyle(
                                //       color: Colors.blue[900],
                                //       fontWeight: FontWeight.bold),
                                //   recognizer: TapGestureRecognizer()
                                //     ..onTap = (){
                                //       AuthHelper.c.otpChangeEnabled.value= false;
                                //     },
                                // )
                              ]),
                        ),
                      ],
                      SizedBox(height: heightSpace(3)),
                      Container(
                          height: heightSpace(9),
                          padding: EdgeInsets.symmetric(
                              horizontal: widthSpace(5),
                              vertical: heightSpace(1.5)),
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Obx(
                              () => AuthHelper.c.otpChangeEnabled.value
                                  ? CommonButton(
                                      title: Get.find<TranslationHelper>()
                                          .translations
                                          .general
                                          .verify,
                                      backgroundBg: Colors.black,
                                      borderRadius: 5,
                                      isDisabled:
                                          !AuthHelper.c.submitOtpEnabled.value,
                                      isLoading: AuthHelper.c.isLoading.value,
                                      minimumSize:
                                          Size.fromWidth(widthSpace(25)),
                                      onPressed: () {
                                        if (AuthHelper
                                            .c.submitOtpEnabled.value) {
                                          AuthHelper.c.onSubmitVerifyOtp();
                                        }
                                      })
                                  : CommonButton(
                                      title: Get.find<TranslationHelper>()
                                          .translations
                                          .hostDashboard
                                          .change,
                                      backgroundBg: Colors.black,
                                      borderRadius: 5,
                                      isDisabled: !AuthHelper
                                          .c.emailChangeEnabled.value,
                                      isLoading: AuthHelper.c.isLoading.value,
                                      minimumSize:
                                          Size.fromWidth(widthSpace(25)),
                                      onPressed: () {
                                        if (AuthHelper
                                            .c.emailChangeEnabled.value) {
                                          AuthHelper.c.onSubmitChangeEmail(
                                              isNavigate: false);
                                        }
                                      }),
                            ),
                          ))
                    ]).paddingAll(widthSpace(viewPadding)),
              ),
            ),
          ),
        );
      }));
}
