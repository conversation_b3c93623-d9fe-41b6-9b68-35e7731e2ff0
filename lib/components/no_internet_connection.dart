import 'package:flutter/material.dart';

import '../utils/sizeconfig.dart';

class NoInternetConnection extends StatelessWidget{
  final Function() onRefresh;
  const NoInternetConnection({super.key, required this.onRefresh});
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Center(
        child: SizedBox(
          width: widthSpace(60),
          height: heightSpace(25),
          child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Container(
                            color: Colors.grey[200],
                            padding: EdgeInsets.all(widthSpace(3)),
                            child: Icon(Icons.signal_wifi_connected_no_internet_4,size: widthSpace(9), color: Colors.red[300])),
                        const SizedBox(width: 15),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text('No Internet\nConnection !!',style: textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w500)),
                              const SizedBox(height: 7),
                              Text('Turn on the internet connection and try again later.',style: textTheme.bodyMedium?.copyWith(color: Colors.black26))
                            ],
                          ),
                        )]),
                ),
                const SizedBox(height:10),
                const Divider(height: 20),
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                  Text('Try again',style: textTheme.bodyMedium?.copyWith(color: Colors.black54)),
                  IconButton(
                    icon:const Icon(Icons.restart_alt,size: 20),constraints: const BoxConstraints.tightFor(width: 30,height: 30),
                    onPressed: onRefresh,
                  )
                ])
                // SizedBox(height: heightSpace(1)),
                // CommonButton(
                //   title:'Try again',
                //   onPressed: onRefresh,
                //   isBorder:true,
                //   icon: Icons.restart_alt,
                //   maximumSize: Size(widthSpace(39),79),
                // )
              ]),
        ));
  }

}