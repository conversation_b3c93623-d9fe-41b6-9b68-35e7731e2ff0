import 'package:darent/components/place_provider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SearchComponent extends SearchDelegate<Suggestion?> {
  SearchComponent(this.sessionToken) {
    apiClient = PlaceApiProvider(sessionToken);
  }

  final sessionToken;
  late PlaceApiProvider apiClient;
  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        tooltip: 'clear'.tr,
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      )
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return FutureBuilder<List<Suggestion>>(
      future: query == ""
          ? null
          : apiClient.fetchSuggestions(
          query, Get.deviceLocale?.languageCode ?? "en"),
      builder: (context, snapshot) => query == ''
          ? const SizedBox()
          : snapshot.hasData
          ? ListView.builder(
        itemBuilder: (context, index) => ListTile(
          title: Text("${snapshot.data![index].mainText}"),
          subtitle: Text("${snapshot.data![index].secondaryText}"),
          onTap: () {
            close(context, snapshot.data![index]);
          },
        ),
        itemCount: snapshot.data!.length,
      )
          : Text('loading'.tr),
    );
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return FutureBuilder<List<Suggestion>>(
      future: query == ""
          ? null
          : apiClient.fetchSuggestions(
              query, Get.deviceLocale?.languageCode ?? "en"),
      builder: (context, snapshot) => query == ''
          ? const SizedBox()
          : snapshot.hasData
              ? ListView.builder(
                  itemBuilder: (context, index) => ListTile(
                    title: Text("${snapshot.data![index].mainText}"),
                    subtitle: Text("${snapshot.data![index].secondaryText}"),
                    onTap: () {
                      close(context, snapshot.data![index]);
                    },
                  ),
                  itemCount: snapshot.data!.length,
                )
              : Text('loading'.tr),
    );
  }
}
