import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'custom_text.dart';
import '../helperMethods/authHelper.dart';
import '../helperMethods/translation_helper.dart';
import '../utils/sizeconfig.dart';

class TransactionComponent extends StatelessWidget {
  final int i;
  const TransactionComponent({Key? key, required this.i}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: widthSpace(3), vertical: widthSpace(2)),
      decoration: BoxDecoration(
          color: Colors.blueGrey[50], borderRadius: BorderRadius.circular(5)),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          CustomText(AuthHelper.c.myTransactions[i].transactionCategories,
              size: 1.9, weight: FontWeight.w500, color: Colors.blueGrey),
          CustomText(
              DateFormat(DateFormat.YEAR_ABBR_MONTH_DAY)
                  .format(AuthHelper.c.myTransactions[i].date),
              size: 1.9),
          // CustomText("${AuthHelper.c.myTransactions[index].date.day} ${months[Get.locale?.languageCode??'en']![AuthHelper.c.myTransactions[index].date.month - 1]} ${AuthHelper.c.myTransactions[index].date.year}", size: 1.9),
        ]),
        CustomText(
          "${AuthHelper.c.myTransactions[i].amount} ${Get.find<TranslationHelper>().translations.hostDashboard.sar}",
          color: AuthHelper.c.myTransactions[i].transactionTypes == "CREDIT"
              ? Colors.green
              : Colors.redAccent,
          weight: FontWeight.w500,
        ),
      ]),
    );
  }
}
