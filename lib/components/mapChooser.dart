import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:map_launcher/map_launcher.dart';

import '../helperMethods/translation_helper.dart';

showMapChooser(latitude, longitude, name) async {
  final availableMaps = await MapLauncher.installedMaps;
  return ViewsCommon.showModalBottom(
    DraggableScrollableSheet(
        maxChildSize: .35,
        initialChildSize: .30,
        expand: false,
        builder: (context, scrollController) {
          return Padding(
              padding: EdgeInsets.all(widthSpace(viewPadding)),
              child: Column(
                children: [
                  Align(
                    alignment: Alignment.center,
                    child: Container(
                      width: 26,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  AppBar(
                    automaticallyImplyLeading: false,
                    elevation: 1,
                    title: Text(Get.find<TranslationHelper>()
                        .translations
                        .reservation
                        .propertyLocation),
                  ),
                  ListView.separated(
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final map = availableMaps[index];
                      return InkWell(
                        onTap: () {
                          Navigator.pop(context);
                          map.showMarker(
                            coords: Coords(latitude, longitude),
                            title: name,
                          );
                        },
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            SizedBox(width: widthSpace(2.5)),
                            Expanded(
                              child: CustomText(map.mapName,
                                  size: 2.1, weight: FontWeight.w500),
                            ),
                            const Spacer(),
                            map.icon.contains("svg")
                                ? SvgPicture.asset(map.icon,
                                    height: widthSpace(10),
                                    width: widthSpace(10))
                                : ClipOval(
                                    child: Image(
                                      image:GlobalHelper.buildNetworkImageProvider(url: map.icon??''),
                                      height: widthSpace(10),
                                      width: widthSpace(10),
                                      fit: BoxFit.fitWidth,
                                      errorBuilder:
                                          (context, error, stackTrace) {
                                        return Icon(Icons.map_outlined,
                                            size: widthSpace(10));
                                      },
                                    ),
                                  ),
                            SizedBox(width: widthSpace(2.5)),
                          ],
                        ),
                      );
                    },
                    separatorBuilder: (context, index) {
                      return Divider(height: heightSpace(4));
                    },
                    itemCount: availableMaps.length,
                  ),
                ],
              ));
        }),
  );
}
