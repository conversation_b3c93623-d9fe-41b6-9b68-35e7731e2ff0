import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';

class CustomText extends StatelessWidget {
  final text,
      size,
      color,
      weight,
      lineSpacing,
      textAlign,
      underline,
      strikeThrough,
      textOverflow,
      maxlines;
  const CustomText(this.text,
      {Key? key,
      this.size = 2.0,
      this.maxlines,
      this.color = Colors.black,
      this.weight = FontWeight.normal,
      this.lineSpacing,
      this.textAlign,
      this.underline = false,
      this.strikeThrough = false,
      this.textOverflow})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text ?? "",
      textAlign: textAlign,
      maxLines: maxlines,
      textHeightBehavior: const TextHeightBehavior(
        applyHeightToLastDescent: false,
        applyHeightToFirstAscent: true,
        leadingDistribution: TextLeadingDistribution.proportional,
      ),
      style: TextStyle(
        fontSize: heightSpace(size),
        height: lineSpacing,
        color: color,
        fontWeight: weight,
        decoration: strikeThrough
            ? TextDecoration.lineThrough
            : underline
                ? TextDecoration.underline
                : TextDecoration.none,
        leadingDistribution: TextLeadingDistribution.proportional,
        overflow: textOverflow,
      ),
    );
  }
}
