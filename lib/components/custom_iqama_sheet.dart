import 'package:darent/components/common_button.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/custom_textfield.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/remote_config.dart';
import 'package:darent/helperMethods/translation_helper.dart';
import 'package:darent/models/listing_prefs_model.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:intl_phone_field/intl_phone_field.dart';

import '../helperMethods/authHelper.dart';

dynamic customIqamaSheet({bool isStep = false}) {
  ViewsCommon.showModalBottom(DraggableScrollableSheet(
      maxChildSize: .75,
      initialChildSize: .75,
      expand: false,
      builder: (context, scrollController) {
        return Obx(
          () => SingleChildScrollView(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              SizedBox(height: heightSpace(2.5)),
              CustomText(
                  Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .nationalitySelect,
                  size: 2.1,
                  weight: FontWeight.w500),
              Row(
                children: [
                  Radio<int>(
                    value: 0,
                    groupValue: AuthHelper.c.iqamaValue.value,
                    onChanged: AuthHelper.c.updateIqamaValue,
                  ),
                  CustomText(Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .nationalityOption1),
                ],
              ),
              Row(
                children: [
                  Radio<int>(
                    value: 1,
                    groupValue: AuthHelper.c.iqamaValue.value,
                    onChanged: AuthHelper.c.updateIqamaValue,
                  ),
                  CustomText(Get.find<TranslationHelper>()
                      .translations
                      .usersProfile
                      .nationalityOption2),
                ],
              ),
              // Row(
              //   children: [
              //     Radio<int>(
              //       value: 3,
              //       groupValue: AuthHelper.c.iqamaValue.value,
              //       onChanged:AuthHelper.c.updateIqamaValue,
              //     ),
              //     CustomText(Get.find<TranslationHelper>().translations.usersProfile.nationalityOption4),
              //   ],
              // ),
              if (AuthHelper.c.iqamaValue.value != 5) ...[
                SizedBox(height: heightSpace(2.5)),
                if (AuthHelper.c.iqamaValue.value == 2) ...[
                  Container(
                      width: double.maxFinite,
                      decoration: const BoxDecoration(
                          border: Border(bottom: BorderSide(width: .5))),
                      alignment: Alignment.centerLeft,
                      padding:
                          const EdgeInsets.only(left: 15, right: 15, bottom: 5),
                      child: Directionality(
                        textDirection: TextDirection.ltr,
                        child: IntlPhoneField(
                          controller: AuthHelper.c.iqma,
                          initialCountryCode: AuthHelper.c.selectedCountryCode,
                          onCountryChanged: (countryCode) =>
                              AuthHelper.c.selectedCountryCode =
                                  countryCode.code.toLowerCase(),
                        ),
                      )
                      // CountryCodePicker(
                      //   onChanged: (countryCode){
                      //     AuthHelper.c.selectedCountryCode = countryCode.code!.toLowerCase();
                      //   },
                      //   builder: (e) => Row(
                      //     children: [
                      //       Image.asset(
                      //         e!.flagUri!,
                      //         package: 'country_code_picker',
                      //         width: widthSpace(7),
                      //       ),
                      //       SizedBox(width: widthSpace(20),),
                      //       Text(e.name!),
                      //       const Spacer(),
                      //       Icon(
                      //         Icons.arrow_drop_down,
                      //         color: Colors.grey,
                      //         size: widthSpace(5),
                      //       )
                      //     ],
                      //   ),
                      //   initialSelection: 'sa',
                      // ),
                      ),
                  CustomTextField(
                    controller: AuthHelper.c.iqma,
                    maxLength: 9,
                    hint: Get.find<TranslationHelper>()
                        .translations
                        .usersProfile
                        .enterYourPass,
                    textCapitalization: TextCapitalization.characters,
                  )
                ] else ...[
                  Container(
                    width: double.maxFinite,
                    padding: EdgeInsets.only(
                        bottom: heightSpace(2), top: heightSpace(1.7)),
                    decoration: const BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Color.fromARGB(255, 230, 230, 230),
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CustomText(
                          AuthHelper.c.iqamaValue.value == 0
                              ? AuthHelper.c.dobHijriDate.value == null
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .hostDashboard
                                      .dateOfBirth
                                  : "${AuthHelper.c.dobHijriDate.value!.year}-${GlobalHelper.twoNumberFormat(AuthHelper.c.dobHijriDate.value!.month)}-${GlobalHelper.twoNumberFormat(AuthHelper.c.dobHijriDate.value!.day)}"
                              : AuthHelper.c.dobVerification.value == null
                                  ? Get.find<TranslationHelper>()
                                      .translations
                                      .hostDashboard
                                      .dateOfBirth
                                  : formDateFormat.format(
                                      AuthHelper.c.dobVerification.value!),
                          size: 2.1,
                          color: AuthHelper.c.dobVerification.value == null
                              ? const Color(greyText)
                              : null,
                          weight: FontWeight.w500,
                        ),
                        InkWell(
                          onTap: AuthHelper.c.selectDobVerification,
                          child: const Icon(Icons.calendar_month_outlined),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: heightSpace(2.5)),
                  CustomTextField(
                    controller: AuthHelper.c.iqma,
                    maxLength: 10,
                    hint: Get.find<TranslationHelper>()
                        .translations
                        .usersProfile
                        .enterYourIqama,
                    inputType: TextInputType.number,
                  ),
                  SizedBox(height: heightSpace(2.5)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Row(
                        children: [
                          Radio(
                            value: true,
                            groupValue: AuthHelper.c.iqamaValue.value == 0,
                            onChanged: (val) {},
                          ),
                          CustomText(Get.find<TranslationHelper>()
                              .translations
                              .usersProfile
                              .hijri),
                        ],
                      ),
                      Row(
                        children: [
                          Radio(
                            value: AuthHelper.c.iqamaValue.value != 0,
                            groupValue: true,
                            onChanged: (val) {},
                          ),
                          CustomText(Get.find<TranslationHelper>()
                              .translations
                              .usersProfile
                              .gregorian),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: heightSpace(2.5)),
                  RichText(
                    text: TextSpan(
                        text:
                            "${Get.find<TranslationHelper>().translations.signUp.bySubmittingYourIqama} ",
                        style: TextStyle(
                            color: const Color(greyText),
                            fontSize: heightSpace(1.75)),
                        children: <TextSpan>[
                          TextSpan(
                              text: Get.find<TranslationHelper>()
                                  .translations
                                  .footer
                                  .privacyPolicy,
                              style: TextStyle(color: Colors.blue[900]),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () => AuthHelper.c.openWebView(
                                    'privacy-policy',
                                    Get.find<TranslationHelper>()
                                        .translations
                                        .footer
                                        .privacyPolicy))
                        ]),
                  ),
                ],
                SizedBox(height: heightSpace(2.5)),
                Align(
                    alignment: Alignment.bottomRight,
                    child: CommonButton(
                        title: Get.find<TranslationHelper>()
                            .translations
                            .usersProfile
                            .save,
                        onPressed: () => AuthHelper.c.submitIqma(
                            isStep: isStep,
                            maxLength:
                                AuthHelper.c.iqamaValue.value == 2 ? 9 : 10),
                        isLoading: AuthHelper.c.isLoading.isTrue,
                        backgroundBg: isHost ? Colors.black : null,
                        horizontalPadding: 8))
              ],
            ]).paddingAll(widthSpace(viewPadding)),
          ),
        );
      }));
}

dynamic customCommunicationSheet() {
  ViewsCommon.showModalBottom(DraggableScrollableSheet(
      maxChildSize: .60,
      initialChildSize: .60,
      expand: false,
      builder: (context, scrollController) {
        return Obx(
          () => SingleChildScrollView(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              SizedBox(height: heightSpace(3)),
              CustomText(
                  Get.find<TranslationHelper>()
                          .translations
                          .usersProfile
                          .yourPrefMethod ??
                      "What is your preferred method of communication?",
                  size: 2.4,
                  maxlines: 2,
                  weight: FontWeight.w500,
                  textAlign: TextAlign.center),
              Padding(
                padding: EdgeInsets.symmetric(vertical: heightSpace(1.5)),
                child: Column(children: [
                  for (GeneralModel item
                      in AuthHelper.c.preferredContactTypeList ?? []) ...[
                    InkWell(
                      onTap: () =>
                          AuthHelper.c.selectPreferredContactType(item),
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 800),
                        curve: Curves.fastOutSlowIn,
                        margin: EdgeInsets.only(bottom: heightSpace(2)),
                        padding: EdgeInsets.symmetric(
                            horizontal: widthSpace(6), vertical: widthSpace(5)),
                        decoration: BoxDecoration(
                            border: item.isChecked!
                                ? Border.all(color: Colors.black)
                                : Border.all(color: const Color(greyBorder)),
                            borderRadius: BorderRadius.circular(8)),
                        child: Row(mainAxisSize: MainAxisSize.max, children: [
                          Expanded(
                              flex: 2,
                              child: Row(
                                children: [
                                  item.image == null
                                      ? Image.asset("assets/icons/apartment.png")
                                      : GlobalHelper.buildNetworkSvgWidget(
                                    url: item.image ??"",
                                    width: widthSpace(8),
                                    defaultOption: Image.asset("assets/icons/apartment.png"),
                                  ),
                                ],
                              )),
                          Expanded(
                            flex: 3,
                            child: CustomText(
                                Get.locale?.languageCode == "ar"
                                    ? item.nameAr
                                    : item.name,
                                color: item.isChecked!
                                    ? Colors.black
                                    : Colors.grey[700],
                                weight: FontWeight.w500,
                                size: 2.1),
                          ),
                        ]),
                      ),
                    )
                  ]
                ]),
              ),
              SizedBox(height: heightSpace(1.5)),
              Align(
                  alignment: Alignment.bottomRight,
                  child: CommonButton(
                      title: Get.find<TranslationHelper>()
                          .translations
                          .usersProfile
                          .save,
                      onPressed: AuthHelper.c.editPreferredContact,
                      isLoading: AuthHelper.c.isLoading.isTrue,
                      backgroundBg: isHost ? Colors.black : null,
                      horizontalPadding: 8))
            ]).paddingAll(widthSpace(viewPadding)),
          ),
        );
      }));
}
