import 'package:darent/components/custom_text.dart';
import 'package:darent/models/host/dwelling_model.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shimmer/shimmer.dart';
import 'package:url_launcher/url_launcher.dart';

import '../utils/constants.dart';

class ViewsCommon{
  static Map<String,TextEditingController> priceControllers = {"price":TextEditingController(text:'50')};

  static addPriceControllers(List? prices){
    if(prices!=null){
      for(String price in prices){
        priceControllers[price] = TextEditingController(text:'50');
      }
    }
  }
  static addDwellingPriceToControllers(SpecialDaysPrice? prices, perNightPrice){
    priceControllers = {"price":TextEditingController(text:perNightPrice.toStringAsFixed(0))};
    if(prices!=null){
      prices.toJson().forEach((key, value) {
        priceControllers[key] = TextEditingController(text: value?.toStringAsFixed(0) ?? '');
      });
    }
  }

  static Widget shadowContainer(Widget child,{double padding=0,double? height}){
    return Container(
        height: height,
        padding: EdgeInsets.all(padding),
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Color(greyBorder)),
            boxShadow: ViewsCommon.boxShadow
        ),
        child:child
    );
  }

  static Shimmer getShimmerLoading(){
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[300]!),

        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(width: widthSpace(20),),
                      CircleAvatar(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(100),
                            color: Colors.white,
                          ),
                          width: double.infinity,
                          height: 18.0,
                        ),

                      )
                    ],
                  ),
                  SizedBox(height:heightSpace(1)),
                  Container(
                    width: double.infinity,
                    height: 18.0,
                    color: Colors.white,
                  ),
                  SizedBox(height: heightSpace(0.7)),
                  Container(
                    width: double.infinity,
                    height: 18.0,
                    color: Colors.white,
                  ),
                  SizedBox(height: heightSpace(.7)),
                  Container(
                    width: double.infinity,
                    height: 18.0,
                    color: Colors.white,
                  ),
                  SizedBox(height: heightSpace(0.2)),

                ],
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical:widthSpace(3)),
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                  border: Border(top: BorderSide(color: Colors.white))),
              child: Container(
                width: widthSpace(80),
                height: 18.0,
                color: Colors.white,
              ),),
          ],
        ),

      ),
    );

  }
  static String formatAppIdToName(String appId) {
    // Remove common package name prefixes for better readability
    String name = appId
        .replaceAll("com.", "")
        .replaceAll("net.", "")
        .replaceAll("org.", "")
        .replaceAll("android.", "")
        .replaceAll("apple.", "")
        .replaceAll(".sharingextension", "") // e.g., iOS-specific suffixes
        .replaceAll(".messaging", "")
        .replaceAll(".apps", "")
        .replaceAll(".android", "")
        .replaceAll(".ios", "");
    // Replace dots with spaces and capitalize words for better readability
    name = name.split('.').map((word) {
      return word[0].toUpperCase() + word.substring(1);
    }).join(' ');
    return name;
  }
  static clearPriceControllers(){
    for(String price in priceControllers.keys){
      priceControllers[price]!.text = "50";
    }
  }
  static share(code,{required String title,required String itemId}){
    Share.share(code).then((shareRes){
      if(shareRes.status==ShareResultStatus.success){
        Map form = {
        'contentType':title,
        'itemId': itemId,
        'method': formatAppIdToName(shareRes.raw)
        };
        analytics.logShare(
            contentType:title,
            itemId: itemId,
            method: formatAppIdToName(shareRes.raw));
      }
    });
  }
  static showModalBottom(Widget child,{Color backgroundColor=Colors.white,double radius=30,Function(dynamic)? then}){
    Get.bottomSheet(
        child, shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(
          top: Radius.circular(radius))),
        clipBehavior: Clip.antiAlias,
        isScrollControlled: true,
        backgroundColor: backgroundColor
    ).then(then??(_){});
  }
  static List<BoxShadow> boxShadow = [
      const BoxShadow(
        color: Colors.black12,
        spreadRadius: .2,
        blurRadius: 2,
        offset: Offset(0, 2),
      )
    ];
  static final shimmerGradient = LinearGradient(colors: [
  Colors.grey[300]!,
  Colors.grey[100]!,
  ]);
  Future<void> launchAppStore() async {
    const String appId = 'your_app_id'; // Replace with your app ID

    // Replace these URLs with your app's URLs on Google Play Store and Apple App Store
    final String googlePlayUrl = 'https://play.google.com/store/apps/details?id=$appId';
    final String appStoreUrl = 'https://apps.apple.com/app/id$appId';

    // Try to open the app store review page based on platform
    try {
      final Uri googlePlayUri = Uri.parse(googlePlayUrl);
      final Uri appStoreUri = Uri.parse(appStoreUrl);

      if (await canLaunchUrl(googlePlayUri)) {
        await launchUrl(googlePlayUri);
      } else if (await canLaunchUrl(appStoreUri)) {
        await launchUrl(appStoreUri);
      } else {
        throw 'Could not launch app store.';
      }
    } catch (e) {
      // Avoid using print in production code
      debugPrint('Error launching app store: $e');
    }
  }
static switchButton(String name,{onConfirm,bool toogle=false,double fontSize=2}){
        return Row( mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CustomText(name,size:fontSize),
            GestureDetector(
            onTap: onConfirm,
            child: Container(
              width: 45,
              height: 25,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(15),
                color: toogle ? Colors.black : Colors.black.withValues(red: 0, green: 0, blue: 0, alpha: 46),
              ),
              child: Padding(
                padding: const EdgeInsets.all(2.5),
                child: Align(
                  alignment: toogle
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: Container(
                    width: 22,
                    height: 22,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                    ),
                    child: Center(
                      child: Visibility(
                        visible: toogle,
                        child: const Icon(Icons.done, size: 17, color: Colors.black),
                      ),
                    ),
                  ),
                ),
              ),
            ),
      ),
          ],
        );
      }
    static showSnackbar(String desc,{DialogKeyword keyword=DialogKeyword.success, onConfirmd,int displayTime = 1000}){
      if(!Get.isSnackbarOpen){
        Get.snackbar(
            '',
            '',
            duration: Duration(milliseconds: displayTime),
            backgroundColor: Colors.white.withValues(red: 255, green: 255, blue: 255, alpha: 217),
            messageText: Row(
              children: [
                CircleAvatar(
                    backgroundColor: Color(keyword == DialogKeyword.success?successColor : themeColor),
                    radius: widthSpace(4),
                    child:  Center(
                      child: Icon(
                          keyword == DialogKeyword.success ? Icons.done : Icons.warning,
                          size: 18, color: Colors.white),
                    )),
                const SizedBox(width: 15), // Spacer between icon and text
                Expanded(child: CustomText(desc)),
              ],
            ),
            titleText: const SizedBox(),
            padding: const EdgeInsets.only(left: 20,right: 20,top: 10, bottom: 15),
            borderWidth: 1,
            borderColor: Colors.grey.withValues(red: 128, green: 128, blue: 128, alpha: 51),
            borderRadius: 10,
            snackPosition: SnackPosition.BOTTOM,
            boxShadows: [ BoxShadow(
              blurRadius: 2,
              color: Colors.grey.withValues(red: 128, green: 128, blue: 128, alpha: 102),
              offset:  const Offset(2,4),
            )],
            margin: const EdgeInsets.only(bottom: 35, left: 10,right: 10),
            mainButton: onConfirmd);
      }
    }
}