import 'package:carousel_slider/carousel_slider.dart';
import 'package:darent/components/custom_text.dart';
import 'package:darent/components/views_common.dart';
import 'package:darent/controllers/property_detail_controller.dart';
import 'package:darent/helperMethods/globalHelpers.dart';
import 'package:darent/helperMethods/search_helper.dart';
import 'package:darent/helperMethods/wishlist_helper.dart';
import 'package:darent/models/homeProperty.dart';
import 'package:darent/models/translationModel.dart';
import 'package:darent/screens/property_single/property_details.dart';
import 'package:darent/utils/constants.dart';
import 'package:darent/utils/sizeconfig.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../analytics/analytics_constants.dart';
import '../analytics/analytics_manager.dart';
import '../utils/routes.dart';

class ProductItem extends StatelessWidget {
  final HomeProperty data;
  final wishlistGroupIndex;
  final TranslationModel? translations;
  final String? wishlistLoader;
  final Function? onPhotoChanged;
  final Function? onTap;
  const ProductItem(
      {super.key,
      required this.data,
      this.wishlistGroupIndex = -1,
      required this.translations,
      this.wishlistLoader,
      this.onPhotoChanged,
      this.onTap});
  @override
  Widget build(BuildContext context) {
    final borderRadius = heightSpace(1.6);
    return InkWell(
      //onTap ??
      onTap: go,
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Container(
          clipBehavior: Clip.antiAlias,
          height: heightSpace(31),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius)),
          child: Stack(fit: StackFit.expand, children: [
            data.propertyPhotos!.length < 2
            ? renderImage(data.photo)
            : CarouselSlider(
                items: (data.propertyPhotos!.length > 5
                        ? data.propertyPhotos!.sublist(0, 5)
                        : data.propertyPhotos)!
                    .map((item) {
                  return renderImage(item.photo);
                }).toList(),
                options: CarouselOptions(
                    height: heightSpace(55),
                    viewportFraction: 1.1,
                    onPageChanged: (index, reason) {
                      if (onPhotoChanged != null) {
                        onPhotoChanged!(data.propertyPhotos![index].id);
                      }
                    })),
            Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //wishlistGroupIndex==-1
                  Row(
                      mainAxisAlignment:MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                //          Column(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                // crossAxisAlignment: CrossAxisAlignment.start,
                // children: [
                        // if (!SearchHelper.planTripCleared && !data.available)
                        //   tagView(icon:Icons.not_interested,title:translations?.propertySingle.dateNotAvailable??''),
                        // else if(data.daysDiscount!=null) tagView(title:translations?.propertySingle.toJson()[data.daysDiscount]??'',value:data.daysDiscountValue,isDiscount: true)
                        // else const SizedBox.shrink(),

                             Row(
                                 crossAxisAlignment: CrossAxisAlignment.center,
                                 children: [
                               if ((data.views ?? "").isNotEmpty && data.views != "0")
                                 Container(
                                   padding: EdgeInsets.symmetric(horizontal: widthSpace(2),vertical: widthSpace(1)),
                                   decoration: BoxDecoration(
                                     borderRadius: BorderRadius.circular(borderRadius),
                                       color: Colors.black.withOpacity(.6)),
                                   child: Row(
                                     mainAxisAlignment: MainAxisAlignment.center,
                                     children: [
                                       Icon(Icons.remove_red_eye_outlined,
                                           color: Colors.white,
                                           size: widthSpace(5)),
                                       CustomText(data.views ?? "",color: Colors.white,size: 1.6,weight: FontWeight.w500).paddingOnly(left: widthSpace(1)),
                                     ],
                                   ),
                                 ),
                               if (!SearchHelper.planTripCleared && !data.available)
                                 Container(
                                   padding: EdgeInsets.symmetric(horizontal: widthSpace(2),vertical: widthSpace(1)),
                                 decoration: BoxDecoration(
                                     color: Colors.white.withOpacity(.6),
                                   borderRadius: BorderRadius.circular(borderRadius),
                                     border: Border.all(color: Colors.white)
                                 ),
                                 child: Row(
                                   children: [
                                     SvgPicture.asset('assets/icons/info.svg',width: widthSpace(5)),
                                     CustomText(translations?.general.notAvailable,color: const Color(0xffD1372E),size: 1.6,weight: FontWeight.w500).paddingSymmetric(horizontal: widthSpace(1))
                                   ],
                                 ),
                               ).paddingOnly(left: widthSpace(2))
                               else if(data.exclusive)
                                 Container(
                                   padding: EdgeInsets.symmetric(horizontal: widthSpace(2),vertical: widthSpace(1)),
                                       decoration: BoxDecoration(
                                           color: const Color(themeColor).withOpacity(.6),
                                           borderRadius: BorderRadius.circular(borderRadius),
                                         border: Border.all(color: const Color(themeColor))
                                           ),
                                       child: Row(
                                         children: [
                                           SvgPicture.asset('assets/icons/exclusive.svg',width: widthSpace(5)),
                                           CustomText(translations?.propertySingle.exclusive,size: 1.6,weight: FontWeight.w500).paddingSymmetric(horizontal: widthSpace(1))
                                         ],
                                       ),
                                     ).paddingOnly(left: widthSpace(2)),
                             ]).paddingAll(widthSpace(2)),
                // ]),
                        // Container(
                        //   padding: EdgeInsets.symmetric(horizontal:widthSpace(3.4),vertical: widthSpace(1.35)),
                        //   margin: EdgeInsets.all(widthSpace(2)),
                        //   decoration: BoxDecoration(
                        //     color: Colors.black.withOpacity(.6),
                        //     borderRadius: BorderRadius.circular(100),
                        //   ),child: Row(children: [
                        //   Icon(Icons.remove_red_eye,color: Colors.white,size: widthSpace(4)),
                        //   SizedBox(width: widthSpace(2)),
                        //   Text(data.views!,style:const TextStyle(color: Colors.white,fontSize: 10)),
                        // ])),
                        Column(children: [
                          wishlistLoader == data.slug
                              ? Container(
                            margin: EdgeInsets.all(widthSpace(3)),
                            width: 16,
                            height: 16,
                            child: const CircularProgressIndicator(
                                strokeWidth: 2),
                          )
                              : InkWell(
                            child: CircleAvatar(
                              backgroundColor: const Color(0xffD9D9D9CC).withOpacity(.8),
                              child: Icon(
                                  data.wishlist?Icons.favorite:Icons.favorite_border_rounded,
                                  color: Colors.black,
                                  size: widthSpace(5)),
                            ),
                            onTap: () async {
                              bool fromWishlist = wishlistGroupIndex > -1;
                              if (data.wishlist) {
                                // if (fromWishlist &&
                                //     SearchHelper
                                //             .c
                                //             .wishlistGroups[
                                //                 wishlistGroupIndex]
                                //             .wishlistProperties
                                //             ?.length ==
                                //         1) {
                                //   Get.back();
                                // }
                                await WishlistHelper.removeFromWishlist(
                                    data);
                              } else if (fromWishlist) {
                                WishlistHelper.toggleWishlist(
                                    data, wishlistGroupIndex);
                              } else {
                                WishlistHelper.checkWishlist(data);
                              }
                            },
                          ),
                          SizedBox(height: widthSpace(2)),
                          InkWell(
                            onTap:share,
                            child: CircleAvatar(
                              backgroundColor: const Color(0xffD9D9D9CC).withOpacity(.8),
                              child: Icon(
                                  Icons.share_outlined,
                                  size: widthSpace(6),color: Colors.black),
                            ),
                          ),
                        ]).paddingAll(widthSpace(2))
                      ]),
                  Padding(
                    padding: EdgeInsets.all(widthSpace(viewPadding)),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (data.badge == "top_feature")
                          Container(
                            padding: const EdgeInsets.all(1), // Border thickness
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(borderRadius / 2 + 1), // Slightly larger radius for outer container
                            ),
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: widthSpace(2),
                                  vertical: widthSpace(0.5)),
                              decoration: BoxDecoration(
                                  color: const Color(0xffB3A5E9),
                                  borderRadius: BorderRadius.circular(borderRadius / 2)
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SvgPicture.asset('assets/icons/top.svg', width: widthSpace(5)),
                                  CustomText(translations?.general.topFeature, size: 1.6, weight: FontWeight.w500).paddingSymmetric(horizontal: widthSpace(1))
                                ],
                              ),
                            ),
                          )
                        else if (data.badge == "high_demand")
                          Container(
                            padding: const EdgeInsets.all(1), // Border thickness
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(borderRadius / 2 + 1), // Slightly larger radius for outer container
                            ),
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: widthSpace(2),
                                  vertical: widthSpace(0.5)),
                              decoration: BoxDecoration(
                                  color: const Color(0xffE86629),
                                  borderRadius: BorderRadius.circular(borderRadius / 2)
                              ),
                              child: Row(
                                children: [
                                  SvgPicture.asset('assets/icons/demand.svg', width: widthSpace(5)),
                                  CustomText(translations?.general.highDemand, size: 1.6, weight: FontWeight.w500).paddingSymmetric(horizontal: widthSpace(1))
                                ],
                              ),
                            ),
                          ),

                        const Spacer(),
                        if (data.propertyPhotos!.isNotEmpty)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: (data.propertyPhotos!.length > 5
                                ? data.propertyPhotos!.sublist(0, 5)
                                : data.propertyPhotos)!
                                .map((item) => Container(
                              width: data.currentSliderId == item.id ? 11 : 8,
                              height: data.currentSliderId == item.id ? 11 : 8,
                              margin: const EdgeInsets.only(right: 5),
                              decoration: BoxDecoration(
                                  color: data.currentSliderId == item.id
                                      ? Colors.white
                                      : Colors.white.withOpacity(.5),
                                  shape: BoxShape.circle),
                            ))
                                .toList(),
                          ),
                        if (data.discount != null)
                          Container(
                            padding: const EdgeInsets.only(left: 15, right: 15, bottom: 5),
                            decoration: BoxDecoration(
                                color: const Color(themeColor).withOpacity(.7),
                                borderRadius: BorderRadius.circular(50)),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                CustomText(data.discount.toString(),
                                    size: 2.3, weight: FontWeight.w500, color: Colors.white),
                                const CustomText("%", weight: FontWeight.bold, color: Colors.white)
                              ],
                            ),
                          )
                        else
                          const SizedBox.shrink(),
                        Spacer(flex: data.badge == "none" ?1 :5,),
                      ],
                    ),
                  ),
                ])
          ]),
        ),
        Row(children: [
          if(data.bedroom != null && data.bedroom != 0)...[
            boxWidget('room','${data.bedroom??1}')
          ],
          if(data.beds != null && data.beds != 0)...[
            SizedBox(width: widthSpace(3)),
            boxWidget('bed','${data.beds??1}'),
          ],
          if(data.bathrooms != null && data.bathrooms != 0)...[
            SizedBox(width: widthSpace(3)),
            boxWidget('bathtub','${data.bathrooms??1}'),
          ],
        ]).paddingSymmetric(vertical: heightSpace(2)),
        Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: CustomText(
                  data.title ?? "",
                  weight: FontWeight.w500,
                  textOverflow: TextOverflow.ellipsis,
                ),
              ),
              if(data.isNewLable)
                Row(
                  children: [
                    const Icon(
                      Icons.star_rounded,
                      size: 17,
                      // color: Colors.white,
                    ),
                    const SizedBox(width: 3,),
                    CustomText(
                      'new'.tr,
                      size: 1.5,
                      // color: Colors.white,
                    ),
                  ],
                )
              else
              Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
                const Icon(Icons.star_rounded, size: 20),
                CustomText(data.rating, size: 1.7, weight: FontWeight.bold),
                CustomText(" (${data.ratingCount})", size: 1.7,color: Colors.grey[700]),
              ])
            ]),
        // SizedBox(height: heightSpace(.8)),
        InkWell(
          onTap:()=>go(scrollToMap: true),
          child: Row(
            children: [
              SvgPicture.asset('assets/icons/location_pin.svg'),
              SizedBox(width:widthSpace(1.4)),
              CustomText(data.location ?? "", size: 1.8, color: Colors.grey[700], textOverflow: TextOverflow.ellipsis,underline: true),
            ]).paddingSymmetric(vertical: heightSpace(1.5)),
        ),
        // CustomText(
        //     data.summary ?? translations?.home.noDescription ?? "No Description",
        //     size: 1.75,
        //     color: Colors.black54,
        //     maxlines: 2),
        // CustomText(translations?.propertySingle.readMore ?? "Read More",
        //     color: const Color(themeColor),
        //     underline: true,
        //     size: 1.8,
        //     weight: FontWeight.w500),
        // SizedBox(height: heightSpace(.8)),

        // CustomText(
        //     '${data.beds ??0} ${translations?.propertySingle.bed}',
        //     size: 1.75,
        //     color: Colors.black54,
        //     maxlines: 2),
        // SizedBox(height: heightSpace(.8)),
        Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
          Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
            Row(children: [
              CustomText('${data.dayPrice?.toStringAsFixed(2)} ',size: 1.9,weight: FontWeight.w500),
              SvgPicture.asset('assets/icons/riyal.svg'),
              CustomText(' ${translations?.propertySingle.perNight}',size: 1.8,color: Color(greyText)),
            ]),
            InkWell(
              onTap: data.available?()=>showBottomSheet(translations!):null,
              child: Row(
                children: [
                  CustomText(
                      '${translations?.listing.totalPrice} ${data.totalPrice?.toStringAsFixed(2)}',size: 1.9,color: Colors.grey[700],underline: true),
                  SizedBox(width:widthSpace(1)),
                  SvgPicture.asset('assets/icons/riyal.svg',color: Colors.grey[700]),
                ],
              ).paddingSymmetric(vertical: heightSpace(1)),
            )
          ]),
              if(data.daysDiscount!=null || data.hasDailyDiscount==true)Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(heightSpace(1.8)),
                  border: Border.all(color: const Color(successColor)),
                ),
                child: IntrinsicHeight(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: widthSpace(2)),
                        decoration: BoxDecoration(
                          color: const Color(successColor),
                          borderRadius: BorderRadius.horizontal(
                            left: Get.locale?.languageCode=='ar'?Radius.zero:Radius.circular(heightSpace(1.6)),
                            right: Get.locale?.languageCode=='ar'?Radius.circular(heightSpace(1.6)):Radius.zero,
                          ),
                        ),
                        child: Center(
                          child: SvgPicture.asset(
                            'assets/icons/discount_filled.svg',
                            height: 20,
                          ),
                        ),
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomText(
                            translations?.hostDashboard.discounts,
                            size: 1.8,
                            color: const Color(successColor),
                            weight: FontWeight.normal,
                          ),
                          if(data.daysDiscount == "monthly_and_weekly_discount")...[
                            CustomText(
                              "${translations?.hostDashboard.weekly} . ${translations?.hostDashboard.monthly}",
                              size: 1.8,
                              color: const Color(successColor),
                              weight: FontWeight.bold,
                            ),
                          ]
                          else if(data.daysDiscount == "weekly_discount_available")...[
                            CustomText(
                              "${translations?.hostDashboard.weekly}${data.hasDailyDiscount==true?' . ${translations?.listingPrice.dailyDiscount}':''}",
                              size: 1.8,
                              color: const Color(successColor),
                              weight: FontWeight.bold,
                            ),
                          ]
                          else if(data.daysDiscount == "monthly_discount_available")...[
                              CustomText(
                                "${translations?.hostDashboard.monthly}${data.hasDailyDiscount==true?' . ${translations?.listingPrice.dailyDiscount}':''}",
                                size: 1.8,
                                color: const Color(successColor),
                                weight: FontWeight.bold,
                              ),
                            ]else if(data.hasDailyDiscount==true)CustomText(
                              translations?.listingPrice.dailyDiscount,
                              size: 1.8,
                              color: const Color(successColor),
                              weight: FontWeight.bold,
                            ),
                        ],
                      ).paddingSymmetric(horizontal: 4, vertical: 4),
                    ],
                  ),
                ),
              ),

        ]),


        // if (data.totalPrice != data.beforeDiscount) ...[
        //   Text.rich(
        //     TextSpan(
        //       children: [
        //         TextSpan(
        //             text:
        //                 "${data.dayPrice?.toStringAsFixed(2)} ${translations?.hostDashboard.sar ?? "SAR"}",
        //             style: TextStyle(
        //                 fontSize: heightSpace(1.75),
        //                 color: Colors.grey,
        //                 decoration: TextDecoration.lineThrough)),
        //         TextSpan(
        //             text:
        //                 " ${(data.totalPrice! / (data.numberOfDays ?? 1)).toStringAsFixed(2)} ${translations?.hostDashboard.sar ?? "SAR"}",
        //             style: TextStyle(
        //                 fontWeight: FontWeight.w500,
        //                 fontSize: heightSpace(1.9))),
        //         TextSpan(
        //           text: "/${translations?.propertySingle.night ?? "night"}",
        //           style: TextStyle(
        //               color: Colors.grey,
        //               fontWeight: FontWeight.w500,
        //               fontSize: heightSpace(1.8)),
        //         ),
        //       ],
        //     ),
        //   )
        // ] else ...[
        //   Text.rich(
        //     TextSpan(
        //       children: [
        //         TextSpan(
        //             text:
        //                 "${data.dayPrice?.toStringAsFixed(2)} ${translations?.hostDashboard.sar ?? "SAR"}",
        //             style: TextStyle(
        //                 fontSize: heightSpace(1.9),
        //                 fontWeight: FontWeight.w500)),
        //         TextSpan(
        //           text: "/${translations?.propertySingle.night ?? "night"}",
        //           style: TextStyle(
        //               color: Colors.grey,
        //               fontWeight: FontWeight.w500,
        //               fontSize: heightSpace(1.8)),
        //         )
        //       ],
        //     ),
        //   ),
        // ],
        // if ((data.numberOfDays ?? 0) > 0) ...[
        //   SizedBox(height: heightSpace(.8)),
        //   CustomText(
        //       '${translations?.search.totalOf} (${data.numberOfDays} ${translations?.search.nights}) ${data.totalPrice?.toStringAsFixed(2)} ${translations?.hostDashboard.sar}',
        //       color: const Color(greyText),
        //       size: 1.9,
        //       weight: FontWeight.w500)
        // ]
      ]),
    );
  }
  go({bool scrollToMap=false}) {
    Get.delete<PropertyDetailController>();
    Get.to(() => PropertyDetailScreen(slug: data.slug!, data: data,scrollToMap:scrollToMap))?.then((_){
      if(onTap!=null){
        onTap!(refresh: true);
      }
    });
    // Get.toNamed('/${Routes.propertySingle}/${data.slug}?fromHome=true');
  }
  share(){
    ViewsCommon.share(
        '$baseUrl/${Get.locale?.languageCode ?? 'en'}/properties/${data.slug}',
        title: 'Property',
        itemId: '${data.id}');
    Get.find<AnalyticsManager>().trackEvent(
      AnalyticsEvents.propertyShare,
      eventAttributes: {
        AnalyticsAttributes.guestId: userModel.value?.id?.toString(),
        AnalyticsAttributes.propertyId: data.id?.toString(),
        AnalyticsAttributes.propertyType: data.propertyTypeName,
        AnalyticsAttributes.city: data.location,
        AnalyticsAttributes.sessionTimestamp: DateTime.now().toIso8601String(),
        'share_method': 'native_share',
      },
    );
  }
  showBottomSheet(TranslationModel trans){
    if(data.propertyPrice==null){
      SearchHelper.c.getPrice(data);
    }
    if(data.priceError!=null){
      SearchHelper.c.priceMessage.value = data.priceError;
    }
    print(data.daysDiscount);
    ViewsCommon.showModalBottom(
        SizedBox(height: heightSpace(40),child: BottomSheet(
            onClosing:(){},
            builder: (ctx) => Obx(()=>Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(
                        alignment: Alignment.topRight,
                        child: CircleAvatar(
                          backgroundColor: Colors.grey[200],
                          child: IconButton(
                          onPressed: Get.back,
                          padding: EdgeInsets.zero,
                          icon: const Icon(Icons.close,size: 20,color: Colors.black)),
                        )),
                    if(SearchHelper.c.priceLoading.value)
                    const Center(child:CircularProgressIndicator())
                        else if(SearchHelper.c.priceMessage.value!=null)Column(
                          children: [
                            SizedBox(height: heightSpace(10)),
                            Container(
                              padding: const EdgeInsets.all(15),
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: const Color(warningColor).withOpacity(.1),
                                  border: Border.all(color:const Color(warningColor)),
                                  borderRadius: BorderRadius.circular(12)
                                ),
                                child: CustomText(SearchHelper.c.priceMessage.value,weight:FontWeight.w500,color: Color(warningColor))),
                          ],
                        )
                        else...[
                      CustomText(trans.reservation.priceDetails,weight: FontWeight.bold,size: 2.25),
                      SizedBox(height: heightSpace(4)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(children: [
                            CustomText('${data.propertyPrice?.propertyPrice} ',weight: FontWeight.w500),
                            SvgPicture.asset('assets/icons/riyal.svg'),
                            CustomText(' x ${data.propertyPrice?.dateWithPrice.length} ${translations?.search.nights}',color: const Color(greyText)),
                          ]),
                          Row(children: [
                            CustomText('${data.propertyPrice?.totalNightPrice?.toStringAsFixed(2)} ',weight: FontWeight.w500),
                            SvgPicture.asset('assets/icons/riyal.svg'),
                          ])
                        ],
                      ),
                      if((data.propertyPrice?.serviceFee??0)>0)...[
                        SizedBox(height: heightSpace(1.5)),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                          CustomText('${translations?.propertySingle.serviceFee}'),
                          Row(
                              children: [
                                CustomText('${(data.propertyPrice?.serviceFeeDiscount ??data.propertyPrice?.serviceFeeWithDiscount ?? 0.0).toStringAsFixed(2)} '),
                                SvgPicture.asset('assets/icons/riyal.svg'),
                              ])
                        ]),
                      ],
                        if(data.propertyPrice!.yousavedWithoutSymbol!>0 && (data.propertyPrice?.monthlyWeeklyDiscountPercentage??0)>0)...[
                          SizedBox(height: heightSpace(1.5)),
                          Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(child: CustomText('${translations?.propertySingle.toJson()[data.daysDiscount]} (-${data.propertyPrice?.monthlyWeeklyDiscountPercentage}%)')),
                                Row(children: [
                                  CustomText('- ${data.propertyPrice!.yousavedWithoutSymbol?.toStringAsFixed(2)} ' ,color: const Color(successColor)),
                                  SvgPicture.asset('assets/icons/riyal.svg',color: const Color(successColor)),
                                ])
                              ]),
                        ]else if((data.propertyPrice?.dailyDiscountAmount??0)>0)...[
                        SizedBox(height: heightSpace(1.5)),
                        Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CustomText('${translations?.propertySingle.dailyDiscount} (${(data.propertyPrice?.dailyDiscountPercent??0)>0?"-${data.propertyPrice?.dailyDiscountPercent}":'<1'}%)' ),
                              Row(children: [
                                CustomText('- ${data.propertyPrice?.dailyDiscountAmount?.toStringAsFixed(2)} ' ,color: const Color(successColor)),
                                SvgPicture.asset('assets/icons/riyal.svg',color: const Color(successColor)),
                              ])
                            ]),
                      ],
                      Divider(height: heightSpace(4)),
                      Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CustomText('${trans.listing.totalPrice}',weight: FontWeight.w500,size: 2.1),
                            Row(children: [
                              CustomText('${data.propertyPrice?.totalWithDiscount?.toStringAsFixed(2)} ',weight: FontWeight.bold,size: 2.1),
                              SvgPicture.asset('assets/icons/riyal.svg'),
                            ])
                          ]),
                    ],
              ]).paddingAll(widthSpace(viewPadding)),
            ))),then: (p0) => SearchHelper.c.priceMessage.value=null);
  }
  boxWidget(icon,title){
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(6)
      ),
      child: Row(children: [
        SvgPicture.asset('assets/icons/$icon.svg',color: Colors.black87),
        SizedBox(width: widthSpace(1)),
        CustomText(title,size: 1.9,color: Colors.black87)
      ]),
    );
  }
  tagView({icon,required String title,int? value,bool isDiscount=false}){
    if(value!=null){
      title += ' $value%';
    }
    return Container(
        padding: const EdgeInsets.symmetric(
            horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isDiscount?const Color(0xffFFF3CD):Colors.white.withOpacity(.8),
          // borderRadius: BorderRadius.only(boto),
        ),
        child: Row(children: [
          if(icon!=null)...[
            Icon(icon,color: const Color(warningColor)),
            SizedBox(width: widthSpace(2))
          ],
          CustomText(
              title,
              size: 1.9,
              color: isDiscount?const Color(themeColor):null,
              weight: FontWeight.bold)
        ]));
  }
  Widget renderImage(photo) {
    return FadeInImage.assetNetwork(
      placeholder: 'assets/default-image.png',
      image: GlobalHelper.resolveImageUrl(photo),
      imageErrorBuilder: (c, e, s) => Image.asset('assets/default-image.png',width: double.maxFinite,
        fit: BoxFit.cover,),
      width: double.maxFinite,
      fit: BoxFit.cover,
      color: data.available ? null:Colors.grey,
      colorBlendMode: BlendMode.saturation,
    );
  }
}
