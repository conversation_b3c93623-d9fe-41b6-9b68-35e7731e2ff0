name: darent
description: A new Flutter project.
publish_to: 'none'
version: 3.3.1+365
environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  get: 4.7.2
  get_storage:
  carousel_slider: ^5.0.0
  google_maps_flutter:
  syncfusion_flutter_datepicker: 28.1.38

  syncfusion_flutter_sliders:
  syncfusion_flutter_calendar:
  dotted_border:
  image_picker:
  flutter_rating_bar:
  label_marker:
  custom_info_window:
#  flutter_google_places: ^0.3.0
  uuid: ^4.0.0
  intl:
  flutter_svg:
  geocoding:
  geolocator: ^13.0.2
  myfatoorah_flutter:
#  google_sign_in:
#  sign_in_with_apple:
#  crypto:
  firebase_core:
  photo_view:
  webview_flutter: ^4.13.0
#  webview_flutter_android: ^3.15.0
#  webview_flutter_wkwebview: ^3.12.0
  firebase_messaging:
  flutter_local_notifications: ^19.2.1
  sms_autofill:
  file_picker:
  url_launcher:
  permission_handler:
  http: ^1.2.0
  table_calendar:
  firebase_analytics:
  facebook_app_events: ^0.19.7
  ## for iOS app running
  # facebook_app_events:
  #   git:
  #     url: https://github.com/oddbit/flutter_facebook_app_events
  #     ref: 3e4e062ded3eb92b37e43c274ee84da4a21edfa2
#  flutter_tiktok_sdk:
  app_links: ^3.4.5
  # snapkit: ^2.0.0
  location: ^6.0.2
#  pdf: ^3.0.6
  path_provider:
#  open_file:
#  flutter_colorpicker:
  share_plus:
  pusher_channels_flutter: 2.5.0
  flutter_draggable_gridview:
  shimmer:
  map_launcher:
  flutter_pdfview:
  webengage_flutter:
  flutter_localization: ^0.1.14
  connectivity_plus: ^6.1.1
  sqflite: ^2.3.0
  path: ^1.8.3
  upgrader: ^10.0.0
  firebase_crashlytics: ^4.1.3
  reorderable_grid_view:
#  country_code_picker: ^3.0.0
  intl_phone_field: ^3.2.0
  tabby_flutter_inapp_sdk: ^1.9.0
#  sentry_flutter: ^8.9.0
#  sentry_logging: ^8.9.0
  moyasar: 2.0.16
#  syncfusion_flutter_charts:
#  flutter_dynamic_icon: ^2.1.0
  device_info_plus: ^10.1.0
  firebase_remote_config:
  # Flutter plugin that can be used in an application to call the native FingerprintJS Pro libraries and identify devices.
  fpjs_pro_plugin: ^3.3.2
  flutter_dotenv: ^5.2.1
#  flutter_config:
#    git:
#      url: https://github.com/DG-Development/flutter_config.git
  flutter_image_compress: ^2.4.0
#  image_compression_flutter:
#    git:
#      url: https://github.com/mahmoodabdulrazek96/image_compression_flutter.git

  # MoEngage SDK dependencies
  moengage_flutter: ^9.2.1
  appsflyer_sdk: ^6.16.2

dependency_overrides:
  ## for iOS app running
  pay: ^3.2.1

  # webview_flutter_wkwebview: ^3.21.0
  # Override for compatibility with Flutter 3.29.1
  collection: ^1.18.0
  js: ^0.7.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  mocktail: any
  flutter_lints: ^3.0.0
  # dart_code_metrics is discontinued, using flutter_lints instead
  # No direct replacement for dart_code_metrics
  flutter_launcher_icons: ^0.14.2
  flutter_native_splash: 2.3.10

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/icons/d_logo.png"
  adaptive_icon_background: "#F5C33E"
  adaptive_icon_foreground: "assets/icons/app-icon.png"
  remove_alpha_ios: true


flutter_native_splash:
  color: "#F5C33E"
  image: 'assets/icons/darent_logo.png'
  icon_background_color: "#F5C33E"
  ios: true

  # add this
  android_12:
    icon_background_color: "#F5C33E"
    #image: 'assets/icons/darent_logo.png'
    icon_background_color_dark: "#F5C33E"
    #image_dark: 'assets/icons/darent_logo.png'

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  assets:
    - assets/
    - assets/icons/
    - assets/icons/payment_methods/
    - assets/icons/host_new/
    - .env


  fonts:
    - family: PingAR+LT
      fonts:
#        - asset: fonts/font-thin.ttf
#          weight: 200
        - asset: fonts/font-light.ttf
          weight: 300
        - asset: fonts/font-regular.ttf
          weight: 400
        - asset: fonts/font-medium.ttf
          weight: 500
        - asset: fonts/font-bold.ttf
          weight: 600
        - asset: fonts/font-heavy.ttf
          weight: 600

#  fonts:
#    - family: PingAR+LT
#      fonts:
#        - asset: fonts/PingAR+LT-Thin.otf
#        - asset: fonts/PingAR+LT-Light.otf
#        - asset: fonts/PingAR+LT-ExtraLight.otf
#        - asset: fonts/PingAR+LT-Regular.otf
#        - asset: fonts/PingAR+LT-Medium.otf
#        - asset: fonts/PingAR+LT-Bold.otf
#        - asset: fonts/PingAR+LT-Black.otf
#        - asset: fonts/PingAR+LT-Heavy.otf
#        - asset: fonts/PingAR+LT-Hairline.otf


