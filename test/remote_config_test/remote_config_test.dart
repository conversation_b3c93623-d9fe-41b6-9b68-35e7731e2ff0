// import 'dart:async';
//
// import 'package:darent/helperMethods/remote_config.dart';
// import 'package:darent/utils/constants.dart';
// import 'package:firebase_remote_config/firebase_remote_config.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:mocktail/mocktail.dart';
//
// import 'fakes/dummy_remote_config_update.dart';
// import 'stubs/firebase_remote_config_error_stub.dart';
// import 'stubs/firebase_remote_config_stub.dart';
//
// class _StreamTracksSubscriptions<ElementType> extends Stream<ElementType> {
//   _StreamTracksSubscriptions(this._stream) : _subscriptionsCount = 0;
//
//   late int _subscriptionsCount;
//   final Stream<ElementType> _stream;
//
//   int get subscriptionsCount => _subscriptionsCount;
//
//   @override
//   StreamSubscription<ElementType> listen(
//     void Function(ElementType event)? onData, {
//     Function? onError,
//     void Function()? onDone,
//     bool? cancelOnError,
//   }) {
//     _subscriptionsCount++;
//     return _stream.listen(
//       onData,
//       onDone: onDone,
//       onError: onError,
//       cancelOnError: cancelOnError,
//     );
//   }
// }
//
// class _OnRemoteConfigUpdated {
//   _OnRemoteConfigUpdated() : _callsCount = 0;
//
//   late int _callsCount;
//
//   void call() => _callsCount++;
//
//   int get callsCount => _callsCount;
// }
//
// late RemoteConfig _remoteConfig;
// late FirebaseRemoteConfig _firebaseRemoteConfig;
// late _OnRemoteConfigUpdated _onRemoteConfigUpdated;
// late StreamController<RemoteConfigUpdate> _updatesStreamController;
// late _StreamTracksSubscriptions<RemoteConfigUpdate> _updatesStream;
//
// void main() {
//   setUp(() async {
//     _updatesStreamController = StreamController.broadcast();
//     _updatesStream =
//         _StreamTracksSubscriptions(_updatesStreamController.stream);
//     _firebaseRemoteConfig =
//         FirebaseRemoteConfigStub(onConfigUpdated: _updatesStream);
//     _onRemoteConfigUpdated = _OnRemoteConfigUpdated();
//     _remoteConfig = await RemoteConfig.instance(
//       remoteConfig: _firebaseRemoteConfig,
//       onRemoteConfigUpdated: _onRemoteConfigUpdated,
//     );
//
//     _initializeRequiredEnvVars();
//   });
//
//   group('Remote Config', () {
//     test('default remote config on failed fetching', () async {
//       final remoteConfig = await RemoteConfig.instance(
//         onRemoteConfigUpdated: () {},
//         remoteConfig: FirebaseRemoteConfigErrorStub(),
//       );
//
//       expect(
//         remoteConfig,
//         _EqualsRemoteConfigWith.defaultVariables(),
//       );
//     });
//
//     test('successful fetching', () async {
//       expect(
//         _remoteConfig,
//         _EqualsRemoteConfigWith(
//           dobGapeYears: 15,
//           imagesBaseUrl: '',
//           allowedCountries: '',
//           tamayouzEnabled: false,
//           licenseMandatory: false,
//           enableNoOfApartments: true,
//         ),
//       );
//     });
//
//     test('after update', () async {
//       expectLater(
//         _updatesStream,
//         emitsInOrder([
//           predicate((_) {
//             expect(_remoteConfig.imagesBaseUrl, '/test');
//             expect(_onRemoteConfigUpdated.callsCount, 1);
//             return true;
//           }),
//         ]),
//       );
//
//       expect(_remoteConfig.imagesBaseUrl, '');
//       expect(_onRemoteConfigUpdated.callsCount, 0);
//
//       when(() => _firebaseRemoteConfig.activate()).thenAnswer((_) {
//         when(() => _firebaseRemoteConfig.getString('imagesBaseUrl'))
//             .thenReturn('/test');
//         return Future.value(true);
//       });
//       _updatesStreamController.add(DummyRemoteConfigUpdate());
//     });
//
//     test('listens to updates only once on variable', () {
//       _remoteConfig.dobGapeYears;
//       _remoteConfig.dobGapeYears;
//
//       expect(_updatesStream.subscriptionsCount, 1);
//     });
//   });
// }
//
// void _initializeRequiredEnvVars() {
//   // FlutterConfig.loadValueForTesting({
//   //   'BASE_URL': 'https://darent.com',
//   // });
// }
//
// class _EqualsRemoteConfigWith extends Matcher implements RemoteConfig {
//   _EqualsRemoteConfigWith.defaultVariables()
//       : this(
//           dobGapeYears: 15,
//           allowedCountries: '',
//           imagesBaseUrl: baseUrl,
//           tamayouzEnabled: false,
//           licenseMandatory: false,
//           enableNoOfApartments: true,
//         );
//
//   _EqualsRemoteConfigWith({
//     required this.dobGapeYears,
//     required this.imagesBaseUrl,
//     required this.allowedCountries,
//     required this.tamayouzEnabled,
//     required this.licenseMandatory,
//     required this.enableNoOfApartments,
//   });
//
//   @override
//   final int dobGapeYears;
//
//   @override
//   final String imagesBaseUrl;
//
//   @override
//   final bool tamayouzEnabled;
//
//   @override
//   final bool licenseMandatory;
//
//   @override
//   final String allowedCountries;
//
//   @override
//   final bool enableNoOfApartments;
//
//   @override
//   Description describe(
//     Description description,
//   ) =>
//       _matcher().describe(description);
//
//   @override
//   bool matches(
//     item,
//     Map matchState,
//   ) =>
//       _matcher().matches(item, matchState);
//
//   Matcher _matcher() {
//     return predicate<RemoteConfig>((remoteConfig) {
//       expect(remoteConfig.dobGapeYears, dobGapeYears);
//       expect(remoteConfig.imagesBaseUrl, imagesBaseUrl);
//       expect(remoteConfig.tamayouzEnabled, tamayouzEnabled);
//       expect(remoteConfig.licenseMandatory, licenseMandatory);
//       expect(remoteConfig.allowedCountries, allowedCountries);
//       expect(remoteConfig.enableNoOfApartments, enableNoOfApartments);
//
//       return true;
//     });
//   }
//
//   @override
//   bool get enableForceUpdate => throw UnimplementedError();
// }
