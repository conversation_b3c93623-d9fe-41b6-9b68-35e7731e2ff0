import 'dart:async';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:mocktail/mocktail.dart';

class FirebaseRemoteConfigErrorStub extends Mock
    implements FirebaseRemoteConfig {
  @override
  Future<void> setConfigSettings(
    RemoteConfigSettings remoteConfigSettings,
  ) =>
      throw Exception();

  @override
  Future<bool> fetchAndActivate() => throw Exception();

  @override
  Future<bool> activate() => throw Exception();
}