import 'dart:async';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:mocktail/mocktail.dart';

class FirebaseRemoteConfigStub extends Mock implements FirebaseRemoteConfig {
  FirebaseRemoteConfigStub({
    required Stream<RemoteConfigUpdate> onConfigUpdated,
    Map<String, dynamic> remoteConfigValues = const <String, dynamic>{
      'dobGapeYears': 15,
      'imagesBaseUrl': '',
      'allowedCountries': '',
      'tamayouzEnabled': false,
      'licenseMandatory': false,
      'enableNoOfApartments': true,
    },
  }) {
    when(() => activate()).thenAnswer((_) async => true);
    when(() => fetchAndActivate()).thenAnswer((_) async => true);
    when(() => this.onConfigUpdated).thenAnswer((_) => onConfigUpdated);
    _fillRemoteConfigVariables(remoteConfigValues);
  }

  @override
  Future<void> setConfigSettings(
    RemoteConfigSettings remoteConfigSettings,
  ) =>
      Future.value();

  void _fillRemoteConfigVariables(Map<String, dynamic> remoteConfig) {
    for (final entry in remoteConfig.entries) {
      _stubGetValueBy(entry);
    }
  }

  void _stubGetValueBy(
    MapEntry<String, dynamic> entry,
  ) {
    if (entry.value is String) {
      when(() => getString(entry.key)).thenReturn(entry.value);
    }

    if (entry.value is bool) {
      when(() => getBool(entry.key)).thenReturn(entry.value);
    }
    if (entry.value is int) {
      when(() => getInt(entry.key)).thenReturn(entry.value);
    }
  }
}